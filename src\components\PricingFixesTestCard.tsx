/**
 * PRICING FIXES TEST CARD
 * 
 * Test component to demonstrate the Pak'nSave pricing fixes
 * Shows before/after comparison of problematic products
 */

import React, { useState, useMemo } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { useTheme } from '../context/ThemeContext';
import { IProduct } from '../types/shoppingList';
import { ProductDeduplicationService } from '../services/productDeduplicationService';
import { unitPriceCalculationService } from '../services/unitPriceCalculationService';
import { Ionicons } from '@expo/vector-icons';

interface PricingFixesTestCardProps {
  onClose?: () => void;
}

export const PricingFixesTestCard: React.FC<PricingFixesTestCardProps> = ({ onClose }) => {
  const { colors } = useTheme();
  const [showDetails, setShowDetails] = useState(false);

  // Test products that were previously problematic
  const testProducts: IProduct[] = useMemo(() => [
    {
      id: '1',
      name: 'Roasted Salted Pistachios',
      price: 44.90,
      store: 'paknsave',
      size: 'per kg',
      category: 'nuts',
      brand: 'Generic'
    },
    {
      id: '2',
      name: 'Roasted Salted Pistachios',
      price: 8.00,
      store: 'woolworths',
      size: '170g',
      category: 'nuts',
      brand: 'Generic'
    },
    {
      id: '3',
      name: 'Fresh Milk',
      price: 3.50,
      store: 'paknsave',
      size: '1L',
      category: 'dairy',
      brand: 'Anchor'
    },
    {
      id: '4',
      name: 'Fresh Milk',
      price: 7.00,
      store: 'newworld',
      size: '2L',
      category: 'dairy',
      brand: 'Anchor'
    }
  ], []);

  // Test with old configuration (problematic)
  const oldResults = useMemo(() => {
    const service = new ProductDeduplicationService({
      requireSizeMatch: false, // OLD: Allowed different sizes to match
      enableFuzzySizeMatching: true,
      nameSimilarityThreshold: 0.85,
      minimumConfidence: 0.7
    });
    
    return service.deduplicateProducts(testProducts);
  }, [testProducts]);

  // Test with new configuration (fixed)
  const newResults = useMemo(() => {
    const service = new ProductDeduplicationService({
      requireSizeMatch: true, // NEW: Requires size matching
      enableFuzzySizeMatching: true,
      nameSimilarityThreshold: 0.85,
      minimumConfidence: 0.7
    });
    
    return service.deduplicateProducts(testProducts);
  }, [testProducts]);

  // Analyze unit prices
  const unitPriceAnalysis = useMemo(() => {
    return testProducts.map(product => ({
      product,
      unitPrice: unitPriceCalculationService.calculateUnitPrice(product),
      problems: unitPriceCalculationService.detectProblematicMatches([product])
    }));
  }, [testProducts]);

  const renderComparisonSection = (title: string, results: any, isFixed: boolean) => (
    <View style={[styles.section, { borderColor: colors.border }]}>
      <View style={styles.sectionHeader}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>{title}</Text>
        <View style={[
          styles.statusBadge, 
          { backgroundColor: isFixed ? colors.success || '#4CAF50' : colors.error || '#F44336' }
        ]}>
          <Text style={styles.statusText}>
            {isFixed ? '✅ FIXED' : '❌ PROBLEMATIC'}
          </Text>
        </View>
      </View>
      
      <Text style={[styles.statsText, { color: colors.textSecondary }]}>
        {results.stats.originalCount} products → {results.stats.groupCount} groups → {results.stats.unifiedCount} unified
      </Text>
      
      {results.unifiedProducts.map((product: any, index: number) => (
        <View key={index} style={[styles.productCard, { backgroundColor: colors.backgroundSecondary }]}>
          <Text style={[styles.productName, { color: colors.text }]}>{product.name}</Text>
          <Text style={[styles.productStores, { color: colors.textSecondary }]}>
            Available at: {product.allStores.join(', ')}
          </Text>
          <View style={styles.priceRow}>
            <Text style={[styles.priceRange, { color: colors.primary }]}>
              ${product.lowestPrice.toFixed(2)} - ${product.highestPrice.toFixed(2)}
            </Text>
            {product.maxSavings > 0 && (
              <Text style={[
                styles.savings, 
                { color: product.maxSavings > 20 ? colors.error || '#F44336' : colors.success || '#4CAF50' }
              ]}>
                Save ${product.maxSavings.toFixed(2)}
                {product.maxSavings > 20 && ' ⚠️'}
              </Text>
            )}
          </View>
        </View>
      ))}
    </View>
  );

  return (
    <ScrollView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.text }]}>
          🔧 Pak'nSave Pricing Fixes Test
        </Text>
        {onClose && (
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={colors.text} />
          </TouchableOpacity>
        )}
      </View>

      <Text style={[styles.description, { color: colors.textSecondary }]}>
        This test demonstrates how the size normalization fixes prevent incorrect price comparisons 
        between per-kg products and fixed-size products.
      </Text>

      {renderComparisonSection('Before Fixes (Problematic)', oldResults, false)}
      {renderComparisonSection('After Fixes (Corrected)', newResults, true)}

      <TouchableOpacity 
        style={[styles.detailsButton, { backgroundColor: colors.primary }]}
        onPress={() => setShowDetails(!showDetails)}
      >
        <Text style={styles.detailsButtonText}>
          {showDetails ? 'Hide' : 'Show'} Unit Price Analysis
        </Text>
        <Ionicons 
          name={showDetails ? 'chevron-up' : 'chevron-down'} 
          size={20} 
          color="white" 
        />
      </TouchableOpacity>

      {showDetails && (
        <View style={[styles.section, { borderColor: colors.border }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Unit Price Analysis</Text>
          
          {unitPriceAnalysis.map((analysis, index) => (
            <View key={index} style={[styles.analysisCard, { backgroundColor: colors.backgroundSecondary }]}>
              <Text style={[styles.productName, { color: colors.text }]}>
                {analysis.product.name} ({analysis.product.store})
              </Text>
              <Text style={[styles.originalPrice, { color: colors.textSecondary }]}>
                Original: ${analysis.product.price} ({analysis.product.size})
              </Text>
              <Text style={[styles.unitPrice, { color: colors.primary }]}>
                Unit Price: {unitPriceCalculationService.formatUnitPrice(analysis.unitPrice)}
              </Text>
              <Text style={[styles.comparable, { 
                color: analysis.unitPrice.isComparable ? colors.success || '#4CAF50' : colors.warning || '#FF9500' 
              }]}>
                Comparable: {analysis.unitPrice.isComparable ? 'Yes' : 'No'}
              </Text>
            </View>
          ))}
        </View>
      )}

      <View style={[styles.summarySection, { backgroundColor: colors.backgroundSecondary }]}>
        <Text style={[styles.summaryTitle, { color: colors.text }]}>✅ Fixes Applied</Text>
        <Text style={[styles.summaryItem, { color: colors.textSecondary }]}>
          • Per-kg products no longer match with fixed-size products
        </Text>
        <Text style={[styles.summaryItem, { color: colors.textSecondary }]}>
          • Size matching is now required by default
        </Text>
        <Text style={[styles.summaryItem, { color: colors.textSecondary }]}>
          • Unit price calculations are accurate
        </Text>
        <Text style={[styles.summaryItem, { color: colors.textSecondary }]}>
          • Problematic matches are detected and prevented
        </Text>
        <Text style={[styles.summaryItem, { color: colors.textSecondary }]}>
          • Misleading savings alerts are eliminated
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 8,
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 20,
  },
  section: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  statusText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  statsText: {
    fontSize: 12,
    marginBottom: 12,
  },
  productCard: {
    padding: 12,
    borderRadius: 6,
    marginBottom: 8,
  },
  productName: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  productStores: {
    fontSize: 12,
    marginBottom: 8,
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  priceRange: {
    fontSize: 14,
    fontWeight: '600',
  },
  savings: {
    fontSize: 12,
    fontWeight: '600',
  },
  detailsButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  detailsButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    marginRight: 8,
  },
  analysisCard: {
    padding: 12,
    borderRadius: 6,
    marginBottom: 8,
  },
  originalPrice: {
    fontSize: 12,
    marginBottom: 4,
  },
  unitPrice: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  comparable: {
    fontSize: 12,
    fontWeight: '500',
  },
  summarySection: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  summaryItem: {
    fontSize: 14,
    marginBottom: 6,
    paddingLeft: 8,
  },
});
