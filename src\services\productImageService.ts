/**
 * Product Image Service
 * 
 * Handles fetching and caching product images from Supabase,
 * specifically for brand dropdown selections and product suggestions.
 */

import { supabase } from '../supabase/client';
import { Database } from '../supabase/types';

type ProductRow = Database['public']['Tables']['products']['Row'];

export interface ProductImage {
  id: string;
  productName: string;
  brand: string;
  imageUrl: string;
  store: string;
  thumbnailUrl?: string;
  lastUpdated: string;
}

export interface BrandImageOptions {
  productName: string;
  brand: string;
  preferredStore?: 'woolworths' | 'newworld' | 'paknsave';
  size?: 'thumbnail' | 'full';
  fallbackToGeneric?: boolean;
}

class ProductImageService {
  private imageCache = new Map<string, ProductImage>();
  private brandImageCache = new Map<string, ProductImage[]>();
  private fallbackImages = new Map<string, string>();

  constructor() {
    this.initializeFallbackImages();
  }

  /**
   * Initialize fallback images for common product categories
   */
  private initializeFallbackImages() {
    this.fallbackImages.set('dairy', '🥛');
    this.fallbackImages.set('meat', '🥩');
    this.fallbackImages.set('produce', '🥬');
    this.fallbackImages.set('bakery', '🍞');
    this.fallbackImages.set('pantry', '🥫');
    this.fallbackImages.set('frozen', '🧊');
    this.fallbackImages.set('beverages', '🥤');
    this.fallbackImages.set('snacks', '🍿');
    this.fallbackImages.set('household', '🧽');
    this.fallbackImages.set('health & beauty', '🧴');
    this.fallbackImages.set('cereal', '🥣');
    this.fallbackImages.set('condiments', '🍯');
  }

  /**
   * Get product image for a specific brand
   */
  async getBrandImage(options: BrandImageOptions): Promise<ProductImage | null> {
    const { productName, brand, preferredStore, size = 'full', fallbackToGeneric = true } = options;
    
    const cacheKey = `${productName}_${brand}_${preferredStore || 'any'}_${size}`;
    
    if (this.imageCache.has(cacheKey)) {
      return this.imageCache.get(cacheKey)!;
    }

    try {
      console.log(`🖼️ Fetching image for: ${productName} - ${brand}`);

      // Build query to find exact product match
      let query = supabase
        .from('products')
        .select(`
          id, name, brand, image_url, thumbnail_url, store, 
          category, price
        `)
        .not('image_url', 'is', null)
        .not('price', 'is', null);

      // Filter by product name (fuzzy match)
      const productNameVariants = this.generateProductNameVariants(productName);
      const nameFilters = productNameVariants.map(variant => `name.ilike.%${variant}%`).join(',');
      query = query.or(nameFilters);

      // Filter by brand if specified
      if (brand && brand !== 'Generic') {
        query = query.ilike('brand', `%${brand}%`);
      }

      // Prefer specific store if provided
      if (preferredStore) {
        query = query.eq('store', preferredStore);
      }

      // Order by relevance
      query = query
        .order('price', { ascending: true })
        .limit(10);

      const { data: products, error } = await query;

      if (error) {
        console.error('Error fetching product images:', error);
        return null;
      }

      if (!products || products.length === 0) {
        console.log(`❌ No images found for: ${productName} - ${brand}`);
        
        if (fallbackToGeneric) {
          return this.getFallbackImage(productName, brand);
        }
        return null;
      }

      // Find best match
      const bestMatch = this.findBestImageMatch(products, productName, brand, preferredStore);
      
      if (bestMatch) {
        const imageUrl = size === 'thumbnail' && bestMatch.thumbnail_url 
          ? bestMatch.thumbnail_url 
          : bestMatch.image_url;

        if (imageUrl) {
          const productImage: ProductImage = {
            id: bestMatch.id,
            productName: bestMatch.name,
            brand: bestMatch.brand || 'Generic',
            imageUrl,
            store: bestMatch.store,
            thumbnailUrl: bestMatch.thumbnail_url || undefined,
            lastUpdated: bestMatch.updated_at || new Date().toISOString()
          };

          // Cache the result
          this.imageCache.set(cacheKey, productImage);
          console.log(`✅ Found image for: ${productName} - ${brand}`);
          
          return productImage;
        }
      }

      // Fallback if no good match found
      if (fallbackToGeneric) {
        return this.getFallbackImage(productName, brand);
      }

      return null;

    } catch (error) {
      console.error('Product image service error:', error);
      return null;
    }
  }

  /**
   * Get all available brand images for a product
   */
  async getBrandImages(productName: string, maxResults = 10): Promise<ProductImage[]> {
    const cacheKey = `brands_${productName}_${maxResults}`;
    
    if (this.brandImageCache.has(cacheKey)) {
      return this.brandImageCache.get(cacheKey)!;
    }

    try {
      console.log(`🖼️ Fetching brand images for: ${productName}`);

      const productNameVariants = this.generateProductNameVariants(productName);
      const nameFilters = productNameVariants.map(variant => `name.ilike.%${variant}%`).join(',');

      const { data: products, error } = await supabase
        .from('products')
        .select(`
          id, name, brand, image_url, thumbnail_url, store, 
          category, updated_at, availability
        `)
        .or(nameFilters)
        .eq('availability', 'available')
        .not('image_url', 'is', null)
        .not('brand', 'is', null)
        .order('price', { ascending: true })
        .limit(50);

      if (error || !products) {
        console.error('Error fetching brand images:', error);
        return [];
      }

             // Group by brand and pick best image for each
       const brandGroups = new Map<string, any[]>();
       
       products.forEach(product => {
         const brand = product.brand || 'Generic';
         if (!brandGroups.has(brand)) {
           brandGroups.set(brand, []);
         }
         brandGroups.get(brand)!.push(product);
       });

      const brandImages: ProductImage[] = [];

      brandGroups.forEach((brandProducts, brand) => {
        // Find best product for this brand (first one since they're sorted by price)
        const bestProduct = brandProducts[0];

        if (bestProduct?.image_url) {
          brandImages.push({
            id: bestProduct.id,
            productName: bestProduct.name,
            brand: brand,
            imageUrl: bestProduct.image_url,
            store: bestProduct.store,
            thumbnailUrl: bestProduct.thumbnail_url || undefined,
            lastUpdated: bestProduct.updated_at || new Date().toISOString()
          });
        }
      });

      const result = brandImages.slice(0, maxResults);
      this.brandImageCache.set(cacheKey, result);
      
      console.log(`✅ Found ${result.length} brand images for: ${productName}`);
      return result;

    } catch (error) {
      console.error('Brand images fetch error:', error);
      return [];
    }
  }

  /**
   * Generate product name variants for better matching
   */
  private generateProductNameVariants(productName: string): string[] {
    const variants = [productName.toLowerCase().trim()];
    
    // Remove common words
    const commonWords = ['organic', 'free', 'range', 'fresh', 'premium', 'select'];
    let cleanName = productName.toLowerCase();
    commonWords.forEach(word => {
      cleanName = cleanName.replace(new RegExp(`\\b${word}\\b`, 'g'), '').trim();
    });
    
    if (cleanName !== productName.toLowerCase()) {
      variants.push(cleanName);
    }

    // Add singular/plural variants
    if (productName.endsWith('s') && productName.length > 3) {
      variants.push(productName.slice(0, -1));
    } else {
      variants.push(productName + 's');
    }

    // Add common abbreviations
    const abbreviations: Record<string, string> = {
      'tomato sauce': 'sauce',
      'baked beans': 'beans',
      'breakfast cereal': 'cereal',
      'toilet paper': 'tissue',
      'dishwashing liquid': 'detergent'
    };

    const normalized = productName.toLowerCase();
    Object.entries(abbreviations).forEach(([full, abbrev]) => {
      if (normalized.includes(full)) {
        variants.push(abbrev);
      } else if (normalized.includes(abbrev)) {
        variants.push(full);
      }
    });

    return [...new Set(variants)];
  }

  /**
   * Find the best image match from search results
   */
  private findBestImageMatch(
    products: any[],
    targetProduct: string,
    targetBrand: string,
    preferredStore?: string
  ): any {
    if (products.length === 0) return null;

    const normalizedTarget = targetProduct.toLowerCase();
    const normalizedBrand = targetBrand.toLowerCase();

    // Score each product
    const scoredProducts = products.map(product => {
      let score = 0;
      const productName = product.name.toLowerCase();
      const productBrand = (product.brand || '').toLowerCase();

      // Exact name match
      if (productName === normalizedTarget) score += 50;
      else if (productName.includes(normalizedTarget)) score += 30;
      else if (normalizedTarget.includes(productName)) score += 20;

      // Brand match
      if (targetBrand !== 'Generic') {
        if (productBrand === normalizedBrand) score += 40;
        else if (productBrand.includes(normalizedBrand)) score += 25;
        else if (normalizedBrand.includes(productBrand)) score += 15;
      }

      // Store preference
      if (preferredStore && product.store === preferredStore) score += 20;

      // Image quality bonus (prefer full images over thumbnails)
      if (product.image_url) score += 5;
      if (product.thumbnail_url) score += 2;

      return { product, score };
    });

    // Return highest scoring product
    const best = scoredProducts.sort((a, b) => b.score - a.score)[0];
    return best.score > 0 ? best.product : null;
  }

  /**
   * Get fallback image when no real image is available
   */
  private getFallbackImage(productName: string, brand: string): ProductImage {
    // Determine category for fallback emoji
    const normalizedName = productName.toLowerCase();
    let category = 'other';
    
    if (normalizedName.includes('milk') || normalizedName.includes('cheese') || normalizedName.includes('butter')) {
      category = 'dairy';
    } else if (normalizedName.includes('meat') || normalizedName.includes('chicken') || normalizedName.includes('beef')) {
      category = 'meat';
    } else if (normalizedName.includes('bread') || normalizedName.includes('cake')) {
      category = 'bakery';
    } else if (normalizedName.includes('apple') || normalizedName.includes('banana') || normalizedName.includes('vegetable')) {
      category = 'produce';
    } else if (normalizedName.includes('cereal') || normalizedName.includes('muesli')) {
      category = 'cereal';
    }

    const fallbackEmoji = this.fallbackImages.get(category) || '📦';

    return {
      id: `fallback_${productName}_${brand}`,
      productName,
      brand,
      imageUrl: `data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100"><text y="50" font-size="50">${fallbackEmoji}</text></svg>`,
      store: 'generic',
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * Preload images for a list of products (for performance)
   */
  async preloadImages(products: Array<{ name: string; brand: string }>): Promise<void> {
    const loadPromises = products.map(({ name, brand }) =>
      this.getBrandImage({ productName: name, brand, fallbackToGeneric: false })
        .catch(error => console.warn(`Failed to preload image for ${name} - ${brand}:`, error))
    );

    await Promise.allSettled(loadPromises);
    console.log(`📦 Preloaded images for ${products.length} products`);
  }

  /**
   * Clear image cache
   */
  clearCache(): void {
    this.imageCache.clear();
    this.brandImageCache.clear();
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { imageCache: number; brandImageCache: number } {
    return {
      imageCache: this.imageCache.size,
      brandImageCache: this.brandImageCache.size
    };
  }
}

export const productImageService = new ProductImageService(); 