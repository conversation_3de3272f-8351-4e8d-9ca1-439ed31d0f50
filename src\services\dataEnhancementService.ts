/**
 * Data Enhancement Service
 * 
 * Enhances scraped product data with search keywords, aliases, and metadata
 * to improve search functionality and user experience.
 */

import { supabase } from '../supabase/client';

export interface ProductEnhancement {
  id: string;
  shopping_aliases: string[];
  tags: string[];
  dietary_info: Record<string, any>;
  seasonal_info: Record<string, any>;
  is_common_item: boolean;
  search_rank: number;
}

export interface NZProductKeywords {
  [category: string]: {
    commonTerms: string[];
    brandMappings: Record<string, string[]>;
    aliases: Record<string, string[]>;
  };
}

// NZ-specific product keywords and aliases
const NZ_PRODUCT_KEYWORDS: NZProductKeywords = {
  'Dairy & Eggs': {
    commonTerms: ['milk', 'cheese', 'butter', 'yogurt', 'cream', 'eggs'],
    brandMappings: {
      'Anchor': ['anchor milk', 'blue top', 'green top', 'red top'],
      'Mainland': ['mainland cheese', 'tasty cheese'],
      'Tararua': ['tararua butter'],
      'Yoplait': ['yoplait yogurt', 'yoghurt']
    },
    aliases: {
      'milk': ['malk', 'mylk', 'blue top', 'green top', 'red top', 'trim milk', 'whole milk'],
      'cheese': ['chees', 'tasty cheese', 'mild cheese', 'vintage cheese'],
      'butter': ['butta', 'margarine', 'marg'],
      'yogurt': ['yoghurt', 'yogurt', 'yoghurt pot'],
      'eggs': ['egg', 'free range eggs', 'cage free eggs']
    }
  },
  'Meat & Seafood': {
    commonTerms: ['chicken', 'beef', 'lamb', 'pork', 'fish', 'salmon', 'mince'],
    brandMappings: {
      'Tegel': ['tegel chicken'],
      'Hellers': ['hellers sausages', 'hellers bacon'],
      'Regal': ['regal salmon']
    },
    aliases: {
      'chicken': ['chook', 'chiken', 'chicken breast', 'chicken thigh'],
      'beef': ['steak', 'mince', 'beef mince', 'scotch fillet'],
      'lamb': ['lamb chops', 'lamb leg', 'lamb shoulder'],
      'pork': ['bacon', 'ham', 'pork chops'],
      'fish': ['salmon', 'snapper', 'fish fillets'],
      'mince': ['mince meat', 'beef mince', 'lamb mince']
    }
  },
  'Bakery': {
    commonTerms: ['bread', 'rolls', 'bagels', 'muffins', 'croissants'],
    brandMappings: {
      'Tip Top': ['tip top bread'],
      'Molenberg': ['molenberg bread'],
      'Burgen': ['burgen bread']
    },
    aliases: {
      'bread': ['loaf', 'white bread', 'brown bread', 'wholemeal bread', 'grain bread'],
      'rolls': ['bread rolls', 'dinner rolls', 'burger buns'],
      'bagels': ['bagel', 'everything bagel'],
      'muffins': ['muffin', 'blueberry muffin', 'chocolate muffin']
    }
  },
  'Pantry': {
    commonTerms: ['rice', 'pasta', 'flour', 'sugar', 'oil', 'sauce', 'spices'],
    brandMappings: {
      'Watties': ['watties tomato sauce', 'watties baked beans'],
      'Maggi': ['maggi noodles', 'maggi soup'],
      'Uncle Bens': ['uncle bens rice']
    },
    aliases: {
      'rice': ['basmati rice', 'jasmine rice', 'brown rice', 'white rice'],
      'pasta': ['spaghetti', 'penne', 'fusilli', 'macaroni'],
      'sauce': ['tomato sauce', 'ketchup', 'pasta sauce', 'soy sauce'],
      'oil': ['olive oil', 'vegetable oil', 'canola oil'],
      'flour': ['plain flour', 'self raising flour', 'wholemeal flour']
    }
  },
  'Snacks & Confectionery': {
    commonTerms: ['chips', 'chocolate', 'biscuits', 'nuts', 'lollies'],
    brandMappings: {
      'Bluebird': ['bluebird chips', 'chippies'],
      'Cadbury': ['cadbury chocolate'],
      'Griffins': ['griffins biscuits'],
      'Whittakers': ['whittakers chocolate']
    },
    aliases: {
      'chips': ['chippies', 'crisps', 'potato chips'],
      'chocolate': ['choc', 'chocolate bar', 'dark chocolate', 'milk chocolate'],
      'biscuits': ['cookies', 'crackers', 'tim tams'],
      'nuts': ['peanuts', 'almonds', 'cashews', 'mixed nuts'],
      'lollies': ['candy', 'sweets', 'gummies', 'jellybeans']
    }
  },
  'Beverages': {
    commonTerms: ['water', 'juice', 'soft drink', 'coffee', 'tea', 'wine', 'beer'],
    brandMappings: {
      'Coca Cola': ['coke', 'coca cola', 'diet coke'],
      'L&P': ['lemon and paeroa', 'l&p', 'lnp'],
      'Steinlager': ['steinlager beer'],
      'Sauvignon Blanc': ['sav blanc', 'sauvignon blanc']
    },
    aliases: {
      'soft drink': ['fizzy drink', 'soda', 'pop', 'fizzy'],
      'juice': ['orange juice', 'apple juice', 'fruit juice'],
      'coffee': ['instant coffee', 'ground coffee', 'coffee beans'],
      'tea': ['tea bags', 'green tea', 'black tea', 'herbal tea'],
      'wine': ['red wine', 'white wine', 'sav blanc', 'pinot noir'],
      'beer': ['lager', 'ale', 'craft beer']
    }
  },
  'Household': {
    commonTerms: ['toilet paper', 'paper towels', 'cleaning', 'laundry', 'dishwashing'],
    brandMappings: {
      'Kleenex': ['kleenex tissues'],
      'Finish': ['finish dishwasher tablets'],
      'Persil': ['persil laundry powder']
    },
    aliases: {
      'toilet paper': ['loo paper', 'dunny roll', 'toilet roll', 'tp'],
      'paper towels': ['kitchen towels', 'paper towel'],
      'cleaning': ['cleaning products', 'surface cleaner', 'bathroom cleaner'],
      'laundry': ['washing powder', 'fabric softener', 'laundry detergent'],
      'dishwashing': ['dishwashing liquid', 'dish soap', 'dishwasher tablets']
    }
  }
};

// Common NZ slang and misspellings
const COMMON_MISSPELLINGS: Record<string, string[]> = {
  'avocado': ['avacado', 'avacados', 'avos'],
  'broccoli': ['brocoli', 'brocolli'],
  'zucchini': ['zuchinni', 'zuchini', 'courgette'],
  'capsicum': ['capscium', 'bell pepper', 'pepper'],
  'tomato': ['tomatoe', 'tomatos'],
  'potato': ['potatoe', 'potatos', 'spuds'],
  'banana': ['bannana', 'banannas'],
  'strawberry': ['strawbery', 'strawberrys'],
  'chocolate': ['choclate', 'chocolat', 'choc'],
  'biscuit': ['biscut', 'biscit', 'cookie'],
  'yogurt': ['yoghurt', 'yogurt'],
  'muesli': ['museli', 'muesly'],
  'spaghetti': ['spagetti', 'spaghetii'],
  'parmesan': ['parmasan', 'parmasean']
};

// Dietary and allergen information
const DIETARY_KEYWORDS: Record<string, string[]> = {
  'gluten_free': ['gluten free', 'gf', 'coeliac', 'celiac'],
  'dairy_free': ['dairy free', 'lactose free', 'vegan', 'plant based'],
  'vegetarian': ['vegetarian', 'veggie', 'meat free'],
  'vegan': ['vegan', 'plant based', 'dairy free'],
  'organic': ['organic', 'bio', 'natural'],
  'low_fat': ['low fat', 'lite', 'light', 'reduced fat'],
  'sugar_free': ['sugar free', 'no sugar', 'diet', 'zero sugar'],
  'high_protein': ['high protein', 'protein', 'lean'],
  'keto': ['keto', 'ketogenic', 'low carb'],
  'paleo': ['paleo', 'paleolithic', 'caveman diet']
};

class DataEnhancementService {
  /**
   * Enhance all products with search metadata
   */
  async enhanceAllProducts(): Promise<void> {
    console.log('🔄 Starting product enhancement process...');
    
    try {
      // Get all products that need enhancement
      const { data: products, error } = await supabase
        .from('products')
        .select('id, name, brand, category, description')
        .is('shopping_aliases', null);

      if (error) {
        throw new Error(`Failed to fetch products: ${error.message}`);
      }

      if (!products || products.length === 0) {
        console.log('✅ No products need enhancement');
        return;
      }

      console.log(`📊 Enhancing ${products.length} products...`);

      // Process products in batches
      const batchSize = 50;
      for (let i = 0; i < products.length; i += batchSize) {
        const batch = products.slice(i, i + batchSize);
        await this.enhanceProductBatch(batch);
        console.log(`✅ Enhanced batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(products.length / batchSize)}`);
      }

      console.log('🎉 Product enhancement completed!');
    } catch (error) {
      console.error('❌ Product enhancement failed:', error);
      throw error;
    }
  }

  /**
   * Enhance a batch of products
   */
  private async enhanceProductBatch(products: any[]): Promise<void> {
    const enhancements = products.map(product => this.generateEnhancement(product));
    
    // Update products with enhancements
    for (const enhancement of enhancements) {
      await supabase
        .from('products')
        .update({
          shopping_aliases: enhancement.shopping_aliases,
          tags: enhancement.tags,
          dietary_info: enhancement.dietary_info,
          seasonal_info: enhancement.seasonal_info,
          is_common_item: enhancement.is_common_item,
          search_rank: enhancement.search_rank
        })
        .eq('id', enhancement.id);
    }
  }

  /**
   * Generate enhancement data for a single product
   */
  private generateEnhancement(product: any): ProductEnhancement {
    const name = product.name?.toLowerCase() || '';
    const brand = product.brand?.toLowerCase() || '';
    const category = product.category || '';
    const description = product.description?.toLowerCase() || '';

    // Generate aliases
    const aliases = this.generateAliases(name, brand, category);
    
    // Generate tags
    const tags = this.generateTags(name, brand, category, description);
    
    // Generate dietary info
    const dietary_info = this.generateDietaryInfo(name, description);
    
    // Generate seasonal info
    const seasonal_info = this.generateSeasonalInfo(name, category);
    
    // Determine if common item
    const is_common_item = this.isCommonItem(name, category);
    
    // Calculate search rank
    const search_rank = this.calculateSearchRank(name, brand, category);

    return {
      id: product.id,
      shopping_aliases: aliases,
      tags,
      dietary_info,
      seasonal_info,
      is_common_item,
      search_rank
    };
  }

  /**
   * Generate search aliases for a product
   */
  private generateAliases(name: string, brand: string, category: string): string[] {
    const aliases = new Set<string>();

    // Add the original name
    aliases.add(name);

    // Add brand-specific aliases
    const categoryData = NZ_PRODUCT_KEYWORDS[category];
    if (categoryData?.brandMappings[brand]) {
      categoryData.brandMappings[brand].forEach(alias => aliases.add(alias));
    }

    // Add category-specific aliases
    if (categoryData?.aliases) {
      Object.entries(categoryData.aliases).forEach(([key, values]) => {
        if (name.includes(key)) {
          values.forEach(alias => aliases.add(alias));
        }
      });
    }

    // Add common misspellings
    Object.entries(COMMON_MISSPELLINGS).forEach(([correct, misspellings]) => {
      if (name.includes(correct)) {
        misspellings.forEach(misspelling => aliases.add(misspelling));
      }
    });

    // Add brand variations
    if (brand) {
      aliases.add(brand);
      aliases.add(brand.replace(/\s+/g, ''));
      aliases.add(brand.replace(/[^a-z0-9]/gi, ''));
    }

    // Add size variations (remove size info for broader matching)
    const withoutSize = name.replace(/\d+\s*(ml|l|g|kg|pack|pk)\b/gi, '').trim();
    if (withoutSize !== name) {
      aliases.add(withoutSize);
    }

    return Array.from(aliases).filter(alias => alias.length > 1);
  }

  /**
   * Generate tags for categorization and filtering
   */
  private generateTags(name: string, brand: string, category: string, description: string): string[] {
    const tags = new Set<string>();

    // Add category tag
    tags.add(category.toLowerCase());

    // Add brand tag
    if (brand) {
      tags.add(`brand:${brand.toLowerCase()}`);
    }

    // Add size tags
    if (name.match(/\bsmall\b/i)) tags.add('size:small');
    if (name.match(/\bmedium\b/i)) tags.add('size:medium');
    if (name.match(/\blarge\b/i)) tags.add('size:large');
    if (name.match(/\bfamily\b/i)) tags.add('size:family');

    // Add packaging tags
    if (name.match(/\bpack\b/i)) tags.add('packaging:pack');
    if (name.match(/\bbottle\b/i)) tags.add('packaging:bottle');
    if (name.match(/\bcan\b/i)) tags.add('packaging:can');
    if (name.match(/\bbag\b/i)) tags.add('packaging:bag');

    // Add dietary tags
    Object.entries(DIETARY_KEYWORDS).forEach(([diet, keywords]) => {
      keywords.forEach(keyword => {
        if (name.includes(keyword) || description.includes(keyword)) {
          tags.add(`diet:${diet}`);
        }
      });
    });

    // Add freshness tags
    if (category.includes('Fresh') || category.includes('Produce')) {
      tags.add('fresh');
    }
    if (category.includes('Frozen')) {
      tags.add('frozen');
    }

    return Array.from(tags);
  }

  /**
   * Generate dietary information
   */
  private generateDietaryInfo(name: string, description: string): Record<string, any> {
    const dietary: Record<string, any> = {};

    Object.entries(DIETARY_KEYWORDS).forEach(([diet, keywords]) => {
      const matches = keywords.filter(keyword => 
        name.includes(keyword) || description.includes(keyword)
      );
      if (matches.length > 0) {
        dietary[diet] = true;
        dietary[`${diet}_keywords`] = matches;
      }
    });

    return dietary;
  }

  /**
   * Generate seasonal information
   */
  private generateSeasonalInfo(name: string, category: string): Record<string, any> {
    const seasonal: Record<string, any> = {};

    // Seasonal fruits and vegetables
    const seasonalItems = {
      summer: ['strawberry', 'cherry', 'peach', 'nectarine', 'plum', 'apricot', 'berry'],
      autumn: ['apple', 'pear', 'pumpkin', 'squash', 'persimmon'],
      winter: ['citrus', 'orange', 'lemon', 'lime', 'grapefruit', 'mandarin'],
      spring: ['asparagus', 'artichoke', 'rhubarb']
    };

    Object.entries(seasonalItems).forEach(([season, items]) => {
      items.forEach(item => {
        if (name.includes(item)) {
          seasonal.peak_season = season;
          seasonal.seasonal_item = true;
        }
      });
    });

    return seasonal;
  }

  /**
   * Determine if this is a commonly purchased item
   */
  private isCommonItem(name: string, category: string): boolean {
    const commonItems = [
      'milk', 'bread', 'eggs', 'butter', 'cheese', 'banana', 'apple',
      'chicken', 'mince', 'rice', 'pasta', 'toilet paper', 'coffee',
      'tea', 'sugar', 'flour', 'oil', 'onion', 'potato', 'tomato'
    ];

    return commonItems.some(item => name.includes(item)) ||
           ['Dairy & Eggs', 'Bakery', 'Fresh Produce'].includes(category);
  }

  /**
   * Calculate search ranking score
   */
  private calculateSearchRank(name: string, brand: string, category: string): number {
    let rank = 1.0;

    // Boost common items
    if (this.isCommonItem(name, category)) {
      rank += 0.2;
    }

    // Boost known brands
    const knownBrands = ['Anchor', 'Watties', 'Cadbury', 'Tip Top', 'Mainland'];
    if (knownBrands.includes(brand)) {
      rank += 0.1;
    }

    // Boost fresh items
    if (category.includes('Fresh') || category.includes('Produce')) {
      rank += 0.1;
    }

    return Math.min(2.0, rank);
  }

  /**
   * Update search rankings based on analytics
   */
  async updateSearchRankings(): Promise<void> {
    try {
      const { error } = await supabase.rpc('update_search_rankings');
      if (error) {
        throw new Error(`Failed to update search rankings: ${error.message}`);
      }
      console.log('✅ Search rankings updated successfully');
    } catch (error) {
      console.error('❌ Failed to update search rankings:', error);
      throw error;
    }
  }

  /**
   * Refresh materialized views
   */
  async refreshSearchViews(): Promise<void> {
    try {
      const { error } = await supabase.rpc('refresh_search_views');
      if (error) {
        throw new Error(`Failed to refresh search views: ${error.message}`);
      }
      console.log('✅ Search views refreshed successfully');
    } catch (error) {
      console.error('❌ Failed to refresh search views:', error);
      throw error;
    }
  }

  /**
   * Get enhancement statistics
   */
  async getEnhancementStats(): Promise<{
    total_products: number;
    enhanced_products: number;
    common_items: number;
    categories_covered: number;
  }> {
    try {
      const { data, error } = await supabase
        .from('products')
        .select('id, shopping_aliases, is_common_item, category');

      if (error) {
        throw new Error(`Failed to get enhancement stats: ${error.message}`);
      }

      const enhanced = data?.filter(p => p.shopping_aliases && p.shopping_aliases.length > 0) || [];
      const commonItems = data?.filter(p => p.is_common_item) || [];
      const categories = new Set(data?.map(p => p.category) || []);

      return {
        total_products: data?.length || 0,
        enhanced_products: enhanced.length,
        common_items: commonItems.length,
        categories_covered: categories.size
      };
    } catch (error) {
      console.error('❌ Failed to get enhancement stats:', error);
      throw error;
    }
  }
}

export const dataEnhancementService = new DataEnhancementService(); 