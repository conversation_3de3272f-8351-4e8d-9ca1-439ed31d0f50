/**
 * UNIT PRICE CALCULATION SERVICE
 * 
 * Handles accurate unit price calculations for products with different sizes,
 * preventing misleading price comparisons between per-kg and fixed-size products.
 */

import { IProduct } from '../types/shoppingList';
import { normalizeSize } from '../utils/stringMatching';

export interface UnitPriceInfo {
  originalPrice: number;
  unitPrice: number;
  unitType: 'per-kg' | 'per-litre' | 'per-100g' | 'per-100ml' | 'per-item' | 'unknown';
  displayUnit: string;
  isComparable: boolean;
  size: string;
  calculationMethod: string;
}

export class UnitPriceCalculationService {
  
  /**
   * Calculate unit price for a product
   */
  public calculateUnitPrice(product: IProduct): UnitPriceInfo {
    const size = product.size || product.unit || '';
    const price = product.price;
    
    if (!size || price <= 0) {
      return {
        originalPrice: price,
        unitPrice: price,
        unitType: 'unknown',
        displayUnit: 'each',
        isComparable: false,
        size: size,
        calculationMethod: 'no-size-info'
      };
    }
    
    const normalized = normalizeSize(size);
    const sizeStr = size.toLowerCase().trim();
    
    // Handle per-unit pricing (already unit prices)
    if (sizeStr.includes('per kg')) {
      return {
        originalPrice: price,
        unitPrice: price,
        unitType: 'per-kg',
        displayUnit: '/kg',
        isComparable: true,
        size: size,
        calculationMethod: 'already-per-kg'
      };
    }
    
    if (sizeStr.includes('per 100g')) {
      return {
        originalPrice: price,
        unitPrice: price * 10, // Convert to per-kg
        unitType: 'per-kg',
        displayUnit: '/kg',
        isComparable: true,
        size: size,
        calculationMethod: 'per-100g-to-per-kg'
      };
    }
    
    if (sizeStr.includes('per l') || sizeStr.includes('per litre')) {
      return {
        originalPrice: price,
        unitPrice: price,
        unitType: 'per-litre',
        displayUnit: '/L',
        isComparable: true,
        size: size,
        calculationMethod: 'already-per-litre'
      };
    }
    
    if (sizeStr.includes('per 100ml')) {
      return {
        originalPrice: price,
        unitPrice: price * 10, // Convert to per-litre
        unitType: 'per-litre',
        displayUnit: '/L',
        isComparable: true,
        size: size,
        calculationMethod: 'per-100ml-to-per-litre'
      };
    }
    
    // Handle fixed sizes - calculate unit prices
    if (normalized.valid) {
      if (normalized.unit === 'g') {
        return {
          originalPrice: price,
          unitPrice: (price / normalized.value) * 1000, // Price per kg
          unitType: 'per-kg',
          displayUnit: '/kg',
          isComparable: true,
          size: size,
          calculationMethod: `${normalized.value}g-to-per-kg`
        };
      }
      
      if (normalized.unit === 'ml') {
        return {
          originalPrice: price,
          unitPrice: (price / normalized.value) * 1000, // Price per litre
          unitType: 'per-litre',
          displayUnit: '/L',
          isComparable: true,
          size: size,
          calculationMethod: `${normalized.value}ml-to-per-litre`
        };
      }
      
      if (normalized.unit === 'pack' || normalized.unit === 'count') {
        return {
          originalPrice: price,
          unitPrice: price / normalized.value, // Price per item
          unitType: 'per-item',
          displayUnit: '/item',
          isComparable: true,
          size: size,
          calculationMethod: `${normalized.value}pack-to-per-item`
        };
      }
    }
    
    // Fallback for unknown sizes
    return {
      originalPrice: price,
      unitPrice: price,
      unitType: 'unknown',
      displayUnit: 'each',
      isComparable: false,
      size: size,
      calculationMethod: 'unknown-size-format'
    };
  }
  
  /**
   * Check if two products can be compared by unit price
   */
  public areComparable(product1: IProduct, product2: IProduct): boolean {
    const unit1 = this.calculateUnitPrice(product1);
    const unit2 = this.calculateUnitPrice(product2);
    
    return unit1.isComparable && 
           unit2.isComparable && 
           unit1.unitType === unit2.unitType;
  }
  
  /**
   * Get comparable products from a list
   */
  public getComparableProducts(products: IProduct[]): IProduct[][] {
    const groups: Map<string, IProduct[]> = new Map();
    
    products.forEach(product => {
      const unitInfo = this.calculateUnitPrice(product);
      
      if (unitInfo.isComparable) {
        const key = unitInfo.unitType;
        if (!groups.has(key)) {
          groups.set(key, []);
        }
        groups.get(key)!.push(product);
      }
    });
    
    return Array.from(groups.values()).filter(group => group.length > 1);
  }
  
  /**
   * Format unit price for display
   */
  public formatUnitPrice(unitPriceInfo: UnitPriceInfo): string {
    if (!unitPriceInfo.isComparable) {
      return `$${unitPriceInfo.originalPrice.toFixed(2)}`;
    }
    
    return `$${unitPriceInfo.unitPrice.toFixed(2)}${unitPriceInfo.displayUnit}`;
  }
  
  /**
   * Get price comparison warning for products with different sizes
   */
  public getPriceComparisonWarning(products: IProduct[]): string | null {
    const unitInfos = products.map(p => this.calculateUnitPrice(p));
    const unitTypes = [...new Set(unitInfos.map(u => u.unitType))];
    
    if (unitTypes.length > 1) {
      return `⚠️ Comparing different product types: ${unitTypes.join(', ')}`;
    }
    
    const sizes = [...new Set(products.map(p => p.size || p.unit || ''))];
    if (sizes.length > 1 && !unitInfos.every(u => u.isComparable)) {
      return `⚠️ Comparing different sizes: ${sizes.join(', ')}`;
    }
    
    return null;
  }
  
  /**
   * Detect problematic per-kg vs fixed-size matches
   */
  public detectProblematicMatches(products: IProduct[]): {
    hasProblems: boolean;
    issues: string[];
    recommendations: string[];
  } {
    const issues: string[] = [];
    const recommendations: string[] = [];
    
    const unitInfos = products.map(p => ({
      product: p,
      unitInfo: this.calculateUnitPrice(p)
    }));
    
    // Check for per-kg vs fixed-size mixing
    const hasPerKg = unitInfos.some(u => u.product.size?.includes('per kg'));
    const hasFixedSize = unitInfos.some(u => u.product.size && !u.product.size.includes('per'));
    
    if (hasPerKg && hasFixedSize) {
      issues.push('Per-kg products matched with fixed-size products');
      recommendations.push('Separate per-kg products from fixed-size products');
    }
    
    // Check for large price discrepancies
    const prices = products.map(p => p.price);
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    
    if (maxPrice / minPrice > 10) {
      issues.push(`Extreme price difference: $${minPrice.toFixed(2)} - $${maxPrice.toFixed(2)}`);
      recommendations.push('Verify products are actually the same item');
    }
    
    return {
      hasProblems: issues.length > 0,
      issues,
      recommendations
    };
  }
}

export const unitPriceCalculationService = new UnitPriceCalculationService();
