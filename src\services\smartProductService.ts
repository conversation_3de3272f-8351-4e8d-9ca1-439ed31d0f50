interface ProductVariant {
  id: string;
  name: string;
  brand: string;
  size: string;
  category: string;
  unit: 'g' | 'kg' | 'ml' | 'L' | 'pack' | 'each';
  storePrices: {
    woolworths?: number;
    paknsave?: number;
    newworld?: number;
    countdown?: number;
  };
  available: {
    woolworths: boolean;
    paknsave: boolean;
    newworld: boolean;
    countdown: boolean;
  };
  imageUrl?: string;
  description?: string;
  matchScore?: number;
  relevance_score?: number;
  match_type?: string;
  isCommonItem?: boolean;
}

interface ProductGroup {
  productName: string;
  category: string;
  variants: ProductVariant[];
  suggestedSizes: string[];
  bestPrice: {
    price: number;
    store: string;
    variant: ProductVariant;
  };
}

interface SearchResult {
  query: string;
  matches: ProductGroup[];
  categories: string[];
  totalVariants: number;
}

export class SmartProductService {
  private products: ProductVariant[] = [];
  private categories: Map<string, string[]> = new Map();

  // Initialize with your scraper data
  loadScrapedData(scrapedProducts: any[]) {
    this.products = this.normalizeScrapedData(scrapedProducts);
    this.buildCategoryIndex();
  }

  private normalizeScrapedData(scrapedProducts: any[]): ProductVariant[] {
    return scrapedProducts.map(product => ({
      id: product.id || this.generateId(),
      name: this.cleanProductName(product.name),
      brand: this.extractBrand(product.name),
      size: this.extractSize(product.name),
      category: this.categorizeProduct(product.name),
      unit: this.determineUnit(product.name),
      storePrices: {
        woolworths: product.woolworths_price,
        paknsave: product.paknsave_price,
        newworld: product.newworld_price,
        countdown: product.countdown_price,
      },
      available: {
        woolworths: !!product.woolworths_price,
        paknsave: !!product.paknsave_price,
        newworld: !!product.newworld_price,
        countdown: !!product.countdown_price,
      },
      imageUrl: product.image_url,
      description: product.description,
    }));
  }

  // Smart search that matches user input to products
  searchProducts(query: string): SearchResult {
    const normalizedQuery = query.toLowerCase().trim();
    
    if (!normalizedQuery) {
      return { query, matches: [], categories: [], totalVariants: 0 };
    }

    // Find matching products using various strategies
    const matchingProducts = this.findMatches(normalizedQuery);
    
    // Group by product type (e.g., all coffee products together)
    const groupedProducts = this.groupProducts(matchingProducts);
    
    // Sort groups by relevance and best prices
    const sortedGroups = this.sortProductGroups(groupedProducts, normalizedQuery);

    return {
      query,
      matches: sortedGroups,
      categories: [...new Set(sortedGroups.map(g => g.category))],
      totalVariants: matchingProducts.length,
    };
  }

  private findMatches(query: string): ProductVariant[] {
    const matches: ProductVariant[] = [];
    const queryWords = query.split(/\s+/);

    for (const product of this.products) {
      const searchText = `${product.name} ${product.brand} ${product.category}`.toLowerCase();
      
      // Exact match gets highest priority
      if (searchText.includes(query)) {
        matches.push({ ...product, matchScore: 100, relevance_score: 100, match_type: 'exact' });
        continue;
      }

      // Partial word matches
      const matchingWords = queryWords.filter(word => 
        searchText.includes(word) && word.length > 2
      );

      if (matchingWords.length > 0) {
        const score = (matchingWords.length / queryWords.length) * 80;
        matches.push({ ...product, matchScore: score, relevance_score: score, match_type: 'partial' });
      }
    }

    return matches.sort((a, b) => (b.matchScore || 0) - (a.matchScore || 0));
  }

  private groupProducts(products: ProductVariant[]): ProductGroup[] {
    const groups = new Map<string, ProductVariant[]>();

    // Group by cleaned product name
    for (const product of products) {
      const baseProductName = this.getBaseProductName(product.name);
      if (!groups.has(baseProductName)) {
        groups.set(baseProductName, []);
      }
      groups.get(baseProductName)!.push(product);
    }

    return Array.from(groups.entries()).map(([productName, variants]) => {
      const bestPrice = this.findBestPrice(variants);
      const category = variants[0]?.category || 'Other';
      const suggestedSizes = this.getSuggestedSizes(variants);

      return {
        productName,
        category,
        variants: this.sortVariants(variants),
        suggestedSizes,
        bestPrice,
      };
    });
  }

  private getBaseProductName(fullName: string): string {
    // Remove brand names, sizes, and descriptors to get core product
    const commonBrands = ['anchor', 'pams', 'homebrand', 'signature', 'essentials'];
    const sizePattern = /\d+\s*(g|kg|ml|l|pack|ea|each)/gi;
    
    let baseName = fullName.toLowerCase()
      .replace(sizePattern, '') // Remove sizes
      .replace(/\b(organic|free range|premium|select)\b/gi, '') // Remove descriptors
      .trim();

    // Remove common brand names
    for (const brand of commonBrands) {
      baseName = baseName.replace(new RegExp(`\\b${brand}\\b`, 'gi'), '').trim();
    }

    return baseName.charAt(0).toUpperCase() + baseName.slice(1);
  }

  private findBestPrice(variants: ProductVariant[]): ProductGroup['bestPrice'] {
    let bestPrice = Infinity;
    let bestStore = '';
    let bestVariant = variants[0];

    for (const variant of variants) {
      Object.entries(variant.storePrices).forEach(([store, price]) => {
        if (price && price < bestPrice) {
          bestPrice = price;
          bestStore = store;
          bestVariant = variant;
        }
      });
    }

    return {
      price: bestPrice === Infinity ? 0 : bestPrice,
      store: bestStore,
      variant: bestVariant,
    };
  }

  private getSuggestedSizes(variants: ProductVariant[]): string[] {
    const sizes = variants.map(v => v.size).filter(Boolean);
    const uniqueSizes = [...new Set(sizes)];
    
    // Sort sizes intelligently (500g before 1kg, 500ml before 1L)
    return uniqueSizes.sort((a, b) => {
      const aNum = parseFloat(a);
      const bNum = parseFloat(b);
      
      if (!isNaN(aNum) && !isNaN(bNum)) {
        return aNum - bNum;
      }
      
      return a.localeCompare(b);
    });
  }

  private sortVariants(variants: ProductVariant[]): ProductVariant[] {
    return variants.sort((a, b) => {
      // Sort by availability first, then by best price
      const aHasPrices = Object.values(a.storePrices).some(p => p && p > 0);
      const bHasPrices = Object.values(b.storePrices).some(p => p && p > 0);
      
      if (aHasPrices && !bHasPrices) return -1;
      if (!aHasPrices && bHasPrices) return 1;
      
      // Compare best prices
      const aBestPrice = Math.min(...Object.values(a.storePrices).filter(p => p && p > 0));
      const bBestPrice = Math.min(...Object.values(b.storePrices).filter(p => p && p > 0));
      
      return aBestPrice - bBestPrice;
    });
  }

  private sortProductGroups(groups: ProductGroup[], query: string): ProductGroup[] {
    return groups.sort((a, b) => {
      // Prioritize groups where product name closely matches query
      const aNameMatch = a.productName.toLowerCase().includes(query) ? 1 : 0;
      const bNameMatch = b.productName.toLowerCase().includes(query) ? 1 : 0;
      
      if (aNameMatch !== bNameMatch) {
        return bNameMatch - aNameMatch;
      }
      
      // Then sort by best price
      return a.bestPrice.price - b.bestPrice.price;
    });
  }

  // Helper methods for data normalization
  private cleanProductName(name: string): string {
    return name.replace(/\s+/g, ' ').trim();
  }

  private extractBrand(name: string): string {
    const commonBrands = ['Anchor', 'Pams', 'Homebrand', 'Signature', 'Essentials', 'Watties', 'Tip Top'];
    const found = commonBrands.find(brand => 
      name.toLowerCase().includes(brand.toLowerCase())
    );
    return found || 'Generic';
  }

  private extractSize(name: string): string {
    const sizeMatch = name.match(/(\d+\.?\d*)\s*(g|kg|ml|L|pack|ea|each)/i);
    return sizeMatch ? `${sizeMatch[1]}${sizeMatch[2].toLowerCase()}` : '';
  }

  private categorizeProduct(name: string): string {
    const categoryKeywords = {
      'Dairy': ['milk', 'cheese', 'butter', 'yogurt', 'cream'],
      'Bakery': ['bread', 'buns', 'rolls', 'bagels', 'croissant'],
      'Meat': ['chicken', 'beef', 'pork', 'lamb', 'sausage', 'bacon'],
      'Beverages': ['coffee', 'tea', 'juice', 'soft drink', 'water'],
      'Pantry': ['rice', 'pasta', 'flour', 'sugar', 'oil'],
      'Produce': ['apple', 'banana', 'potato', 'onion', 'carrot'],
      'Frozen': ['frozen', 'ice cream', 'frozen peas'],
      'Household': ['detergent', 'toilet paper', 'cleaning'],
    };

    const lowerName = name.toLowerCase();
    
    for (const [category, keywords] of Object.entries(categoryKeywords)) {
      if (keywords.some(keyword => lowerName.includes(keyword))) {
        return category;
      }
    }
    
    return 'Other';
  }

  private determineUnit(name: string): ProductVariant['unit'] {
    const lowerName = name.toLowerCase();
    
    if (lowerName.includes('kg')) return 'kg';
    if (lowerName.includes('g') && !lowerName.includes('kg')) return 'g';
    if (lowerName.includes('l') && !lowerName.includes('ml')) return 'L';
    if (lowerName.includes('ml')) return 'ml';
    if (lowerName.includes('pack')) return 'pack';
    
    return 'each';
  }

  private buildCategoryIndex() {
    this.categories.clear();
    
    for (const product of this.products) {
      if (!this.categories.has(product.category)) {
        this.categories.set(product.category, []);
      }
      this.categories.get(product.category)!.push(product.name);
    }
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  // Get products by category for list organization
  getProductsByCategory(): Map<string, ProductVariant[]> {
    const categoryMap = new Map<string, ProductVariant[]>();
    
    for (const product of this.products) {
      if (!categoryMap.has(product.category)) {
        categoryMap.set(product.category, []);
      }
      categoryMap.get(product.category)!.push(product);
    }
    
    return categoryMap;
  }

  // Get all available categories
  getCategories(): string[] {
    return [...this.categories.keys()].sort();
  }
}

export const smartProductService = new SmartProductService();