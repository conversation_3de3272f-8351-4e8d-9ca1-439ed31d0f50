import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  StyleSheet,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '../context/ThemeContext';
import { theme } from '../styles';
import { priceComparisonService, PriceComparisonResult, StoreSearchResult } from '../services/priceComparisonService';

interface SearchPriceComparisonScreenProps {
  onSelectProduct?: (result: StoreSearchResult) => void;
}

export const SearchPriceComparisonScreen: React.FC<SearchPriceComparisonScreenProps> = ({
  onSelectProduct,
}) => {
  const { colors } = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [comparison, setComparison] = useState<PriceComparisonResult | null>(null);
  const [sortBy, setSortBy] = useState<'price' | 'relevance'>('price');

  const searchPrices = async (query: string) => {
    if (!query.trim()) {
      setComparison(null);
      return;
    }

    setLoading(true);
    try {
      const result = await priceComparisonService.searchProductAcrossStores(query, {
        maxResults: 10,
        sortBy,
        includeOutOfStock: false,
      });
      setComparison(result);
    } catch (error) {
      console.error('Price search failed:', error);
      Alert.alert('Search Error', 'Failed to search for prices. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      if (searchQuery) {
        searchPrices(searchQuery);
      }
    }, 500);

    return () => clearTimeout(debounceTimer);
  }, [searchQuery, sortBy]);

  const formatPrice = (price: number): string => {
    return price > 0 ? `$${price.toFixed(2)}` : 'N/A';
  };

  const getStoreColor = (store: string): string => {
    const storeColors = {
      woolworths: '#00A651',
      newworld: '#E31E24',
      paknsave: '#FFD100',
    };
    return storeColors[store as keyof typeof storeColors] || theme.colors.primary;
  };

  const renderComparisonTable = () => {
    if (!comparison || comparison.results.length === 0) {
      return (
        <View style={[styles.emptyState, { backgroundColor: colors.surface }]}>
          <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
            {searchQuery ? 'No products found' : 'Enter a product name to search'}
          </Text>
        </View>
      );
    }

    // Group results by brand for comparison
    const brandGroups = comparison.results.reduce((groups, result) => {
      const brand = result.product.brand || 'Generic';
      if (!groups[brand]) {
        groups[brand] = [];
      }
      groups[brand].push(result);
      return groups;
    }, {} as Record<string, StoreSearchResult[]>);

    return (
      <View style={[styles.tableContainer, { backgroundColor: colors.surface }]}>
        <View style={[styles.tableHeader, { borderBottomColor: colors.border }]}>
          <Text style={[styles.brandHeader, { color: colors.text }]}>Brand</Text>
          <Text style={[styles.priceHeader, { color: colors.text }]}>Woolworths</Text>
          <Text style={[styles.priceHeader, { color: colors.text }]}>New World</Text>
          <Text style={[styles.priceHeader, { color: colors.text }]}>Pak'nSave</Text>
        </View>

        <ScrollView style={styles.tableBody}>
          {Object.entries(brandGroups).map(([brand, results]) => {
            const woolworthsResult = results.find(r => r.store === 'woolworths');
            const newworldResult = results.find(r => r.store === 'newworld');
            const paknsaveResult = results.find(r => r.store === 'paknsave');

            const prices = [
              woolworthsResult?.product.price || 0,
              newworldResult?.product.price || 0,
              paknsaveResult?.product.price || 0,
            ].filter(p => p > 0);

            const minPrice = prices.length > 0 ? Math.min(...prices) : 0;

            return (
              <View key={brand} style={[styles.tableRow, { borderBottomColor: colors.border }]}>
                <View style={styles.brandCell}>
                  <Text style={[styles.brandName, { color: colors.text }]} numberOfLines={2}>
                    {brand}
                  </Text>
                  {results[0]?.product.name && (
                    <Text style={[styles.productName, { color: colors.textSecondary }]} numberOfLines={1}>
                      {results[0].product.name}
                    </Text>
                  )}
                </View>

                <TouchableOpacity
                  style={[
                    styles.priceCell,
                    woolworthsResult?.product.price === minPrice && minPrice > 0 && styles.bestPriceCell
                  ]}
                  onPress={() => woolworthsResult && onSelectProduct?.(woolworthsResult)}
                  disabled={!woolworthsResult}
                >
                  <Text style={[
                    styles.priceText,
                    { color: woolworthsResult ? colors.text : colors.textTertiary },
                    woolworthsResult?.product.price === minPrice && minPrice > 0 && styles.bestPriceText
                  ]}>
                    {woolworthsResult ? formatPrice(woolworthsResult.product.price) : '-'}
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.priceCell,
                    newworldResult?.product.price === minPrice && minPrice > 0 && styles.bestPriceCell
                  ]}
                  onPress={() => newworldResult && onSelectProduct?.(newworldResult)}
                  disabled={!newworldResult}
                >
                  <Text style={[
                    styles.priceText,
                    { color: newworldResult ? colors.text : colors.textTertiary },
                    newworldResult?.product.price === minPrice && minPrice > 0 && styles.bestPriceText
                  ]}>
                    {newworldResult ? formatPrice(newworldResult.product.price) : '-'}
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.priceCell,
                    paknsaveResult?.product.price === minPrice && minPrice > 0 && styles.bestPriceCell
                  ]}
                  onPress={() => paknsaveResult && onSelectProduct?.(paknsaveResult)}
                  disabled={!paknsaveResult}
                >
                  <Text style={[
                    styles.priceText,
                    { color: paknsaveResult ? colors.text : colors.textTertiary },
                    paknsaveResult?.product.price === minPrice && minPrice > 0 && styles.bestPriceText
                  ]}>
                    {paknsaveResult ? formatPrice(paknsaveResult.product.price) : '-'}
                  </Text>
                </TouchableOpacity>
              </View>
            );
          })}
        </ScrollView>
      </View>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.backgroundSecondary }]}>
      <View style={[styles.searchContainer, { backgroundColor: colors.surface }]}>
        <TextInput
          style={[styles.searchInput, { 
            backgroundColor: colors.backgroundSecondary,
            color: colors.text,
            borderColor: colors.border 
          }]}
          placeholder="Search for products (e.g., MILK)"
          placeholderTextColor={colors.textSecondary}
          value={searchQuery}
          onChangeText={setSearchQuery}
          autoCapitalize="none"
          autoCorrect={false}
        />

        <View style={styles.sortContainer}>
          <TouchableOpacity
            style={[
              styles.sortButton,
              { 
                backgroundColor: sortBy === 'price' ? theme.colors.primary : colors.surface,
                borderColor: colors.border 
              }
            ]}
            onPress={() => setSortBy('price')}
          >
            <Text style={[
              styles.sortText,
              { color: sortBy === 'price' ? 'white' : colors.textSecondary }
            ]}>
              By Price
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.sortButton,
              { 
                backgroundColor: sortBy === 'relevance' ? theme.colors.primary : colors.surface,
                borderColor: colors.border 
              }
            ]}
            onPress={() => setSortBy('relevance')}
          >
            <Text style={[
              styles.sortText,
              { color: sortBy === 'relevance' ? 'white' : colors.textSecondary }
            ]}>
              By Relevance
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.contentContainer}>
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
              Searching prices...
            </Text>
          </View>
        ) : (
          renderComparisonTable()
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchContainer: {
    paddingHorizontal: theme.spacing.base,
    paddingVertical: theme.spacing.base,
    borderBottomWidth: 1,
  },
  searchInput: {
    height: 48,
    borderRadius: theme.radius.lg,
    paddingHorizontal: theme.spacing.base,
    fontSize: theme.typography.fontSize.base,
    borderWidth: 1,
    marginBottom: theme.spacing.base,
  },
  sortContainer: {
    flexDirection: 'row',
    gap: theme.spacing.sm,
  },
  sortButton: {
    paddingHorizontal: theme.spacing.base,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.radius.full,
    borderWidth: 1,
  },
  sortText: {
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.medium,
  },
  contentContainer: {
    flex: 1,
    padding: theme.spacing.base,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    fontSize: theme.typography.fontSize.base,
    marginTop: theme.spacing.base,
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: theme.radius.lg,
    padding: theme.spacing['2xl'],
  },
  emptyText: {
    fontSize: theme.typography.fontSize.base,
    textAlign: 'center',
  },
  tableContainer: {
    flex: 1,
    borderRadius: theme.radius.lg,
    overflow: 'hidden',
  },
  tableHeader: {
    flexDirection: 'row',
    paddingVertical: theme.spacing.base,
    paddingHorizontal: theme.spacing.sm,
    borderBottomWidth: 2,
  },
  brandHeader: {
    flex: 2,
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.bold,
    textAlign: 'left',
  },
  priceHeader: {
    flex: 1,
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.bold,
    textAlign: 'center',
  },
  tableBody: {
    flex: 1,
  },
  tableRow: {
    flexDirection: 'row',
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.sm,
    borderBottomWidth: 1,
    minHeight: 60,
  },
  brandCell: {
    flex: 2,
    justifyContent: 'center',
    paddingRight: theme.spacing.xs,
  },
  brandName: {
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.semibold,
    marginBottom: 2,
  },
  productName: {
    fontSize: theme.typography.fontSize.xs,
  },
  priceCell: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: theme.radius.sm,
    marginHorizontal: 2,
  },
  bestPriceCell: {
    backgroundColor: '#E8F5E8',
  },
  priceText: {
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.medium,
  },
  bestPriceText: {
    color: '#00A651',
    fontWeight: theme.typography.fontWeight.bold,
  },
});