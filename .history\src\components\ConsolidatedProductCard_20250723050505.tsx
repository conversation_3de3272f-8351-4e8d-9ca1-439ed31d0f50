/**
 * Consolidated Product Card Component
 * Story 1.2: Consolidated Product Card with Multi-Store Price Display
 * 
 * Displays a single product with multiple store options and prices in an organized,
 * scannable format. Integrates with the deduplication service from Story 1.1.
 */

import React, { useState, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { OptimizedImage } from './OptimizedImage';
import { unitPriceCalculationService } from '../services/unitPriceCalculationService';
import { ShoppingListSelector } from './ShoppingListSelector';
import { IUnifiedProduct, StoreConfig } from '../types/shoppingList';
import { productDeduplicationService } from '../services/productDeduplicationService';

const { width } = Dimensions.get('window');
const CARD_MARGIN = 16;
const CARD_WIDTH = width - (CARD_MARGIN * 2);

interface ConsolidatedProductCardProps {
  product: IUnifiedProduct;
  viewMode?: 'grid' | 'list';
  onPress?: (product: IUnifiedProduct) => void;
  onAddToList?: (product: IUnifiedProduct, store: string) => void;
  style?: any;
}

export const ConsolidatedProductCard: React.FC<ConsolidatedProductCardProps> = ({
  product,
  viewMode = 'list',
  onPress,
  onAddToList,
  style,
}) => {
  const { colors } = useTheme();
  const [selectedStore, setSelectedStore] = useState<string | null>(null);
  const [showListSelector, setShowListSelector] = useState(false);
  const [expanded, setExpanded] = useState(false);

  // Get store configurations
  const storeConfigs = useMemo(() => 
    productDeduplicationService.getStoreConfigs(), []
  );

  // Get store configuration by ID
  const getStoreConfig = useCallback((storeId: string): StoreConfig | undefined => 
    productDeduplicationService.getStoreConfig(storeId), []
  );

  // Calculate price statistics with enhanced size validation and unit price analysis
  const priceStats = useMemo(() => {
    const prices = Object.values(product.storePrices).filter(price => price > 0);
    if (prices.length === 0) return {
      lowest: 0,
      highest: 0,
      savings: 0,
      sizeConsistent: true,
      hasProblematicMatches: false,
      unitPriceWarning: null
    };

    // Check if all products have the same size (they should after our fixes)
    // Note: product.allStores is an array of store names, not product objects
    // We'll use the product's size/unit info for validation
    const productSize = product.size || product.unit || '';
    const sizeConsistent = true; // After our fixes, grouped products should have consistent sizes

    // Check for problematic per-kg vs fixed-size matches
    const hasPerKg = productSize.toLowerCase().includes('per kg');
    const hasFixedSize = productSize && !productSize.toLowerCase().includes('per');
    const hasProblematicMatches = false; // Our fixes should prevent this

    // Get unit price warning if needed (create a mock product for the service)
    const mockProduct = { ...product, size: productSize };
    const unitPriceWarning = productSize ?
      unitPriceCalculationService.getPriceComparisonWarning([mockProduct]) : null;

    const lowest = Math.min(...prices);
    const highest = Math.max(...prices);
    const savings = highest - lowest;

    // Flag suspicious savings (likely indicates problematic matching)
    const suspiciousSavings = savings > 20 && hasProblematicMatches;

    return {
      lowest,
      highest,
      savings,
      sizeConsistent,
      hasProblematicMatches,
      unitPriceWarning,
      suspiciousSavings
    };
  }, [product.storePrices, product.allStores]);

  // Get the store with the lowest price
  const bestPriceStore = useMemo(() => {
    let bestStore = null;
    let bestPrice = Infinity;
    
    Object.entries(product.storePrices).forEach(([store, price]) => {
      if (price > 0 && price < bestPrice) {
        bestPrice = price;
        bestStore = store;
      }
    });
    
    return bestStore;
  }, [product.storePrices]);

  // Handle card press
  const handleCardPress = useCallback(() => {
    if (onPress) {
      onPress(product);
    } else {
      setExpanded(!expanded);
    }
  }, [onPress, product, expanded]);

  // Handle store selection for adding to list
  const handleStoreSelect = useCallback((storeId: string) => {
    setSelectedStore(storeId);
    setShowListSelector(true);
  }, []);

  // Handle successful addition to shopping list
  const handleAddSuccess = useCallback((listName: string) => {
    setShowListSelector(false);
    setSelectedStore(null);
    Alert.alert(
      '✅ Added to Shopping List',
      `"${product.name}" has been added to "${listName}"`
    );
  }, [product.name]);

  // Render store price option
  const renderStorePrice = useCallback((storeId: string, price: number) => {
    const storeConfig = getStoreConfig(storeId);
    if (!storeConfig || price <= 0) return null;

    const isBestPrice = storeId === bestPriceStore;
    const priceDifference = price - priceStats.lowest;

    return (
      <TouchableOpacity
        key={storeId}
        style={[
          styles.storeOption,
          {
            backgroundColor: isBestPrice ? `${storeConfig.color}15` : colors.surface,
            borderColor: isBestPrice ? storeConfig.color : colors.border,
          }
        ]}
        onPress={() => handleStoreSelect(storeId)}
        activeOpacity={0.7}
      >
        <View style={styles.storeInfo}>
          <View style={[styles.storeIcon, { backgroundColor: storeConfig.color }]}>
            <Text style={[styles.storeIconText, { color: '#FFFFFF' }]}>
              {storeConfig.icon}
            </Text>
          </View>
          <View style={styles.storeDetails}>
            <Text style={[styles.storeName, { color: colors.text }]}>
              {storeConfig.displayName}
            </Text>
            {isBestPrice && (
              <View style={[styles.bestDealBadge, { backgroundColor: storeConfig.color }]}>
                <Text style={styles.bestDealText}>Best Deal</Text>
              </View>
            )}
          </View>
        </View>
        
        <View style={styles.priceSection}>
          <Text style={[
            styles.price,
            {
              color: isBestPrice ? storeConfig.color : colors.text,
              fontWeight: isBestPrice ? '700' : '600'
            }
          ]}>
            ${price.toFixed(2)}
          </Text>
          {priceDifference > 0 && (
            <Text style={[styles.priceDifference, { color: colors.textSecondary }]}>
              +${priceDifference.toFixed(2)}
            </Text>
          )}
        </View>

        <Ionicons 
          name="add-circle" 
          size={24} 
          color={isBestPrice ? storeConfig.color : colors.primary} 
        />
      </TouchableOpacity>
    );
  }, [bestPriceStore, priceStats.lowest, colors, getStoreConfig, handleStoreSelect]);

  // Render compact store indicators (for collapsed view)
  const renderCompactStoreIndicators = useCallback(() => {
    const availableStores = Object.entries(product.storePrices)
      .filter(([_, price]) => price > 0)
      .map(([storeId]) => storeId);

    return (
      <View style={styles.compactStoreIndicators}>
        {availableStores.map(storeId => {
          const storeConfig = getStoreConfig(storeId);
          if (!storeConfig) return null;

          const isBestPrice = storeId === bestPriceStore;
          
          return (
            <View
              key={storeId}
              style={[
                styles.compactStoreIndicator,
                {
                  backgroundColor: isBestPrice ? storeConfig.color : `${storeConfig.color}30`,
                  borderColor: storeConfig.color,
                }
              ]}
            >
              <Text style={[
                styles.compactStoreText,
                { color: isBestPrice ? '#FFFFFF' : storeConfig.color }
              ]}>
                {storeConfig.icon}
              </Text>
            </View>
          );
        })}
      </View>
    );
  }, [product.storePrices, bestPriceStore, getStoreConfig]);

  // Get the best product image
  const getProductImage = useCallback(() => {
    // Use the best image selected by deduplication service
    return product.imageUrl || product.displayImageUrl;
  }, [product]);

  return (
    <>
      <TouchableOpacity
        style={[styles.card, { backgroundColor: colors.surface }, style]}
        onPress={handleCardPress}
        activeOpacity={0.8}
      >
        {/* Product Header */}
        <View style={styles.productHeader}>
          <View style={styles.productImageContainer}>
            {getProductImage() ? (
              <OptimizedImage
                source={{ uri: getProductImage() }}
                style={styles.productImage}
                placeholder={
                  <View style={[styles.imagePlaceholder, { backgroundColor: colors.border }]}>
                    <Ionicons name="image-outline" size={24} color={colors.textSecondary} />
                  </View>
                }
              />
            ) : (
              <View style={[styles.imagePlaceholder, { backgroundColor: colors.border }]}>
                <Ionicons name="image-outline" size={24} color={colors.textSecondary} />
              </View>
            )}
          </View>

          <View style={styles.productInfo}>
            <Text style={[styles.productName, { color: colors.text }]} numberOfLines={2}>
              {product.name}
            </Text>
            
            {product.brand && (
              <Text style={[styles.productBrand, { color: colors.textSecondary }]}>
                {product.brand}
              </Text>
            )}

            <View style={styles.priceRange}>
              <Text style={[styles.priceRangeText, { color: colors.primary }]}>
                ${priceStats.lowest.toFixed(2)}
                {priceStats.savings > 0 && (
                  <Text style={[styles.priceRangeHigh, { color: colors.textSecondary }]}>
                    {' - '}${priceStats.highest.toFixed(2)}
                  </Text>
                )}
              </Text>
              {priceStats.savings > 0.50 && !priceStats.suspiciousSavings && (
                <Text style={[styles.savingsText, { color: colors.success }]}>
                  Save ${priceStats.savings.toFixed(2)}
                </Text>
              )}

              {priceStats.suspiciousSavings && (
                <Text style={[styles.warningText, { color: colors.warning || '#FF9500' }]}>
                  ⚠️ Price comparison may be inaccurate
                </Text>
              )}

              {priceStats.unitPriceWarning && (
                <Text style={[styles.warningText, { color: colors.warning || '#FF9500' }]}>
                  {priceStats.unitPriceWarning}
                </Text>
              )}

              {!priceStats.sizeConsistent && (
                <Text style={[styles.warningText, { color: colors.warning || '#FF9500' }]}>
                  ⚠️ Different sizes - compare carefully
                </Text>
              )}
            </View>

            {!expanded && renderCompactStoreIndicators()}
          </View>

          <TouchableOpacity
            style={styles.expandButton}
            onPress={() => setExpanded(!expanded)}
          >
            <Ionicons 
              name={expanded ? "chevron-up" : "chevron-down"} 
              size={20} 
              color={colors.textSecondary} 
            />
          </TouchableOpacity>
        </View>

        {/* Expanded Store Options */}
        {expanded && (
          <View style={styles.storeOptionsContainer}>
            <Text style={[styles.storeOptionsTitle, { color: colors.text }]}>
              Available at {Object.values(product.storePrices).filter(p => p > 0).length} stores:
            </Text>
            <View style={styles.storeOptions}>
              {Object.entries(product.storePrices).map(([storeId, price]) => 
                renderStorePrice(storeId, price)
              )}
            </View>
          </View>
        )}
      </TouchableOpacity>

      {/* Shopping List Selector Modal */}
      {showListSelector && selectedStore && (
        <ShoppingListSelector
          visible={showListSelector}
          onClose={() => {
            setShowListSelector(false);
            setSelectedStore(null);
          }}
          product={{
            ...product.allStores.find(p => p.store === selectedStore) || product.allStores[0],
            store: selectedStore,
            price: product.storePrices[selectedStore] || 0,
          }}
          onSuccess={handleAddSuccess}
        />
      )}
    </>
  );
};

const styles = StyleSheet.create({
  card: {
    marginHorizontal: CARD_MARGIN,
    marginVertical: 8,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  productHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  productImageContainer: {
    width: 80,
    height: 80,
    marginRight: 12,
  },
  productImage: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  imagePlaceholder: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  productInfo: {
    flex: 1,
    marginRight: 8,
  },
  productName: {
    fontSize: 16,
    fontWeight: '600',
    lineHeight: 22,
    marginBottom: 4,
  },
  productBrand: {
    fontSize: 14,
    marginBottom: 6,
  },
  priceRange: {
    marginBottom: 8,
  },
  priceRangeText: {
    fontSize: 18,
    fontWeight: '700',
  },
  priceRangeHigh: {
    fontSize: 16,
    fontWeight: '500',
  },
  savingsText: {
    fontSize: 12,
    fontWeight: '600',
    marginTop: 2,
  },
  warningText: {
    fontSize: 11,
    fontWeight: '500',
    marginTop: 2,
    fontStyle: 'italic',
  },
  compactStoreIndicators: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
  },
  compactStoreIndicator: {
    width: 28,
    height: 28,
    borderRadius: 14,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  compactStoreText: {
    fontSize: 10,
    fontWeight: '700',
  },
  expandButton: {
    padding: 4,
  },
  storeOptionsContainer: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  storeOptionsTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 12,
  },
  storeOptions: {
    gap: 8,
  },
  storeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  storeInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  storeIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  storeIconText: {
    fontSize: 12,
    fontWeight: '700',
  },
  storeDetails: {
    flex: 1,
  },
  storeName: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  bestDealBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    alignSelf: 'flex-start',
  },
  bestDealText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '700',
    textTransform: 'uppercase',
  },
  priceSection: {
    alignItems: 'flex-end',
    marginRight: 12,
  },
  price: {
    fontSize: 16,
    fontWeight: '600',
  },
  priceDifference: {
    fontSize: 12,
    marginTop: 2,
  },
});
