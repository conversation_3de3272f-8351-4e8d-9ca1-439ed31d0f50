/**
 * Store Option Selector Component
 * Sub-component for ConsolidatedProductCard to handle individual store selection
 */

import React, { useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { StoreConfig } from '../types/shoppingList';

interface StoreOptionSelectorProps {
  storeId: string;
  storeConfig: StoreConfig;
  price: number;
  isBestPrice: boolean;
  priceDifference: number;
  onSelect: (storeId: string) => void;
}

export const StoreOptionSelector: React.FC<StoreOptionSelectorProps> = ({
  storeId,
  storeConfig,
  price,
  isBestPrice,
  priceDifference,
  onSelect,
}) => {
  const { colors } = useTheme();

  const handlePress = useCallback(() => {
    onSelect(storeId);
  }, [storeId, onSelect]);

  return (
    <TouchableOpacity
      style={[
        styles.storeOption,
        {
          backgroundColor: isBestPrice ? `${storeConfig.color}15` : colors.surface,
          borderColor: isBestPrice ? storeConfig.color : colors.border,
        }
      ]}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      <View style={styles.storeInfo}>
        <View style={[styles.storeIcon, { backgroundColor: storeConfig.color }]}>
          <Text style={[styles.storeIconText, { color: '#FFFFFF' }]}>
            {storeConfig.icon}
          </Text>
        </View>
        <View style={styles.storeDetails}>
          <Text style={[styles.storeName, { color: colors.text }]}>
            {storeConfig.displayName}
          </Text>
          {isBestPrice && (
            <View style={[styles.bestDealBadge, { backgroundColor: storeConfig.color }]}>
              <Text style={styles.bestDealText}>Best Deal</Text>
            </View>
          )}
        </View>
      </View>
      
      <View style={styles.priceSection}>
        <Text style={[
          styles.price,
          {
            color: isBestPrice ? storeConfig.color : colors.text,
            fontWeight: isBestPrice ? '700' : '600'
          }
        ]}>
          ${price.toFixed(2)}
        </Text>
        {priceDifference > 0 && (
          <Text style={[styles.priceDifference, { color: colors.textSecondary }]}>
            +${priceDifference.toFixed(2)}
          </Text>
        )}
      </View>

      <Ionicons 
        name="add-circle" 
        size={24} 
        color={isBestPrice ? storeConfig.color : colors.primary} 
      />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  storeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 8,
  },
  storeInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  storeIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  storeIconText: {
    fontSize: 12,
    fontWeight: '700',
  },
  storeDetails: {
    flex: 1,
  },
  storeName: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  bestDealBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    alignSelf: 'flex-start',
  },
  bestDealText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '700',
    textTransform: 'uppercase',
  },
  priceSection: {
    alignItems: 'flex-end',
    marginRight: 12,
  },
  price: {
    fontSize: 16,
    fontWeight: '600',
  },
  priceDifference: {
    fontSize: 12,
    marginTop: 2,
  },
});
