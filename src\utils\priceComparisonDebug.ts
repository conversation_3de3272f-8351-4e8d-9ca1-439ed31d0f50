import { priceComparisonService } from '../services/priceComparisonService';
import { API_CONFIG } from '../constants/config';

export const debugPriceComparison = async () => {
  console.log('🔍 Price Comparison Debug Test');
  
  // Check configuration
  console.log('📋 Configuration Check:');
  console.log('- Algolia: Not configured (using Supabase)');
  console.log('- Service Configured:', priceComparisonService.isConfigured() ? '✅ Yes' : '❌ No');
  
  if (!priceComparisonService.isConfigured()) {
    console.log('❌ Price comparison service not configured');
    return;
  }
  
  // Test store configuration
  console.log('\n🏪 Store Configuration:');
  const storeInfo = priceComparisonService.getStoreInfo();
  Object.entries(storeInfo).forEach(([key, config]) => {
    console.log(`- ${key}: ${config.displayName} (${config.indexName})`);
  });
  
  // Test simple product search
  console.log('\n🔍 Testing Product Search...');
  const testQueries = ['milk', 'bread', 'eggs', 'butter'];
  
  for (const query of testQueries) {
    console.log(`\nSearching for: "${query}"`);
    try {
      const result = await priceComparisonService.searchProductAcrossStores(query, {
        maxResults: 3,
        sortBy: 'price'
      });
      
      console.log(`- Found ${result.results.length} results`);
      
      if (result.results.length > 0) {
        result.results.forEach((item, index) => {
          console.log(`  ${index + 1}. ${item.storeName}: ${item.product.name} - $${item.product.price.toFixed(2)}`);
        });
        
        if (result.cheapestOption) {
          console.log(`  💰 Cheapest: ${result.cheapestOption.storeName} - $${result.cheapestOption.product.price.toFixed(2)}`);
        }
        
        if (result.priceRange) {
          console.log(`  📊 Price Range: $${result.priceRange.min.toFixed(2)} - $${result.priceRange.max.toFixed(2)} (Save $${result.priceRange.savings.toFixed(2)})`);
        }
      } else {
        console.log('  ❌ No results found');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.log(`  ❌ Error: ${errorMessage}`);
    }
  }
  
  // Test shopping list optimization
  console.log('\n📝 Testing Shopping List Optimization...');
  try {
    const optimization = await priceComparisonService.optimizeShoppingList([
      'milk', 'bread', 'eggs'
    ], 'cheapest_total');
    
    console.log(`- Total Cost: $${optimization.totalCost.toFixed(2)}`);
    console.log(`- Items Found: ${optimization.itemCount}`);
    console.log(`- Stores Needed: ${optimization.storeBreakdown.length}`);
    console.log(`- Strategy: ${optimization.recommendedStrategy}`);
    
    optimization.storeBreakdown.forEach(store => {
      console.log(`  ${store.storeName}: ${store.itemCount} items, $${store.subtotal.toFixed(2)}`);
    });
    
    if (optimization.unavailableItems.length > 0) {
      console.log(`- Unavailable: ${optimization.unavailableItems.join(', ')}`);
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.log(`❌ Optimization Error: ${errorMessage}`);
  }
  
  console.log('\n✅ Debug test complete');
};

// Simple test function for UI components
export const testPriceSearch = async (query: string) => {
  if (!query.trim()) {
    console.log('❌ Empty search query');
    return null;
  }
  
  console.log(`🔍 Searching for: "${query}"`);
  
  try {
    const result = await priceComparisonService.searchProductAcrossStores(query, {
      maxResults: 5,
      sortBy: 'price'
    });
    
    console.log(`Found ${result.results.length} results:`);
    result.results.forEach((item, index) => {
      console.log(`${index + 1}. ${item.storeName}: ${item.product.name} - $${item.product.price.toFixed(2)}`);
    });
    
    return result;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.log(`❌ Search failed: ${errorMessage}`);
    return null;
  }
};