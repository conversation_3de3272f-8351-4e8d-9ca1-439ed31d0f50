import { Recipe } from '../utils/storage';

// Import types from our exported data
interface ExportedRecipe {
  id: string;
  title: string;
  description: string;
  prepTime: string;
  cookTime: string;
  totalTime: string;
  servings: string;
  ingredients: string[];
  instructions: string[];
  category: string;
  cuisine: string;
  difficulty: string;
  tags: string[];
  dietary: string[];
  mealType: string[];
  imageUrl: string;
  source: string;
  url: string;
  aiProcessed: boolean;
  appealScore: number;
  searchableText: string;
  createdAt: string;
  updatedAt: string;
}

class ImportedRecipeService {
  private recipes: Recipe[] = [];
  private isLoaded = false;

  // Convert exported recipe to app's Recipe interface
  private adaptRecipe(exportedRecipe: ExportedRecipe): Recipe {
    return {
      id: exportedRecipe.id,
      title: exportedRecipe.title,
      description: exportedRecipe.description,
      ingredients: exportedRecipe.ingredients,
      instructions: exportedRecipe.instructions,
      cookingTime: exportedRecipe.totalTime || `${exportedRecipe.prepTime} + ${exportedRecipe.cookTime}`,
      difficulty: exportedRecipe.difficulty,
      mealType: exportedRecipe.mealType.join(', '),
      createdAt: exportedRecipe.createdAt,
      servings: parseInt(exportedRecipe.servings) || 4,
      tags: [...exportedRecipe.tags, ...exportedRecipe.dietary],
      
      // Enhanced fields
      imageUrl: exportedRecipe.imageUrl,
      category: exportedRecipe.category,
      cuisine: exportedRecipe.cuisine,
      dishType: exportedRecipe.mealType[0] || 'Main Course',
      
      // Estimated nutrition (you might want to calculate this properly later)
      calories: this.estimateCalories(exportedRecipe.ingredients.length, exportedRecipe.servings),
    };
  }

  // Simple calorie estimation based on ingredient count
  private estimateCalories(ingredientCount: number, servings: string): number {
    const avgCaloriesPerIngredient = 150;
    const totalCalories = ingredientCount * avgCaloriesPerIngredient;
    const servingCount = parseInt(servings) || 4;
    return Math.round(totalCalories / servingCount);
  }

  // Load sample recipes for testing
  async loadSampleRecipes(): Promise<Recipe[]> {
    try {
      // No more sample data - recipes come from Algolia
      const sampleData: ExportedRecipe[] = [];

      this.recipes = sampleData.map(recipe => this.adaptRecipe(recipe));
      this.isLoaded = true;
      return this.recipes;
    } catch (error) {
      console.error('Failed to load sample recipes:', error);
      return [];
    }
  }

  // Get recipes by category
  async getRecipesByCategory(category: string): Promise<Recipe[]> {
    if (!this.isLoaded) {
      await this.loadSampleRecipes();
    }
    return this.recipes.filter(recipe => 
      recipe.category?.toLowerCase() === category.toLowerCase()
    );
  }

  // Get recipes by cuisine
  async getRecipesByCuisine(cuisine: string): Promise<Recipe[]> {
    if (!this.isLoaded) {
      await this.loadSampleRecipes();
    }
    return this.recipes.filter(recipe => 
      recipe.cuisine?.toLowerCase() === cuisine.toLowerCase()
    );
  }

  // Search recipes
  async searchRecipes(query: string): Promise<Recipe[]> {
    if (!this.isLoaded) {
      await this.loadSampleRecipes();
    }
    
    const lowerQuery = query.toLowerCase();
    return this.recipes.filter(recipe => 
      recipe.title.toLowerCase().includes(lowerQuery) ||
      recipe.description?.toLowerCase().includes(lowerQuery) ||
      recipe.ingredients.some(ingredient => 
        ingredient.toLowerCase().includes(lowerQuery)
      ) ||
      recipe.tags?.some(tag => 
        tag.toLowerCase().includes(lowerQuery)
      )
    );
  }

  // Get all recipes
  async getAllRecipes(): Promise<Recipe[]> {
    if (!this.isLoaded) {
      await this.loadSampleRecipes();
    }
    return this.recipes;
  }

  // Get recipe by ID
  async getRecipeById(id: string): Promise<Recipe | null> {
    if (!this.isLoaded) {
      await this.loadSampleRecipes();
    }
    return this.recipes.find(recipe => recipe.id === id) || null;
  }

  // Get random recipes
  async getRandomRecipes(count: number = 5): Promise<Recipe[]> {
    if (!this.isLoaded) {
      await this.loadSampleRecipes();
    }
    
    const shuffled = [...this.recipes].sort(() => Math.random() - 0.5);
    return shuffled.slice(0, count);
  }

  // Get available categories
  getAvailableCategories(): string[] {
    if (!this.isLoaded) return [];
    
    const categories = new Set(this.recipes.map(recipe => recipe.category).filter((category): category is string => Boolean(category)));
    return Array.from(categories);
  }

  // Get available cuisines
  getAvailableCuisines(): string[] {
    if (!this.isLoaded) return [];
    
    const cuisines = new Set(this.recipes.map(recipe => recipe.cuisine).filter((cuisine): cuisine is string => Boolean(cuisine)));
    return Array.from(cuisines);
  }

  // Get recipe statistics
  getRecipeStats() {
    if (!this.isLoaded) return null;

    const categories = this.getAvailableCategories();
    const cuisines = this.getAvailableCuisines();
    
    const categoryStats = categories.reduce((acc, category) => {
      acc[category] = this.recipes.filter(r => r.category === category).length;
      return acc;
    }, {} as Record<string, number>);

    const cuisineStats = cuisines.reduce((acc, cuisine) => {
      acc[cuisine] = this.recipes.filter(r => r.cuisine === cuisine).length;
      return acc;
    }, {} as Record<string, number>);

    return {
      total: this.recipes.length,
      byCategory: categoryStats,
      byCuisine: cuisineStats,
      withImages: this.recipes.filter(r => r.imageUrl).length,
      withInstructions: this.recipes.filter(r => r.instructions.length > 0).length,
    };
  }
}

export const importedRecipeService = new ImportedRecipeService(); 