# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Development
- `npm run ts:check` - Run TypeScript type checking
- `expo start` - Start development server with Metro bundler
- `expo start --android` - Run on Android device/emulator
- `expo start --ios` - Run on iOS device/simulator
- `expo start --web` - Run web version

### Supabase Operations
- `npm run supabase:migrate` - Run database migrations via ts-node
- `npm run products:sync` - Sync product data with Supabase database

### Testing & Debugging
- TypeScript compilation errors are caught by `npm run ts:check`
- Debug Supabase connections by creating temporary Node.js scripts (see database troubleshooting section)

## Architecture Overview

### Core Technologies
- **React Native + Expo**: Cross-platform mobile app framework
- **TypeScript**: Full type safety throughout the application
- **Supabase**: PostgreSQL database with realtime capabilities and file storage
- **React Navigation**: Navigation stack management
- **Expo Router**: File-based routing system

### Database Architecture (Supabase)
The app connects to a **normalized Supabase database** representing New Zealand supermarket price data:

**Critical Understanding**: Product data is stored across multiple related tables:
- `products` table: Contains product info but many records have NULL `store` and `current_price` fields
- `prices` table: Contains the actual price records linking products to stores (22K+ records)  
- `stores` table: Contains store metadata (Woolworths, Pak'nSave, New World, etc.)

**Query Pattern**: Always use JOINs to get complete product data:
```sql
SELECT products.*, prices.price, stores.name 
FROM products 
INNER JOIN prices ON products.id = prices.product_id 
INNER JOIN stores ON prices.store_id = stores.id
```

### Key Data Services

#### Supabase Integration (`src/supabase/`)
- `client.ts` - Supabase client configuration with environment variables
- `types.ts` - Generated TypeScript types from database schema  
- `priceService.ts` - Product and price data queries using joined tables
- `recipeService.ts` - Recipe CRUD operations

#### Product Data Layer (`src/hooks/`, `src/services/`)
- `useOptimizedProducts.ts` - **Main product data hook** with pagination and search
- `useProductDeduplication.ts` - Groups similar products from different stores
- `productDeduplicationService.ts` - Core logic for product grouping and comparison
- `priceComparisonService.ts` - Cross-store price comparison logic

#### Theme System (`src/styles/`, `src/context/ThemeContext.tsx`)
Centralized design system with:
- Theme tokens (spacing, colors, typography) in `src/styles/theme.ts`
- Light/Dark mode support via ThemeContext
- Common reusable styles in `src/styles/commonStyles.ts`
- **Always import from `src/styles` index file**

### Component Architecture

#### Screen Structure (`src/screens/`)
- `main/AllProductsScreen.tsx` - Primary product browsing interface
- `main/RecipesScreen.tsx` - Recipe management and display
- `auth/` - Authentication screens (Auth0 integration)
- `onboarding/` - First-time user experience

#### Key Components (`src/components/`)
- `ModernProductCard.tsx` - Product display with price comparison
- `ModernSearchBar.tsx` - Search with autocomplete and filters
- `ModernFilterPills.tsx` - Category and store filtering
- `PriceComparisonModal.tsx` - Cross-store price comparison UI
- `ErrorBoundary.tsx` - Global error handling

### State Management Patterns

#### Context Providers
- `ThemeContext` - Theme and dark mode state
- `AuthContext` - User authentication state
- `ShoppingContext` - Shopping list and cart state

#### Custom Hooks Pattern
All complex state logic is extracted into custom hooks:
- `useAppBootstrap.ts` - Centralized app initialization
- `useOptimizedProducts.ts` - Product data with caching and pagination
- `useSmartSearch.ts` - Debounced search with autocomplete
- Export all hooks from `src/hooks/index.ts`

## Environment Configuration

### Required Environment Variables
Copy `.env.example` to `.env` and configure:

```bash
# Supabase (Required)
EXPO_PUBLIC_SUPABASE_URL=your_supabase_project_url  
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_service_role_key

# AI Services (Optional)
EXPO_PUBLIC_GEMINI_API_KEY=your_gemini_api_key

# Auth0 (Required for auth)
EXPO_PUBLIC_AUTH0_DOMAIN=your-domain.auth0.com
EXPO_PUBLIC_AUTH0_CLIENT_ID=your_client_id
```

**Security Note**: Use service role key for Supabase in development. For production, implement proper RLS policies and use anon key.

## Security Guidelines

### API Key Management
- **NEVER** commit API keys or use `EXPO_PUBLIC_` prefix for secrets in production
- Use Firebase Cloud Functions for external API calls in production (see `.cursor/rules/security-and-firebase.md`)
- Current development setup uses exposed keys for rapid development - migrate to secure backend

### Database Security
- Supabase RLS (Row Level Security) policies should be implemented for production
- Currently using service role key for development convenience
- Validate all user inputs before database queries

## Common Development Patterns

### Database Troubleshooting
When products aren't loading:
1. Check Supabase connection with a simple Node.js test script
2. Verify the query is using JOINs to get complete data (many products have NULL store/price in main table)
3. Check that environment variables are loaded correctly
4. Test queries in Supabase dashboard SQL editor first

### Adding New Product Queries
Always follow the joined query pattern in `src/supabase/priceService.ts`:
```typescript
const { data, error } = await supabase
  .from('products')
  .select(`
    *,
    prices!inner (
      price,
      recorded_at,
      stores!inner (name, display_name)
    )
  `)
```

### Theme Integration
Use the centralized theme system:
```typescript
import { useTheme } from '../context/ThemeContext';
const { colors, isDark } = useTheme();
```

### Error Handling
Implement proper error boundaries and user-friendly error states:
```typescript
// Wrap components in ErrorBoundary
<ErrorBoundary onError={logError}>
  <YourComponent />
</ErrorBoundary>
```

## TypeScript Configuration

The project uses strict TypeScript configuration:
- All files must have proper type annotations
- Database types are auto-generated in `src/supabase/types.ts`
- Component props should have interfaces
- Use `npm run ts:check` frequently during development

## File Organization Conventions

- Components: One component per file with co-located styles
- Services: Stateless business logic and API calls
- Hooks: Stateful logic extraction from components  
- Types: Interface definitions in `src/types/`
- Utils: Pure utility functions in `src/utils/`
- Always use absolute imports from `src/` root