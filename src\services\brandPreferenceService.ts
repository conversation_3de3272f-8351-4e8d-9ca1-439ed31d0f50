import AsyncStorage from '@react-native-async-storage/async-storage';

interface BrandPreference {
  productName: string;
  selectedBrand: string;
  confidence: number; // 0-1 scale, increases with usage
  lastUsed: string;
  timesUsed: number;
  store: string;
  price: number;
}

interface BrandPreferenceStats {
  totalPreferences: number;
  mostUsedBrands: Array<{ brand: string; count: number }>;
  averageConfidence: number;
  recentSelections: BrandPreference[];
}

const STORAGE_KEY = '@brand_preferences';
const MAX_PREFERENCES = 1000; // Limit to prevent storage bloat

class BrandPreferenceService {
  private preferences: Map<string, BrandPreference> = new Map();
  private isInitialized = false;

  /**
   * Initialize the service by loading preferences from storage
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      const stored = await AsyncStorage.getItem(STORAGE_KEY);
      if (stored) {
        const preferencesArray: BrandPreference[] = JSON.parse(stored);
        preferencesArray.forEach(pref => {
          this.preferences.set(this.getKey(pref.productName), pref);
        });
      }
      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize brand preferences:', error);
      this.isInitialized = true; // Continue with empty preferences
    }
  }

  /**
   * Save a brand preference for a product
   */
  async saveBrandPreference(
    productName: string,
    selectedBrand: string,
    store: string,
    price: number
  ): Promise<void> {
    await this.initialize();

    const key = this.getKey(productName);
    const existing = this.preferences.get(key);
    
    const now = new Date().toISOString();
    
    if (existing && existing.selectedBrand === selectedBrand) {
      // Same brand selected again - increase confidence
      existing.confidence = Math.min(1.0, existing.confidence + 0.1);
      existing.lastUsed = now;
      existing.timesUsed += 1;
      existing.store = store;
      existing.price = price;
    } else {
      // New brand or different brand selected
      const preference: BrandPreference = {
        productName: productName.toLowerCase().trim(),
        selectedBrand,
        confidence: existing ? 0.3 : 0.5, // Lower confidence if changing brands
        lastUsed: now,
        timesUsed: 1,
        store,
        price
      };
      this.preferences.set(key, preference);
    }

    await this.saveToStorage();
  }

  /**
   * Get brand preference for a product
   */
  async getBrandPreference(productName: string): Promise<BrandPreference | null> {
    await this.initialize();
    
    const key = this.getKey(productName);
    return this.preferences.get(key) || null;
  }

  /**
   * Get recommended brand for a product based on preferences
   */
  async getRecommendedBrand(productName: string): Promise<string | null> {
    const preference = await this.getBrandPreference(productName);
    
    if (preference && preference.confidence > 0.3) {
      return preference.selectedBrand;
    }
    
    return null;
  }

  /**
   * Get all brand preferences for a user
   */
  async getAllPreferences(): Promise<BrandPreference[]> {
    await this.initialize();
    return Array.from(this.preferences.values());
  }

  /**
   * Get brand preference statistics
   */
  async getPreferenceStats(): Promise<BrandPreferenceStats> {
    await this.initialize();
    
    const prefs = Array.from(this.preferences.values());
    const brandCounts: { [brand: string]: number } = {};
    
    prefs.forEach(pref => {
      brandCounts[pref.selectedBrand] = (brandCounts[pref.selectedBrand] || 0) + 1;
    });
    
    const mostUsedBrands = Object.entries(brandCounts)
      .map(([brand, count]) => ({ brand, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
    
    const averageConfidence = prefs.length > 0 
      ? prefs.reduce((sum, pref) => sum + pref.confidence, 0) / prefs.length 
      : 0;
    
    const recentSelections = prefs
      .sort((a, b) => new Date(b.lastUsed).getTime() - new Date(a.lastUsed).getTime())
      .slice(0, 20);
    
    return {
      totalPreferences: prefs.length,
      mostUsedBrands,
      averageConfidence,
      recentSelections
    };
  }

  /**
   * Clear all brand preferences
   */
  async clearAllPreferences(): Promise<void> {
    this.preferences.clear();
    await AsyncStorage.removeItem(STORAGE_KEY);
  }

  /**
   * Remove a specific brand preference
   */
  async removeBrandPreference(productName: string): Promise<void> {
    await this.initialize();
    
    const key = this.getKey(productName);
    this.preferences.delete(key);
    await this.saveToStorage();
  }

  /**
   * Update brand preference confidence (for machine learning)
   */
  async updateConfidence(productName: string, delta: number): Promise<void> {
    await this.initialize();
    
    const key = this.getKey(productName);
    const preference = this.preferences.get(key);
    
    if (preference) {
      preference.confidence = Math.max(0, Math.min(1, preference.confidence + delta));
      await this.saveToStorage();
    }
  }

  /**
   * Get preferences that might be stale (not used in 30 days)
   */
  async getStalePreferences(): Promise<BrandPreference[]> {
    await this.initialize();
    
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    return Array.from(this.preferences.values())
      .filter(pref => new Date(pref.lastUsed) < thirtyDaysAgo);
  }

  /**
   * Clean up old preferences to prevent storage bloat
   */
  async cleanupOldPreferences(): Promise<void> {
    await this.initialize();
    
    const prefs = Array.from(this.preferences.values());
    
    if (prefs.length > MAX_PREFERENCES) {
      // Keep only the most recent and most used preferences
      const sorted = prefs.sort((a, b) => {
        const scoreA = a.confidence * 0.7 + (a.timesUsed / 100) * 0.3;
        const scoreB = b.confidence * 0.7 + (b.timesUsed / 100) * 0.3;
        return scoreB - scoreA;
      });
      
      this.preferences.clear();
      sorted.slice(0, MAX_PREFERENCES).forEach(pref => {
        this.preferences.set(this.getKey(pref.productName), pref);
      });
      
      await this.saveToStorage();
    }
  }

  /**
   * Export preferences for backup
   */
  async exportPreferences(): Promise<string> {
    await this.initialize();
    const prefs = Array.from(this.preferences.values());
    return JSON.stringify(prefs, null, 2);
  }

  /**
   * Import preferences from backup
   */
  async importPreferences(jsonData: string): Promise<void> {
    try {
      const prefs: BrandPreference[] = JSON.parse(jsonData);
      this.preferences.clear();
      
      prefs.forEach(pref => {
        this.preferences.set(this.getKey(pref.productName), pref);
      });
      
      await this.saveToStorage();
    } catch (error) {
      throw new Error('Invalid preferences data format');
    }
  }

  /**
   * Generate a consistent key for a product name
   */
  private getKey(productName: string): string {
    return productName.toLowerCase().trim().replace(/\s+/g, '_');
  }

  /**
   * Save preferences to AsyncStorage
   */
  private async saveToStorage(): Promise<void> {
    try {
      const prefs = Array.from(this.preferences.values());
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(prefs));
    } catch (error) {
      console.error('Failed to save brand preferences:', error);
    }
  }
}

export const brandPreferenceService = new BrandPreferenceService();
export type { BrandPreference, BrandPreferenceStats };