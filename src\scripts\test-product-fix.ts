/**
 * TEST SCRIPT - Verify Product Display Fix
 * 
 * This script tests the updated product fetching logic to ensure
 * all products from all stores are now being returned.
 */

import { createClient } from '@supabase/supabase-js';

// Use hardcoded values for testing
const supabaseUrl = 'https://emjwniuqwhvohjassnrg.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVtanduaXVxd2h2b2hqYXNzbnJnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjQwMTYwNSwiZXhwIjoyMDY3OTc3NjA1fQ.oTowRoNAjlUmk10O9pSMK2BvL0XlyBb5orVRrlEryHs';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testProductFetch() {
  console.log('🧪 Testing Updated Product Fetch Logic...\n');

  // Test 1: Simulate the updated productFetchService query
  console.log('1. Testing productFetchService query (updated)...');
  
  let supabaseQuery = supabase
    .from('products')
    .select('*');

  // Remove availability filter to show all products
  // supabaseQuery = supabaseQuery.in('availability', ['available', 'in_stock', null]);

  // Apply sorting and use range to bypass 1000 row limit
  supabaseQuery = supabaseQuery
    .order('name', { ascending: true })
    .range(0, 10308); // Get all 10,309 products (0-indexed)

  const { data: products, error } = await supabaseQuery;

  if (error) {
    console.error('   ❌ Error:', error);
    return;
  }

  console.log(`   ✅ Query returned: ${products?.length || 0} products`);

  // Break down by store
  const byStore = products?.reduce((acc: any, product) => {
    acc[product.store] = (acc[product.store] || 0) + 1;
    return acc;
  }, {}) || {};

  console.log('   📊 Breakdown by store:');
  Object.entries(byStore).forEach(([store, count]) => {
    console.log(`      ${store}: ${count} products`);
  });

  // Test 2: Check availability values in the results
  console.log('\n2. Checking availability values in results...');
  const availabilityBreakdown = products?.reduce((acc: any, product) => {
    const availability = product.availability || 'null';
    acc[availability] = (acc[availability] || 0) + 1;
    return acc;
  }, {}) || {};

  console.log('   📊 Availability breakdown:');
  Object.entries(availabilityBreakdown).forEach(([availability, count]) => {
    console.log(`      ${availability}: ${count} products`);
  });

  // Test 3: Sample products from each store
  console.log('\n3. Sample products from each store...');
  const stores = ['woolworths', 'newworld', 'paknsave'];
  
  for (const store of stores) {
    const storeProducts = products?.filter(p => p.store === store).slice(0, 3) || [];
    console.log(`   ${store.toUpperCase()}:`);
    storeProducts.forEach((product, index) => {
      console.log(`      ${index + 1}. ${product.name} - $${product.price || 'N/A'}`);
    });
  }

  // Test 4: Check for products without prices
  console.log('\n4. Checking products without prices...');
  const withoutPrices = products?.filter(p => !p.price || p.price === null) || [];
  console.log(`   Products without prices: ${withoutPrices.length}`);
  
  if (withoutPrices.length > 0) {
    console.log('   Sample products without prices:');
    withoutPrices.slice(0, 3).forEach((product, index) => {
      console.log(`      ${index + 1}. ${product.name} (${product.store})`);
    });
  }

  console.log('\n✅ Product fetch test completed!');
  
  return {
    totalProducts: products?.length || 0,
    byStore,
    availabilityBreakdown,
    productsWithoutPrices: withoutPrices.length
  };
}

// Run the test
if (require.main === module) {
  testProductFetch()
    .then((result) => {
      console.log('\n📊 TEST RESULTS SUMMARY:');
      console.log('='.repeat(50));
      console.log(`Total Products Returned: ${result?.totalProducts || 0}`);
      console.log('\nExpected vs Actual:');
      console.log(`  Expected: ~10,309 products`);
      console.log(`  Actual: ${result?.totalProducts || 0} products`);
      
      if ((result?.totalProducts || 0) > 5000) {
        console.log('✅ SUCCESS: Product display issue is FIXED!');
        console.log('   The app should now show significantly more products.');
      } else {
        console.log('⚠️  PARTIAL: Still not showing all products.');
        console.log('   May need additional filter adjustments.');
      }
      console.log('='.repeat(50));
    })
    .catch((error) => {
      console.error('❌ Test failed:', error);
    });
}

export { testProductFetch };
