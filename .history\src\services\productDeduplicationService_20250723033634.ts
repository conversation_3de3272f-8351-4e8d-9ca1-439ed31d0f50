
import { IProduct, IUnifiedProduct, StoreConfig } from '../types/shoppingList';
import {
  DeduplicationConfig,
  DEFAULT_DEDUPLICATION_CONFIG,
  DeduplicationResult,
  ProductMatch,
  ProductGroup as DeduplicationProductGroup,
  DeduplicationError,
  IProductDeduplicationService,
  NZ_BRANDS,
  ExtractedBrand,
  EnhancedProduct
} from '../types/deduplication';
import {
  calculateFuzzySimilarity,
  brandsMatch,
  sizesMatch,
  normalizeString
} from '../utils/stringMatching';

// Export Product type for tests (alias to IProduct)
export type Product = IProduct;

// Store configuration for display and priority
const STORE_CONFIGS: StoreConfig[] = [
  {
    id: 'woolworths',
    name: 'woolworths',
    displayName: 'Woolworths',
    icon: '🛒',
    color: '#00A651',
    priority: 1
  },
  {
    id: 'newworld',
    name: 'newworld', 
    displayName: 'New World',
    icon: '🍎',
    color: '#E31E24',
    priority: 2
  },
  {
    id: 'paknsave',
    name: 'paknsave',
    displayName: "Pak'nSave",
    icon: '💰',
    color: '#FFD100',
    priority: 3
  }
];

const STORE_CONFIG_MAP = STORE_CONFIGS.reduce((acc, config) => {
  acc[config.id] = config;
  return acc;
}, {} as Record<string, StoreConfig>);

// Define ProductGroup interface for compatibility
export interface ProductGroup {
  products: IProduct[];
  stores: string[];
  name: string;
  brand?: string;
  category?: string;
  bestPrice?: number;
  bestImage?: string;
  totalVariants?: number;
  availableCount?: number;
}

// Re-export IUnifiedProduct for external use
export type { IUnifiedProduct } from '../types/shoppingList';

export class ProductDeduplicationService implements IProductDeduplicationService {
  private config: DeduplicationConfig;

  constructor(config?: Partial<DeduplicationConfig>) {
    this.config = { ...DEFAULT_DEDUPLICATION_CONFIG, ...config };
  }

  // STORY 1.1 REQUIRED METHODS

  /**
   * Main deduplication function as required by story
   * Analyzes products from Supabase queries and returns grouped results
   */
  public deduplicateProducts(products: IProduct[], config?: Partial<DeduplicationConfig>): DeduplicationResult {
    const startTime = performance.now();
    
    // Use provided config or instance config
    const activeConfig = config ? { ...this.config, ...config } : this.config;
    
    try {
      if (!products || products.length === 0) {
        return {
          originalProducts: [],
          groups: [],
          unifiedProducts: [],
          stats: {
            originalCount: 0,
            groupCount: 0,
            unifiedCount: 0,
            averageConfidence: 0,
            processingTimeMs: 0
          },
          config: activeConfig
        };
      }

      console.log(`🔄 Starting deduplication for ${products.length} products with config:`, activeConfig);
      
      // Group products using enhanced matching
      const groups = this.createProductGroups(products, activeConfig);
      
      // Convert groups to unified products
      const unifiedProducts = groups.map(group => this.createUnifiedProductFromGroup(group));
      
      // Calculate stats
      const endTime = performance.now();
      const avgConfidence = groups.length > 0 
        ? groups.reduce((sum, group) => sum + group.groupConfidence, 0) / groups.length
        : 0;
      
      const result: DeduplicationResult = {
        originalProducts: products,
        groups,
        unifiedProducts: unifiedProducts.sort((a, b) => {
          if (a.name !== b.name) return a.name.localeCompare(b.name);
          return a.lowestPrice - b.lowestPrice;
        }),
        stats: {
          originalCount: products.length,
          groupCount: groups.length,
          unifiedCount: unifiedProducts.length,
          averageConfidence: Math.round(avgConfidence * 1000) / 1000,
          processingTimeMs: Math.round(endTime - startTime)
        },
        config: activeConfig
      };

      console.log(`✅ Deduplication complete:`, result.stats);
      return result;
      
    } catch (error) {
      console.error('❌ Deduplication failed:', error);
      throw new DeduplicationError(
        'Failed to deduplicate products',
        'PROCESSING_ERROR',
        error instanceof Error ? error : undefined
      );
    }
  }

  /**
   * Configure matching thresholds as required by story
   */
  public configureThresholds(config: Partial<DeduplicationConfig>): void {
    // Validate configuration
    if (config.nameSimilarityThreshold !== undefined) {
      if (config.nameSimilarityThreshold < 0 || config.nameSimilarityThreshold > 1) {
        throw new DeduplicationError(
          'nameSimilarityThreshold must be between 0 and 1',
          'CONFIG_ERROR'
        );
      }
    }
    
    if (config.minimumConfidence !== undefined) {
      if (config.minimumConfidence < 0 || config.minimumConfidence > 1) {
        throw new DeduplicationError(
          'minimumConfidence must be between 0 and 1',
          'CONFIG_ERROR'
        );
      }
    }

    // Update configuration
    this.config = { ...this.config, ...config };
    console.log('🔧 Deduplication thresholds updated:', this.config);
  }

  /**
   * Get matching confidence between two products as required by story
   */
  public getMatchingConfidence(product1: IProduct, product2: IProduct): number {
    try {
      return this.calculateProductMatchConfidence(product1, product2, this.config);
    } catch (error) {
      console.warn('Failed to calculate matching confidence:', error);
      return 0;
    }
  }

  /**
   * Get current configuration
   */
  public getConfiguration(): DeduplicationConfig {
    return { ...this.config };
  }

  /**
   * Reset configuration to defaults
   */
  public resetConfiguration(): void {
    this.config = { ...DEFAULT_DEDUPLICATION_CONFIG };
    console.log('🔄 Deduplication configuration reset to defaults');
  }

  // ENHANCED INTERNAL METHODS

  /**
   * Enhanced product grouping with sophisticated matching and multi-store aggregation
   */
  public groupProducts(products: IProduct[]): IUnifiedProduct[] {
    if (!products || products.length === 0) {
      return [];
    }

    console.log(`🔄 Starting deduplication for ${products.length} products`);
    
    const productGroups = new Map<string, IProduct[]>();
    
    // Group products by normalized identity
    products.forEach(product => {
      const productKey = this.generateProductKey(product);
      
      if (!productGroups.has(productKey)) {
        productGroups.set(productKey, []);
      }
      
      productGroups.get(productKey)!.push(product);
    });
    
    console.log(`📊 Grouped into ${productGroups.size} unique products`);
    
    // Convert groups to unified products
    const unifiedProducts = Array.from(productGroups.entries()).map(([key, groupProducts]) => 
      this.createUnifiedProduct(key, groupProducts)
    );
    
    console.log(`✅ Created ${unifiedProducts.length} unified products`);
    
    return unifiedProducts.sort((a, b) => {
      // Sort by name, then by lowest price
      if (a.name !== b.name) {
        return a.name.localeCompare(b.name);
      }
      return a.lowestPrice - b.lowestPrice;
    });
  }

  /**
   * Generate a unique key for product grouping based on normalized attributes
   */
  private generateProductKey(product: IProduct): string {
    const normalizedName = this.normalizeName(product.name);
    const normalizedBrand = this.normalizeBrand(product.brand || '');
    const normalizedSize = this.normalizeSize(product.size || product.unit || '');
    
    // Create a composite key for better matching
    return `${normalizedBrand}_${normalizedName}_${normalizedSize}`.toLowerCase();
  }
  
  /**
   * Create a unified product from a group of store-specific products
   */
  private createUnifiedProduct(key: string, products: IProduct[]): IUnifiedProduct {
    // Select the "best" product as the base (prefer one with most complete data)
    const baseProduct = this.selectBestProduct(products);
    
    // Aggregate store data
    const storePrices: { [store: string]: number } = {};
    const storeAvailability: { [store: string]: boolean } = {};
    const storeDetails: { [store: string]: any } = {};
    const allStores: string[] = [];
    
    products.forEach(product => {
      if (product.price > 0) { // Only include products with valid prices
        storePrices[product.store] = product.price;
        storeAvailability[product.store] = product.is_available !== false;
        allStores.push(product.store);
        
        storeDetails[product.store] = {
          price: product.price,
          available: product.is_available !== false,
          imageUrl: product.imageUrl,
          promotions: product.promotionText ? [product.promotionText] : [],
          lastUpdated: product.last_updated,
          isOnSale: product.isOnSale || false,
          originalPrice: product.originalPrice
        };
      }
    });
    
    const prices = Object.values(storePrices);
    const lowestPrice = Math.min(...prices);
    const highestPrice = Math.max(...prices);
    const bestStore = Object.entries(storePrices)
      .find(([store, price]) => price === lowestPrice)?.[0] || allStores[0];
    
    const maxSavings = highestPrice - lowestPrice;
    const savingsPercentage = highestPrice > 0 ? (maxSavings / highestPrice) * 100 : 0;
    
    return {
      // Core product data from base product
      id: baseProduct.id,
      name: baseProduct.name,
      brand: baseProduct.brand,
      size: baseProduct.size,
      category: baseProduct.category,
      unit: baseProduct.unit,
      imageUrl: baseProduct.imageUrl,
      isOnSale: baseProduct.isOnSale,
      promotionText: baseProduct.promotionText,
      availability: baseProduct.availability,
      last_updated: baseProduct.last_updated,
      is_available: baseProduct.is_available,
      
      // Unified product specific fields
      unifiedId: key,
      storePrices,
      storeAvailability,
      storeDetails,
      allStores: [...new Set(allStores)], // Remove duplicates
      lowestPrice,
      highestPrice,
      bestStore,
      maxSavings,
      savingsPercentage: Math.round(savingsPercentage * 100) / 100,
      variants: products.length,
      confidence: this.calculateMatchConfidence(products),
      
      // Display properties from best store
      displayPrice: lowestPrice,
      displayStore: bestStore,
      displayImageUrl: storeDetails[bestStore]?.imageUrl || baseProduct.imageUrl,
      
      // Legacy compatibility fields
      originalPrice: baseProduct.originalPrice,
      selectedPrice: lowestPrice,
      selectedStore: bestStore,
      selectedStoreName: STORE_CONFIG_MAP[bestStore]?.displayName || bestStore,
      potentialSavings: maxSavings,
      allStorePrices: Object.entries(storePrices).map(([store, price]) => ({
        store: store as any,
        price,
        available: storeAvailability[store],
        brand: baseProduct.brand,
        size: baseProduct.size,
        imageUrl: storeDetails[store]?.imageUrl,
        isOnSale: storeDetails[store]?.isOnSale,
        originalPrice: storeDetails[store]?.originalPrice,
        promotions: storeDetails[store]?.promotions,
        lastUpdated: storeDetails[store]?.lastUpdated
      }))
    };
  }
  
  /**
   * Select the best product from a group to use as the base
   */
  private selectBestProduct(products: IProduct[]): IProduct {
    // Prefer products with more complete data, then by store priority
    return products.reduce((best, current) => {
      const bestScore = this.scoreProduct(best);
      const currentScore = this.scoreProduct(current);
      return currentScore > bestScore ? current : best;
    });
  }
  
  /**
   * Score a product based on data completeness and store priority
   */
  private scoreProduct(product: IProduct): number {
    let score = 0;
    
    // Data completeness
    if (product.brand) score += 2;
    if (product.imageUrl) score += 2;
    if (product.size || product.unit) score += 1;
    if (product.category) score += 1;
    if (product.price > 0) score += 3;
    
    // Store priority (lower priority number = higher score)
    const storeConfig = STORE_CONFIG_MAP[product.store];
    if (storeConfig) {
      score += (4 - storeConfig.priority); // Invert priority for scoring
    }
    
    return score;
  }
  
  /**
   * Calculate matching confidence based on product group consistency
   */
  private calculateMatchConfidence(products: IProduct[]): number {
    if (products.length === 1) return 1.0;
    
    // Check consistency across key attributes
    const brands = [...new Set(products.map(p => p.brand).filter(Boolean))];
    const sizes = [...new Set(products.map(p => p.size || p.unit).filter(Boolean))];
    const categories = [...new Set(products.map(p => p.category).filter(Boolean))];
    
    let confidence = 1.0;
    
    // Penalize for inconsistent attributes
    if (brands.length > 1) confidence -= 0.2;
    if (sizes.length > 1) confidence -= 0.1;
    if (categories.length > 1) confidence -= 0.1;
    
    return Math.max(0.5, confidence); // Minimum confidence of 0.5
  }

  /**
   * Create product groups using enhanced matching algorithm
   */
  private createProductGroups(products: IProduct[], config: DeduplicationConfig): DeduplicationProductGroup[] {
    const groups: DeduplicationProductGroup[] = [];
    const processed = new Set<string>();

    for (const product of products) {
      if (processed.has(product.id)) continue;

      // Start a new group with this product
      const group: DeduplicationProductGroup = {
        groupId: `group_${groups.length + 1}`,
        products: [{
          product,
          confidence: 1.0,
          matchBreakdown: {
            nameMatch: 1.0,
            brandMatch: true,
            categoryMatch: true,
            sizeMatch: true
          }
        }],
        groupConfidence: 1.0,
        representative: product,
        stores: [product.store]
      };

      processed.add(product.id);

      // Find matching products
      for (const otherProduct of products) {
        if (processed.has(otherProduct.id)) continue;

        const confidence = this.calculateProductMatchConfidence(product, otherProduct, config);
        
        if (confidence >= config.minimumConfidence) {
          const matchResult = this.getDetailedMatch(product, otherProduct, config);
          
          group.products.push({
            product: otherProduct,
            confidence,
            matchBreakdown: matchResult
          });
          
          if (!group.stores.includes(otherProduct.store)) {
            group.stores.push(otherProduct.store);
          }
          
          processed.add(otherProduct.id);
        }
      }

      // Calculate overall group confidence
      group.groupConfidence = group.products.reduce((sum, p) => sum + p.confidence, 0) / group.products.length;
      
      // Select best representative (highest score)
      group.representative = this.selectBestProduct(group.products.map(p => p.product));
      
      groups.push(group);
    }

    return groups;
  }

  /**
   * Extract brand from product name using NZ brand list
   * Based on analysis showing most brand fields are empty
   */
  private extractBrandFromName(productName: string): ExtractedBrand | undefined {
    const name = productName.toLowerCase();

    // Check for exact brand matches
    for (const brand of NZ_BRANDS) {
      const brandLower = brand.toLowerCase();

      if (name.includes(brandLower)) {
        return {
          brand,
          confidence: 0.9,
          method: 'exact'
        };
      }
    }

    // Try to extract brand from first word if it looks like a brand
    const words = productName.split(' ');
    if (words.length > 0 && this.looksLikeBrand(words[0])) {
      return {
        brand: words[0],
        confidence: 0.5,
        method: 'position'
      };
    }

    return undefined;
  }

  /**
   * Check if a word looks like a brand name
   */
  private looksLikeBrand(word: string): boolean {
    // Brand names are usually capitalized and not common words
    const commonWords = ['the', 'and', 'with', 'from', 'organic', 'fresh', 'premium'];
    return word.length > 2 &&
           word[0] === word[0].toUpperCase() &&
           !commonWords.includes(word.toLowerCase());
  }

  /**
   * Get effective brand for comparison (extracted or original)
   */
  private getEffectiveBrand(product: IProduct): string | undefined {
    // Use original brand if available and not empty/null
    if (product.brand && product.brand !== 'N/A' && product.brand.trim() !== '') {
      return product.brand;
    }

    // Extract from name
    const extracted = this.extractBrandFromName(product.name);
    return extracted?.brand;
  }

  /**
   * Calculate matching confidence between two products
   * Enhanced with brand extraction based on data analysis
   */
  private calculateProductMatchConfidence(product1: IProduct, product2: IProduct, config: DeduplicationConfig): number {
    // Same product ID = perfect match
    if (product1.id === product2.id) return 1.0;

    let confidence = 0;
    let totalWeight = 0;

    // Name similarity (highest weight)
    const nameWeight = 0.4;
    const nameMatch = calculateFuzzySimilarity(
      product1.name || '',
      product2.name || '',
      config.nameSimilarityThreshold
    );
    confidence += nameMatch.similarity * nameWeight;
    totalWeight += nameWeight;

    // Enhanced brand matching with extraction
    const brandWeight = 0.25;
    const brand1 = this.getEffectiveBrand(product1);
    const brand2 = this.getEffectiveBrand(product2);
    const brandsAreEqual = brandsMatch(brand1, brand2);

    if (config.requireExactBrandMatch && !brandsAreEqual) {
      return 0; // Hard requirement
    }
    confidence += (brandsAreEqual ? 1 : 0) * brandWeight;
    totalWeight += brandWeight;

    // Category matching
    const categoryWeight = 0.15;
    const categoriesMatch = normalizeString(product1.category || '') === normalizeString(product2.category || '');
    if (config.requireExactCategoryMatch && !categoriesMatch) {
      return 0; // Hard requirement
    }
    confidence += (categoriesMatch ? 1 : 0) * categoryWeight;
    totalWeight += categoryWeight;

    // Size matching
    const sizeWeight = 0.2;
    const sizesAreEqual = sizesMatch(
      product1.size || product1.unit,
      product2.size || product2.unit,
      config.enableFuzzySizeMatching
    );
    if (config.requireSizeMatch && !sizesAreEqual) {
      return 0; // Hard requirement
    }
    confidence += (sizesAreEqual ? 1 : 0) * sizeWeight;
    totalWeight += sizeWeight;

    // Normalize confidence
    return totalWeight > 0 ? confidence / totalWeight : 0;
  }

  /**
   * Get detailed match breakdown for debugging and transparency
   * Enhanced with brand extraction
   */
  private getDetailedMatch(product1: IProduct, product2: IProduct, config: DeduplicationConfig) {
    const nameMatch = calculateFuzzySimilarity(
      product1.name || '',
      product2.name || '',
      config.nameSimilarityThreshold
    );

    // Use enhanced brand matching
    const brand1 = this.getEffectiveBrand(product1);
    const brand2 = this.getEffectiveBrand(product2);

    return {
      nameMatch: nameMatch.similarity,
      brandMatch: brandsMatch(brand1, brand2),
      categoryMatch: normalizeString(product1.category || '') === normalizeString(product2.category || ''),
      sizeMatch: sizesMatch(
        product1.size || product1.unit,
        product2.size || product2.unit,
        config.enableFuzzySizeMatching
      )
    };
  }

  /**
   * Create unified product from a product group
   */
  private createUnifiedProductFromGroup(group: DeduplicationProductGroup): IUnifiedProduct {
    const products = group.products.map(p => p.product);
    return this.createUnifiedProduct(group.groupId, products);
  }
  
  /**
   * Normalize product names for better matching
   */
  private normalizeName(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^\w\s]/g, '') // Remove special characters
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();
  }
  
  /**
   * Normalize brand names for consistent grouping
   */
  private normalizeBrand(brand: string): string {
    return brand
      .toLowerCase()
      .replace(/[^\w]/g, '') // Remove all non-word characters
      .trim();
  }
  
  /**
   * Normalize size/unit for consistent grouping
   */
  private normalizeSize(size: string): string {
    return size
      .toLowerCase()
      .replace(/[^\w]/g, '') // Remove all non-word characters
      .replace(/\s+/g, '')
      .trim();
  }
  
  /**
   * Get store configuration
   */
  public getStoreConfigs(): StoreConfig[] {
    return STORE_CONFIGS;
  }
  
  /**
   * Get store configuration by ID
   */
  public getStoreConfig(storeId: string): StoreConfig | undefined {
    return STORE_CONFIG_MAP[storeId];
  }
}

export const productDeduplicationService = new ProductDeduplicationService();
