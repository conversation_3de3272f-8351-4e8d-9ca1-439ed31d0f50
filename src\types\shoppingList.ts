/**
 * Central type definitions for shopping list functionality
 * This file consolidates all shopping list related types to prevent conflicts
 */

export interface StorePrice {
  store: 'woolworths' | 'newworld' | 'paknsave';
  price?: number;
  available: boolean;
  brand?: string;
  size?: string;
  imageUrl?: string;
  isOnSale?: boolean;
  originalPrice?: number;
  promotions?: string[];
  lastUpdated?: string;
}

// Store configuration for display
export interface StoreConfig {
  id: 'woolworths' | 'newworld' | 'paknsave';
  name: string;
  displayName: string;
  icon: string;
  color: string;
  priority: number; // For default store selection
}

export interface IProduct {
  id: string;
  name: string;
  price: number;
  originalPrice?: number;
  store: string;
  brand?: string;
  size?: string;
  category?: string;
  imageUrl?: string;
  isOnSale?: boolean;
  promotionText?: string;
  unit?: string;
  availability?: string;
  last_updated?: string;
  is_available?: boolean;
  
  // Enhanced price data properties
  selectedPrice?: number;
  selectedStore?: string;
  selectedStoreName?: string;
  potentialSavings?: number;
  allStorePrices?: StorePrice[];
}

// Enhanced unified product with multi-store pricing support
export interface IUnifiedProduct extends Omit<IProduct, 'store' | 'price'> {
  // Core identification
  unifiedId: string;
  
  // Multi-store pricing data
  storePrices: { [store: string]: number };
  storeAvailability: { [store: string]: boolean };
  allStores: string[];
  lowestPrice: number;
  highestPrice: number;
  bestStore: string;
  
  // Store-specific data aggregation
  storeDetails: {
    [store: string]: {
      price: number;
      available: boolean;
      imageUrl?: string;
      promotions?: string[];
      lastUpdated?: string;
      isOnSale?: boolean;
      originalPrice?: number;
    }
  };
  
  // Savings calculations
  maxSavings: number;
  savingsPercentage: number;
  
  // Product matching metadata
  variants: number;
  confidence: number;
  
  // Display properties (from best/preferred store)
  displayPrice: number;
  displayStore: string;
  displayImageUrl?: string;
}

// Define PriceProduct type (was missing)
export interface PriceProduct extends IProduct {
  storeName: string;
  storeColor?: string;
  storeIcon?: string;
}

export interface ProductPriceData {
  allPrices: PriceProduct[];
  bestPrice?: PriceProduct;
  brandGroups: BrandPriceGroup[];
  sizeGroups: SizePriceGroup[];
}

export interface BrandPriceGroup {
  brand: string;
  prices: IProduct[];
  averagePrice: number;
  bestPrice: PriceProduct;
}

export interface SizePriceGroup {
  size: string;
  prices: IProduct[];
  averagePrice: number;
  bestPrice: PriceProduct;
}

export interface ShoppingListItem {
  id: string;
  name: string;
  checked: boolean;
  quantity: number;
  unit?: string;
  category?: string;
  selectedBrand?: string;
  selectedSize?: string;
  baseProduct?: string;
  brandSelectionStatus?: 'none' | 'selected' | 'preferred';
  storePrices: StorePrice[];
  sizeVariants?: Array<{
    brand: string;
    size: string;
    storePrices: StorePrice[];
  }>;
  priceData?: {
    allStorePrices: StorePrice[];
    selectedPrice?: number;
    selectedStore?: string;
    selectedStoreName?: string;
    potentialSavings?: number;
  };
  addedAt: Date;
  updatedAt?: string;
  notes?: string;
  
  // Legacy properties for backward compatibility
  productName?: string;
  price?: number;
  originalPrice?: number;
  store?: string;
  brand?: string;
  imageUrl?: string;
  isOnSale?: boolean;
  promotionText?: string;
  isCompleted?: boolean;
  priority?: 'low' | 'medium' | 'high';
  estimatedCost?: number;
  completed?: boolean;
}

// Enhanced version with additional price integration features
export interface EnhancedShoppingListItem extends ShoppingListItem {
  priceData: {
    allStorePrices: StorePrice[];
    selectedPrice: number;
    selectedStore: string;
    selectedStoreName: string;
    potentialSavings: number;
  };
  brandSelectionStatus: 'none' | 'selected' | 'preferred';
  baseProduct: string;
}

export interface ShoppingList {
  id: string;
  name: string;
  description?: string;
  items: ShoppingListItem[];
  createdAt: string;
  updatedAt: string;
  isDefault: boolean;
  color: string;
  icon: string;
  totalItems: number;
  totalPrice: number;
  completedItems: number;
  totalEstimatedCost?: number;
  lastUpdated?: Date;
  storeOptimization?: any[];
}

export interface ShoppingListStats {
  totalItems: number;
  completedItems: number;
  totalCost: number;
  potentialSavings: number;
  averageItemPrice: number;
}

export interface ProductToAdd {
  id: string;
  name: string;
  price: number;
  originalPrice?: number;
  store: string;
  brand?: string;
  category?: string;
  imageUrl?: string;
  unit?: string;
  isOnSale?: boolean;
  promotionText?: string;
}

export interface CleanShoppingListItem {
  id: string;
  name: string;
  quantity: number;
  unit?: string;
  category?: string;
  checked: boolean;
  priceData?: {
    allStorePrices: StorePrice[];
    selectedPrice?: number;
    selectedStore?: string;
    potentialSavings?: number;
  };
  addedAt: Date;
  updatedAt?: Date;
  notes?: string;
}

export interface StoreOptimization {
  strategy: 'single' | 'mixed' | 'budget';
  description: string;
  totalSavings: number;
  convenienceScore: number;
  recommendedStores: Array<{
    store: string;
    storeName: string;
    itemCount: number;
    totalCost: number;
    items: string[];
  }>;
}