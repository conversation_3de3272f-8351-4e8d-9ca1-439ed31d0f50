import { Platform } from 'react-native';
import * as Haptics from 'expo-haptics';

// Platform detection utilities
export const isIOS = Platform.OS === 'ios';
export const isAndroid = Platform.OS === 'android';

// Haptic feedback utilities (iOS only)
export const hapticFeedback = {
  light: async () => {
    if (isIOS) {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  },
  medium: async () => {
    if (isIOS) {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
  },
  heavy: async () => {
    if (isIOS) {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    }
  },
  success: async () => {
    if (isIOS) {
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }
  },
  warning: async () => {
    if (isIOS) {
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
    }
  },
  error: async () => {
    if (isIOS) {
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    }
  },
  selection: async () => {
    if (isIOS) {
      await Haptics.selectionAsync();
    }
  },
};

// Platform-specific styling utilities
export const platformStyles = {
  // Shadow styles
  shadow: {
    ios: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 8,
    },
    android: {
      elevation: 4,
    },
  },
  
  // Button styles
  button: {
    ios: {
      borderRadius: 12,
      paddingVertical: 12,
      paddingHorizontal: 24,
    },
    android: {
      borderRadius: 8,
      paddingVertical: 14,
      paddingHorizontal: 24,
      elevation: 2,
    },
  },
  
  // Card styles
  card: {
    ios: {
      borderRadius: 16,
      backgroundColor: '#fff',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.05,
      shadowRadius: 8,
    },
    android: {
      borderRadius: 12,
      backgroundColor: '#fff',
      elevation: 3,
    },
  },
};

// Get platform-specific style
export const getPlatformStyle = (styles: { ios: any; android: any }) => {
  return isIOS ? styles.ios : styles.android;
};

// SF Symbols for iOS (using system emoji as fallback)
export const symbols = {
  home: isIOS ? '􀎞' : '🏠',
  book: isIOS ? '􀉚' : '📖',
  cart: isIOS ? '􀍩' : '🛒',
  settings: isIOS ? '􀍟' : '⚙️',
  plus: isIOS ? '􀅼' : '+',
  minus: isIOS ? '􀅽' : '-',
  star: isIOS ? '􀋂' : '⭐',
  heart: isIOS ? '􀊴' : '❤️',
  search: isIOS ? '􀊫' : '🔍',
  filter: isIOS ? '􀈠' : '🔽',
  share: isIOS ? '􀈂' : '📤',
  delete: isIOS ? '􀈑' : '🗑️',
  edit: isIOS ? '􀈂' : '✏️',
  check: isIOS ? '􀆅' : '✓',
  close: isIOS ? '􀆄' : '✕',
  arrow: {
    right: isIOS ? '􀆊' : '→',
    left: isIOS ? '􀆉' : '←',
    up: isIOS ? '􀆈' : '↑',
    down: isIOS ? '􀆇' : '↓',
  },
  time: isIOS ? '􀐫' : '⏱️',
  serving: isIOS ? '􀦎' : '🍽️',
  difficulty: isIOS ? '􀇿' : '📊',
};

// Android ripple effect utility
export const androidRipple = (color: string = 'rgba(0, 0, 0, 0.1)') => ({
  android_ripple: {
    color,
    borderless: false,
  },
});

// Status bar utilities
export const getStatusBarStyle = (isDark: boolean = false) => {
  if (isIOS) {
    return isDark ? 'light' : 'dark';
  }
  return 'auto';
};

// Platform-specific constants
export const platformConstants = {
  headerHeight: isIOS ? 44 : 56,
  tabBarHeight: isIOS ? 49 : 56,
  statusBarHeight: isIOS ? 20 : 24,
  borderRadius: {
    small: isIOS ? 8 : 6,
    medium: isIOS ? 12 : 8,
    large: isIOS ? 16 : 12,
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
  },
}; 