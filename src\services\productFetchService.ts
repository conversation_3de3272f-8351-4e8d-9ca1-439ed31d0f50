import { supabase } from '../supabase/client';

export interface Product {
  id: string;
  name: string;
  price: number;
  store: 'woolworths' | 'newworld' | 'paknsave';
  storeName: string;
  category: string;
  brand: string;
  unit: string;
  availability: 'in_stock' | 'low_stock' | 'out_of_stock';
  image?: string;
  description?: string;
  promotions?: string[];
  originalData?: any; // Keep original data for debugging
}

export interface ProductsByStore {
  woolworths: Product[];
  newworld: Product[];
  paknsave: Product[];
}

export interface ProductSearchResult {
  products: Product[];
  totalCount: number;
  byStore: ProductsByStore;
  searchQuery?: string;
  categories: string[];
  brands: string[];
}

export interface ProductFetchOptions {
  searchQuery?: string;
  category?: string;
  brand?: string;
  store?: 'woolworths' | 'newworld' | 'paknsave';
  maxResults?: number;
  sortBy?: 'price' | 'name' | 'relevance';
  sortOrder?: 'asc' | 'desc';
  includeOutOfStock?: boolean;
}

const STORE_CONFIG = {
  woolworths: {
    storeName: 'woolworths',
    displayName: 'Woolworths',
    emoji: '🛒',
    color: '#00A651'
  },
  newworld: {
    storeName: 'newworld',
    displayName: 'New World',
    emoji: '🍎',
    color: '#E31E24'
  },
  paknsave: {
    storeName: 'paknsave',
    displayName: "Pak'nSave",
    emoji: '💰',
    color: '#FFD100'
  }
} as const;

class ProductFetchError extends Error {
  constructor(message: string, public statusCode?: number) {
    super(message);
    this.name = 'ProductFetchError';
  }
}

const getSupabaseClient = () => {
  return supabase;
};

// Helper to extract price from different store formats
const extractPrice = (hit: any, store: string): number => {
  let price: number;
  
  if (store === 'woolworths') {
    price = hit.currentPrice || hit.unitPrice || hit.price;
  } else {
    price = hit.price;
  }
  
  const parsedPrice = typeof price === 'number' ? price : parseFloat(price || '0');
  return isNaN(parsedPrice) ? 0 : parsedPrice;
};

// Helper to normalize product data from Supabase
const normalizeProduct = (product: any, store: 'woolworths' | 'newworld' | 'paknsave'): Product => {
  const storeConfig = STORE_CONFIG[store];
  const price = extractPrice(product, store);
  
  return {
    id: product.objectid || product.id || product.objectID,
    name: product.name || 'Unknown Product',
    price: price,
    store: store,
    storeName: storeConfig.displayName,
    category: product.category || 'Other',
    brand: product.brand || 'Generic',
    unit: product.unit || product.unitName || product.size || 'each',
    availability: product.availability || 'in_stock',
    image: product.image_url || product.imageUrl || product.image,
    description: product.description || '',
    promotions: product.promotions || [],
    originalData: product
  };
};

export const productFetchService = {
  /**
   * Fetch products from all stores with filtering and search using Supabase
   */
  async fetchProducts(options: ProductFetchOptions = {}): Promise<ProductSearchResult> {
    try {
      const client = getSupabaseClient();
      const {
        searchQuery = '',
        category,
        brand,
        store,
        maxResults = 10000, // Increased to show full product catalog (10,309 products available)
        sortBy = 'relevance',
        sortOrder = 'asc',
        includeOutOfStock = false
      } = options;

      console.log('🔍 Fetching products from Supabase with options:', {
        searchQuery,
        category,
        brand,
        store,
        maxResults
      });

      // Build Supabase query
      let supabaseQuery = client
        .from('products')
        .select('*');

      // Add search conditions
      if (searchQuery.trim()) {
        supabaseQuery = supabaseQuery.ilike('name', `%${searchQuery}%`);
      }

      // Add filters
      if (store) {
        supabaseQuery = supabaseQuery.eq('store', store);
      }

      if (category) {
        supabaseQuery = supabaseQuery.eq('category', category);
      }

      if (brand) {
        supabaseQuery = supabaseQuery.eq('brand', brand);
      }

      if (!includeOutOfStock) {
        supabaseQuery = supabaseQuery.eq('availability', 'available'); // Fixed: database uses 'available' not 'in_stock'
      }

      // Add sorting
      if (sortBy === 'price') {
        supabaseQuery = supabaseQuery.order('price', { ascending: sortOrder === 'asc' });
      } else if (sortBy === 'name') {
        supabaseQuery = supabaseQuery.order('name', { ascending: sortOrder === 'asc' });
      } else {
        supabaseQuery = supabaseQuery.order('name', { ascending: true });
      }

      // Limit results
      supabaseQuery = supabaseQuery.limit(maxResults);

      const { data: products, error } = await supabaseQuery;

      if (error) {
        console.error('Supabase query error:', error);
        throw new ProductFetchError(`Database query failed: ${error.message}`);
      }

      if (!products) {
        console.log('❌ No products found in Supabase');
        return {
          products: [],
          totalCount: 0,
          byStore: { woolworths: [], newworld: [], paknsave: [] },
          searchQuery,
          categories: [],
          brands: []
        };
      }

      console.log(`📊 Found ${products.length} products from Supabase`);

      // Convert to Product format and organize by store
      const allProducts: Product[] = [];
      const productsByStore: ProductsByStore = {
        woolworths: [],
        newworld: [],
        paknsave: []
      };

      products.forEach((product: any) => {
        const price = extractPrice(product, product.store);
        if (price > 0 && price < 1000) {
          const normalizedProduct = normalizeProduct(product, product.store);
          allProducts.push(normalizedProduct);
          
          if (normalizedProduct.store in productsByStore) {
            productsByStore[normalizedProduct.store as keyof ProductsByStore].push(normalizedProduct);
          }
        }
      });

      // Extract unique categories and brands
      const categories = [...new Set(allProducts.map(p => p.category))].sort();
      const brands = [...new Set(allProducts.map(p => p.brand))].sort();

      console.log('✅ Product fetch complete from Supabase:', {
        totalProducts: allProducts.length,
        woolworths: productsByStore.woolworths.length,
        newworld: productsByStore.newworld.length,
        paknsave: productsByStore.paknsave.length,
        categories: categories.length,
        brands: brands.length
      });

      return {
        products: allProducts,
        totalCount: allProducts.length,
        byStore: productsByStore,
        searchQuery,
        categories,
        brands
      };

    } catch (error) {
      console.error('❌ Product fetch failed:', error);
      throw new ProductFetchError(`Failed to fetch products: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  /**
   * Fetch products for a specific category
   */
  async fetchProductsByCategory(category: string, options: Omit<ProductFetchOptions, 'category'> = {}): Promise<ProductSearchResult> {
    return this.fetchProducts({ ...options, category });
  },

  /**
   * Fetch products for a specific brand
   */
  async fetchProductsByBrand(brand: string, options: Omit<ProductFetchOptions, 'brand'> = {}): Promise<ProductSearchResult> {
    return this.fetchProducts({ ...options, brand });
  },

  /**
   * Search products across all stores
   */
  async searchProducts(query: string, options: Omit<ProductFetchOptions, 'searchQuery'> = {}): Promise<ProductSearchResult> {
    return this.fetchProducts({ ...options, searchQuery: query });
  },

  /**
   * Get all available categories
   */
  async getCategories(): Promise<string[]> {
    const result = await this.fetchProducts({ maxResults: 1000 });
    return result.categories;
  },

  /**
   * Get all available brands
   */
  async getBrands(): Promise<string[]> {
    const result = await this.fetchProducts({ maxResults: 1000 });
    return result.brands;
  },

  /**
   * Get store information
   */
  getStoreInfo() {
    return STORE_CONFIG;
  },

  /**
   * Check if service is configured
   */
  isConfigured(): boolean {
    return !!supabase;
  },

  /**
   * Get price comparison for a specific product
   */
  async compareProductPrices(productName: string): Promise<{
    productName: string;
    products: Product[];
    cheapest?: Product;
    priceRange?: {
      min: number;
      max: number;
      savings: number;
    };
  }> {
    const result = await this.searchProducts(productName, { maxResults: 50 });
    
    if (result.products.length === 0) {
      return {
        productName,
        products: []
      };
    }

    const prices = result.products.map(p => p.price).filter(p => p > 0);
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    const cheapest = result.products.find(p => p.price === minPrice);

    return {
      productName,
      products: result.products,
      cheapest,
      priceRange: {
        min: minPrice,
        max: maxPrice,
        savings: maxPrice - minPrice
      }
    };
  }
};