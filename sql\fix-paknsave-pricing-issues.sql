-- FIX PAK'NSAVE PRICING ISSUES
-- Critical fixes for per-kg vs fixed-size matching problems

-- Drop existing problematic table
DROP TABLE IF EXISTS product_matches;

-- Create enhanced product matches with strict size matching
CREATE TABLE product_matches AS
WITH size_normalized_products AS (
  SELECT *,
    CASE 
      -- Normalize per-unit pricing
      WHEN LOWER(COALESCE(size, unit, '')) LIKE '%per kg%' THEN 'per-kg'
      WHEN LOWER(COALESCE(size, unit, '')) LIKE '%per 100g%' THEN 'per-100g'
      WHEN LOWER(COALESCE(size, unit, '')) LIKE '%per l%' OR LOWER(COALESCE(size, unit, '')) LIKE '%per litre%' THEN 'per-litre'
      WHEN LOWER(COALESCE(size, unit, '')) LIKE '%per 100ml%' THEN 'per-100ml'
      
      -- Normalize fixed sizes
      WHEN LOWER(COALESCE(size, unit, '')) ~ '\d+\s*kg' THEN 
        CONCAT(
          (regexp_match(LOWER(COALESCE(size, unit, '')), '(\d+(?:\.\d+)?)\s*kg'))[1], 
          'kg'
        )
      WHEN LOWER(COALESCE(size, unit, '')) ~ '\d+\s*g' THEN 
        CONCAT(
          (regexp_match(LOWER(COALESCE(size, unit, '')), '(\d+(?:\.\d+)?)\s*g'))[1], 
          'g'
        )
      WHEN LOWER(COALESCE(size, unit, '')) ~ '\d+\s*l' THEN 
        CONCAT(
          (regexp_match(LOWER(COALESCE(size, unit, '')), '(\d+(?:\.\d+)?)\s*l'))[1], 
          'l'
        )
      WHEN LOWER(COALESCE(size, unit, '')) ~ '\d+\s*ml' THEN 
        CONCAT(
          (regexp_match(LOWER(COALESCE(size, unit, '')), '(\d+(?:\.\d+)?)\s*ml'))[1], 
          'ml'
        )
      
      -- Multi-pack handling
      WHEN LOWER(COALESCE(size, unit, '')) ~ '\d+\s*x\s*\d+' THEN 
        CONCAT('multipack-', COALESCE(size, unit, ''))
      WHEN LOWER(COALESCE(size, unit, '')) ~ '\d+\s*pack' THEN 
        CONCAT(
          (regexp_match(LOWER(COALESCE(size, unit, '')), '(\d+)\s*pack'))[1], 
          'pack'
        )
      
      ELSE COALESCE(size, unit, 'no-size')
    END as normalized_size
  FROM products
  WHERE price > 0
),
matched_products AS (
  SELECT 
    p1.id as pakn_id,
    p2.id as neww_id,
    p3.id as wool_id,
    p1.name as pakn_name,
    p2.name as neww_name,
    p3.name as wool_name,
    p1.price as pakn_price,
    p2.price as neww_price,
    p3.price as wool_price,
    p1.normalized_size as pakn_size,
    p2.normalized_size as neww_size,
    p3.normalized_size as wool_size,
    similarity(p1.norm_name, p2.norm_name) as neww_similarity,
    similarity(p1.norm_name, p3.norm_name) as wool_similarity
  FROM size_normalized_products p1
  LEFT JOIN size_normalized_products p2 ON p2.store = 'newworld' 
    AND p1.norm_name % p2.norm_name
    AND similarity(p1.norm_name, p2.norm_name) >= 0.7
    -- CRITICAL: Only match products with identical normalized sizes
    AND p1.normalized_size = p2.normalized_size
  LEFT JOIN size_normalized_products p3 ON p3.store = 'woolworths' 
    AND p1.norm_name % p3.norm_name
    AND similarity(p1.norm_name, p3.norm_name) >= 0.7
    -- CRITICAL: Only match products with identical normalized sizes
    AND p1.normalized_size = p3.normalized_size
  WHERE p1.store = 'paknsave'
    AND (p2.id IS NOT NULL OR p3.id IS NOT NULL)
)
SELECT *,
  LEAST(
    pakn_price, 
    COALESCE(neww_price, pakn_price), 
    COALESCE(wool_price, pakn_price)
  ) as best_price,
  GREATEST(
    pakn_price, 
    COALESCE(neww_price, pakn_price), 
    COALESCE(wool_price, pakn_price)
  ) - LEAST(
    pakn_price, 
    COALESCE(neww_price, pakn_price), 
    COALESCE(wool_price, pakn_price)
  ) as max_savings,
  now() as created_at
FROM matched_products;

-- Create indexes for performance
CREATE INDEX idx_product_matches_pakn_fixed ON product_matches(pakn_id);
CREATE INDEX idx_product_matches_neww_fixed ON product_matches(neww_id);
CREATE INDEX idx_product_matches_wool_fixed ON product_matches(wool_id);
CREATE INDEX idx_product_matches_savings_fixed ON product_matches(max_savings DESC);
CREATE INDEX idx_product_matches_sizes_fixed ON product_matches(pakn_size, neww_size, wool_size);

-- Create a separate table for per-kg products (for reference only, not matching)
CREATE TABLE per_kg_products AS
SELECT 
  id,
  name,
  price,
  store,
  COALESCE(size, unit, '') as size_info,
  category,
  brand,
  price as price_per_kg
FROM products
WHERE LOWER(COALESCE(size, unit, '')) LIKE '%per kg%'
  AND price > 0
ORDER BY store, price DESC;

-- Create index for per-kg products
CREATE INDEX idx_per_kg_products_store ON per_kg_products(store);
CREATE INDEX idx_per_kg_products_price ON per_kg_products(price_per_kg DESC);

-- Grant permissions
GRANT SELECT ON product_matches TO authenticated;
GRANT SELECT ON per_kg_products TO authenticated;
