# EPIC: Remove Demo/Mock Data and Implement Full Supabase Integration

**Epic ID**: EP-001  
**Priority**: High  
**Status**: Ready for Development  
**Epic Owner**: Development Team  
**Business Stakeholders**: Product Owner, QA Team  

## Epic Summary
Remove all demo/mock data functionality from the AI Recipe Planner app and fully integrate with live Supabase product and pricing data. This enhancement will transition the app from development/demo mode to a production-ready application operating exclusively on real product and pricing data from Supabase.

## Business Value
- **Production Readiness**: Eliminates demo data dependencies, making the app ready for production release
- **Data Accuracy**: Ensures users see real product prices and availability from New Zealand supermarket chains
- **Performance Optimization**: Removes unnecessary fallback systems and optimizes for live data queries
- **User Trust**: Real pricing data builds user confidence in price comparison features
- **Maintainability**: Reduces codebase complexity by removing dual data paths

## Current State Analysis

### Demo/Mock Data Currently Present:
1. **Enhanced Shopping Data Service** (`src/services/enhancedShoppingDataService.ts`): 800+ lines of hardcoded product/brand data across 50+ categories
2. **Recipe Mock Data** (`src/hooks/useRecipeData.ts`): 3 fallback recipes with mock data system
3. **Product Screen Mock Data** (`src/screens/main/AllProductsScreenFixed.tsx`): 15 hardcoded products
4. **Authentication Mocks**: Development authentication flows for Google and Auth0
5. **Test Components**: `ProductTestComponent` and `SimpleTestContainer` for development testing

### Supabase Integration Status:
- ✅ **Database Schema**: Comprehensive schema with `products`, `recipes`, `stores`, `prices`, `shopping_lists` tables
- ✅ **Service Functions**: Database functions for `search_products`, `compare_product_prices`, `get_cheapest_price`
- ✅ **TypeScript Types**: Complete type definitions for all database tables
- ✅ **Client Setup**: Configured Supabase client with proper authentication
- ⚠️ **Partial Integration**: Some services still fall back to demo data when Supabase queries fail

## Acceptance Criteria

### Epic-Level Success Criteria:
1. **Zero Demo Data**: No mock, demo, or fallback data remains in production code paths
2. **Full Supabase Integration**: All product searches, price comparisons, and shopping lists use Supabase exclusively
3. **Production Performance**: Live data queries perform efficiently with proper caching and loading states
4. **Comprehensive Error Handling**: Graceful handling of Supabase unavailability with appropriate user messaging
5. **Data Completeness Validation**: Sufficient product data across all NZ supermarket chains to support core functionality

## User Stories

### Story 1: Remove Enhanced Shopping Data Service Mock Data
**Story ID**: ST-001  
**Priority**: High  
**Effort**: 5 Story Points  

**As a** developer  
**I want** to remove the 800+ lines of hardcoded product data from `enhancedShoppingDataService.ts`  
**So that** the app relies exclusively on real Supabase product data  

**Acceptance Criteria:**
- [ ] Remove `ENHANCED_CATEGORY_DATA` hardcoded product database (lines 69-399)
- [ ] Remove `BRAND_PATTERNS` comprehensive brand mappings (lines 401-560)  
- [ ] Replace `generateFallbackEnhancedData()` with Supabase queries
- [ ] Update all service consumers to handle Supabase data structure
- [ ] Add proper error handling for missing Supabase data
- [ ] Verify all 50+ product categories are available in Supabase

**Technical Tasks:**
- Audit Supabase `products` table for category completeness
- Refactor service methods to use Supabase client queries
- Update type definitions to match Supabase schema
- Add loading states for async Supabase calls
- Implement error boundaries for data fetch failures

### Story 2: Replace Recipe Mock Data with Supabase Integration
**Story ID**: ST-002  
**Priority**: Medium  
**Effort**: 3 Story Points  

**As a** user  
**I want** to see real recipes from the database instead of hardcoded samples  
**So that** I have access to a comprehensive recipe collection  

**Acceptance Criteria:**
- [ ] Remove `getMockRecipes()` function from `useRecipeData.ts`
- [ ] Remove hardcoded recipes from `RecipesScreenSimple.tsx`
- [ ] Implement full Supabase recipe queries using `recipes` table
- [ ] Add recipe search functionality using database queries
- [ ] Handle empty recipe database gracefully with user guidance
- [ ] Ensure recipe data includes all required fields (ingredients, instructions, etc.)

**Technical Tasks:**
- Remove mock recipe arrays and fallback systems
- Implement `supabase.from('recipes').select()` queries
- Add recipe filtering and search capabilities
- Update UI to handle async recipe loading
- Add empty state handling for recipe lists

### Story 3: Remove Product Screen Mock Data
**Story ID**: ST-003  
**Priority**: Medium  
**Effort**: 2 Story Points  

**As a** user browsing products  
**I want** to see real product information and prices  
**So that** I can make informed shopping decisions  

**Acceptance Criteria:**
- [ ] Remove `mockProducts` array from `AllProductsScreenFixed.tsx`
- [ ] Integrate with Supabase product queries for all product displays
- [ ] Ensure product cards show real pricing data
- [ ] Handle product image loading from Supabase URLs
- [ ] Add proper loading states for product fetching
- [ ] Implement pagination for large product sets

**Technical Tasks:**
- Replace mock data with `productFetchService` Supabase queries
- Update product card components for real data structure
- Add image loading error handling
- Implement infinite scrolling or pagination
- Add product availability status handling

### Story 4: Implement Production Authentication
**Story ID**: ST-004  
**Priority**: High  
**Effort**: 8 Story Points  

**As a** user  
**I want** to authenticate with real OAuth providers  
**So that** I can securely access my personal data  

**Acceptance Criteria:**
- [ ] Remove mock authentication from `AuthContext.tsx`
- [ ] Implement real Google OAuth integration
- [ ] Implement real Auth0 authentication flow  
- [ ] Add proper authentication error handling
- [ ] Ensure user data persists correctly in Supabase
- [ ] Add authentication loading states and error messages
- [ ] Test authentication flows on all platforms (iOS, Android, Web)

**Technical Tasks:**
- Configure real OAuth providers in environment
- Remove mock authentication functions
- Implement proper token management
- Add authentication error boundaries
- Update user profile management with real APIs
- Test OAuth flows across platforms

### Story 5: Remove Development Test Components
**Story ID**: ST-005  
**Priority**: Low  
**Effort**: 1 Story Point  

**As a** developer  
**I want** to remove development-only components from production builds  
**So that** the app bundle is optimized for production  

**Acceptance Criteria:**
- [ ] Remove or conditionally exclude `ProductTestComponent.tsx`
- [ ] Remove or conditionally exclude `SimpleTestContainer.tsx`
- [ ] Clean up development navigation paths
- [ ] Ensure test components don't appear in production builds
- [ ] Remove unused development dependencies

**Technical Tasks:**
- Add development environment checks
- Remove test components from production navigation
- Clean up development imports and references
- Update build configuration to exclude dev components

### Story 6: Optimize Supabase Query Performance  
**Story ID**: ST-006  
**Priority**: Medium  
**Effort**: 5 Story Points  

**As a** user  
**I want** the app to load quickly with real data  
**So that** I have a smooth shopping experience  

**Acceptance Criteria:**
- [ ] Implement query result caching for frequently accessed data
- [ ] Add proper loading indicators for all data fetching
- [ ] Optimize database queries for minimal data transfer
- [ ] Implement retry logic for failed network requests
- [ ] Add offline data caching where appropriate
- [ ] Monitor and optimize query performance

**Technical Tasks:**
- Implement React Query or similar caching solution
- Add loading skeleton components
- Optimize Supabase queries with proper indexing
- Add retry logic with exponential backoff
- Implement AsyncStorage caching for offline access
- Add performance monitoring and analytics

### Story 7: Validate Data Completeness and Quality
**Story ID**: ST-007  
**Priority**: High  
**Effort**: 3 Story Points  

**As a** product owner  
**I want** to ensure the Supabase database contains sufficient product data  
**So that** users have a complete shopping experience  

**Acceptance Criteria:**
- [ ] Verify product coverage across Woolworths, New World, and Pak'nSave
- [ ] Ensure all major product categories are represented
- [ ] Validate price data freshness and accuracy
- [ ] Confirm product images are accessible and loading
- [ ] Test search functionality across different product types
- [ ] Ensure brand information is complete and accurate

**Technical Tasks:**
- Create data validation scripts
- Audit database for completeness across stores
- Test product search across various categories
- Validate image URL accessibility
- Check price data recency and accuracy
- Document any data gaps for resolution

### Story 8: Implement Comprehensive Error Handling
**Story ID**: ST-008  
**Priority**: Medium  
**Effort**: 4 Story Points  

**As a** user  
**I want** to receive helpful messages when data is unavailable  
**So that** I understand what's happening and what I can do  

**Acceptance Criteria:**
- [ ] Replace all demo data fallbacks with user-friendly error messages
- [ ] Add retry buttons for failed data requests
- [ ] Implement different error states (network, server, no data)
- [ ] Provide offline mode messaging when appropriate
- [ ] Add error reporting for debugging purposes
- [ ] Ensure errors don't crash the app

**Technical Tasks:**
- Create comprehensive error handling components
- Implement error boundaries around data-dependent components
- Add retry mechanisms with user controls
- Create error message constants and translations
- Add error logging for debugging
- Test error scenarios thoroughly

### Story 9: Update Configuration and Environment Management
**Story ID**: ST-009  
**Priority**: Medium  
**Effort**: 2 Story Points  

**As a** developer  
**I want** to have clear production vs development configuration  
**So that** demo data is only used in development  

**Acceptance Criteria:**
- [ ] Update `config.ts` to remove demo data configuration options
- [ ] Ensure all production environment variables are required
- [ ] Add validation for required Supabase configuration
- [ ] Remove development-only configuration branches
- [ ] Add production readiness checks

**Technical Tasks:**
- Clean up configuration constants
- Remove demo mode toggles and flags
- Validate environment variable requirements
- Add production configuration validation
- Update documentation for required environment setup

## Technical Considerations

### Database Performance:
- **Indexing**: Ensure proper indexes on `products.name`, `products.category`, `products.store` for fast queries
- **Query Optimization**: Use database functions for complex price comparisons
- **Caching Strategy**: Implement client-side caching for frequently accessed data

### Data Migration:
- **Backup Strategy**: Ensure current Supabase data is backed up before major changes
- **Gradual Rollout**: Consider feature flags for gradual demo data removal
- **Rollback Plan**: Maintain ability to quickly restore demo data if issues arise

### Monitoring and Alerting:
- **Error Tracking**: Implement comprehensive error logging for Supabase query failures
- **Performance Monitoring**: Track query performance and user experience metrics
- **Data Quality Monitoring**: Alert on missing or stale data in the database

## Definition of Done
- [ ] All demo/mock data removed from production code paths
- [ ] All features working with live Supabase data
- [ ] Performance meets acceptable standards (< 3s load times)
- [ ] Error handling provides clear user guidance
- [ ] Code review completed and approved
- [ ] QA testing passed on all platforms
- [ ] Documentation updated
- [ ] Production deployment successful

## Dependencies
- **External**: Supabase database populated with production-quality data
- **Internal**: Environment configuration completed
- **Team**: QA team availability for comprehensive testing

## Risks and Mitigation
- **Risk**: Insufficient product data in Supabase  
  **Mitigation**: Validate data completeness before starting development
- **Risk**: Performance degradation with live data  
  **Mitigation**: Implement caching and optimization from the start
- **Risk**: Authentication issues in production  
  **Mitigation**: Thorough testing of OAuth flows across all platforms

## Success Metrics
- **Technical**: Zero fallbacks to demo data in production logs
- **Performance**: < 3 second load times for all major screens
- **Quality**: < 5% error rate for data fetching operations
- **User Experience**: Smooth transitions between loading and loaded states

---

**Epic Created**: [Current Date]  
**Last Updated**: [Current Date]  
**Next Review**: [Date + 1 week]