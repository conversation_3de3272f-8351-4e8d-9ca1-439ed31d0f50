# Profile & Preferences Feature

## Overview
The ProfileScreen allows users to view and edit their personal cooking preferences, making the AI Recipe Planner more personalized and functional.

## Features

### 🍽️ **Default Servings**
- Select how many people you usually cook for (1-6+)
- Used by AI recipe generator to adjust ingredient quantities
- Displayed on home screen for quick reference

### 💰 **Weekly Grocery Budget** 
- Set your typical weekly grocery budget
- Options: $0-25, $25-50, $50-75, $75-100, $100-150, $150-200, $200+
- Helps AI suggest cost-appropriate recipes

### 🥗 **Dietary Preferences**
- Multiple selection from: Vegetarian, Vegan, Gluten-Free, Dairy-Free, Keto, Paleo, Low-Carb, Low-Fat, High-Protein, Mediterranean
- AI recipe generator respects these restrictions
- Shown on home screen when set

### 🔪 **Kitchen Equipment**
- Select available cooking equipment: Oven, Stove, Microwave, Air Fryer, Slow Cooker, Pressure Cooker, Blender, Food Processor, Grill, Toaster
- AI only suggests recipes you can actually make
- Ensures recipe compatibility with your kitchen

### ⚠️ **Allergies & Food Restrictions**
- Safety-focused allergy tracking: Nuts, Peanuts, Shellfish, Fish, Eggs, Soy, Wheat, Dairy, Sesame, Tree Nuts
- AI avoids these ingredients in all recipe suggestions
- Critical for user safety

### 🌍 **Favorite Cuisines**
- Select preferred cuisines: Italian, Mexican, Asian, Mediterranean, Indian, American, French, Thai, Japanese, Middle Eastern, Greek, Spanish
- AI biases recipe suggestions toward your favorites
- Improves recipe relevance

## Technical Implementation

### Storage
- Uses AsyncStorage for local data persistence
- Preferences automatically load on app start
- Real-time updates across the app

### Integration Points
- **HomeScreen**: Displays key preferences (servings, budget, dietary restrictions)
- **RecipeGenerator**: Uses all preferences to personalize AI suggestions  
- **Authentication**: Preferences tied to user account via AuthContext

### Navigation
- Accessible via main tab navigation (Profile tab)
- Replaces the basic Settings screen
- Includes logout functionality

## User Experience

### Onboarding Integration
- Preferences can be set during initial app setup
- Progressive disclosure - not overwhelming for new users

### Visual Design  
- Clean, sectioned interface
- Toggle-style buttons for easy selection
- Clear visual feedback for selected options
- Consistent with app's design language

### Performance
- Optimized re-renders with React hooks
- Smooth animations and interactions
- Efficient data loading and saving

## Code Structure

```
src/screens/main/ProfileScreen.tsx  # Main profile interface
src/utils/storage.ts                # Preference storage functions  
src/context/AuthContext.tsx        # User authentication integration
App.tsx                            # Navigation setup
```

## Future Enhancements

### Possible Additions
- **Social Features**: Share favorite recipes, follow other users
- **Advanced Preferences**: Spice tolerance, meal timing preferences
- **Integration**: Connect with fitness apps, grocery delivery services
- **Personalization**: Learning from user recipe ratings and cooking history
- **Export/Import**: Backup and restore preferences

### Analytics Opportunities
- Track most popular dietary preferences
- Analyze budget ranges to optimize recipe suggestions
- Monitor kitchen equipment usage for feature prioritization

## Benefits

### For Users
- ✅ Highly personalized recipe suggestions
- ✅ Safe cooking (allergy awareness)
- ✅ Realistic recipes (equipment-based)
- ✅ Budget-conscious meal planning
- ✅ Dietary goal support

### For App Quality
- ✅ Improved user engagement
- ✅ Better recipe relevance
- ✅ Enhanced safety features
- ✅ Professional app experience
- ✅ Data-driven personalization

This feature transforms the AI Recipe Planner from a generic recipe generator into a truly personalized cooking assistant! 🚀 