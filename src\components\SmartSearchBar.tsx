/**
 * Smart Search Bar with Real-Time Suggestions
 * 
 * Enhanced search component that provides intelligent autocomplete
 * suggestions while the user types.
 */

import React, { useRef, useState, useEffect, useCallback, useMemo } from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  Animated,
  StyleSheet,
  Platform,
  FlatList,
  Text,
  Modal,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { getGroceryTheme, ThemeMode } from '../styles/modernGroceryTheme';
import { smartSearchService, SearchSuggestion } from '../services/smartSearchService';

const { height: screenHeight } = Dimensions.get('window');

interface SmartSearchBarProps {
  value: string;
  onChangeText: (text: string) => void;
  onSuggestionSelect?: (suggestion: string) => void;
  placeholder?: string;
  onFocus?: () => void;
  onBlur?: () => void;
  themeMode?: ThemeMode;
  autoFocus?: boolean;
  editable?: boolean;
  maxSuggestions?: number;
  showCategories?: boolean;
}

export const SmartSearchBar: React.FC<SmartSearchBarProps> = ({
  value,
  onChangeText,
  onSuggestionSelect,
  placeholder = 'Search products...',
  onFocus,
  onBlur,
  themeMode = 'light',
  autoFocus = false,
  editable = true,
  maxSuggestions = 8,
  showCategories = true,
}) => {
  const theme = getGroceryTheme(themeMode);
  const [isFocused, setIsFocused] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Create defensive copies to prevent frozen object issues
  const safeValue = useMemo(() => value || '', [value]);
  const safePlaceholder = useMemo(() => placeholder || 'Search products...', [placeholder]);
  
  // Animation values - use standard React Native pattern
  const borderAnimatedValue = useRef(new Animated.Value(0)).current;
  const shadowAnimatedValue = useRef(new Animated.Value(0)).current;
  const scaleAnimatedValue = useRef(new Animated.Value(1)).current;
  const suggestionAnimatedValue = useRef(new Animated.Value(0)).current;

  // Input ref for methods
  const inputRef = useRef<TextInput>(null);
  
  // Debounced search timeout
  const searchTimeoutRef = useRef<NodeJS.Timeout>();

  // Handle text changes with debouncing
  const handleTextChange = useCallback((text: string) => {
    onChangeText(text);
    
    // Clear previous timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    
    // If text is empty, hide suggestions
    if (!text.trim()) {
      setShowSuggestions(false);
      setSuggestions([]);
      return;
    }
    
    // Debounce suggestions fetching
    searchTimeoutRef.current = setTimeout(async () => {
      if (text.trim().length >= 2) {
        setIsLoading(true);
        try {
          const newSuggestions = await smartSearchService.getSuggestions(text.trim(), maxSuggestions);
          setSuggestions(newSuggestions);
          setShowSuggestions(newSuggestions.length > 0 && isFocused);
        } catch (error) {
          console.error('Failed to fetch suggestions:', error);
          setSuggestions([]);
          setShowSuggestions(false);
        } finally {
          setIsLoading(false);
        }
      }
    }, 300); // 300ms debounce
  }, [onChangeText, maxSuggestions, isFocused]);

  useEffect(() => {
    // Animate JS-driven properties (colors, shadows, etc.)
    Animated.parallel([
      Animated.timing(borderAnimatedValue, {
        toValue: isFocused ? 1 : 0,
        duration: theme.animations.timing.normal,
        useNativeDriver: false,
      }),
      Animated.timing(shadowAnimatedValue, {
        toValue: isFocused ? 1 : 0,
        duration: theme.animations.timing.normal,
        useNativeDriver: false,
      }),
    ]).start();

    // Animate scale properties (disable native driver to avoid conflicts)
    Animated.timing(scaleAnimatedValue, {
      toValue: isFocused ? 1.02 : 1,
      duration: theme.animations.timing.normal,
      useNativeDriver: false, // Disabled to prevent animation conflicts
    }).start();
  }, [isFocused, theme.animations.timing]);

  useEffect(() => {
    // Animate suggestions (disable native driver to avoid conflicts)
    Animated.timing(suggestionAnimatedValue, {
      toValue: showSuggestions ? 1 : 0,
      duration: theme.animations.timing.fast,
      useNativeDriver: false, // Disabled to prevent animation conflicts
    }).start();
  }, [showSuggestions, theme.animations.timing]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  // Handle focus
  const handleFocus = useCallback(() => {
    setIsFocused(true);
    onFocus?.();
    
    // Show suggestions if we have text and suggestions
    if (safeValue.trim() && suggestions.length > 0) {
      setShowSuggestions(true);
    }
    
    // Add haptic feedback (optional)
    try {
      if (Platform.OS === 'ios') {
        const { HapticFeedback } = require('expo-haptics');
        HapticFeedback.selectionAsync();
      }
    } catch (error) {
      // Graceful fallback if expo-haptics is not installed
    }
  }, [onFocus, safeValue, suggestions.length]);

  // Handle blur
  const handleBlur = useCallback(() => {
    setIsFocused(false);
    onBlur?.();
    
    // Hide suggestions after a small delay to allow for selection
    setTimeout(() => {
      setShowSuggestions(false);
    }, 150);
  }, [onBlur]);

  // Handle suggestion selection
  const handleSuggestionSelect = useCallback((suggestion: SearchSuggestion) => {
    onChangeText(suggestion.query);
    onSuggestionSelect?.(suggestion.query);
    setShowSuggestions(false);
    inputRef.current?.blur();
    
    // Add haptic feedback
    try {
      if (Platform.OS === 'ios') {
        const { HapticFeedback } = require('expo-haptics');
        HapticFeedback.impactAsync(HapticFeedback.ImpactFeedbackStyle.Light);
      }
    } catch (error) {
      // Graceful fallback
    }
  }, [onChangeText, onSuggestionSelect]);

  // Handle clear
  const handleClear = useCallback(() => {
    onChangeText('');
    setSuggestions([]);
    setShowSuggestions(false);
    inputRef.current?.focus();
    
    // Add haptic feedback
    try {
      if (Platform.OS === 'ios') {
        const { HapticFeedback } = require('expo-haptics');
        HapticFeedback.impactAsync(HapticFeedback.ImpactFeedbackStyle.Light);
      }
    } catch (error) {
      // Graceful fallback
    }
  }, [onChangeText]);

  // Animated styles - JS driven (colors, shadows)
  const animatedBorderColor = borderAnimatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [theme.colors.semantic.borderLight, theme.colors.primary[300]],
  });

  const animatedBackgroundColor = borderAnimatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [theme.colors.neutral[50], theme.colors.neutral[0]],
  });

  const animatedShadowOpacity = shadowAnimatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0.04, 0.1],
  });

  const animatedIconColor = borderAnimatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [theme.colors.neutral[400], theme.colors.primary[500]],
  });

  // Suggestion item renderer
  const renderSuggestionItem = ({ item }: { item: SearchSuggestion }) => (
    <TouchableOpacity
      style={[styles.suggestionItem, { borderColor: theme.colors.semantic.borderLight }]}
      onPress={() => handleSuggestionSelect(item)}
      activeOpacity={0.7}
    >
      <View style={styles.suggestionContent}>
        <Ionicons
          name={getSuggestionIcon(item.type)}
          size={16}
          color={theme.colors.neutral[500]}
          style={styles.suggestionIcon}
        />
        <View style={styles.suggestionTextContainer}>
          <Text style={[styles.suggestionText, { color: theme.colors.neutral[900] }]}>
            {item.query}
          </Text>
          {showCategories && item.category && (
            <Text style={[styles.suggestionCategory, { color: theme.colors.neutral[500] }]}>
              {item.category}
            </Text>
          )}
        </View>
        <Ionicons
          name="arrow-up-outline"
          size={14}
          color={theme.colors.neutral[400]}
          style={{ transform: [{ rotate: '45deg' }] }}
        />
      </View>
    </TouchableOpacity>
  );

  // Get icon based on suggestion type
  const getSuggestionIcon = (type: string) => {
    switch (type) {
      case 'product': return 'cube-outline';
      case 'brand': return 'business-outline';
      case 'category': return 'list-outline';
      default: return 'search-outline';
    }
  };

  // Dynamic styles
  const containerStyle = [
    styles.container,
    {
      backgroundColor: animatedBackgroundColor,
      borderColor: animatedBorderColor,
      shadowOpacity: animatedShadowOpacity,
      shadowColor: theme.colors.primary[500],
    },
  ];

  const transformStyle = {
    transform: [{ scale: scaleAnimatedValue }],
  };

  return (
    <View style={styles.wrapper}>
      <Animated.View style={[containerStyle, transformStyle]}>
        {/* Search Icon */}
        <Animated.View style={styles.iconContainer}>
          <Ionicons
            name="search"
            size={20}
            color={animatedIconColor as any}
          />
        </Animated.View>

        {/* Text Input */}
        <TextInput
          ref={inputRef}
          style={[
            styles.input,
            {
              color: theme.colors.neutral[900],
              fontFamily: theme.typography.fontFamily.primary,
            },
          ]}
          value={safeValue}
          onChangeText={handleTextChange}
          placeholder={safePlaceholder}
          placeholderTextColor={theme.colors.neutral[400]}
          onFocus={handleFocus}
          onBlur={handleBlur}
          autoFocus={autoFocus}
          editable={editable}
          autoCorrect={false}
          autoCapitalize="none"
          returnKeyType="search"
          blurOnSubmit={false}
          selectionColor={theme.colors.primary[500]}
          keyboardAppearance={themeMode === 'dark' ? 'dark' : 'light'}
        />

        {/* Loading Indicator */}
        {isLoading && (
          <View style={styles.loadingContainer}>
            <Ionicons
              name="refresh"
              size={16}
              color={theme.colors.primary[500]}
              style={{ opacity: 0.6 }}
            />
          </View>
        )}

        {/* Clear Button */}
        {value.length > 0 && !isLoading && (
          <TouchableOpacity
            style={styles.clearButton}
            onPress={handleClear}
            hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
          >
            <View style={[styles.clearButtonDefault, { backgroundColor: theme.colors.neutral[200] }]}>
              <Ionicons
                name="close"
                size={16}
                color={theme.colors.neutral[600]}
              />
            </View>
          </TouchableOpacity>
        )}
      </Animated.View>

      {/* Suggestions Dropdown */}
      {showSuggestions && suggestions.length > 0 && (
        <Animated.View
          style={[
            styles.suggestionsContainer,
            {
              backgroundColor: theme.colors.semantic.surface,
              borderColor: theme.colors.semantic.borderLight,
              opacity: suggestionAnimatedValue,
              transform: [{
                translateY: suggestionAnimatedValue.interpolate({
                  inputRange: [0, 1],
                  outputRange: [-10, 0],
                })
              }],
            },
          ]}
        >
          <FlatList
            data={suggestions}
            keyExtractor={(item, index) => `${item.query}-${index}`}
            renderItem={renderSuggestionItem}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
            style={styles.suggestionsList}
            contentContainerStyle={styles.suggestionsContent}
            maxToRenderPerBatch={5}
            windowSize={5}
          />
        </Animated.View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    position: 'relative',
    zIndex: 1500,
  },

  container: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1.5,
    marginVertical: 8,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 8,
    elevation: 3,
    minHeight: 52,
  },

  iconContainer: {
    marginRight: 12,
  },

  input: {
    flex: 1,
    fontSize: 16,
    fontWeight: '400',
    lineHeight: 24,
    padding: 0,
    textAlignVertical: 'center',
  },

  loadingContainer: {
    marginLeft: 8,
    justifyContent: 'center',
    alignItems: 'center',
    width: 20,
    height: 20,
  },

  clearButton: {
    marginLeft: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },

  clearButtonDefault: {
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },

  suggestionsContainer: {
    position: 'absolute',
    top: 60,
    left: 0,
    right: 0,
    borderRadius: 12,
    borderWidth: 1,
    maxHeight: screenHeight * 0.4,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
    zIndex: 1501,
  },

  suggestionsList: {
    flex: 1,
  },

  suggestionsContent: {
    paddingVertical: 4,
  },

  suggestionItem: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 0.5,
  },

  suggestionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  suggestionIcon: {
    marginRight: 12,
  },

  suggestionTextContainer: {
    flex: 1,
  },

  suggestionText: {
    fontSize: 15,
    fontWeight: '400',
    lineHeight: 20,
  },

  suggestionCategory: {
    fontSize: 12,
    fontWeight: '300',
    lineHeight: 16,
    marginTop: 2,
    textTransform: 'capitalize',
  },
});

export default SmartSearchBar;