-- Enhanced Product Schema for Smart Search
-- Optimized for NZ supermarket scraped data with advanced search capabilities

-- ============================================================================
-- ENHANCED PRODUCTS TABLE WITH SEARCH OPTIMIZATIONS
-- ============================================================================

-- Add enhanced search columns to existing products table
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS searchable_text tsvector GENERATED ALWAYS AS (
  to_tsvector('english', 
    COALESCE(name, '') || ' ' || 
    COALESCE(brand, '') || ' ' || 
    COALESCE(category, '') || ' ' || 
    COALESCE(description, '') || ' ' ||
    COALESCE(array_to_string(shopping_aliases, ' '), '')
  )
) STORED;

-- Add search ranking and popularity fields
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS search_popularity INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS search_rank DECIMAL(3,2) DEFAULT 1.0,
ADD COLUMN IF NOT EXISTS is_common_item BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS last_searched_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS search_count INTEGER DEFAULT 0;

-- Add enhanced metadata for better categorization
ALTER TABLE products
ADD COLUMN IF NOT EXISTS tags TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS dietary_info JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS seasonal_info JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS price_history JSONB DEFAULT '[]';

-- ============================================================================
-- OPTIMIZED INDEXES FOR FAST SEARCH
-- ============================================================================

-- Full-text search index (GIN for fast text search)
CREATE INDEX IF NOT EXISTS idx_products_searchable_text_gin 
ON products USING GIN (searchable_text);

-- Trigram index for fuzzy/typo-tolerant search
CREATE EXTENSION IF NOT EXISTS pg_trgm;
CREATE INDEX IF NOT EXISTS idx_products_name_trgm 
ON products USING GIN (name gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_products_brand_trgm 
ON products USING GIN (brand gin_trgm_ops);

-- Category and store filtering
CREATE INDEX IF NOT EXISTS idx_products_category_store 
ON products (category, store);

-- Popularity and ranking for smart suggestions
CREATE INDEX IF NOT EXISTS idx_products_popularity_rank 
ON products (search_popularity DESC, search_rank DESC, is_common_item DESC);

-- Price filtering and sorting
CREATE INDEX IF NOT EXISTS idx_products_price_range 
ON products (store, category, price) WHERE price IS NOT NULL;

-- Shopping aliases for keyword matching
CREATE INDEX IF NOT EXISTS idx_products_shopping_aliases_gin 
ON products USING GIN (shopping_aliases);

-- Tags for advanced filtering
CREATE INDEX IF NOT EXISTS idx_products_tags_gin 
ON products USING GIN (tags);

-- Recent searches for analytics
CREATE INDEX IF NOT EXISTS idx_products_recent_searches 
ON products (last_searched_at DESC) WHERE last_searched_at IS NOT NULL;

-- ============================================================================
-- SEARCH ANALYTICS TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS search_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  query TEXT NOT NULL,
  user_id UUID,
  results_count INTEGER,
  selected_product_id UUID REFERENCES products(id),
  search_type TEXT CHECK (search_type IN ('text', 'fuzzy', 'alias', 'category')),
  response_time_ms INTEGER,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  metadata JSONB DEFAULT '{}'
);

-- Indexes for search analytics
CREATE INDEX IF NOT EXISTS idx_search_analytics_query_trgm 
ON search_analytics USING GIN (query gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_search_analytics_created_at 
ON search_analytics (created_at DESC);
CREATE INDEX IF NOT EXISTS idx_search_analytics_user_id 
ON search_analytics (user_id, created_at DESC);

-- ============================================================================
-- MATERIALIZED VIEWS FOR FAST ACCESS
-- ============================================================================

-- Popular products view for quick suggestions
CREATE MATERIALIZED VIEW IF NOT EXISTS popular_products AS
SELECT 
  p.id,
  p.name,
  p.brand,
  p.category,
  p.store,
  p.price,
  p.image_url,
  p.search_popularity,
  p.search_rank,
  p.is_common_item,
  p.shopping_aliases,
  p.tags
FROM products p
WHERE p.is_common_item = TRUE OR p.search_popularity > 10
ORDER BY p.search_popularity DESC, p.search_rank DESC
LIMIT 1000;

-- Create unique index for materialized view
CREATE UNIQUE INDEX IF NOT EXISTS idx_popular_products_id 
ON popular_products (id);

-- Category-wise popular products
CREATE MATERIALIZED VIEW IF NOT EXISTS popular_products_by_category AS
SELECT 
  category,
  json_agg(
    json_build_object(
      'id', id,
      'name', name,
      'brand', brand,
      'price', price,
      'image_url', image_url,
      'search_popularity', search_popularity
    ) ORDER BY search_popularity DESC
  ) as products
FROM (
  SELECT DISTINCT ON (category, name, brand)
    id, name, brand, category, price, image_url, search_popularity
  FROM products 
  WHERE search_popularity > 5
) ranked
GROUP BY category;

-- ============================================================================
-- OPTIMIZED SEARCH FUNCTIONS
-- ============================================================================

-- Enhanced full-text search with ranking
CREATE OR REPLACE FUNCTION search_products_enhanced(
  search_query TEXT,
  max_results INTEGER DEFAULT 20,
  store_filter TEXT DEFAULT NULL,
  category_filter TEXT DEFAULT NULL,
  min_price DECIMAL DEFAULT NULL,
  max_price DECIMAL DEFAULT NULL
) RETURNS TABLE (
  id UUID,
  name TEXT,
  brand TEXT,
  category TEXT,
  store TEXT,
  price DECIMAL,
  image_url TEXT,
  relevance_score REAL,
  match_type TEXT
) LANGUAGE plpgsql AS $$
BEGIN
  RETURN QUERY
  WITH search_results AS (
    -- Exact matches (highest priority)
    SELECT 
      p.id, p.name, p.brand, p.category, p.store, p.price, p.image_url,
      ts_rank(p.searchable_text, websearch_to_tsquery('english', search_query)) as relevance,
      'exact'::TEXT as match_type,
      1 as priority
    FROM products p
    WHERE p.searchable_text @@ websearch_to_tsquery('english', search_query)
      AND (store_filter IS NULL OR p.store = store_filter)
      AND (category_filter IS NULL OR p.category = category_filter)
      AND (min_price IS NULL OR p.price >= min_price)
      AND (max_price IS NULL OR p.price <= max_price)
    
    UNION ALL
    
    -- Alias matches (medium priority)
    SELECT 
      p.id, p.name, p.brand, p.category, p.store, p.price, p.image_url,
      0.8::REAL as relevance,
      'alias'::TEXT as match_type,
      2 as priority
    FROM products p
    WHERE search_query = ANY(p.shopping_aliases)
      AND (store_filter IS NULL OR p.store = store_filter)
      AND (category_filter IS NULL OR p.category = category_filter)
      AND (min_price IS NULL OR p.price >= min_price)
      AND (max_price IS NULL OR p.price <= max_price)
    
    UNION ALL
    
    -- Fuzzy matches (lower priority)
    SELECT 
      p.id, p.name, p.brand, p.category, p.store, p.price, p.image_url,
      similarity(p.name, search_query) as relevance,
      'fuzzy'::TEXT as match_type,
      3 as priority
    FROM products p
    WHERE similarity(p.name, search_query) > 0.3
      AND (store_filter IS NULL OR p.store = store_filter)
      AND (category_filter IS NULL OR p.category = category_filter)
      AND (min_price IS NULL OR p.price >= min_price)
      AND (max_price IS NULL OR p.price <= max_price)
  )
  SELECT DISTINCT ON (sr.id)
    sr.id, sr.name, sr.brand, sr.category, sr.store, sr.price, sr.image_url,
    sr.relevance, sr.match_type
  FROM search_results sr
  ORDER BY sr.id, sr.priority, sr.relevance DESC
  LIMIT max_results;
END;
$$;

-- Fast autocomplete suggestions
CREATE OR REPLACE FUNCTION get_autocomplete_suggestions(
  partial_query TEXT,
  max_suggestions INTEGER DEFAULT 10
) RETURNS TABLE (
  suggestion TEXT,
  category TEXT,
  popularity INTEGER
) LANGUAGE plpgsql AS $$
BEGIN
  RETURN QUERY
  WITH suggestions AS (
    -- Product names
    SELECT DISTINCT 
      p.name as suggestion,
      p.category,
      p.search_popularity as popularity
    FROM products p
    WHERE p.name ILIKE partial_query || '%'
    
    UNION ALL
    
    -- Brands
    SELECT DISTINCT 
      p.brand as suggestion,
      'Brand' as category,
      MAX(p.search_popularity) as popularity
    FROM products p
    WHERE p.brand ILIKE partial_query || '%'
    GROUP BY p.brand
    
    UNION ALL
    
    -- Aliases
    SELECT DISTINCT 
      alias as suggestion,
      p.category,
      p.search_popularity as popularity
    FROM products p, unnest(p.shopping_aliases) as alias
    WHERE alias ILIKE partial_query || '%'
  )
  SELECT s.suggestion, s.category, s.popularity
  FROM suggestions s
  ORDER BY s.popularity DESC, s.suggestion
  LIMIT max_suggestions;
END;
$$;

-- Get popular products by category
CREATE OR REPLACE FUNCTION get_popular_products_by_category(
  target_category TEXT,
  max_results INTEGER DEFAULT 20
) RETURNS TABLE (
  id UUID,
  name TEXT,
  brand TEXT,
  price DECIMAL,
  image_url TEXT,
  search_popularity INTEGER
) LANGUAGE plpgsql AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.id, p.name, p.brand, p.price, p.image_url, p.search_popularity
  FROM products p
  WHERE p.category = target_category
    AND (p.is_common_item = TRUE OR p.search_popularity > 5)
  ORDER BY p.search_popularity DESC, p.search_rank DESC
  LIMIT max_results;
END;
$$;

-- Update search analytics and popularity
CREATE OR REPLACE FUNCTION record_search_analytics(
  search_query TEXT,
  user_id UUID DEFAULT NULL,
  results_count INTEGER DEFAULT 0,
  selected_product_id UUID DEFAULT NULL,
  search_type TEXT DEFAULT 'text',
  response_time_ms INTEGER DEFAULT NULL
) RETURNS UUID LANGUAGE plpgsql AS $$
DECLARE
  analytics_id UUID;
BEGIN
  -- Insert search analytics
  INSERT INTO search_analytics (
    query, user_id, results_count, selected_product_id, 
    search_type, response_time_ms
  ) VALUES (
    search_query, user_id, results_count, selected_product_id,
    search_type, response_time_ms
  ) RETURNING id INTO analytics_id;
  
  -- Update product popularity if a product was selected
  IF selected_product_id IS NOT NULL THEN
    UPDATE products 
    SET 
      search_count = search_count + 1,
      search_popularity = search_popularity + 1,
      last_searched_at = NOW()
    WHERE id = selected_product_id;
  END IF;
  
  RETURN analytics_id;
END;
$$;

-- ============================================================================
-- UTILITY FUNCTIONS FOR DATA MANAGEMENT
-- ============================================================================

-- Refresh materialized views
CREATE OR REPLACE FUNCTION refresh_search_views()
RETURNS VOID LANGUAGE plpgsql AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY popular_products;
  REFRESH MATERIALIZED VIEW CONCURRENTLY popular_products_by_category;
END;
$$;

-- Update product search rankings based on analytics
CREATE OR REPLACE FUNCTION update_search_rankings()
RETURNS VOID LANGUAGE plpgsql AS $$
BEGIN
  -- Update search rankings based on recent analytics
  WITH ranking_data AS (
    SELECT 
      p.id,
      COALESCE(recent_searches.search_count, 0) as recent_count,
      COALESCE(p.search_count, 0) as total_count
    FROM products p
    LEFT JOIN (
      SELECT 
        selected_product_id,
        COUNT(*) as search_count
      FROM search_analytics 
      WHERE created_at > NOW() - INTERVAL '30 days'
        AND selected_product_id IS NOT NULL
      GROUP BY selected_product_id
    ) recent_searches ON p.id = recent_searches.selected_product_id
  )
  UPDATE products 
  SET 
    search_rank = GREATEST(0.1, LEAST(2.0, 
      1.0 + (rd.recent_count * 0.1) + (rd.total_count * 0.01)
    )),
    is_common_item = (rd.recent_count > 10 OR rd.total_count > 50)
  FROM ranking_data rd
  WHERE products.id = rd.id;
END;
$$;

-- ============================================================================
-- RLS POLICIES FOR SECURITY
-- ============================================================================

-- Enable RLS on search analytics
ALTER TABLE search_analytics ENABLE ROW LEVEL SECURITY;

-- Policy for users to see their own search history
CREATE POLICY "Users can view their own search analytics" ON search_analytics
  FOR SELECT USING (auth.uid() = user_id);

-- Policy for inserting search analytics
CREATE POLICY "Anyone can insert search analytics" ON search_analytics
  FOR INSERT WITH CHECK (true);

-- ============================================================================
-- SCHEDULED JOBS FOR MAINTENANCE
-- ============================================================================

-- Create extension for cron jobs if not exists
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- Schedule daily ranking updates (if pg_cron is available)
-- SELECT cron.schedule('update-search-rankings', '0 2 * * *', 'SELECT update_search_rankings();');

-- Schedule weekly view refresh
-- SELECT cron.schedule('refresh-search-views', '0 3 * * 0', 'SELECT refresh_search_views();');

-- ============================================================================
-- INDEXES FOR PERFORMANCE MONITORING
-- ============================================================================

-- Monitor slow queries
CREATE INDEX IF NOT EXISTS idx_search_analytics_slow_queries 
ON search_analytics (response_time_ms DESC) 
WHERE response_time_ms > 1000;

-- Track popular search terms
CREATE INDEX IF NOT EXISTS idx_search_analytics_popular_queries 
ON search_analytics (query) WHERE created_at > NOW() - INTERVAL '7 days';

-- ============================================================================
-- COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON TABLE products IS 'Enhanced products table with search optimization for NZ supermarket data';
COMMENT ON COLUMN products.searchable_text IS 'Generated tsvector for full-text search including name, brand, category, and aliases';
COMMENT ON COLUMN products.shopping_aliases IS 'Array of alternative names, nicknames, and common misspellings';
COMMENT ON COLUMN products.search_popularity IS 'Popularity score based on search frequency and user interactions';
COMMENT ON COLUMN products.search_rank IS 'Ranking score for search result ordering (1.0 = normal, 2.0 = highly relevant)';
COMMENT ON COLUMN products.is_common_item IS 'Flag indicating if this is a commonly searched/purchased item';

COMMENT ON TABLE search_analytics IS 'Analytics table for tracking search queries and user behavior';
COMMENT ON FUNCTION search_products_enhanced IS 'Enhanced search function with multiple matching strategies and ranking';
COMMENT ON FUNCTION get_autocomplete_suggestions IS 'Fast autocomplete suggestions based on partial queries';
COMMENT ON FUNCTION record_search_analytics IS 'Records search analytics and updates product popularity scores'; 