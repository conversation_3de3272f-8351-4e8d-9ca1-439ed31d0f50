/**
 * useSmartSearch Hook
 * 
 * Comprehensive React hook for smart product search with caching,
 * analytics, and enhanced user experience features.
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { smartSearchService, SmartSearchResult } from '../services/smartSearchService';
import { productImageService } from '../services/productImageService';
import { debounce } from '../utils/debounce';

export interface UseSmartSearchOptions {
  maxResults?: number;
  debounceMs?: number;
  enableCache?: boolean;
  enableAnalytics?: boolean;
  storeFilter?: 'woolworths' | 'newworld' | 'paknsave';
  categoryFilter?: string;
  minPrice?: number;
  maxPrice?: number;
}

export interface SearchState {
  query: string;
  results: SmartSearchResult[];
  suggestions: string[];
  isLoading: boolean;
  isSearching: boolean;
  error: string | null;
  hasSearched: boolean;
  spellingCorrections: string[];
  popularTerms: string[];
  recentSearches: string[];
}

export interface UseSmartSearchReturn extends SearchState {
  search: (query: string) => Promise<void>;
  setQuery: (query: string) => void;
  clearSearch: () => void;
  selectResult: (result: SmartSearchResult) => Promise<void>;
  getSuggestions: (partialQuery: string) => Promise<string[]>;
  getPopularTerms: () => Promise<string[]>;
  retrySearch: () => Promise<void>;
  updateFilters: (filters: Partial<UseSmartSearchOptions>) => void;
}

const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
const MAX_RECENT_SEARCHES = 10;

export function useSmartSearch(options: UseSmartSearchOptions = {}): UseSmartSearchReturn {
  const {
    maxResults = 20,
    debounceMs = 300,
    enableCache = true,
    enableAnalytics = true,
    storeFilter,
    categoryFilter,
    minPrice,
    maxPrice
  } = options;

  // State management
  const [state, setState] = useState<SearchState>({
    query: '',
    results: [],
    suggestions: [],
    isLoading: false,
    isSearching: false,
    error: null,
    hasSearched: false,
    spellingCorrections: [],
    popularTerms: [],
    recentSearches: []
  });

  // Refs for cleanup and caching
  const searchCache = useRef<Map<string, { results: SmartSearchResult[]; timestamp: number }>>(new Map());
  const abortController = useRef<AbortController | null>(null);
  const currentFilters = useRef({ storeFilter, categoryFilter, minPrice, maxPrice });

  // Load initial data
  useEffect(() => {
    loadInitialData();
    loadRecentSearches();
  }, []);

  /**
   * Load popular terms and suggestions on mount
   */
  const loadInitialData = async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true }));
      
      const [popularTerms, suggestions] = await Promise.all([
        smartSearchService.getPopularTerms(10),
        smartSearchService.getSuggestions('', 12)
      ]);

      setState(prev => ({
        ...prev,
        popularTerms: popularTerms.map(term => term.query),
        suggestions: suggestions.map(s => s.query),
        isLoading: false
      }));
    } catch (error) {
      console.warn('Failed to load initial search data:', error);
      setState(prev => ({ ...prev, isLoading: false }));
    }
  };

  /**
   * Load recent searches from localStorage
   */
  const loadRecentSearches = () => {
    try {
      const stored = localStorage.getItem('recentSearches');
      if (stored) {
        const recentSearches = JSON.parse(stored);
        setState(prev => ({ ...prev, recentSearches }));
      }
    } catch (error) {
      console.warn('Failed to load recent searches:', error);
    }
  };

  /**
   * Save search to recent searches
   */
  const saveRecentSearch = (query: string) => {
    try {
      const stored = localStorage.getItem('recentSearches');
      let recentSearches: string[] = stored ? JSON.parse(stored) : [];
      
      // Remove if already exists and add to front
      recentSearches = recentSearches.filter(search => search !== query);
      recentSearches.unshift(query);
      
      // Keep only the most recent searches
      recentSearches = recentSearches.slice(0, MAX_RECENT_SEARCHES);
      
      localStorage.setItem('recentSearches', JSON.stringify(recentSearches));
      setState(prev => ({ ...prev, recentSearches }));
    } catch (error) {
      console.warn('Failed to save recent search:', error);
    }
  };

  /**
   * Get cached search results
   */
  const getCachedResults = (cacheKey: string): SmartSearchResult[] | null => {
    if (!enableCache) return null;
    
    const cached = searchCache.current.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached.results;
    }
    
    // Clean up expired cache entries
    searchCache.current.delete(cacheKey);
    return null;
  };

  /**
   * Cache search results
   */
  const cacheResults = (cacheKey: string, results: SmartSearchResult[]) => {
    if (!enableCache) return;
    
    searchCache.current.set(cacheKey, {
      results,
      timestamp: Date.now()
    });
  };

  /**
   * Generate cache key for current search
   */
  const generateCacheKey = (query: string, filters: any) => {
    return `${query}_${JSON.stringify(filters)}`;
  };

  /**
   * Perform smart search
   */
  const performSearch = async (query: string): Promise<void> => {
    if (!query.trim()) {
      setState(prev => ({
        ...prev,
        results: [],
        error: null,
        hasSearched: false,
        spellingCorrections: []
      }));
      return;
    }

    // Cancel previous search
    if (abortController.current) {
      abortController.current.abort();
    }
    abortController.current = new AbortController();

    const startTime = Date.now();
    const filters = currentFilters.current;
    const cacheKey = generateCacheKey(query, filters);

    try {
      setState(prev => ({ ...prev, isSearching: true, error: null }));

      // Check cache first
      const cachedResults = getCachedResults(cacheKey);
      if (cachedResults) {
        setState(prev => ({
          ...prev,
          results: cachedResults,
          isSearching: false,
          hasSearched: true
        }));
        return;
      }

      // Perform search
      const searchResults = await smartSearchService.searchProducts(query, {
        maxResults,
        storeFilter: filters.storeFilter,
        categoryFilter: filters.categoryFilter,
        minPrice: filters.minPrice,
        maxPrice: filters.maxPrice
      });

      // Get spelling corrections if no results
      let spellingCorrections: string[] = [];
      if (searchResults.length === 0) {
        try {
          const corrections = await smartSearchService.getSpellingCorrections(query);
          spellingCorrections = corrections;
        } catch (error) {
          console.warn('Failed to get spelling corrections:', error);
        }
      }

      // Cache results
      cacheResults(cacheKey, searchResults);

      // Record analytics
      if (enableAnalytics) {
        const responseTime = Date.now() - startTime;
        smartSearchService.recordSearchAnalytics(
          query,
          searchResults.length,
          undefined,
          'text',
          responseTime
        ).catch(error => console.warn('Failed to record analytics:', error));
      }

      // Save to recent searches
      saveRecentSearch(query);

      setState(prev => ({
        ...prev,
        results: searchResults,
        isSearching: false,
        hasSearched: true,
        spellingCorrections,
        error: null
      }));

    } catch (error: any) {
      if (error.name !== 'AbortError') {
        console.error('Search failed:', error);
        setState(prev => ({
          ...prev,
          error: error.message || 'Search failed',
          isSearching: false,
          hasSearched: true,
          results: []
        }));
      }
    }
  };

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce(performSearch, debounceMs),
    [maxResults, enableCache, enableAnalytics]
  );

  /**
   * Public search function
   */
  const search = useCallback(async (query: string) => {
    setState(prev => ({ ...prev, query }));
    await debouncedSearch(query);
  }, [debouncedSearch]);

  /**
   * Set query without triggering search
   */
  const setQuery = useCallback((query: string) => {
    setState(prev => ({ ...prev, query }));
  }, []);

  /**
   * Clear search results and query
   */
  const clearSearch = useCallback(() => {
    // Cancel ongoing search
    if (abortController.current) {
      abortController.current.abort();
    }

    setState(prev => ({
      ...prev,
      query: '',
      results: [],
      error: null,
      hasSearched: false,
      isSearching: false,
      spellingCorrections: []
    }));
  }, []);

  /**
   * Select a search result (for analytics)
   */
  const selectResult = useCallback(async (result: SmartSearchResult) => {
    if (enableAnalytics) {
      try {
        await smartSearchService.recordSearchAnalytics(
          state.query,
          state.results.length,
          result.id,
          'text'
        );
      } catch (error) {
        console.warn('Failed to record result selection:', error);
      }
    }
  }, [state.query, state.results.length, enableAnalytics]);

  /**
   * Get autocomplete suggestions
   */
  const getSuggestions = useCallback(async (partialQuery: string): Promise<string[]> => {
    try {
      const suggestions = await smartSearchService.getSuggestions(partialQuery, 8);
      return suggestions.map(s => s.query);
    } catch (error) {
      console.warn('Failed to get suggestions:', error);
      return [];
    }
  }, []);

  /**
   * Get popular search terms
   */
  const getPopularTerms = useCallback(async (): Promise<string[]> => {
    try {
      const terms = await smartSearchService.getPopularTerms(10);
      return terms.map(term => term.query);
    } catch (error) {
      console.warn('Failed to get popular terms:', error);
      return [];
    }
  }, []);

  /**
   * Retry the last search
   */
  const retrySearch = useCallback(async () => {
    if (state.query) {
      await performSearch(state.query);
    }
  }, [state.query]);

  /**
   * Update search filters
   */
  const updateFilters = useCallback((filters: Partial<UseSmartSearchOptions>) => {
    currentFilters.current = {
      ...currentFilters.current,
      ...filters
    };

    // Clear cache when filters change
    searchCache.current.clear();

    // Re-search if we have a query
    if (state.query) {
      debouncedSearch(state.query);
    }
  }, [state.query, debouncedSearch]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortController.current) {
        abortController.current.abort();
      }
    };
  }, []);

  return {
    ...state,
    search,
    setQuery,
    clearSearch,
    selectResult,
    getSuggestions,
    getPopularTerms,
    retrySearch,
    updateFilters
  };
} 