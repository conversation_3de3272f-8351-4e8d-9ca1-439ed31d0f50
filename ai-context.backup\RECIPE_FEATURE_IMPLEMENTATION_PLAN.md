# Recipe Feature Implementation Plan
## Adding Browse, Like, and Shopping List Integration

Based on analysis of your current app architecture, this document provides a comprehensive step-by-step implementation plan for enhancing the recipes feature with browsing, liking, and direct shopping list integration.

## Current Architecture Analysis

### Existing Components
- ✅ **CookbookScreen** - Basic recipe listing with 2-column grid
- ✅ **RecipeCard** - Advanced component with theming, variants, and actions
- ✅ **RecipeDetailCard** - Modal detail view with ingredients display
- ✅ **Recipe Database** - Supabase recipes table with full schema
- ✅ **Shopping List System** - Multiple lists with AsyncStorage + Context
- ✅ **Theme System** - Comprehensive design tokens and styling

### Current Recipe Features
- Recipe browsing in grid layout
- Favorite/bookmark functionality
- Shopping list integration (basic)
- Recipe detail modal
- Image loading with fallbacks
- Theming support

## Implementation Plan

### Phase 1: Enhanced Recipe Browsing & Search (Week 1)

#### 1.1 Create Enhanced Recipe Browser Screen
```typescript
// src/screens/main/RecipeBrowserScreen.tsx
```

**Features to Implement:**
- Search functionality with filters
- Category-based browsing
- Sort options (newest, popular, cook time, difficulty)
- Infinite scroll/pagination
- Loading states and error handling

**Dependencies:**
- Extend existing `CookbookScreen.tsx`
- Use existing `RecipeCard` component
- Integrate with Supabase recipe queries

**Database Updates Needed:**
```sql
-- Add indexes for better search performance
CREATE INDEX IF NOT EXISTS idx_recipes_search ON recipes USING gin(to_tsvector('english', title || ' ' || description));
CREATE INDEX IF NOT EXISTS idx_recipes_category ON recipes(cuisine);
CREATE INDEX IF NOT EXISTS idx_recipes_difficulty ON recipes(difficulty);
CREATE INDEX IF NOT EXISTS idx_recipes_cook_time ON recipes(cook_time);
```

#### 1.2 Implement Advanced Search & Filtering
```typescript
// src/components/RecipeSearchBar.tsx
// src/components/RecipeFilters.tsx
```

**Search Features:**
- Text search across title, description, ingredients
- Category filters (cuisine, difficulty, meal type)
- Cook time filters
- Dietary restriction filters
- Tag-based filtering

**UI Components:**
- Search input with debounced queries
- Filter chips/pills
- Sort dropdown
- Clear filters action

#### 1.3 Enhanced Recipe Service Layer
```typescript
// src/services/enhancedRecipeService.ts
```

**API Methods:**
```typescript
interface RecipeSearchParams {
  query?: string;
  category?: string;
  cuisine?: string;
  difficulty?: 'easy' | 'medium' | 'hard';
  maxCookTime?: number;
  tags?: string[];
  dietary?: string[];
  sortBy?: 'newest' | 'popular' | 'cook_time' | 'rating';
  limit?: number;
  offset?: number;
}

export const searchRecipes = async (params: RecipeSearchParams): Promise<Recipe[]>
export const getRecipesByCategory = async (category: string): Promise<Recipe[]>
export const getTrendingRecipes = async (limit: number): Promise<Recipe[]>
export const getRecommendedRecipes = async (userId: string): Promise<Recipe[]>
```

### Phase 2: Enhanced Liking & Social Features (Week 2)

#### 2.1 Implement Comprehensive Recipe Liking System
```typescript
// src/services/recipeLikingService.ts
```

**Database Schema Updates:**
```sql
-- Create recipe likes table
CREATE TABLE recipe_likes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  recipe_id UUID REFERENCES recipes(id) ON DELETE CASCADE,
  created_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(user_id, recipe_id)
);

-- Create recipe ratings table
CREATE TABLE recipe_ratings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  recipe_id UUID REFERENCES recipes(id) ON DELETE CASCADE,
  rating INTEGER CHECK (rating >= 1 AND rating <= 5),
  review TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(user_id, recipe_id)
);

-- Add aggregated fields to recipes table
ALTER TABLE recipes ADD COLUMN likes_count INTEGER DEFAULT 0;
ALTER TABLE recipes ADD COLUMN average_rating DECIMAL(3,2) DEFAULT 0;
ALTER TABLE recipes ADD COLUMN ratings_count INTEGER DEFAULT 0;
```

**Service Methods:**
```typescript
export const likeRecipe = async (recipeId: string): Promise<void>
export const unlikeRecipe = async (recipeId: string): Promise<void>
export const isRecipeLiked = async (recipeId: string): Promise<boolean>
export const getLikedRecipes = async (userId: string): Promise<Recipe[]>
export const rateRecipe = async (recipeId: string, rating: number, review?: string): Promise<void>
export const getRecipeRating = async (recipeId: string): Promise<number | null>
```

#### 2.2 Create User Recipe Collections
```typescript
// src/services/recipeCollectionService.ts
```

**Database Schema:**
```sql
-- Create recipe collections table
CREATE TABLE recipe_collections (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  is_public BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Create collection recipes junction table
CREATE TABLE collection_recipes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  collection_id UUID REFERENCES recipe_collections(id) ON DELETE CASCADE,
  recipe_id UUID REFERENCES recipes(id) ON DELETE CASCADE,
  added_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(collection_id, recipe_id)
);
```

**Features:**
- Create custom recipe collections (e.g., "Weekend Cooking", "Quick Meals")
- Add/remove recipes from collections
- Share collections with other users
- Browse public collections

#### 2.3 Update Recipe Card Component
```typescript
// Enhanced src/components/RecipeCard.tsx
```

**New Features:**
- Like button with animation
- Rating display (stars)
- Collection management
- Share functionality
- Quick actions menu

### Phase 3: Advanced Shopping List Integration (Week 3)

#### 3.1 Intelligent Ingredient Mapping
```typescript
// src/services/ingredientMappingService.ts
```

**Features:**
- Map recipe ingredients to store products
- Handle quantity conversions
- Brand preference integration
- Smart product matching

**Service Methods:**
```typescript
interface IngredientMapping {
  originalIngredient: string;
  quantity: number;
  unit: string;
  mappedProducts: Array<{
    store: string;
    productName: string;
    price: number;
    confidence: number;
  }>;
  suggestedQuantity: number;
  suggestedUnit: string;
}

export const mapRecipeIngredients = async (recipe: Recipe): Promise<IngredientMapping[]>
export const optimizeIngredientsForShopping = async (ingredients: IngredientMapping[]): Promise<ShoppingOptimization>
```

#### 3.2 Enhanced Shopping List Integration
```typescript
// src/services/recipeShoppingIntegration.ts
```

**Database Updates:**
```sql
-- Add recipe reference to shopping list items
ALTER TABLE shopping_list_items ADD COLUMN recipe_id UUID REFERENCES recipes(id);
ALTER TABLE shopping_list_items ADD COLUMN original_ingredient TEXT;
ALTER TABLE shopping_list_items ADD COLUMN serving_multiplier DECIMAL(3,2) DEFAULT 1.0;
```

**Features:**
- Add entire recipe to shopping list with serving adjustments
- Intelligent quantity calculations
- Merge duplicate ingredients across recipes
- Price comparison for recipe ingredients
- Shopping list optimization

**UI Components:**
```typescript
// src/components/RecipeToShoppingListModal.tsx
// src/components/ServingAdjuster.tsx
// src/components/IngredientSelector.tsx
```

#### 3.3 Recipe Meal Planning Integration
```typescript
// src/services/mealPlanningService.ts
```

**Features:**
- Weekly meal planning
- Automatic shopping list generation from meal plans
- Nutritional analysis
- Budget tracking

### Phase 4: User Experience Enhancements (Week 4)

#### 4.1 Recipe Recommendation Engine
```typescript
// src/services/recipeRecommendationService.ts
```

**Algorithm Features:**
- Based on liked recipes
- Seasonal recommendations
- Dietary preferences
- Cooking skill level
- Available ingredients
- Time constraints

#### 4.2 Advanced Recipe Detail View
```typescript
// Enhanced src/components/RecipeDetailCard.tsx
```

**New Features:**
- Step-by-step cooking mode
- Timer integration
- Ingredient substitution suggestions
- Nutritional information
- User reviews and ratings
- Related recipes
- Cooking tips and notes

#### 4.3 Social Features
```typescript
// src/services/recipeSocialService.ts
```

**Features:**
- Recipe sharing
- User recipe submissions
- Comment system
- Recipe challenges/contests
- Following other users
- Activity feed

## Implementation Details

### File Structure
```
src/
├── components/
│   ├── recipe/
│   │   ├── RecipeBrowserScreen.tsx
│   │   ├── RecipeSearchBar.tsx
│   │   ├── RecipeFilters.tsx
│   │   ├── RecipeToShoppingListModal.tsx
│   │   ├── ServingAdjuster.tsx
│   │   ├── IngredientSelector.tsx
│   │   ├── RecipeCollectionManager.tsx
│   │   └── CookingModeView.tsx
│   └── (existing components...)
├── services/
│   ├── enhancedRecipeService.ts
│   ├── recipeLikingService.ts
│   ├── recipeCollectionService.ts
│   ├── ingredientMappingService.ts
│   ├── recipeShoppingIntegration.ts
│   ├── mealPlanningService.ts
│   ├── recipeRecommendationService.ts
│   └── recipeSocialService.ts
├── hooks/
│   ├── useRecipeSearch.ts
│   ├── useRecipeLikes.ts
│   ├── useRecipeCollections.ts
│   └── useMealPlanning.ts
└── types/
    ├── recipe.ts
    └── mealPlanning.ts
```

### Context Updates
```typescript
// Enhanced src/context/RecipeContext.tsx
interface RecipeContextType {
  // Search & Browse
  searchRecipes: (params: RecipeSearchParams) => Promise<Recipe[]>;
  searchState: {
    results: Recipe[];
    loading: boolean;
    hasMore: boolean;
  };
  
  // Likes & Collections
  likedRecipes: string[];
  collections: RecipeCollection[];
  toggleLike: (recipeId: string) => Promise<void>;
  addToCollection: (recipeId: string, collectionId: string) => Promise<void>;
  
  // Shopping Integration
  addRecipeToShoppingList: (recipe: Recipe, servings: number, listId: string) => Promise<void>;
  
  // Recommendations
  recommendedRecipes: Recipe[];
  refreshRecommendations: () => Promise<void>;
}
```

### Database Optimization
```sql
-- Performance indexes
CREATE INDEX idx_recipes_user_likes ON recipe_likes(user_id);
CREATE INDEX idx_recipes_popular ON recipes(likes_count DESC, created_at DESC);
CREATE INDEX idx_recipes_rating ON recipes(average_rating DESC, ratings_count DESC);
CREATE INDEX idx_collection_recipes_collection ON collection_recipes(collection_id);

-- Views for common queries
CREATE VIEW popular_recipes AS 
SELECT r.*, rl.likes_count, rr.average_rating 
FROM recipes r 
LEFT JOIN (
  SELECT recipe_id, COUNT(*) as likes_count 
  FROM recipe_likes 
  GROUP BY recipe_id
) rl ON r.id = rl.recipe_id
LEFT JOIN (
  SELECT recipe_id, AVG(rating) as average_rating 
  FROM recipe_ratings 
  GROUP BY recipe_id
) rr ON r.id = rr.recipe_id
ORDER BY rl.likes_count DESC NULLS LAST;
```

## Integration Points

### 1. Existing Shopping List System
- Leverage `ShoppingContext` for list management
- Extend `Product` interface for recipe ingredients
- Use existing price comparison services
- Maintain AsyncStorage backup functionality

### 2. Existing Theme System
- Use established design tokens
- Follow existing component patterns
- Maintain consistency with current UI
- Support light/dark theme switching

### 3. Existing Navigation
- Integrate with bottom tab navigation
- Use modal presentations for details
- Follow established routing patterns
- Maintain navigation state

### 4. Existing Authentication
- Use current auth context
- Respect user permissions
- Handle guest user scenarios
- Integrate with user preferences

## Testing Strategy

### Unit Tests
```typescript
// tests/services/enhancedRecipeService.test.ts
// tests/services/recipeLikingService.test.ts
// tests/components/RecipeBrowserScreen.test.tsx
```

### Integration Tests
```typescript
// tests/integration/recipeShoppingIntegration.test.ts
// tests/integration/recipeLiking.test.ts
```

### E2E Tests
```typescript
// e2e/recipe-browsing.spec.ts
// e2e/recipe-to-shopping-list.spec.ts
```

## Performance Considerations

### 1. Data Loading
- Implement pagination for recipe lists
- Use lazy loading for images
- Cache frequently accessed data
- Optimize Supabase queries

### 2. Image Optimization
- Use optimized image formats
- Implement progressive loading
- Add proper caching headers
- Provide multiple image sizes

### 3. Search Performance
- Implement debounced search
- Use database full-text search
- Cache popular search results
- Optimize filter queries

## Security Considerations

### 1. Data Access
- Implement proper RLS policies
- Validate user permissions
- Sanitize user inputs
- Protect sensitive data

### 2. Recipe Submissions
- Content moderation system
- Image upload validation
- Rate limiting
- Spam prevention

## Deployment Strategy

### 1. Feature Flags
- Implement feature toggles
- Gradual rollout capability
- A/B testing support
- Quick rollback ability

### 2. Database Migrations
- Safe migration scripts
- Backward compatibility
- Data validation
- Rollback procedures

## Success Metrics

### 1. User Engagement
- Recipe view time
- Like/save rates
- Shopping list conversion
- Search usage

### 2. Business Metrics
- Shopping list completion
- Recipe discovery rate
- User retention
- Feature adoption

## Timeline Summary

| Phase | Duration | Key Deliverables |
|-------|----------|------------------|
| Phase 1 | Week 1 | Enhanced browsing, search, filtering |
| Phase 2 | Week 2 | Liking system, collections, ratings |
| Phase 3 | Week 3 | Advanced shopping integration |
| Phase 4 | Week 4 | UX enhancements, recommendations |

## Next Steps

1. **Review and approve** this implementation plan
2. **Set up development environment** with required dependencies
3. **Create database migrations** for new tables and indexes
4. **Begin Phase 1 implementation** with enhanced recipe browsing
5. **Set up testing framework** for new features
6. **Establish monitoring** for performance and usage metrics

This plan leverages your existing architecture while adding significant value through enhanced recipe discovery, social features, and intelligent shopping list integration.