import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  Animated,
  Dimensions,
} from 'react-native';

const { width } = Dimensions.get('window');

interface LoadingSpinnerProps {
  loading: boolean;
  text?: string;
  overlay?: boolean;
  size?: 'small' | 'large';
  color?: string;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  loading,
  text = 'Loading...',
  overlay = false,
  size = 'large',
  color = '#007AFF',
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    if (loading) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.8,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [loading, fadeAnim, scaleAnim]);

  if (!loading) return null;

  const containerStyle = overlay ? styles.overlayContainer : styles.inlineContainer;

  return (
    <Animated.View
      style={[
        containerStyle,
        {
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }],
        },
      ]}
    >
      <View style={styles.content}>
        <ActivityIndicator size={size} color={color} style={styles.spinner} />
        <Text style={[styles.text, { color }]}>{text}</Text>
      </View>
    </Animated.View>
  );
};

// Skeleton loader for lists
interface SkeletonProps {
  lines?: number;
  animate?: boolean;
}

export const SkeletonLoader: React.FC<SkeletonProps> = ({ 
  lines = 3, 
  animate = true 
}) => {
  const shimmerAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (animate) {
      const shimmer = () => {
        Animated.sequence([
          Animated.timing(shimmerAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(shimmerAnim, {
            toValue: 0,
            duration: 1000,
            useNativeDriver: true,
          }),
        ]).start(() => shimmer());
      };
      shimmer();
    }
  }, [animate, shimmerAnim]);

  const opacity = animate ? shimmerAnim : 0.3;

  return (
    <View style={styles.skeletonContainer}>
      {Array.from({ length: lines }, (_, index) => (
        <Animated.View
          key={index}
          style={[
            styles.skeletonLine,
            {
              opacity,
              width: index === lines - 1 ? '60%' : '100%',
            },
          ]}
        />
      ))}
    </View>
  );
};

// Loading state for recipe cards
export const RecipeCardSkeleton: React.FC = () => {
  return (
    <View style={styles.recipeCardSkeleton}>
      <SkeletonLoader lines={1} />
      <View style={styles.skeletonImagePlaceholder} />
      <View style={styles.skeletonContent}>
        <SkeletonLoader lines={2} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  overlayContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  inlineContainer: {
    paddingVertical: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    alignItems: 'center',
  },
  spinner: {
    marginBottom: 12,
  },
  text: {
    fontSize: 16,
    fontWeight: '500',
  },
  skeletonContainer: {
    padding: 16,
  },
  skeletonLine: {
    height: 12,
    backgroundColor: '#E1E9EE',
    borderRadius: 6,
    marginBottom: 8,
  },
  recipeCardSkeleton: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 16,
    overflow: 'hidden',
  },
  skeletonImagePlaceholder: {
    height: 140,
    backgroundColor: '#E1E9EE',
  },
  skeletonContent: {
    padding: 16,
  },
}); 