import { Recipe } from '../utils/storage';
import { aiApiService } from './aiApiService';

// Organization categories and types
export const RECIPE_CATEGORIES = {
  CUISINE: [
    'Italian', 'Asian', 'Mexican', 'American', 'French', 'Indian', 'Thai',
    'Mediterranean', 'Japanese', 'Chinese', 'Korean', 'Vietnamese', 'Greek',
    'Spanish', 'Middle Eastern', 'African', 'Caribbean', 'Nordic', 'Latin American'
  ],
  
  MEAL_TYPE: [
    'Breakfast', 'Brunch', 'Lunch', 'Dinner', 'Snack', 'Appetizer', 
    'Dessert', 'Beverage', 'Side Dish'
  ],
  
  DISH_TYPE: [
    'Main Course', 'Appetizer', 'Side Dish', 'Salad', 'Soup', 'Pasta',
    'Rice Dish', 'Sandwich', 'Pizza', 'Bread', 'Sauce', 'Marinade'
  ],
  
  COOKING_METHOD: [
    'Baked', 'Grilled', 'Fried', 'Roasted', 'Steamed', 'Sautéed',
    'Braised', 'Slow Cooked', 'Pressure Cooked', 'Raw', 'No-Cook'
  ],
  
  DIETARY: [
    'Vegetarian', 'Vegan', 'Gluten-Free', 'Dairy-Free', 'Low-Carb',
    'Keto', 'Paleo', 'High-Protein', 'Low-Sodium', 'Sugar-Free'
  ],
  
  OCCASION: [
    'Weeknight', 'Weekend', 'Holiday', 'Party', 'Date Night', 'Family',
    'Picnic', 'Potluck', 'Meal Prep', 'Quick & Easy'
  ]
};

export interface RecipeOrganization {
  category: string;
  subcategory?: string;
  cuisine: string;
  dishType: string;
  mealType: string;
  tags: string[];
  cookingMethod: string;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  dietaryTags: string[];
  occasionTags: string[];
}

export interface OrganizationSuggestion {
  field: keyof RecipeOrganization;
  value: string;
  confidence: number;
  reason: string;
}

/**
 * AI-Powered Recipe Organization Service
 * Automatically categorizes, tags, and organizes recipes for better discovery
 */
export class RecipeOrganizationService {

  /**
   * Automatically organize a recipe using AI analysis
   */
  static async organizeRecipe(recipe: Recipe): Promise<Recipe> {
    try {
      console.log(`🤖 AI organizing recipe: ${recipe.title}`);
      
      // Get AI-powered organization suggestions
      const organization = await this.getAIOrganization(recipe);
      
      // Apply the organization to the recipe
      const organizedRecipe: Recipe = {
        ...recipe,
        category: organization.category,
        subcategory: organization.subcategory,
        cuisine: organization.cuisine,
        dishType: organization.dishType,
        mealType: organization.mealType,
        difficulty: organization.difficulty,
        tags: this.mergeTags(recipe.tags || [], [
          ...organization.tags,
          ...organization.dietaryTags,
          ...organization.occasionTags,
          organization.cookingMethod
        ])
      };

      console.log(`✅ Recipe organized - Category: ${organization.category}, Cuisine: ${organization.cuisine}`);
      return organizedRecipe;
      
    } catch (error) {
      console.error('Error organizing recipe:', error);
      // Return recipe with basic organization fallback
      return {
        ...recipe,
        category: this.getBasicCategory(recipe),
        cuisine: this.getBasicCuisine(recipe),
        dishType: this.getBasicDishType(recipe),
        mealType: recipe.mealType || 'Dinner',
        difficulty: recipe.difficulty || 'Medium'
      };
    }
  }

  /**
   * Get AI-powered organization analysis
   */
  private static async getAIOrganization(recipe: Recipe): Promise<RecipeOrganization> {
    try {
      // Get AI analysis for organization
      const aiResult = await this.analyzeRecipeWithAI(recipe);
      
      // Structure the organization data
      return this.structureOrganization(aiResult, recipe);
      
    } catch (error) {
      console.error('AI organization failed, using fallback:', error);
      return this.getFallbackOrganization(recipe);
    }
  }

  /**
   * Analyze recipe with AI for organization data
   */
  private static async analyzeRecipeWithAI(recipe: Recipe): Promise<any> {
    try {
      // 🔥 Try secure Firebase AI service first (recommended)
      console.log('🔥 Using secure Firebase AI categorization...');
      // Fallback categorization based on ingredients
      const categorization = {
        primaryCategory: 'main',
        cuisine: 'International',
        dishType: 'Main Course',
        cookingMethod: 'Sautéed',
        difficulty: 'Medium' as const,
        dietaryTags: [],
        suggestedTags: []
      };
      
      console.log(`🎯 AI categorized "${recipe.title}":`, categorization);
      
      return {
        cuisine: categorization.cuisine,
        dishType: categorization.dishType,
        cookingMethod: categorization.cookingMethod,
        difficulty: categorization.difficulty,
        dietaryTags: categorization.dietaryTags,
        tags: categorization.suggestedTags,
        primaryCategory: categorization.primaryCategory
      };
      
    } catch (firebaseError) {
      console.log('🔄 Firebase AI failed, trying direct AI service...');
      
      try {
        // Fallback to direct AI service
        const categorization = await aiApiService.categorizeRecipeFromIngredients(recipe.ingredients);
        
        console.log(`🎯 Direct AI categorized "${recipe.title}":`, categorization);
        
        return {
          cuisine: categorization.cuisine,
          dishType: categorization.dishType,
          cookingMethod: categorization.cookingMethod,
          difficulty: categorization.difficulty,
          dietaryTags: categorization.dietaryTags,
          tags: categorization.suggestedTags,
          primaryCategory: categorization.primaryCategory
        };
        
      } catch (aiError) {
        console.log('🔄 All AI services failed, using pattern analysis fallback');
        
        // Final fallback to pattern-based analysis
        const suggestions = await aiApiService.getIngredientSuggestions(recipe.ingredients.join(', ')).catch(() => []);
        return this.inferOrganizationFromContent(recipe, suggestions);
      }
    }
  }

  /**
   * Infer organization from recipe content and AI suggestions
   */
  private static inferOrganizationFromContent(recipe: Recipe, aiSuggestions: string[]): any {
    const text = `${recipe.title} ${recipe.description} ${recipe.ingredients.join(' ')}`.toLowerCase();
    
    // Detect cuisine
    const cuisine = this.detectCuisine(text) || 'American';
    
    // Detect dish type
    const dishType = this.detectDishType(text) || 'Main Course';
    
    // Detect cooking method
    const cookingMethod = this.detectCookingMethod(text, recipe.instructions) || 'Cooked';
    
    // Detect dietary tags
    const dietaryTags = this.detectDietaryTags(text, recipe.ingredients);
    
    // Generate descriptive tags
    const tags = this.generateDescriptiveTags(recipe, aiSuggestions);
    
    return {
      cuisine,
      dishType,
      cookingMethod,
      difficulty: this.assessDifficulty(recipe),
      dietaryTags,
      tags
    };
  }

  /**
   * Detect cuisine from recipe content
   */
  private static detectCuisine(text: string): string | null {
    for (const cuisine of RECIPE_CATEGORIES.CUISINE) {
      if (text.includes(cuisine.toLowerCase()) || 
          this.getCuisineKeywords(cuisine).some(keyword => text.includes(keyword))) {
        return cuisine;
      }
    }
    return null;
  }

  /**
   * Get keywords associated with each cuisine
   */
  private static getCuisineKeywords(cuisine: string): string[] {
    const keywords: Record<string, string[]> = {
      'Italian': ['pasta', 'pizza', 'risotto', 'parmesan', 'basil', 'oregano', 'mozzarella'],
      'Asian': ['soy sauce', 'ginger', 'garlic', 'sesame', 'rice wine', 'wasabi'],
      'Mexican': ['tortilla', 'salsa', 'cilantro', 'lime', 'jalapeño', 'cumin', 'chili'],
      'Indian': ['curry', 'turmeric', 'cumin', 'coriander', 'garam masala', 'naan', 'basmati'],
      'Thai': ['coconut milk', 'lemongrass', 'fish sauce', 'lime leaves', 'thai basil'],
      'French': ['butter', 'cream', 'wine', 'herbs de provence', 'baguette', 'bourguignon'],
      'Mediterranean': ['olive oil', 'olives', 'feta', 'hummus', 'pita', 'tzatziki'],
      'Chinese': ['soy sauce', 'ginger', 'scallions', 'hoisin', 'bok choy'],
      'Japanese': ['miso', 'sake', 'mirin', 'nori', 'wasabi', 'dashi']
    };
    
    return keywords[cuisine] || [];
  }

  /**
   * Detect dish type from content
   */
  private static detectDishType(text: string): string | null {
    const dishKeywords: Record<string, string[]> = {
      'Soup': ['soup', 'broth', 'bisque', 'chowder', 'stew'],
      'Salad': ['salad', 'greens', 'lettuce', 'mixed greens'],
      'Pasta': ['pasta', 'spaghetti', 'penne', 'fettuccine', 'linguine'],
      'Pizza': ['pizza', 'flatbread', 'dough'],
      'Sandwich': ['sandwich', 'burger', 'wrap', 'panini'],
      'Dessert': ['dessert', 'cake', 'cookie', 'pie', 'tart', 'sweet', 'chocolate'],
      'Appetizer': ['appetizer', 'starter', 'dip', 'bite', 'canapé']
    };

    for (const [dishType, keywords] of Object.entries(dishKeywords)) {
      if (keywords.some(keyword => text.includes(keyword))) {
        return dishType;
      }
    }
    return null;
  }

  /**
   * Detect cooking method
   */
  private static detectCookingMethod(text: string, instructions: string[]): string | null {
    const instructionText = instructions.join(' ').toLowerCase();
    const allText = `${text} ${instructionText}`;

    const methodKeywords: Record<string, string[]> = {
      'Baked': ['bake', 'oven', 'baking', 'roast'],
      'Grilled': ['grill', 'barbecue', 'bbq', 'char'],
      'Fried': ['fry', 'deep fry', 'pan fry', 'sauté'],
      'Steamed': ['steam', 'steamer'],
      'Slow Cooked': ['slow cook', 'crock pot', 'slow cooker'],
      'No-Cook': ['no cook', 'raw', 'fresh', 'chilled']
    };

    for (const [method, keywords] of Object.entries(methodKeywords)) {
      if (keywords.some(keyword => allText.includes(keyword))) {
        return method;
      }
    }
    return null;
  }

  /**
   * Detect dietary tags
   */
  private static detectDietaryTags(text: string, ingredients: string[]): string[] {
    const tags: string[] = [];
    const ingredientText = ingredients.join(' ').toLowerCase();
    const allText = `${text} ${ingredientText}`;

    // Check for dietary indicators
    if (!allText.includes('meat') && !allText.includes('chicken') && !allText.includes('beef') && 
        !allText.includes('pork') && !allText.includes('fish')) {
      tags.push('Vegetarian');
    }

    if (!allText.includes('dairy') && !allText.includes('milk') && !allText.includes('cheese') && 
        !allText.includes('butter') && !allText.includes('cream')) {
      tags.push('Dairy-Free');
    }

    if (allText.includes('gluten-free') || allText.includes('gluten free')) {
      tags.push('Gluten-Free');
    }

    if (allText.includes('vegan')) {
      tags.push('Vegan');
    }

    if (allText.includes('low-carb') || allText.includes('keto')) {
      tags.push('Low-Carb');
    }

    return tags;
  }

  /**
   * Generate descriptive tags
   */
  private static generateDescriptiveTags(recipe: Recipe, aiSuggestions: string[]): string[] {
    const tags: string[] = [];
    
    // Time-based tags
    const timeMinutes = this.extractTimeMinutes(recipe.cookingTime);
    if (timeMinutes && timeMinutes <= 20) {
      tags.push('Quick', 'Easy');
    } else if (timeMinutes && timeMinutes <= 30) {
      tags.push('Quick');
    }

    // Serving size tags
    if (recipe.servings >= 6) {
      tags.push('Family Size', 'Crowd Pleaser');
    } else if (recipe.servings <= 2) {
      tags.push('For Two', 'Date Night');
    }

    // Add some AI-suggested ingredients as tags
    if (aiSuggestions.length > 0) {
      tags.push(...aiSuggestions.slice(0, 2));
    }

    // Add nutritional tags if available
    if (recipe.nutritionInfo) {
      if (recipe.nutritionInfo.calories < 300) {
        tags.push('Low Calorie');
      }
      if (recipe.nutritionInfo.protein > 20) {
        tags.push('High Protein');
      }
    }

    tags.push('Homemade', 'Delicious');

    return tags;
  }

  /**
   * Assess recipe difficulty
   */
  private static assessDifficulty(recipe: Recipe): 'Easy' | 'Medium' | 'Hard' {
    if (recipe.difficulty) {
      return recipe.difficulty as 'Easy' | 'Medium' | 'Hard';
    }

    let difficultyScore = 0;

    // More ingredients = harder
    if (recipe.ingredients.length > 10) difficultyScore += 1;
    if (recipe.ingredients.length > 15) difficultyScore += 1;

    // More steps = harder
    if (recipe.instructions.length > 8) difficultyScore += 1;
    if (recipe.instructions.length > 12) difficultyScore += 1;

    // Longer cooking time = harder
    const timeMinutes = this.extractTimeMinutes(recipe.cookingTime);
    if (timeMinutes && timeMinutes > 60) difficultyScore += 1;

    if (difficultyScore <= 1) return 'Easy';
    if (difficultyScore <= 3) return 'Medium';
    return 'Hard';
  }

  /**
   * Structure the organization data
   */
  private static structureOrganization(aiResult: any, recipe: Recipe): RecipeOrganization {
    // Determine primary category based on cuisine and dish type
    const category = this.determinePrimaryCategory(aiResult.cuisine, aiResult.dishType, recipe);
    
    return {
      category,
      subcategory: this.determineSubcategory(aiResult.dishType, aiResult.cookingMethod),
      cuisine: aiResult.cuisine || 'American',
      dishType: aiResult.dishType || 'Main Course',
      mealType: this.determineMealType(recipe, aiResult.dishType),
      tags: aiResult.tags || [],
      cookingMethod: aiResult.cookingMethod || 'Cooked',
      difficulty: aiResult.difficulty || 'Medium',
      dietaryTags: aiResult.dietaryTags || [],
      occasionTags: this.determineOccasionTags(recipe, aiResult)
    };
  }

  /**
   * Determine primary category for recipe
   */
  private static determinePrimaryCategory(cuisine: string, dishType: string, recipe: Recipe): string {
    // If it's a dessert, prioritize that
    if (dishType === 'Dessert' || recipe.title.toLowerCase().includes('dessert') || 
        recipe.title.toLowerCase().includes('cake') || recipe.title.toLowerCase().includes('cookie')) {
      return 'Desserts';
    }
    
    // If cooking time is very short, it might be quick meals
    const timeMinutes = this.extractTimeMinutes(recipe.cookingTime);
    if (timeMinutes && timeMinutes <= 20) {
      return 'Quick Meals';
    }
    
    // If it has health-related keywords, classify as healthy
    const healthKeywords = ['healthy', 'low-fat', 'low-carb', 'fresh', 'light'];
    const recipeText = `${recipe.title} ${recipe.description}`.toLowerCase();
    if (healthKeywords.some(keyword => recipeText.includes(keyword))) {
      return 'Healthy';
    }
    
    // Otherwise use cuisine as primary category
    return cuisine || 'International';
  }

  /**
   * Determine subcategory
   */
  private static determineSubcategory(dishType: string, cookingMethod: string): string {
    if (dishType === 'Main Course') {
      return cookingMethod;
    }
    return dishType;
  }

  /**
   * Determine meal type from context
   */
  private static determineMealType(recipe: Recipe, dishType: string): string {
    if (recipe.mealType) return recipe.mealType;
    
    const title = recipe.title.toLowerCase();
    
    if (title.includes('breakfast') || title.includes('morning')) return 'Breakfast';
    if (title.includes('lunch') || title.includes('sandwich')) return 'Lunch';
    if (title.includes('dinner') || title.includes('main')) return 'Dinner';
    if (dishType === 'Dessert') return 'Dessert';
    if (dishType === 'Appetizer') return 'Appetizer';
    
    return 'Dinner'; // Default
  }

  /**
   * Determine occasion tags
   */
  private static determineOccasionTags(recipe: Recipe, aiResult: any): string[] {
    const tags: string[] = [];
    const timeMinutes = this.extractTimeMinutes(recipe.cookingTime);
    
    if (timeMinutes && timeMinutes <= 30) {
      tags.push('Quick & Easy', 'Weeknight');
    }
    
    if (recipe.servings && recipe.servings >= 6) {
      tags.push('Family', 'Party');
    }
    
    return tags;
  }

  /**
   * Extract time in minutes from cooking time string
   */
  private static extractTimeMinutes(cookingTime: string): number | null {
    const timeStr = cookingTime.toLowerCase();
    const minuteMatch = timeStr.match(/(\d+)\s*min/);
    const hourMatch = timeStr.match(/(\d+)\s*hour/);
    
    if (minuteMatch) return parseInt(minuteMatch[1]);
    if (hourMatch) return parseInt(hourMatch[1]) * 60;
    
    return null;
  }

  /**
   * Get fallback organization when AI fails
   */
  private static getFallbackOrganization(recipe: Recipe): RecipeOrganization {
    return {
      category: this.getBasicCategory(recipe),
      cuisine: this.getBasicCuisine(recipe),
      dishType: this.getBasicDishType(recipe),
      mealType: recipe.mealType || 'Dinner',
      tags: ['Homemade'],
      cookingMethod: 'Cooked',
      difficulty: 'Medium',
      dietaryTags: [],
      occasionTags: []
    };
  }

  /**
   * Basic category detection from recipe content
   */
  private static getBasicCategory(recipe: Recipe): string {
    const text = `${recipe.title} ${recipe.description}`.toLowerCase();
    
    if (text.includes('dessert') || text.includes('cake') || text.includes('cookie')) {
      return 'Desserts';
    }
    if (text.includes('healthy') || text.includes('salad')) {
      return 'Healthy';
    }
    if (text.includes('quick') || text.includes('easy')) {
      return 'Quick Meals';
    }
    
    return 'Main Dishes';
  }

  /**
   * Basic cuisine detection
   */
  private static getBasicCuisine(recipe: Recipe): string {
    const text = `${recipe.title} ${recipe.ingredients.join(' ')}`.toLowerCase();
    
    // Check for cuisine indicators in title and ingredients
    if (text.includes('pasta') || text.includes('italian')) return 'Italian';
    if (text.includes('curry') || text.includes('indian')) return 'Indian';
    if (text.includes('stir') || text.includes('soy sauce')) return 'Asian';
    if (text.includes('taco') || text.includes('mexican')) return 'Mexican';
    
    return 'American';
  }

  /**
   * Basic dish type detection
   */
  private static getBasicDishType(recipe: Recipe): string {
    const text = recipe.title.toLowerCase();
    
    if (text.includes('soup')) return 'Soup';
    if (text.includes('salad')) return 'Salad';
    if (text.includes('dessert') || text.includes('cake')) return 'Dessert';
    if (text.includes('appetizer') || text.includes('starter')) return 'Appetizer';
    
    return 'Main Course';
  }

  /**
   * Merge and deduplicate tags
   */
  private static mergeTags(existingTags: string[], newTags: string[]): string[] {
    const allTags = [...existingTags, ...newTags];
    return Array.from(new Set(allTags)).filter(tag => tag && tag.trim());
  }

  /**
   * Organize a collection of recipes
   */
  static async organizeRecipeCollection(recipes: Recipe[]): Promise<Recipe[]> {
    console.log(`🗂️ Organizing ${recipes.length} recipes...`);
    
    const organizedRecipes: Recipe[] = [];
    
    // Process recipes in batches to avoid overwhelming the AI service
    const batchSize = 5;
    for (let i = 0; i < recipes.length; i += batchSize) {
      const batch = recipes.slice(i, i + batchSize);
      
      const organizedBatch = await Promise.all(
        batch.map(recipe => this.organizeRecipe(recipe))
      );
      
      organizedRecipes.push(...organizedBatch);
      
      // Small delay between batches
      if (i + batchSize < recipes.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    console.log(`✅ Organized ${organizedRecipes.length} recipes!`);
    return organizedRecipes;
  }

  /**
   * Get recipe organization statistics
   */
  static getOrganizationStats(recipes: Recipe[]): {
    categories: Record<string, number>;
    cuisines: Record<string, number>;
    dishTypes: Record<string, number>;
    totalRecipes: number;
  } {
    const stats = {
      categories: {} as Record<string, number>,
      cuisines: {} as Record<string, number>,
      dishTypes: {} as Record<string, number>,
      totalRecipes: recipes.length
    };

    recipes.forEach(recipe => {
      // Count categories
      if (recipe.category) {
        stats.categories[recipe.category] = (stats.categories[recipe.category] || 0) + 1;
      }
      
      // Count cuisines
      if (recipe.cuisine) {
        stats.cuisines[recipe.cuisine] = (stats.cuisines[recipe.cuisine] || 0) + 1;
      }
      
      // Count dish types
      if (recipe.dishType) {
        stats.dishTypes[recipe.dishType] = (stats.dishTypes[recipe.dishType] || 0) + 1;
      }
    });

    return stats;
  }

  /**
   * Suggest recipe organization improvements
   */
  static async suggestOrganizationImprovements(recipes: Recipe[]): Promise<OrganizationSuggestion[]> {
    const suggestions: OrganizationSuggestion[] = [];
    
    // Find recipes that need better organization
    const needsOrganization = recipes.filter(recipe => 
      !recipe.category || !recipe.cuisine || !recipe.dishType || 
      !recipe.tags || recipe.tags.length < 2
    );

    if (needsOrganization.length > 0) {
      suggestions.push({
        field: 'category',
        value: 'Auto-organize',
        confidence: 0.9,
        reason: `${needsOrganization.length} recipes need better categorization`
      });
    }

    // Check for missing cuisine information
    const missingCuisine = recipes.filter(recipe => !recipe.cuisine);
    if (missingCuisine.length > 0) {
      suggestions.push({
        field: 'cuisine',
        value: 'Auto-detect',
        confidence: 0.8,
        reason: `${missingCuisine.length} recipes missing cuisine classification`
      });
    }

    return suggestions;
  }
} 