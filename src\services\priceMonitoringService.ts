// TEMPORARILY DISABLED: NotificationService causing issues
import { NotificationService, PriceAlert } from './notificationService';
import { Product } from '../context/ShoppingContext';

export interface PriceHistory {
  productId: string;
  store: string;
  price: number;
  date: Date;
}

export interface PriceMonitoringPreferences {
  budgetMode: 'budgeting' | 'bulk_buying' | 'convenience';
  priceDropThreshold: number; // Percentage drop to trigger alert
  checkFrequency: 'hourly' | 'daily' | 'weekly';
  notificationPreferences: {
    priceDrops: boolean;
    saleAlerts: boolean;
    weeklyDigest: boolean;
  };
}

export class PriceMonitoringService {
  private static instance: PriceMonitoringService;
  private notificationService: NotificationService;
  private priceAlerts: Map<string, PriceAlert> = new Map();
  private priceHistory: PriceHistory[] = [];
  private monitoringInterval: NodeJS.Timeout | null = null;

  public static getInstance(): PriceMonitoringService {
    if (!PriceMonitoringService.instance) {
      PriceMonitoringService.instance = new PriceMonitoringService();
    }
    return PriceMonitoringService.instance;
  }

  constructor() {
    this.notificationService = NotificationService.getInstance();
  }

  async initialize(): Promise<void> {
    await this.notificationService.initialize();
    console.log('✅ Price monitoring service initialized');
  }

  async createPriceAlert(
    productId: string,
    productName: string,
    targetPrice: number,
    currentPrice: number,
    store: string,
    userId: string
  ): Promise<string> {
    const alertId = `alert-${productId}-${store}-${Date.now()}`;
    
    const alert: PriceAlert = {
      id: alertId,
      productId,
      productName,
      targetPrice,
      currentPrice,
      store,
      userId,
      isActive: true,
      createdAt: new Date(),
    };

    this.priceAlerts.set(alertId, alert);
    
    console.log(`🎯 Price alert created: ${productName} at ${store} for $${targetPrice}`);
    
    // Check if current price is already below target
    if (currentPrice <= targetPrice) {
      await this.notificationService.sendPriceDropAlert(alert);
    }

    return alertId;
  }

  async updatePriceAlert(alertId: string, newTargetPrice: number): Promise<boolean> {
    const alert = this.priceAlerts.get(alertId);
    if (!alert) {
      console.error(`❌ Price alert not found: ${alertId}`);
      return false;
    }

    alert.targetPrice = newTargetPrice;
    this.priceAlerts.set(alertId, alert);
    
    console.log(`✅ Price alert updated: ${alert.productName} new target $${newTargetPrice}`);
    return true;
  }

  async deletePriceAlert(alertId: string): Promise<boolean> {
    const alert = this.priceAlerts.get(alertId);
    if (!alert) {
      console.error(`❌ Price alert not found: ${alertId}`);
      return false;
    }

    this.priceAlerts.delete(alertId);
    console.log(`✅ Price alert deleted: ${alert.productName}`);
    return true;
  }

  async getPriceAlerts(userId: string): Promise<PriceAlert[]> {
    return Array.from(this.priceAlerts.values()).filter(alert => 
      alert.userId === userId && alert.isActive
    );
  }

  async recordPriceHistory(productId: string, store: string, price: number): Promise<void> {
    const historyEntry: PriceHistory = {
      productId,
      store,
      price,
      date: new Date(),
    };

    this.priceHistory.push(historyEntry);
    
    // Keep only last 100 entries per product per store
    this.priceHistory = this.priceHistory
      .filter(entry => entry.productId === productId && entry.store === store)
      .slice(-100)
      .concat(
        this.priceHistory.filter(entry => 
          !(entry.productId === productId && entry.store === store)
        )
      );
  }

  async checkPriceChanges(products: Product[]): Promise<void> {
    console.log('🔍 Checking price changes...');

    for (const product of products) {
      // Record current price in history
      await this.recordPriceHistory(product.id, product.store, product.price);

      // Check all alerts for this product
      const productAlerts = Array.from(this.priceAlerts.values()).filter(alert => 
        alert.productId === product.id && alert.isActive
      );

      for (const alert of productAlerts) {
        const oldPrice = alert.currentPrice;
        const newPrice = product.price;

        // Update alert with new price
        alert.currentPrice = newPrice;
        this.priceAlerts.set(alert.id, alert);

        // Check if price dropped below target
        if (newPrice <= alert.targetPrice && oldPrice > alert.targetPrice) {
          await this.notificationService.sendPriceDropAlert(alert);
          console.log(`🎉 Price drop alert triggered: ${alert.productName} at ${alert.store}`);
        }
      }
    }

    console.log(`✅ Price check completed for ${products.length} products`);
  }

  async findBestDeals(products: Product[], threshold: number = 0.15): Promise<Product[]> {
    const deals: Product[] = [];

    for (const product of products) {
      const history = this.priceHistory.filter(entry => 
        entry.productId === product.id && entry.store === product.store
      );

      if (history.length > 0) {
        const recentPrices = history.slice(-10); // Last 10 price points
        const avgPrice = recentPrices.reduce((sum, entry) => sum + entry.price, 0) / recentPrices.length;
        
        const priceDropPercentage = (avgPrice - product.price) / avgPrice;
        
        if (priceDropPercentage >= threshold) {
          deals.push(product);
        }
      }
    }

    return deals.sort((a, b) => {
      const aHistory = this.priceHistory.filter(entry => 
        entry.productId === a.id && entry.store === a.store
      );
      const bHistory = this.priceHistory.filter(entry => 
        entry.productId === b.id && entry.store === b.store
      );

      if (aHistory.length === 0 || bHistory.length === 0) return 0;

      const aAvgPrice = aHistory.slice(-10).reduce((sum, entry) => sum + entry.price, 0) / Math.min(aHistory.length, 10);
      const bAvgPrice = bHistory.slice(-10).reduce((sum, entry) => sum + entry.price, 0) / Math.min(bHistory.length, 10);

      const aDropPercentage = (aAvgPrice - a.price) / aAvgPrice;
      const bDropPercentage = (bAvgPrice - b.price) / bAvgPrice;

      return bDropPercentage - aDropPercentage; // Sort by biggest drop first
    });
  }

  async startMonitoring(products: Product[], intervalMinutes: number = 60): Promise<void> {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }

    console.log(`🚀 Starting price monitoring with ${intervalMinutes}min interval`);

    this.monitoringInterval = setInterval(async () => {
      await this.checkPriceChanges(products);
    }, intervalMinutes * 60 * 1000);

    // Initial check
    await this.checkPriceChanges(products);
  }

  async stopMonitoring(): Promise<void> {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
      console.log('⏹️ Price monitoring stopped');
    }
  }

  async generatePriceReport(userId: string): Promise<{
    totalAlerts: number;
    activeAlerts: number;
    triggeredAlerts: number;
    avgSavings: number;
    bestDeals: Product[];
  }> {
    const userAlerts = await this.getPriceAlerts(userId);
    const triggeredAlerts = userAlerts.filter(alert => alert.currentPrice <= alert.targetPrice);
    
    const avgSavings = triggeredAlerts.reduce((sum, alert) => {
      return sum + (alert.targetPrice - alert.currentPrice);
    }, 0) / triggeredAlerts.length || 0;

    return {
      totalAlerts: userAlerts.length,
      activeAlerts: userAlerts.filter(alert => alert.isActive).length,
      triggeredAlerts: triggeredAlerts.length,
      avgSavings,
      bestDeals: [], // Would be populated with actual product data
    };
  }

  // Mock method for testing
  async simulatePriceChanges(products: Product[]): Promise<void> {
    console.log('🧪 Simulating price changes...');

    const updatedProducts = products.map(product => ({
      ...product,
      price: product.price * (0.8 + Math.random() * 0.4), // Random price change ±20%
    }));

    await this.checkPriceChanges(updatedProducts);
    console.log('✅ Price simulation completed');
  }

  // Mock method for testing notifications
  async testPriceMonitoring(): Promise<void> {
    console.log('🧪 Testing price monitoring...');

    // Create mock products
    const mockProducts: Product[] = [
      {
        id: 'milk-2l-woolworths',
        name: 'Milk 2L',
        price: 4.50,
        store: 'woolworths',
        unit: '2L',
        category: 'dairy',
        brand: 'Anchor',
        image_url: '',
        availability: 'in_stock',
      },
      {
        id: 'bread-white-newworld',
        name: 'White Bread',
        price: 2.99,
        store: 'newworld',
        unit: '800g',
        category: 'bakery',
        brand: 'Tip Top',
        image_url: '',
        availability: 'in_stock',
      },
    ];

    // Create price alerts
    await this.createPriceAlert(
      'milk-2l-woolworths',
      'Milk 2L',
      4.00,
      4.50,
      'woolworths',
      'test-user'
    );

    await this.createPriceAlert(
      'bread-white-newworld',
      'White Bread',
      2.50,
      2.99,
      'newworld',
      'test-user'
    );

    // Simulate price drops
    setTimeout(async () => {
      const updatedProducts = mockProducts.map(product => ({
        ...product,
        price: product.price * 0.85, // 15% price drop
      }));

      await this.checkPriceChanges(updatedProducts);
    }, 3000);

    console.log('✅ Price monitoring test completed');
  }
}