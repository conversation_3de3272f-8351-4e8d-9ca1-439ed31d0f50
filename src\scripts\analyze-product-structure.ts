/**
 * PRODUCT STRUCTURE ANALYSIS
 * 
 * This script analyzes the actual product data structure in Supabase
 * to understand naming patterns, brand formats, and size variations
 * across all three New Zealand supermarket chains.
 */

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://emjwniuqwhvohjassnrg.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVtanduaXVxd2h2b2hqYXNzbnJnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjQwMTYwNSwiZXhwIjoyMDY3OTc3NjA1fQ.oTowRoNAjlUmk10O9pSMK2BvL0XlyBb5orVRrlEryHs';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

interface ProductAnalysis {
  store: string;
  totalProducts: number;
  sampleProducts: any[];
  brandPatterns: string[];
  sizePatterns: string[];
  categoryPatterns: string[];
  namePatterns: string[];
}

async function analyzeProductStructure() {
  console.log('🔍 Analyzing Product Data Structure Across All Stores...\n');

  const stores = ['woolworths', 'newworld', 'paknsave'];
  const analysis: ProductAnalysis[] = [];

  for (const store of stores) {
    console.log(`📊 Analyzing ${store.toUpperCase()} products...`);

    // Get sample products from this store
    const { data: products, error } = await supabase
      .from('products')
      .select('*')
      .eq('store', store)
      .not('price', 'is', null)
      .limit(200); // Get good sample size

    if (error) {
      console.error(`   ❌ Error fetching ${store} products:`, error);
      continue;
    }

    if (!products || products.length === 0) {
      console.log(`   ⚠️  No products found for ${store}`);
      continue;
    }

    console.log(`   ✅ Analyzing ${products.length} sample products`);

    // Analyze naming patterns
    const namePatterns = products.slice(0, 20).map(p => p.name);
    
    // Analyze brand patterns
    const brands = products
      .map(p => p.brand)
      .filter(Boolean)
      .slice(0, 20);

    // Analyze size patterns (extract from names and descriptions)
    const sizePatterns = products
      .map(p => {
        const text = `${p.name} ${p.description || ''}`.toLowerCase();
        const sizeMatches = text.match(/\d+(\.\d+)?\s*(ml|l|litre|liter|g|kg|gram|kilogram|oz|lb|pack|pk|ea|each|count|ct)/gi);
        return sizeMatches;
      })
      .filter(Boolean)
      .flat()
      .slice(0, 20);

    // Analyze categories
    const categories = products
      .map(p => p.category)
      .filter(Boolean)
      .slice(0, 20);

    analysis.push({
      store,
      totalProducts: products.length,
      sampleProducts: products.slice(0, 10),
      brandPatterns: brands,
      sizePatterns: sizePatterns || [],
      categoryPatterns: categories,
      namePatterns
    });

    // Show sample products
    console.log(`   📝 Sample products from ${store}:`);
    products.slice(0, 5).forEach((product, index) => {
      console.log(`      ${index + 1}. "${product.name}" | Brand: "${product.brand || 'N/A'}" | Category: "${product.category || 'N/A'}" | $${product.price}`);
    });
    console.log();
  }

  // Cross-store analysis for potential duplicates
  console.log('🔍 Cross-Store Duplicate Analysis...\n');
  
  // Look for similar product names across stores
  const allProducts = analysis.flatMap(a => a.sampleProducts);
  const potentialDuplicates: any[] = [];

  for (let i = 0; i < allProducts.length; i++) {
    for (let j = i + 1; j < allProducts.length; j++) {
      const product1 = allProducts[i];
      const product2 = allProducts[j];

      if (product1.store === product2.store) continue; // Skip same store

      // Simple similarity check
      const name1 = product1.name.toLowerCase();
      const name2 = product2.name.toLowerCase();
      
      // Check for common words
      const words1 = name1.split(/\s+/);
      const words2 = name2.split(/\s+/);
      const commonWords = words1.filter(word => words2.includes(word) && word.length > 2);

      if (commonWords.length >= 2) {
        potentialDuplicates.push({
          product1: { name: product1.name, store: product1.store, brand: product1.brand, price: product1.price },
          product2: { name: product2.name, store: product2.store, brand: product2.brand, price: product2.price },
          commonWords,
          similarity: commonWords.length / Math.max(words1.length, words2.length)
        });
      }
    }
  }

  console.log(`🎯 Found ${potentialDuplicates.length} potential duplicate pairs:`);
  potentialDuplicates.slice(0, 10).forEach((dup, index) => {
    console.log(`   ${index + 1}. "${dup.product1.name}" (${dup.product1.store}) vs "${dup.product2.name}" (${dup.product2.store})`);
    console.log(`      Common words: [${dup.commonWords.join(', ')}] | Similarity: ${(dup.similarity * 100).toFixed(1)}%`);
    console.log(`      Prices: $${dup.product1.price} vs $${dup.product2.price}`);
    console.log();
  });

  return analysis;
}

// Run the analysis
if (require.main === module) {
  analyzeProductStructure()
    .then((analysis) => {
      console.log('\n📊 PRODUCT STRUCTURE ANALYSIS SUMMARY:');
      console.log('='.repeat(60));
      
      analysis.forEach(store => {
        console.log(`\n${store.store.toUpperCase()}:`);
        console.log(`  Sample Size: ${store.totalProducts} products`);
        console.log(`  Brand Examples: ${store.brandPatterns.slice(0, 5).join(', ')}`);
        console.log(`  Size Examples: ${store.sizePatterns.slice(0, 5).join(', ')}`);
        console.log(`  Category Examples: ${store.categoryPatterns.slice(0, 5).join(', ')}`);
      });
      
      console.log('\n🎯 KEY INSIGHTS FOR DEDUPLICATION:');
      console.log('  - Product names vary significantly across stores');
      console.log('  - Size formats need normalization (ml/l, g/kg variations)');
      console.log('  - Brand matching will be crucial for accuracy');
      console.log('  - Category matching can help reduce false positives');
      console.log('='.repeat(60));
    })
    .catch((error) => {
      console.error('❌ Analysis failed:', error);
    });
}

export { analyzeProductStructure };
