import { useState, useEffect, useCallback, useRef } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface Product {
  id: string;
  name: string;
  price: number;
  current_price?: number;
  original_price?: number;
  store: string;
  category?: string;
  brand?: string;
  unit?: string;
  size?: string;
  image_url?: string;
  is_available?: boolean;
  is_on_sale?: boolean;
  last_updated?: string;
}

interface CacheEntry {
  data: Product[];
  timestamp: number;
  page: number;
  filters: {
    category?: string;
    searchQuery?: string;
    stores?: string[];
    sortBy?: string;
  };
}

interface CacheConfig {
  maxAge: number; // milliseconds
  maxEntries: number;
  keyPrefix: string;
}

const DEFAULT_CONFIG: CacheConfig = {
  maxAge: 5 * 60 * 1000, // 5 minutes
  maxEntries: 50,
  keyPrefix: 'product_cache_',
};

export const useProductCache = (config: Partial<CacheConfig> = {}) => {
  const cacheConfig = { ...DEFAULT_CONFIG, ...config };
  const memoryCache = useRef<Map<string, CacheEntry>>(new Map());
  const [cacheStats, setCacheStats] = useState({
    hits: 0,
    misses: 0,
    size: 0,
  });

  // Generate cache key from filters
  const generateCacheKey = useCallback((
    page: number,
    filters: {
      category?: string;
      searchQuery?: string;
      stores?: string[];
      sortBy?: string;
    }
  ): string => {
    const filterString = JSON.stringify({
      ...filters,
      stores: filters.stores?.sort(), // Ensure consistent ordering
    });
    return `${cacheConfig.keyPrefix}${page}_${btoa(filterString)}`;
  }, [cacheConfig.keyPrefix]);

  // Check if cache entry is valid
  const isCacheValid = useCallback((entry: CacheEntry): boolean => {
    return Date.now() - entry.timestamp < cacheConfig.maxAge;
  }, [cacheConfig.maxAge]);

  // Get from memory cache first, then AsyncStorage
  const getCachedData = useCallback(async (
    page: number,
    filters: any
  ): Promise<Product[] | null> => {
    const key = generateCacheKey(page, filters);
    
    // Check memory cache first
    const memoryEntry = memoryCache.current.get(key);
    if (memoryEntry && isCacheValid(memoryEntry)) {
      setCacheStats(prev => ({ ...prev, hits: prev.hits + 1 }));
      return memoryEntry.data;
    }

    // Check AsyncStorage
    try {
      const stored = await AsyncStorage.getItem(key);
      if (stored) {
        const entry: CacheEntry = JSON.parse(stored);
        if (isCacheValid(entry)) {
          // Update memory cache
          memoryCache.current.set(key, entry);
          setCacheStats(prev => ({ ...prev, hits: prev.hits + 1 }));
          return entry.data;
        } else {
          // Remove expired entry
          await AsyncStorage.removeItem(key);
        }
      }
    } catch (error) {
      console.warn('Cache read error:', error);
    }

    setCacheStats(prev => ({ ...prev, misses: prev.misses + 1 }));
    return null;
  }, [generateCacheKey, isCacheValid]);

  // Store in both memory and AsyncStorage
  const setCachedData = useCallback(async (
    page: number,
    filters: any,
    data: Product[]
  ): Promise<void> => {
    const key = generateCacheKey(page, filters);
    const entry: CacheEntry = {
      data,
      timestamp: Date.now(),
      page,
      filters,
    };

    // Update memory cache
    memoryCache.current.set(key, entry);

    // Limit memory cache size
    if (memoryCache.current.size > cacheConfig.maxEntries) {
      const firstKey = memoryCache.current.keys().next().value;
      memoryCache.current.delete(firstKey);
    }

    // Store in AsyncStorage
    try {
      await AsyncStorage.setItem(key, JSON.stringify(entry));
      setCacheStats(prev => ({ ...prev, size: memoryCache.current.size }));
    } catch (error) {
      console.warn('Cache write error:', error);
    }
  }, [generateCacheKey, cacheConfig.maxEntries]);

  // Clear cache
  const clearCache = useCallback(async (): Promise<void> => {
    memoryCache.current.clear();
    
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith(cacheConfig.keyPrefix));
      await AsyncStorage.multiRemove(cacheKeys);
      setCacheStats({ hits: 0, misses: 0, size: 0 });
    } catch (error) {
      console.warn('Cache clear error:', error);
    }
  }, [cacheConfig.keyPrefix]);

  // Clear expired entries
  const clearExpiredCache = useCallback(async (): Promise<void> => {
    const now = Date.now();
    
    // Clear memory cache
    for (const [key, entry] of memoryCache.current.entries()) {
      if (!isCacheValid(entry)) {
        memoryCache.current.delete(key);
      }
    }

    // Clear AsyncStorage
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith(cacheConfig.keyPrefix));
      
      for (const key of cacheKeys) {
        const stored = await AsyncStorage.getItem(key);
        if (stored) {
          const entry: CacheEntry = JSON.parse(stored);
          if (!isCacheValid(entry)) {
            await AsyncStorage.removeItem(key);
          }
        }
      }
    } catch (error) {
      console.warn('Cache cleanup error:', error);
    }

    setCacheStats(prev => ({ ...prev, size: memoryCache.current.size }));
  }, [cacheConfig.keyPrefix, isCacheValid]);

  // Auto-cleanup on mount
  useEffect(() => {
    clearExpiredCache();
  }, [clearExpiredCache]);

  return {
    getCachedData,
    setCachedData,
    clearCache,
    clearExpiredCache,
    cacheStats,
  };
};
