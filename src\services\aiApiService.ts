import { Recipe, UserPreferences, ShoppingItem } from '../utils/storage';
import { API_CONFIG, isGeminiConfigured, getConfigurationStatus } from '../constants/config';

// Type definitions for removed services
export interface RecipeGenerationParams {
  prompt: string;
  userPreferences?: UserPreferences;
  dietaryRestrictions?: string[];
  cuisineStyle?: string;
  mealType?: string;
  cookingTime?: string;
  difficulty?: string;
}

export interface BulkRecipeGenerationParams extends RecipeGenerationParams {
  count: number;
  variations?: string[];
}

export interface AlgoliaIngredientResult {
  name: string;
  category: string;
  alternatives?: string[];
}

// Re-export types for convenience (no duplicates)

// Fallback functions
const getRandomCookingTip = (): string => {
  const tips = [
    'Season your food at every step of the cooking process for better flavor.',
    'Let meat rest after cooking to allow juices to redistribute.',
    'Taste as you go - cooking is about adjusting flavors.',
    'Mise en place: prepare all ingredients before you start cooking.',
    'Use a meat thermometer to ensure proper doneness.'
  ];
  return tips[Math.floor(Math.random() * tips.length)];
};

const generateFallbackRecipe = (params: RecipeGenerationParams): Recipe => {
  return {
    id: `fallback-${Date.now()}`,
    title: 'Simple Recipe',
    description: 'A basic recipe generated as fallback',
    ingredients: ['1 cup ingredient 1', '2 tbsp ingredient 2'],
    instructions: ['Step 1: Prepare ingredients', 'Step 2: Cook as desired'],
    cookingTime: '30 minutes',
    difficulty: 'Easy',
    servings: 4,
    mealType: 'Dinner',
    tags: ['Easy', 'Quick'],
    category: 'main',
    cuisine: 'International',
    dishType: 'Main Course',
    createdAt: new Date().toISOString(),
    nutritionInfo: { calories: 300, protein: 20, carbs: 30, fat: 10, fiber: 5 }
  };
};

// AI assistance service interface (no recipe generation)
export interface AIAssistanceService {
  getCookingTip: () => Promise<string>;
  improveRecipe: (recipe: Recipe, improvement: string) => Promise<Recipe>;
  searchIngredients: (query: string) => Promise<AlgoliaIngredientResult[]>;
  getPopularIngredients: () => Promise<string[]>;
  getIngredientSuggestions: (partialQuery: string) => Promise<string[]>;
  suggestRecipeTitle: (ingredients: string[]) => Promise<string>;
  suggestInstructions: (ingredients: string[], title: string) => Promise<string[]>;
  estimateNutrition: (ingredients: string[], servings: number) => Promise<{ calories: number; protein: number; carbs: number; fat: number; fiber: number; }>;
  categorizeRecipeFromIngredients: (ingredients: string[]) => Promise<{
    primaryCategory: string;
    cuisine: string;
    dishType: string;
    cookingMethod: string;
    difficulty: 'Easy' | 'Medium' | 'Hard';
    dietaryTags: string[];
    suggestedTags: string[];
  }>;
}

// Configuration-aware error classes
export class ConfigurationError extends Error {
  constructor(message: string, public serviceName: string) {
    super(message);
    this.name = 'ConfigurationError';
  }
}

export class AIServiceError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'AIServiceError';
  }
}

// Main AI assistance service (no recipe generation)
export const aiApiService: AIAssistanceService = {

  /**
   * Get a cooking tip using AI services
   */
  getCookingTip: async (): Promise<string> => {
    try {
      if (isGeminiConfigured()) {
        // Gemini service disabled - using fallback
        return getRandomCookingTip();
      } else {
        console.log('⚠️ Using offline cooking tips (Gemini not configured)');
        return getRandomCookingTip();
      }
    } catch (error) {
      console.error('Error getting cooking tip:', error);
      // Fallback to offline service
      return getRandomCookingTip();
    }
  },

  /**
   * Improve a recipe using AI services  
   */
  improveRecipe: async (recipe: Recipe, improvement: string): Promise<Recipe> => {
    try {
      if (isGeminiConfigured()) {
        // Improvement disabled - return original recipe
        return recipe;
      } else {
        throw new ConfigurationError(
          'Recipe improvement requires Gemini AI configuration. Please add your Gemini API key to enable this feature.',
          'Gemini AI'
        );
      }
    } catch (error) {
      if (error instanceof ConfigurationError) {
        throw error;
      }
      console.error('Error improving recipe:', error);
      throw new AIServiceError('Failed to improve recipe. Please try again.');
    }
  },

  /**
   * Search for ingredients using Algolia (with local fallback)
   */
  searchIngredients: async (query: string): Promise<AlgoliaIngredientResult[]> => {
    try {
      if (false) { // Algolia disabled
        // Algolia search disabled - using fallback
        return [];
      } else {
        console.log('⚠️ Using local ingredient search (Algolia not configured)');
        // Fallback to local search
        return [];
      }
    } catch (error) {
      console.error('Ingredient search error, using local fallback:', error);
      // Always fallback to offline search
      return [];
    }
  },

  /**
   * Get popular ingredients
   */
  getPopularIngredients: async (): Promise<string[]> => {
    try {
      if (false) { // Algolia disabled
        // Algolia search disabled - using fallback
        return [
          'Chicken Breast', 'Salmon', 'Ground Beef', 'Tofu',
          'Broccoli', 'Spinach', 'Bell Pepper', 'Onion',
          'Garlic', 'Tomato', 'Avocado', 'Sweet Potato',
          'Rice', 'Quinoa', 'Pasta', 'Olive Oil'
        ];
      } else {
        // Fallback list for when Algolia is not configured
        return [
          'Chicken Breast', 'Salmon', 'Ground Beef', 'Tofu',
          'Broccoli', 'Spinach', 'Bell Pepper', 'Onion',
          'Garlic', 'Tomato', 'Avocado', 'Sweet Potato',
          'Rice', 'Quinoa', 'Pasta', 'Olive Oil'
        ];
      }
    } catch (error) {
      console.error('Error getting popular ingredients:', error);
      // Fallback list
      return [
        'Chicken Breast', 'Salmon', 'Ground Beef', 'Tofu',
        'Broccoli', 'Spinach', 'Bell Pepper', 'Onion',
        'Garlic', 'Tomato', 'Avocado', 'Sweet Potato',
        'Rice', 'Quinoa', 'Pasta', 'Olive Oil'
      ];
    }
  },

  /**
   * Get ingredient suggestions based on partial query
   */
  getIngredientSuggestions: async (partialQuery: string): Promise<string[]> => {
    try {
      if (false) { // Algolia disabled
        // Algolia search fallback - return simple suggestions
        return ['Chicken', 'Rice', 'Tomatoes', 'Onions', 'Garlic'].filter(item => 
          item.toLowerCase().includes(partialQuery.toLowerCase())
        );
      } else {
        // Offline fallback - return simple suggestions
        return ['Chicken', 'Rice', 'Tomatoes', 'Onions', 'Garlic'].filter(item => 
          item.toLowerCase().includes(partialQuery.toLowerCase())
        ).slice(0, 5);
      }
    } catch (error) {
      console.error('Error getting ingredient suggestions:', error);
      return ['Chicken', 'Rice', 'Tomatoes', 'Onions', 'Garlic'].filter(item => 
        item.toLowerCase().includes(partialQuery.toLowerCase())
      ).slice(0, 5);
    }
  },

  /**
   * Suggest a recipe title based on ingredients (with Firebase AI priority)
   */
  suggestRecipeTitle: async (ingredients: string[]): Promise<string> => {
    try {
      // Fallback recipe title suggestion
      const firstIngredient = ingredients[0] || 'Delicious';
      const capitalizedIngredient = firstIngredient.charAt(0).toUpperCase() + firstIngredient.slice(1);
      return `${capitalizedIngredient} Recipe`;
    } catch (error) {
      console.log('🔄 Title suggestion failed, using fallback...');
      
      try {
        if (false) { // Disabled gemini for now
          // return await geminiApiService.suggestRecipeTitle(ingredients);
        } else {
          // Fallback: Generate a simple title from main ingredient
          const mainIngredient = ingredients[0] || 'Mixed';
          const titles = [
            `Delicious ${mainIngredient} Recipe`,
            `${mainIngredient} Delight`,
            `Homestyle ${mainIngredient}`,
            `${mainIngredient} Special`,
            `Classic ${mainIngredient} Dish`
          ];
          return titles[Math.floor(Math.random() * titles.length)];
        }
      } catch (error) {
        console.error('Error suggesting recipe title:', error);
        return `Recipe with ${ingredients[0] || 'Mixed Ingredients'}`;
      }
    }
  },

  /**
   * Suggest cooking instructions based on ingredients and title
   */
  suggestInstructions: async (ingredients: string[], title: string): Promise<string[]> => {
    try {
      if (isGeminiConfigured()) {
        // Generate basic fallback instructions
        return [
          'Prepare all ingredients and wash vegetables.',
          'Heat cooking oil in a large pan over medium heat.',
          'Cook ingredients according to preference.',
          'Season with salt and pepper to taste.',
          'Serve hot and enjoy!'
        ];
      } else {
        // Fallback: Generate basic cooking instructions
        return [
          'Prepare all ingredients and wash vegetables.',
          'Heat cooking oil in a large pan over medium heat.',
          `Add ${ingredients[0] || 'main ingredients'} and cook until tender.`,
          'Season with salt, pepper, and desired spices.',
          'Cook until ingredients are well combined and heated through.',
          'Taste and adjust seasoning as needed.',
          'Serve hot and enjoy!'
        ];
      }
    } catch (error) {
      console.error('Error suggesting instructions:', error);
      return ['Follow basic cooking methods for the ingredients provided.'];
    }
  },

  /**
   * Estimate nutrition information for a recipe
   */
  estimateNutrition: async (ingredients: string[], servings: number): Promise<{ calories: number; protein: number; carbs: number; fat: number; fiber: number; }> => {
    try {
      if (isGeminiConfigured()) {
        // Fallback nutrition estimation
        const baseCalories = ingredients.length * 50;
        const totalCalories = Math.max(200, Math.min(800, baseCalories));
        return {
          calories: Math.round(totalCalories / servings),
          protein: Math.round(totalCalories * 0.15 / 4),
          carbs: Math.round(totalCalories * 0.55 / 4),
          fat: Math.round(totalCalories * 0.30 / 9),
          fiber: Math.max(2, Math.round(ingredients.length / 2))
        };
      } else {
        // Fallback: Provide estimated nutrition based on ingredients count
        const baseCalories = ingredients.length * 50;
        const totalCalories = Math.max(200, Math.min(800, baseCalories));
        
        return {
          calories: Math.round(totalCalories / servings),
          protein: Math.round((totalCalories * 0.15) / 4 / servings), // 15% of calories from protein
          carbs: Math.round((totalCalories * 0.50) / 4 / servings),   // 50% of calories from carbs
          fat: Math.round((totalCalories * 0.35) / 9 / servings),     // 35% of calories from fat
          fiber: Math.round(ingredients.length * 2 / servings)        // Estimated fiber
        };
      }
    } catch (error) {
      console.error('Error estimating nutrition:', error);
      return {
        calories: 300,
        protein: 15,
        carbs: 30,
        fat: 10,
        fiber: 5
      };
    }
  },

  /**
   * Categorize recipe from ingredients using AI
   */
  categorizeRecipeFromIngredients: async (ingredients: string[]) => {
    try {
      if (isGeminiConfigured()) {
        // Fallback categorization
        const text = ingredients.join(' ').toLowerCase();
        let cuisine = 'International';
        let category = 'main';
        
        if (text.includes('pasta') || text.includes('tomato')) cuisine = 'Italian';
        else if (text.includes('rice') || text.includes('soy')) cuisine = 'Asian';
        else if (text.includes('bean') || text.includes('avocado')) cuisine = 'Mexican';
        
        if (text.includes('salad') || text.includes('lettuce')) category = 'healthy';
        else if (text.includes('cake') || text.includes('sugar')) category = 'desserts';
        
        return { 
          primaryCategory: category,
          cuisine, 
          dishType: 'Main Course',
          cookingMethod: 'Sautéed',
          difficulty: 'Medium' as const,
          dietaryTags: [],
          suggestedTags: []
        };
      } else {
        // Fallback categorization when AI is not available
        const text = ingredients.join(' ').toLowerCase();
        
        // Basic categorization logic
        let primaryCategory = 'American';
        let cuisine = 'American';
        let dishType = 'Main Course';
        let cookingMethod = 'Cooked';
        let difficulty: 'Easy' | 'Medium' | 'Hard' = 'Medium';
        let dietaryTags: string[] = [];
        let suggestedTags: string[] = ['Homemade'];

        // Simple cuisine detection
        if (text.includes('pasta') || text.includes('italian')) {
          primaryCategory = 'Italian';
          cuisine = 'Italian';
        } else if (text.includes('soy sauce') || text.includes('ginger') || text.includes('asian')) {
          primaryCategory = 'Asian';
          cuisine = 'Asian';
        } else if (text.includes('taco') || text.includes('salsa') || text.includes('mexican')) {
          primaryCategory = 'Mexican';
          cuisine = 'Mexican';
        } else if (text.includes('curry') || text.includes('indian')) {
          primaryCategory = 'Indian';
          cuisine = 'Indian';
        }

        // Dish type detection
        if (text.includes('soup')) dishType = 'Soup';
        else if (text.includes('salad')) dishType = 'Salad';
        else if (text.includes('dessert') || text.includes('cake') || text.includes('cookie')) {
          dishType = 'Dessert';
          primaryCategory = 'Desserts';
        }

        // Dietary tags
        if (!text.includes('meat') && !text.includes('chicken') && !text.includes('beef')) {
          dietaryTags.push('Vegetarian');
        }
        if (!text.includes('dairy') && !text.includes('milk') && !text.includes('cheese')) {
          dietaryTags.push('Dairy-Free');
        }

        // Quick meals detection
        if (ingredients.length <= 5) {
          primaryCategory = 'Quick Meals';
          suggestedTags.push('Quick', 'Easy');
          difficulty = 'Easy';
        }

        return {
          primaryCategory,
          cuisine,
          dishType,
          cookingMethod,
          difficulty,
          dietaryTags,
          suggestedTags
        };
      }
    } catch (error) {
      console.error('Error categorizing recipe from ingredients:', error);
      // Return default categorization
      return {
        primaryCategory: 'American',
        cuisine: 'American',
        dishType: 'Main Course',
        cookingMethod: 'Cooked',
        difficulty: 'Medium' as const,
        dietaryTags: [],
        suggestedTags: ['Homemade']
      };
    }
  }
};

// Export configuration status for use in components
export const getAIServiceStatus = () => {
  const config = getConfigurationStatus();
  return {
    ...config,
    offlineMode: !config.hasAnyConfiguration,
    services: {
      gemini: {
        configured: config.gemini,
        name: 'Gemini AI',
        features: ['Recipe Assistance', 'Recipe Improvement', 'Cooking Tips', 'Smart Suggestions']
      },
      algolia: {
        configured: false,
        name: 'Algolia Search',
        features: ['Recipe Search', 'Ingredient Search', 'Recipe Management', 'Category Browsing']
      }
    }
  };
}; 