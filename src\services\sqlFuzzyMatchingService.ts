/**
 * SQL-Based Fuzzy Matching Service
 * 
 * Implements PostgreSQL fuzzy matching extensions as recommended in
 * Matching_Products_Across_Supermarkets.txt for improved performance
 * and accuracy on large datasets.
 */

import { createClient } from '@supabase/supabase-js';
import { IProduct } from '../types/shoppingList';

const supabaseUrl = 'https://emjwniuqwhvohjassnrg.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVtanduaXVxd2h2b2hqYXNzbnJnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjQwMTYwNSwiZXhwIjoyMDY3OTc3NjA1fQ.oTowRoNAjlUmk10O9pSMK2BvL0XlyBb5orVRrlEryHs';

export class SQLFuzzyMatchingService {
  private supabase;

  constructor() {
    this.supabase = createClient(supabaseUrl, supabaseAnonKey);
  }

  /**
   * Enable PostgreSQL fuzzy matching extensions
   * Based on recommendations from the document
   */
  async enableFuzzyExtensions(): Promise<void> {
    try {
      // Enable fuzzystrmatch extension for Levenshtein distance
      await this.supabase.rpc('enable_fuzzystrmatch');
      
      // Enable pg_trgm extension for trigram similarity
      await this.supabase.rpc('enable_pg_trgm');
      
      console.log('✅ Fuzzy matching extensions enabled');
    } catch (error) {
      console.warn('⚠️ Could not enable fuzzy extensions (may already be enabled):', error);
    }
  }

  /**
   * Find product matches using PostgreSQL trigram similarity
   * Implements the pg_trgm approach from the document
   */
  async findTrigramMatches(
    sourceStore: string, 
    targetStore: string, 
    similarityThreshold: number = 0.5
  ): Promise<any[]> {
    try {
      const { data, error } = await this.supabase.rpc('find_trigram_matches', {
        source_store: sourceStore,
        target_store: targetStore,
        similarity_threshold: similarityThreshold
      });

      if (error) {
        console.error('Error finding trigram matches:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in trigram matching:', error);
      return [];
    }
  }

  /**
   * Find product matches using Levenshtein distance
   * Implements the fuzzystrmatch approach from the document
   */
  async findLevenshteinMatches(
    sourceStore: string, 
    targetStore: string, 
    maxDistance: number = 5
  ): Promise<any[]> {
    try {
      const { data, error } = await this.supabase.rpc('find_levenshtein_matches', {
        source_store: sourceStore,
        target_store: targetStore,
        max_distance: maxDistance
      });

      if (error) {
        console.error('Error finding Levenshtein matches:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in Levenshtein matching:', error);
      return [];
    }
  }

  /**
   * Create normalized name column for better matching
   * Implements the normalization strategy from the document
   */
  async createNormalizedNames(): Promise<void> {
    try {
      const { error } = await this.supabase.rpc('create_normalized_names');
      
      if (error) {
        console.error('Error creating normalized names:', error);
      } else {
        console.log('✅ Normalized names created successfully');
      }
    } catch (error) {
      console.error('Error in name normalization:', error);
    }
  }

  /**
   * Find comprehensive matches using multiple SQL techniques
   * Combines trigram and Levenshtein approaches
   */
  async findComprehensiveMatches(similarityThreshold: number = 0.5): Promise<any[]> {
    try {
      const { data, error } = await this.supabase.rpc('find_comprehensive_matches', {
        similarity_threshold: similarityThreshold
      });

      if (error) {
        console.error('Error finding comprehensive matches:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in comprehensive matching:', error);
      return [];
    }
  }

  /**
   * Create product matches table as suggested in the document
   * This creates a materialized view of all matches for fast querying
   */
  async createProductMatchesTable(): Promise<void> {
    try {
      const { error } = await this.supabase.rpc('create_product_matches_table');
      
      if (error) {
        console.error('Error creating product matches table:', error);
      } else {
        console.log('✅ Product matches table created successfully');
      }
    } catch (error) {
      console.error('Error creating matches table:', error);
    }
  }

  /**
   * Get pre-computed matches from the matches table
   * Fast retrieval of already computed matches
   */
  async getPrecomputedMatches(): Promise<any[]> {
    try {
      const { data, error } = await this.supabase
        .from('product_matches')
        .select('*');

      if (error) {
        console.error('Error getting precomputed matches:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error retrieving matches:', error);
      return [];
    }
  }

  /**
   * Test the SQL fuzzy matching performance
   * Benchmarks different approaches
   */
  async benchmarkSQLMatching(): Promise<any> {
    console.log('🧪 Benchmarking SQL fuzzy matching approaches...');
    
    const results = {
      trigram: { matches: 0, time: 0 },
      levenshtein: { matches: 0, time: 0 },
      comprehensive: { matches: 0, time: 0 }
    };

    try {
      // Test trigram matching
      const trigramStart = Date.now();
      const trigramMatches = await this.findTrigramMatches('paknsave', 'newworld', 0.5);
      results.trigram = {
        matches: trigramMatches.length,
        time: Date.now() - trigramStart
      };

      // Test Levenshtein matching
      const levenshteinStart = Date.now();
      const levenshteinMatches = await this.findLevenshteinMatches('paknsave', 'woolworths', 5);
      results.levenshtein = {
        matches: levenshteinMatches.length,
        time: Date.now() - levenshteinStart
      };

      // Test comprehensive matching
      const comprehensiveStart = Date.now();
      const comprehensiveMatches = await this.findComprehensiveMatches(0.5);
      results.comprehensive = {
        matches: comprehensiveMatches.length,
        time: Date.now() - comprehensiveStart
      };

      console.log('📊 SQL Matching Benchmark Results:');
      console.log(`   Trigram: ${results.trigram.matches} matches in ${results.trigram.time}ms`);
      console.log(`   Levenshtein: ${results.levenshtein.matches} matches in ${results.levenshtein.time}ms`);
      console.log(`   Comprehensive: ${results.comprehensive.matches} matches in ${results.comprehensive.time}ms`);

      return results;
    } catch (error) {
      console.error('Error in SQL matching benchmark:', error);
      return results;
    }
  }
}

// Export singleton instance
export const sqlFuzzyMatchingService = new SQLFuzzyMatchingService();
