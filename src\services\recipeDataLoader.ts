import { Recipe } from '../utils/storage';

// No more sample data - recipes come from Algolia
const sampleRecipesData: ExportedRecipe[] = [];

interface ExportedRecipe {
  id: string;
  title: string;
  description: string;
  prepTime: string;
  cookTime: string;
  totalTime: string;
  servings: string;
  ingredients: string[];
  instructions: string[];
  category: string;
  cuisine: string;
  difficulty: string;
  tags: string[];
  dietary: string[];
  mealType: string[];
  imageUrl: string;
  source: string;
  url: string;
  aiProcessed: boolean;
  appealScore: number;
  searchableText: string;
  createdAt: string;
  updatedAt: string;
}

class RecipeDataLoader {
  private allRecipes: Recipe[] = [];
  private isLoaded = false;

  // Convert exported recipe to app's Recipe interface
  private adaptRecipe(exportedRecipe: ExportedRecipe): Recipe {
    // Generate instructions if missing
    const instructions = exportedRecipe.instructions.length > 0 
      ? exportedRecipe.instructions 
      : this.generateBasicInstructions(exportedRecipe);

    return {
      id: exportedRecipe.id,
      title: exportedRecipe.title,
      description: exportedRecipe.description,
      ingredients: exportedRecipe.ingredients,
      instructions: instructions,
      cookingTime: exportedRecipe.totalTime || `${exportedRecipe.prepTime} + ${exportedRecipe.cookTime}`,
      difficulty: exportedRecipe.difficulty,
      mealType: exportedRecipe.mealType.join(', '),
      createdAt: exportedRecipe.createdAt,
      servings: parseInt(exportedRecipe.servings) || 4,
      tags: [...exportedRecipe.tags, ...exportedRecipe.dietary],
      
      // Enhanced fields
      imageUrl: exportedRecipe.imageUrl || this.getPlaceholderImage(exportedRecipe.category),
      category: exportedRecipe.category,
      cuisine: exportedRecipe.cuisine,
      dishType: exportedRecipe.mealType[0] || 'Main Course',
      
      // Estimated nutrition
      calories: this.estimateCalories(exportedRecipe.ingredients.length, exportedRecipe.servings),
    };
  }

  // Generate basic instructions if missing
  private generateBasicInstructions(recipe: ExportedRecipe): string[] {
    if (recipe.ingredients.length === 0) {
      return ['Instructions not available for this recipe. Please refer to the original source.'];
    }

    const instructions = [
      '1. Gather all ingredients and prepare your workspace.',
    ];

    // Add ingredient prep step
    if (recipe.ingredients.length > 0) {
      instructions.push('2. Prepare and measure all ingredients as listed.');
    }

    // Add cooking steps based on category
    if (recipe.category.toLowerCase().includes('salad')) {
      instructions.push('3. Wash and chop all vegetables.');
      instructions.push('4. Combine ingredients in a large bowl.');
      instructions.push('5. Add dressing and toss to combine.');
      instructions.push('6. Serve immediately or chill before serving.');
    } else if (recipe.category.toLowerCase().includes('main')) {
      instructions.push('3. Preheat oven or prepare cooking surface as needed.');
      instructions.push('4. Season main ingredients with salt and pepper.');
      instructions.push('5. Cook according to your preferred method until done.');
      instructions.push('6. Let rest briefly before serving.');
    } else {
      instructions.push('3. Follow standard preparation methods for this type of dish.');
      instructions.push('4. Cook ingredients as appropriate for the recipe type.');
      instructions.push('5. Adjust seasoning to taste.');
      instructions.push('6. Serve as desired.');
    }

    instructions.push('Note: For detailed instructions, please visit the original recipe source.');

    return instructions;
  }

  // Get placeholder image based on category
  private getPlaceholderImage(category: string): string {
    const imageMap: Record<string, string> = {
      'healthy': 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400&h=300&fit=crop',
      'Main Courses': 'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=400&h=300&fit=crop',
      'desserts': 'https://images.unsplash.com/photo-**********-0bccd828d307?w=400&h=300&fit=crop',
      'Appetizers & Snacks': 'https://images.unsplash.com/photo-1541745537411-b8046dc6d66c?w=400&h=300&fit=crop',
      'Breakfast & Brunch': 'https://images.unsplash.com/photo-1533089860892-a7c6f0a88666?w=400&h=300&fit=crop',
      'Soups & Stews': 'https://images.unsplash.com/photo-**********-23ac45744acd?w=400&h=300&fit=crop',
    };

    return imageMap[category] || 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop';
  }

  // Simple calorie estimation
  private estimateCalories(ingredientCount: number, servings: string): number {
    const avgCaloriesPerIngredient = 120;
    const totalCalories = ingredientCount * avgCaloriesPerIngredient;
    const servingCount = parseInt(servings) || 4;
    return Math.round(totalCalories / servingCount);
  }

  // Load all exported recipes
  async loadAllRecipes(): Promise<Recipe[]> {
    if (this.isLoaded) {
      return this.allRecipes;
    }

    try {
      console.log(`📚 Loading ${sampleRecipesData.length} exported recipes...`);
      
      this.allRecipes = sampleRecipesData.map((recipe: ExportedRecipe) => this.adaptRecipe(recipe));
      this.isLoaded = true;
      
      console.log(`✅ Successfully loaded ${this.allRecipes.length} recipes`);
      
      // Log statistics
      const withIngredients = this.allRecipes.filter(r => r.ingredients.length > 0).length;
      const withInstructions = this.allRecipes.filter(r => r.instructions.length > 1).length;
      const withImages = this.allRecipes.filter(r => r.imageUrl && r.imageUrl.length > 0).length;
      
      console.log(`📊 Recipe Statistics:`);
      console.log(`   - With ingredients: ${withIngredients}/${this.allRecipes.length}`);
      console.log(`   - With instructions: ${withInstructions}/${this.allRecipes.length}`);
      console.log(`   - With images: ${withImages}/${this.allRecipes.length}`);
      
      return this.allRecipes;
    } catch (error) {
      console.error('Failed to load exported recipes:', error);
      return [];
    }
  }

  // Get recipes by category
  async getRecipesByCategory(category: string): Promise<Recipe[]> {
    const recipes = await this.loadAllRecipes();
    return recipes.filter(recipe => 
      recipe.category?.toLowerCase() === category.toLowerCase()
    );
  }

  // Get recipes by cuisine
  async getRecipesByCuisine(cuisine: string): Promise<Recipe[]> {
    const recipes = await this.loadAllRecipes();
    return recipes.filter(recipe => 
      recipe.cuisine?.toLowerCase() === cuisine.toLowerCase()
    );
  }

  // Search recipes
  async searchRecipes(query: string): Promise<Recipe[]> {
    const recipes = await this.loadAllRecipes();
    const lowerQuery = query.toLowerCase();
    
    return recipes.filter(recipe =>
      recipe.title.toLowerCase().includes(lowerQuery) ||
      recipe.description?.toLowerCase().includes(lowerQuery) ||
      recipe.ingredients.some(ingredient => 
        ingredient.toLowerCase().includes(lowerQuery)
      ) ||
      recipe.tags?.some(tag => 
        tag.toLowerCase().includes(lowerQuery)
      )
    );
  }

  // Get recipe by ID
  async getRecipeById(id: string): Promise<Recipe | null> {
    const recipes = await this.loadAllRecipes();
    return recipes.find(recipe => recipe.id === id) || null;
  }

  // Get random recipes
  async getRandomRecipes(count: number = 10): Promise<Recipe[]> {
    const recipes = await this.loadAllRecipes();
    const shuffled = [...recipes].sort(() => Math.random() - 0.5);
    return shuffled.slice(0, count);
  }

  // Get recipes with complete data (ingredients + instructions)
  async getCompleteRecipes(limit: number = 20): Promise<Recipe[]> {
    const recipes = await this.loadAllRecipes();
    return recipes
      .filter(recipe => 
        recipe.ingredients.length > 0 && 
        recipe.instructions.length > 1
      )
      .slice(0, limit);
  }

  // Get available categories
  getAvailableCategories(): string[] {
    if (!this.isLoaded) return [];
    
    const categories = new Set(
      this.allRecipes
        .map(recipe => recipe.category)
        .filter((category): category is string => Boolean(category))
    );
    return Array.from(categories).sort();
  }

  // Get available cuisines
  getAvailableCuisines(): string[] {
    if (!this.isLoaded) return [];
    
    const cuisines = new Set(
      this.allRecipes
        .map(recipe => recipe.cuisine)
        .filter((cuisine): cuisine is string => Boolean(cuisine) && cuisine !== 'Unknown')
    );
    return Array.from(cuisines).sort();
  }

  // Get recipe statistics
  getRecipeStats() {
    if (!this.isLoaded) return null;

    const categories = this.getAvailableCategories();
    const cuisines = this.getAvailableCuisines();
    
    const categoryStats = categories.reduce((acc, category) => {
      acc[category] = this.allRecipes.filter(r => r.category === category).length;
      return acc;
    }, {} as Record<string, number>);

    const cuisineStats = cuisines.reduce((acc, cuisine) => {
      acc[cuisine] = this.allRecipes.filter(r => r.cuisine === cuisine).length;
      return acc;
    }, {} as Record<string, number>);

    return {
      total: this.allRecipes.length,
      byCategory: categoryStats,
      byCuisine: cuisineStats,
      withImages: this.allRecipes.filter(r => r.imageUrl && r.imageUrl.length > 0).length,
      withInstructions: this.allRecipes.filter(r => r.instructions.length > 1).length,
      withIngredients: this.allRecipes.filter(r => r.ingredients.length > 0).length,
      completeRecipes: this.allRecipes.filter(r => 
        r.ingredients.length > 0 && r.instructions.length > 1
      ).length,
    };
  }
}

export const recipeDataLoader = new RecipeDataLoader(); 