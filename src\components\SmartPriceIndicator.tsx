/**
 * Smart Price Indicator
 * 
 * A minimalistic component that shows current prices, 
 * price changes, and best deals with clean visual design
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  Animated,
} from 'react-native';
import { useTheme } from '../context/ThemeContext';
import { theme } from '../styles';
import { scrapedDataService, PriceChange } from '../services/scrapedDataService';
import { StoreSearchResult } from '../services/priceComparisonService';

interface SmartPriceIndicatorProps {
  productName: string;
  selectedBrand?: string;
  onBrandSelect?: (results: StoreSearchResult[]) => void;
  style?: any;
  compact?: boolean; // For smaller displays
}

interface PriceData {
  results: StoreSearchResult[];
  bestPrice?: StoreSearchResult;
  savings?: number;
  priceChange?: PriceChange;
  isLoading: boolean;
}

export const SmartPriceIndicator: React.FC<SmartPriceIndicatorProps> = ({
  productName,
  selectedBrand,
  onBrandSelect,
  style,
  compact = false
}) => {
  const { colors } = useTheme();
  const [priceData, setPriceData] = useState<PriceData>({ results: [], isLoading: true });
  const [fadeAnim] = useState(new Animated.Value(0));
  const [pulseAnim] = useState(new Animated.Value(1));

  useEffect(() => {
    loadPriceData();
    
    // Subscribe to price updates
    const unsubscribe = scrapedDataService.onDataUpdate(() => {
      loadPriceData();
      animatePriceUpdate();
    });

    return unsubscribe;
  }, [productName, selectedBrand]);

  const loadPriceData = async () => {
    try {
      setPriceData(prev => ({ ...prev, isLoading: true }));
      
      const comparison = await scrapedDataService.getPriceComparison(productName);
      const recentChanges = await scrapedDataService.getRecentPriceChanges();
      
      // Find relevant price change for this product
      const priceChange = recentChanges.find(change => 
        change.product.name.toLowerCase().includes(productName.toLowerCase())
      );

      setPriceData({
        results: comparison.results,
        bestPrice: comparison.bestPrice,
        savings: comparison.savings,
        priceChange,
        isLoading: false
      });

      // Fade in animation
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();

    } catch (error) {
      console.error('Failed to load price data:', error);
      setPriceData(prev => ({ ...prev, isLoading: false }));
    }
  };

  const animatePriceUpdate = () => {
    // Pulse animation for price updates
    Animated.sequence([
      Animated.timing(pulseAnim, {
        toValue: 1.1,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(pulseAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const formatPrice = (price: number): string => `$${price.toFixed(2)}`;

  const getStoreEmoji = (store: string): string => {
    const emojis = {
      woolworths: '🍎',
      newworld: '🛒',
      paknsave: '💰',
    };
    return emojis[store as keyof typeof emojis] || '🏪';
  };

  const getPriceChangeIndicator = () => {
    if (!priceData.priceChange) return null;

    const { changeType, changePercent, isSignificant } = priceData.priceChange;
    const isIncrease = changeType === 'increase';
    
    return (
      <View style={[
        styles.priceChangeContainer,
        {
          backgroundColor: isIncrease ? '#FFE5E5' : '#E5F5E5',
          borderColor: isIncrease ? '#FF6B6B' : '#4CAF50',
        }
      ]}>
        <Text style={[
          styles.priceChangeText,
          { color: isIncrease ? '#FF6B6B' : '#4CAF50' }
        ]}>
          {isIncrease ? '↗' : '↘'} {Math.abs(changePercent).toFixed(1)}%
        </Text>
        {isSignificant && (
          <Text style={styles.significantBadge}>!</Text>
        )}
      </View>
    );
  };

  const renderCompactView = () => (
    <Animated.View 
      style={[
        styles.compactContainer,
        { 
          backgroundColor: colors.surface,
          transform: [{ scale: pulseAnim }],
          opacity: fadeAnim
        },
        style
      ]}
    >
      {priceData.isLoading ? (
        <ActivityIndicator size="small" color={theme.colors.primary} />
      ) : priceData.bestPrice ? (
        <TouchableOpacity 
          style={styles.compactContent}
          onPress={() => {
            console.log('🎯 SmartPriceIndicator (compact) pressed for:', productName);
            onBrandSelect?.(priceData.results);
          }}
          activeOpacity={0.7}
        >
          <View style={styles.compactPriceRow}>
            <Text style={styles.compactStoreEmoji}>
              {getStoreEmoji(priceData.bestPrice.store)}
            </Text>
            <Text style={[styles.compactPrice, { color: colors.text }]}>
              {formatPrice(priceData.bestPrice.product.price)}
            </Text>
            {priceData.savings && priceData.savings > 0.10 && (
              <Text style={styles.compactSavings}>
                -{formatPrice(priceData.savings)}
              </Text>
            )}
          </View>
          {getPriceChangeIndicator()}
        </TouchableOpacity>
      ) : (
        <TouchableOpacity 
          style={styles.compactContent}
          onPress={() => {
            console.log('🔍 SmartPriceIndicator (compact) "Find price" pressed for:', productName);
            onBrandSelect?.([]);
          }}
        >
          <Text style={[styles.findPriceText, { color: colors.textSecondary }]}>
            🔍 Find price
          </Text>
        </TouchableOpacity>
      )}
    </Animated.View>
  );

  const renderFullView = () => (
    <Animated.View 
      style={[
        styles.fullContainer,
        { 
          backgroundColor: colors.surface,
          borderColor: colors.border,
          transform: [{ scale: pulseAnim }],
          opacity: fadeAnim
        },
        style
      ]}
    >
      {priceData.isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Finding prices...
          </Text>
        </View>
      ) : priceData.results.length > 0 ? (
        <TouchableOpacity 
          style={styles.fullContent}
          onPress={() => {
            console.log('🎯 SmartPriceIndicator pressed for:', productName);
            onBrandSelect?.(priceData.results);
          }}
          activeOpacity={0.8}
        >
          <View style={styles.topRow}>
            <View style={styles.bestPriceSection}>
              <Text style={[styles.bestLabel, { color: colors.textSecondary }]}>
                Best price
              </Text>
              <View style={styles.bestPriceRow}>
                <Text style={styles.bestStoreEmoji}>
                  {getStoreEmoji(priceData.bestPrice!.store)}
                </Text>
                <Text style={[styles.bestPrice, { color: colors.text }]}>
                  {formatPrice(priceData.bestPrice!.product.price)}
                </Text>
              </View>
            </View>
            
            {priceData.savings && priceData.savings > 0.10 && (
              <View style={styles.savingsSection}>
                <Text style={styles.savingsAmount}>
                  Save {formatPrice(priceData.savings)}
                </Text>
              </View>
            )}
          </View>

          {priceData.results.length > 1 && (
            <View style={styles.allStoresRow}>
              {priceData.results.slice(0, 3).map((result, index) => (
                <View key={result.store} style={styles.storePrice}>
                  <Text style={styles.storePriceEmoji}>
                    {getStoreEmoji(result.store)}
                  </Text>
                  <Text style={[
                    styles.storePriceText, 
                    { color: colors.textSecondary },
                    result.store === priceData.bestPrice?.store && styles.bestStorePriceText
                  ]}>
                    {formatPrice(result.product.price)}
                  </Text>
                </View>
              ))}
            </View>
          )}

          {getPriceChangeIndicator()}
        </TouchableOpacity>
      ) : (
        <TouchableOpacity 
          style={styles.fullContent}
          onPress={() => {
            console.log('🔍 SmartPriceIndicator "Find price" pressed for:', productName);
            onBrandSelect?.([]);
          }}
        >
          <Text style={[styles.findPriceText, { color: colors.textSecondary }]}>
            🔍 Find best price
          </Text>
        </TouchableOpacity>
      )}
    </Animated.View>
  );

  if (compact) {
    return renderCompactView();
  }

  return renderFullView();
};

const styles = StyleSheet.create({
  compactContainer: {
    borderRadius: 8,
    padding: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  compactContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  compactPriceRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  compactStoreEmoji: {
    fontSize: 14,
    marginRight: 4,
  },
  compactPrice: {
    fontSize: 14,
    fontWeight: '600',
    marginRight: 4,
  },
  compactSavings: {
    fontSize: 11,
    color: '#4CAF50',
    fontWeight: '500',
  },
  
  fullContainer: {
    borderRadius: 12,
    padding: 12,
    borderWidth: 1,
    marginTop: 4,
  },
  fullContent: {
    gap: 8,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  loadingText: {
    fontSize: 12,
  },
  
  topRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  bestPriceSection: {
    flex: 1,
  },
  bestLabel: {
    fontSize: 10,
    fontWeight: '500',
    textTransform: 'uppercase',
    marginBottom: 2,
  },
  bestPriceRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  bestStoreEmoji: {
    fontSize: 16,
    marginRight: 6,
  },
  bestPrice: {
    fontSize: 16,
    fontWeight: '700',
  },
  
  savingsSection: {
    backgroundColor: '#E8F5E8',
    borderRadius: 6,
    paddingHorizontal: 6,
    paddingVertical: 3,
  },
  savingsAmount: {
    fontSize: 11,
    color: '#2E7D32',
    fontWeight: '600',
  },
  
  allStoresRow: {
    flexDirection: 'row',
    gap: 12,
    paddingTop: 4,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  storePrice: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  storePriceEmoji: {
    fontSize: 12,
    marginRight: 3,
  },
  storePriceText: {
    fontSize: 11,
    fontWeight: '500',
  },
  bestStorePriceText: {
    color: '#2E7D32',
    fontWeight: '600',
  },
  
  priceChangeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
    borderRadius: 4,
    borderWidth: 1,
    paddingHorizontal: 4,
    paddingVertical: 2,
    gap: 2,
  },
  priceChangeText: {
    fontSize: 10,
    fontWeight: '600',
  },
  significantBadge: {
    fontSize: 8,
    color: '#FF6B6B',
    fontWeight: 'bold',
  },
  
  findPriceText: {
    fontSize: 12,
    textAlign: 'center',
    fontStyle: 'italic',
  },
}); 