// TEMPORARILY DISABLED: expo-notifications causing navigation issues
// import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';

// Configure notification behavior
// Notifications.setNotificationHandler({
//   handleNotification: async () => ({
//     shouldShowAlert: true,
//     shouldPlaySound: true,
//     shouldSetBadge: false,
//   }),
// });

export interface PriceAlert {
  id: string;
  productId: string;
  productName: string;
  targetPrice: number;
  currentPrice: number;
  store: string;
  userId: string;
  isActive: boolean;
  createdAt: Date;
}

export interface NotificationPreferences {
  priceAlerts: boolean;
  shoppingReminders: boolean;
  saleNotifications: boolean;
  sharedListUpdates: boolean;
}

export class NotificationService {
  private static instance: NotificationService;
  private isInitialized = false;

  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  async initialize(): Promise<boolean> {
    if (this.isInitialized) {
      return true;
    }

    try {
      // Request permissions
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.log('❌ Notification permissions not granted');
        return false;
      }

      // Configure notification channel for Android
      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('price-alerts', {
          name: 'Price Alerts',
          importance: Notifications.AndroidImportance.HIGH,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#FF231F7C',
        });

        await Notifications.setNotificationChannelAsync('shopping-reminders', {
          name: 'Shopping Reminders',
          importance: Notifications.AndroidImportance.DEFAULT,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#FF231F7C',
        });

        await Notifications.setNotificationChannelAsync('shared-lists', {
          name: 'Shared Lists',
          importance: Notifications.AndroidImportance.DEFAULT,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#FF231F7C',
        });
      }

      this.isInitialized = true;
      console.log('✅ Notification service initialized');
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize notification service:', error);
      return false;
    }
  }

  async sendPriceDropAlert(alert: PriceAlert): Promise<void> {
    try {
      const priceDrop = alert.currentPrice - alert.targetPrice;
      const percentageDropped = ((priceDrop / alert.targetPrice) * 100).toFixed(1);

      await Notifications.scheduleNotificationAsync({
        content: {
          title: '🎉 Price Drop Alert!',
          body: `${alert.productName} is now $${alert.currentPrice.toFixed(2)} at ${alert.store} (${percentageDropped}% off your target!)`,
          data: {
            type: 'price_drop',
            productId: alert.productId,
            store: alert.store,
            newPrice: alert.currentPrice,
            targetPrice: alert.targetPrice,
          },
          sound: 'default',
          badge: 1,
        },
        trigger: null, // Send immediately
        identifier: `price-drop-${alert.id}`,
      });

      console.log('✅ Price drop notification sent:', alert.productName);
    } catch (error) {
      console.error('❌ Failed to send price drop notification:', error);
    }
  }

  async sendShoppingReminder(items: string[], totalSavings?: number): Promise<void> {
    try {
      const itemCount = items.length;
      const savingsText = totalSavings 
        ? ` Save $${totalSavings.toFixed(2)} with our recommendations!`
        : '';

      await Notifications.scheduleNotificationAsync({
        content: {
          title: '🛒 Shopping Reminder',
          body: `You have ${itemCount} items in your shopping list.${savingsText}`,
          data: {
            type: 'shopping_reminder',
            itemCount,
            totalSavings,
          },
          sound: 'default',
        },
        trigger: null,
        identifier: `shopping-reminder-${Date.now()}`,
      });

      console.log('✅ Shopping reminder sent');
    } catch (error) {
      console.error('❌ Failed to send shopping reminder:', error);
    }
  }

  async sendSaleNotification(productName: string, store: string, originalPrice: number, salePrice: number): Promise<void> {
    try {
      const savings = originalPrice - salePrice;
      const percentageOff = ((savings / originalPrice) * 100).toFixed(0);

      await Notifications.scheduleNotificationAsync({
        content: {
          title: '🏷️ Sale Alert!',
          body: `${productName} is ${percentageOff}% off at ${store}! Now $${salePrice.toFixed(2)} (was $${originalPrice.toFixed(2)})`,
          data: {
            type: 'sale_notification',
            productName,
            store,
            originalPrice,
            salePrice,
            savings,
          },
          sound: 'default',
        },
        trigger: null,
        identifier: `sale-${productName}-${Date.now()}`,
      });

      console.log('✅ Sale notification sent:', productName);
    } catch (error) {
      console.error('❌ Failed to send sale notification:', error);
    }
  }

  async sendSharedListUpdate(listName: string, updatedBy: string, action: 'added' | 'removed' | 'updated', itemName: string): Promise<void> {
    try {
      const actionText = {
        added: 'added',
        removed: 'removed',
        updated: 'updated'
      }[action];

      await Notifications.scheduleNotificationAsync({
        content: {
          title: `📝 ${listName} Updated`,
          body: `${updatedBy} ${actionText} "${itemName}"`,
          data: {
            type: 'shared_list_update',
            listName,
            updatedBy,
            action,
            itemName,
          },
          sound: 'default',
        },
        trigger: null,
        identifier: `shared-list-${listName}-${Date.now()}`,
      });

      console.log('✅ Shared list notification sent:', listName);
    } catch (error) {
      console.error('❌ Failed to send shared list notification:', error);
    }
  }

  async scheduleWeeklyShoppingReminder(dayOfWeek: number = 0, hour: number = 10): Promise<void> {
    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title: '🛒 Weekly Shopping Reminder',
          body: 'Time to plan your weekly shopping! Check for new deals and price drops.',
          data: {
            type: 'weekly_reminder',
          },
          sound: 'default',
        },
        trigger: {
          weekday: dayOfWeek, // 0 = Sunday, 1 = Monday, etc.
          hour: hour,
          minute: 0,
          repeats: true,
        },
        identifier: 'weekly-shopping-reminder',
      });

      console.log('✅ Weekly shopping reminder scheduled');
    } catch (error) {
      console.error('❌ Failed to schedule weekly reminder:', error);
    }
  }

  async cancelNotification(identifier: string): Promise<void> {
    try {
      await Notifications.cancelScheduledNotificationAsync(identifier);
      console.log('✅ Notification cancelled:', identifier);
    } catch (error) {
      console.error('❌ Failed to cancel notification:', error);
    }
  }

  async cancelAllNotifications(): Promise<void> {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
      console.log('✅ All notifications cancelled');
    } catch (error) {
      console.error('❌ Failed to cancel all notifications:', error);
    }
  }

  async getBadgeCount(): Promise<number> {
    try {
      return await Notifications.getBadgeCountAsync();
    } catch (error) {
      console.error('❌ Failed to get badge count:', error);
      return 0;
    }
  }

  async setBadgeCount(count: number): Promise<void> {
    try {
      await Notifications.setBadgeCountAsync(count);
    } catch (error) {
      console.error('❌ Failed to set badge count:', error);
    }
  }

  async clearBadge(): Promise<void> {
    try {
      await Notifications.setBadgeCountAsync(0);
    } catch (error) {
      console.error('❌ Failed to clear badge:', error);
    }
  }

  // Mock method for testing notifications
  async testNotifications(): Promise<void> {
    console.log('🧪 Testing notifications...');

    // Test price drop alert
    const mockAlert: PriceAlert = {
      id: 'test-alert-1',
      productId: 'test-product-1',
      productName: 'Milk 2L',
      targetPrice: 4.00,
      currentPrice: 3.50,
      store: 'Woolworths',
      userId: 'test-user',
      isActive: true,
      createdAt: new Date(),
    };

    await this.sendPriceDropAlert(mockAlert);

    // Test shopping reminder
    setTimeout(() => {
      this.sendShoppingReminder(['Milk', 'Bread', 'Eggs'], 5.25);
    }, 2000);

    // Test sale notification
    setTimeout(() => {
      this.sendSaleNotification('Bananas 1kg', 'New World', 3.99, 2.99);
    }, 4000);

    // Test shared list update
    setTimeout(() => {
      this.sendSharedListUpdate('Family Shopping', 'Sarah', 'added', 'Chocolate');
    }, 6000);

    console.log('✅ Test notifications scheduled');
  }
}