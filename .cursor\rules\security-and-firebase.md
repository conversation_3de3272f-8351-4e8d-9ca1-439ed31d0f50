# 🔐 Security & Firebase Architecture Rules

## 🚨 Critical Security Requirements

### API Key Security
- **NEVER** commit API keys to git or use `EXPO_PUBLIC_` prefix for secrets in production
- **ALWAYS** use Firebase Cloud Functions for external API calls (Gemini, Algolia)
- **REVOKE** any exposed keys immediately and clean git history
- **USE** environment variables only on secure server-side (Firebase Functions)

### Firebase Cloud Functions Architecture
```typescript
// ✅ CORRECT: Secure server-side API calls
export const generateRecipes = functions.https.onRequest(async (req, res) => {
  const geminiApiKey = functions.config().gemini?.api_key; // Secure!
  // ... API call logic
});

// ❌ WRONG: Client-side exposed keys
const GEMINI_KEY = process.env.EXPO_PUBLIC_GEMINI_API_KEY; // Vulnerable!
```

## 🔥 Firebase Integration Patterns

### Cloud Functions Structure
```typescript
// functions/src/index.ts
import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';

// CORS headers for client requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

// Environment variables accessed securely
const apiKey = functions.config().service?.api_key;
```

### Client-Side Firebase Service
```typescript
// src/services/firebaseService.ts
class FirebaseApiService {
  private baseUrl = `https://us-central1-${PROJECT_ID}.cloudfunctions.net`;
  
  async callSecureFunction(endpoint: string, data: any) {
    const response = await fetch(`${this.baseUrl}/${endpoint}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    // ... error handling
  }
}
```

## 🎨 Centralized Theme System

### Theme Usage Patterns
```typescript
// ✅ CORRECT: Use centralized theme
import { theme, getThemeColors, commonStyles } from '../styles';

const MyComponent = ({ themeMode = 'light' }) => {
  const colors = getThemeColors(themeMode);
  
  return (
    <View style={[commonStyles.card, { backgroundColor: colors.surface }]}>
      <Text style={{ color: colors.text, fontSize: theme.typography.fontSize.lg }}>
        Content
      </Text>
    </View>
  );
};

// ❌ WRONG: Hardcoded values
<View style={{ backgroundColor: '#fff', padding: 16 }}>
```

### Theme Architecture
- **Core Theme**: `src/styles/theme.ts` - tokens, colors, typography, spacing
- **Common Styles**: `src/styles/commonStyles.ts` - reusable UI patterns
- **Recipe Styles**: `src/styles/recipeStyles.ts` - recipe-specific components
- **Always import** from `src/styles` index file

### Dark Mode Support
```typescript
// Theme-aware component pattern
const useStyles = createThemedStyles((theme, colors) => ({
  container: {
    backgroundColor: colors.background,
    padding: theme.spacing.base,
  }
}));
```

## 🪝 Custom Hook Patterns

### App Bootstrap Hook
```typescript
// ✅ CORRECT: Centralized app initialization
const { isAppReady, shouldShowOnboarding, isAuthenticated, error } = useAppBootstrap();

if (!isAppReady) {
  return <LoadingScreen error={error} />;
}

// ❌ WRONG: Multiple scattered state variables
const [loading, setLoading] = useState(true);
const [isFirstTime, setIsFirstTime] = useState(null);
const [authLoading, setAuthLoading] = useState(true);
```

### Hook Organization
- **Location**: `src/hooks/` directory
- **Index**: Export from `src/hooks/index.ts`
- **Naming**: `use` prefix + descriptive name
- **Types**: Export interfaces for hook return values

## 🔒 Secure Service Architecture

### Service Layer Pattern
```typescript
// Secure service with fallbacks
class SecureAiApiService {
  async generateRecipes(params: RecipeParams): Promise<Recipe[]> {
    try {
      // Try Firebase backend first
      return await firebaseApiService.generateRecipes(params);
    } catch (error) {
      // Fallback to offline service
      return await OfflineAiService.generateRecipes(params);
    }
  }
}
```

### Service Migration
- **Old**: Direct API calls with exposed keys → **New**: Firebase proxy
- **Old**: `aiApiService` → **New**: `secureAiApiService`
- **Always** maintain offline fallbacks for reliability

## 📱 Component Architecture Updates

### Error Boundaries
```typescript
// Wrap navigation and screens in ErrorBoundary
<ErrorBoundary onError={logError}>
  <AuthProvider>
    <AppNavigator />
  </AuthProvider>
</ErrorBoundary>
```

### RecipeCard Security
```typescript
// Prevent event propagation for nested touchables
onPress={(event) => {
  event.stopPropagation(); // Prevent parent TouchableOpacity
  onDelete(recipe.id, recipe.title);
}}
```

## 🔧 Development Setup Rules

### Environment Configuration
```bash
# Firebase Functions environment variables (SECURE)
firebase functions:config:set \
  gemini.api_key="YOUR_SECURE_KEY" \
  algolia.app_id="YOUR_APP_ID" \
  algolia.api_key="YOUR_SEARCH_KEY"

# Client environment (Firebase config - safe to expose)
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_API_KEY=your-firebase-web-key  # Not a secret
```

### Git Security
```gitignore
# Always ignore
.env
.env.local
.env.*.local
functions/.runtimeconfig.json

# Never commit these
**/secrets.json
**/api-keys.json
```

## 🚀 Deployment Patterns

### Firebase Functions Deployment
```bash
# Build and deploy functions
cd functions
npm run build
firebase deploy --only functions

# Test deployed functions
curl -X POST "https://your-project.cloudfunctions.net/healthCheck"
```

### Error Handling
```typescript
// Always include proper error handling
try {
  const result = await secureApiService.generateRecipes(params);
  return result;
} catch (error) {
  console.error('Recipe generation failed:', error);
  // Show user-friendly error message
  // Fall back to offline service
}
```

## 📋 Code Review Checklist

### Security Review
- [ ] No API keys in client code
- [ ] All external APIs go through Firebase
- [ ] Proper CORS configuration
- [ ] Input validation on server side
- [ ] Rate limiting implemented

### Theme Consistency
- [ ] Using centralized theme tokens
- [ ] Dark mode compatible
- [ ] Consistent spacing (4px grid)
- [ ] Proper TypeScript types

### Architecture Compliance
- [ ] Custom hooks for complex state
- [ ] Error boundaries implemented  
- [ ] Offline fallbacks available
- [ ] Proper TypeScript interfaces

## 🛡️ Security Best Practices Summary

1. **Server-Side API Keys**: Use Firebase Cloud Functions
2. **Client Security**: No secrets in EXPO_PUBLIC_ variables
3. **Git Hygiene**: Never commit .env or API keys
4. **Access Control**: Validate requests on server side
5. **Monitoring**: Log API usage and errors
6. **Rotation**: Change API keys regularly
7. **Fallbacks**: Always have offline alternatives

**Remember**: Security is not optional. Every API call should go through secure Firebase backend in production. 