import React, { useState, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Dimensions,
  Modal,
  ScrollView,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';
import { SmartSearchBar } from '../../components/SmartSearchBar';
import { ModernFilterPills } from '../../components/ModernFilterPills';
import { useRecipeDataWithFallback } from '../../hooks/useRecipeData';

const { width } = Dimensions.get('window');
const ITEM_WIDTH = (width - 48) / 2;

interface Recipe {
  id: string;
  title: string;
  description?: string;
  ingredients: string[];
  instructions: string;
  image_url?: string;
  prep_time?: number;
  cook_time?: number;
  servings?: number;
  difficulty?: string;
  cuisine?: string;
  tags?: string[];
  is_public?: boolean;
}

const CATEGORIES = [
  { id: 'All', label: 'All Recipes', icon: 'restaurant' },
  { id: 'Breakfast', label: 'Breakfast', icon: 'sunny' },
  { id: 'Lunch', label: 'Lunch', icon: 'cafe' },
  { id: 'Dinner', label: 'Dinner', icon: 'moon' },
  { id: 'Snack', label: 'Snacks', icon: 'fast-food' },
  { id: 'Dessert', label: 'Desserts', icon: 'ice-cream' },
];

const CUISINES = [
  { id: 'All', label: 'All Cuisines' },
  { id: 'Italian', label: 'Italian' },
  { id: 'Asian', label: 'Asian' },
  { id: 'Mexican', label: 'Mexican' },
  { id: 'Indian', label: 'Indian' },
  { id: 'Mediterranean', label: 'Mediterranean' },
];

export const RecipesScreen: React.FC = () => {
  const { colors, mode } = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [selectedCuisine, setSelectedCuisine] = useState('All');
  const [selectedRecipe, setSelectedRecipe] = useState<Recipe | null>(null);
  const [showRecipeModal, setShowRecipeModal] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Use recipe data hook with fallback to mock data
  const {
    recipes,
    isLoading,
    isRefreshing,
    refresh,
  } = useRecipeDataWithFallback({
    searchQuery,
    category: selectedCategory,
    cuisine: selectedCuisine,
  });

  // Filter recipes based on search and filters
  const filteredRecipes = useMemo(() => {
    let filtered = recipes;

    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(recipe => 
        recipe.title.toLowerCase().includes(query) ||
        recipe.description?.toLowerCase().includes(query) ||
        recipe.ingredients.some(ingredient => 
          ingredient.toLowerCase().includes(query)
        ) ||
        recipe.tags?.some(tag => tag.toLowerCase().includes(query))
      );
    }

    if (selectedCategory !== 'All') {
      filtered = filtered.filter(recipe => 
        recipe.tags?.includes(selectedCategory.toLowerCase())
      );
    }

    if (selectedCuisine !== 'All') {
      filtered = filtered.filter(recipe => 
        recipe.cuisine?.toLowerCase() === selectedCuisine.toLowerCase()
      );
    }

    return filtered;
  }, [recipes, searchQuery, selectedCategory, selectedCuisine]);

  const handleRecipePress = useCallback((recipe: Recipe) => {
    setSelectedRecipe(recipe);
    setShowRecipeModal(true);
  }, []);

  const handleAddIngredientsToList = useCallback((recipe: Recipe) => {
    // TODO: Implement adding recipe ingredients to shopping list
    console.log('Adding ingredients to shopping list:', recipe.ingredients);
  }, []);

  const renderHeader = () => (
    <View style={[styles.header, { backgroundColor: colors.background }]}>
      <View style={styles.headerTop}>
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          Recipes
        </Text>
        <View style={styles.headerButtons}>
          <TouchableOpacity
            style={[styles.headerButton, { backgroundColor: colors.surface }]}
            onPress={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
          >
            <Ionicons
              name={viewMode === 'grid' ? 'list' : 'grid'}
              size={20}
              color={colors.text}
            />
          </TouchableOpacity>
        </View>
      </View>

      {/* Smart Search Bar */}
      <SmartSearchBar
        value={searchQuery}
        onChangeText={setSearchQuery}
        onSuggestionSelect={setSearchQuery}
        placeholder="Search recipes..."
        themeMode={mode}
        maxSuggestions={6}
        showCategories={true}
      />

      {/* Category Filter Pills */}
      <ModernFilterPills
        options={CATEGORIES}
        selectedId={selectedCategory}
        onSelectionChange={setSelectedCategory}
        themeMode={mode}
        showCounts={false}
        scrollable={true}
      />

      {/* Cuisine Filter Pills */}
      <ModernFilterPills
        options={CUISINES}
        selectedId={selectedCuisine}
        onSelectionChange={setSelectedCuisine}
        themeMode={mode}
        showCounts={false}
        scrollable={true}
      />

      <View style={styles.resultsCount}>
        <Text style={[styles.resultsCountText, { color: colors.textSecondary }]}>
          {filteredRecipes.length} recipes found
        </Text>
      </View>
    </View>
  );

  const renderRecipeCard = ({ item }: { item: Recipe }) => (
    <TouchableOpacity
      style={[
        styles.recipeCard,
        viewMode === 'grid' ? styles.gridCard : styles.listCard,
        { backgroundColor: colors.surface }
      ]}
      onPress={() => handleRecipePress(item)}
      activeOpacity={0.7}
    >
      <View style={styles.recipeImageContainer}>
        {item.image_url ? (
          <Image
            source={{ uri: item.image_url }}
            style={styles.recipeImage}
            resizeMode="cover"
          />
        ) : (
          <View style={[styles.recipeImagePlaceholder, { backgroundColor: colors.border }]}>
            <Ionicons name="restaurant" size={32} color={colors.textSecondary} />
          </View>
        )}
      </View>

      <View style={styles.recipeInfo}>
        <Text style={[styles.recipeTitle, { color: colors.text }]} numberOfLines={2}>
          {item.title}
        </Text>
        
        {item.description && (
          <Text style={[styles.recipeDescription, { color: colors.textSecondary }]} numberOfLines={2}>
            {item.description}
          </Text>
        )}

        <View style={styles.recipeMetadata}>
          {item.prep_time && (
            <View style={styles.metadataItem}>
              <Ionicons name="time" size={14} color={colors.textSecondary} />
              <Text style={[styles.metadataText, { color: colors.textSecondary }]}>
                {item.prep_time}m
              </Text>
            </View>
          )}
          
          {item.servings && (
            <View style={styles.metadataItem}>
              <Ionicons name="people" size={14} color={colors.textSecondary} />
              <Text style={[styles.metadataText, { color: colors.textSecondary }]}>
                {item.servings}
              </Text>
            </View>
          )}
          
          {item.difficulty && (
            <View style={styles.metadataItem}>
              <Ionicons name="bar-chart" size={14} color={colors.textSecondary} />
              <Text style={[styles.metadataText, { color: colors.textSecondary }]}>
                {item.difficulty}
              </Text>
            </View>
          )}
        </View>
      </View>

      <TouchableOpacity
        style={[styles.addButton, { backgroundColor: colors.primary }]}
        onPress={() => handleAddIngredientsToList(item)}
        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
      >
        <Ionicons name="add" size={20} color="white" />
      </TouchableOpacity>
    </TouchableOpacity>
  );

  const renderRecipeModal = () => (
    <Modal
      visible={showRecipeModal}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={() => setShowRecipeModal(false)}
    >
      <View style={[styles.modalContainer, { backgroundColor: colors.background }]}>
        <View style={styles.modalHeader}>
          <TouchableOpacity
            onPress={() => setShowRecipeModal(false)}
            style={styles.modalCloseButton}
          >
            <Ionicons name="close" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.modalTitle, { color: colors.text }]}>
            Recipe Details
          </Text>
          <TouchableOpacity
            onPress={() => selectedRecipe && handleAddIngredientsToList(selectedRecipe)}
            style={[styles.modalAddButton, { backgroundColor: colors.primary }]}
          >
            <Ionicons name="cart" size={20} color="white" />
          </TouchableOpacity>
        </View>

        {selectedRecipe && (
          <ScrollView style={styles.modalContent}>
            {selectedRecipe.image_url && (
              <Image
                source={{ uri: selectedRecipe.image_url }}
                style={styles.modalImage}
                resizeMode="cover"
              />
            )}
            
            <View style={styles.modalBody}>
              <Text style={[styles.modalRecipeTitle, { color: colors.text }]}>
                {selectedRecipe.title}
              </Text>
              
              {selectedRecipe.description && (
                <Text style={[styles.modalRecipeDescription, { color: colors.textSecondary }]}>
                  {selectedRecipe.description}
                </Text>
              )}

              <View style={styles.modalMetadata}>
                {selectedRecipe.prep_time && (
                  <View style={styles.modalMetadataItem}>
                    <Ionicons name="time" size={16} color={colors.primary} />
                    <Text style={[styles.modalMetadataText, { color: colors.text }]}>
                      Prep: {selectedRecipe.prep_time}m
                    </Text>
                  </View>
                )}
                
                {selectedRecipe.cook_time && (
                  <View style={styles.modalMetadataItem}>
                    <Ionicons name="flame" size={16} color={colors.primary} />
                    <Text style={[styles.modalMetadataText, { color: colors.text }]}>
                      Cook: {selectedRecipe.cook_time}m
                    </Text>
                  </View>
                )}
                
                {selectedRecipe.servings && (
                  <View style={styles.modalMetadataItem}>
                    <Ionicons name="people" size={16} color={colors.primary} />
                    <Text style={[styles.modalMetadataText, { color: colors.text }]}>
                      Serves: {selectedRecipe.servings}
                    </Text>
                  </View>
                )}
              </View>

              <View style={styles.section}>
                <Text style={[styles.sectionTitle, { color: colors.text }]}>
                  Ingredients
                </Text>
                {selectedRecipe.ingredients.map((ingredient, index) => (
                  <View key={index} style={styles.ingredientItem}>
                    <Text style={[styles.ingredientText, { color: colors.text }]}>
                      • {ingredient}
                    </Text>
                  </View>
                ))}
              </View>

              <View style={styles.section}>
                <Text style={[styles.sectionTitle, { color: colors.text }]}>
                  Instructions
                </Text>
                <Text style={[styles.instructionsText, { color: colors.text }]}>
                  {selectedRecipe.instructions}
                </Text>
              </View>
            </View>
          </ScrollView>
        )}
      </View>
    </Modal>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="restaurant-outline" size={64} color={colors.border} />
      <Text style={[styles.emptyTitle, { color: colors.text }]}>
        No recipes found
      </Text>
      <Text style={[styles.emptySubtext, { color: colors.textSecondary }]}>
        Try adjusting your search or filters
      </Text>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <FlatList
        data={filteredRecipes}
        renderItem={renderRecipeCard}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmptyState}
        numColumns={viewMode === 'grid' ? 2 : 1}
        key={viewMode}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={refresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      />

      {renderRecipeModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  listContainer: {
    padding: 16,
  },
  header: {
    padding: 16,
    marginBottom: 16,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  headerButton: {
    padding: 8,
    borderRadius: 8,
  },
  resultsCount: {
    marginTop: 8,
  },
  resultsCountText: {
    fontSize: 14,
  },
  recipeCard: {
    borderRadius: 12,
    padding: 12,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  gridCard: {
    width: ITEM_WIDTH,
    marginHorizontal: 4,
  },
  listCard: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
  },
  recipeImageContainer: {
    alignItems: 'center',
    marginBottom: 8,
  },
  recipeImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
  },
  recipeImagePlaceholder: {
    width: 60,
    height: 60,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  recipeInfo: {
    flex: 1,
  },
  recipeTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  recipeDescription: {
    fontSize: 12,
    marginBottom: 8,
  },
  recipeMetadata: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  metadataItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metadataText: {
    fontSize: 11,
  },
  addButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 8,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  modalCloseButton: {
    padding: 8,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  modalAddButton: {
    padding: 8,
    borderRadius: 8,
  },
  modalContent: {
    flex: 1,
  },
  modalImage: {
    width: '100%',
    height: 200,
  },
  modalBody: {
    padding: 16,
  },
  modalRecipeTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  modalRecipeDescription: {
    fontSize: 16,
    marginBottom: 16,
  },
  modalMetadata: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    marginBottom: 24,
  },
  modalMetadataItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  modalMetadataText: {
    fontSize: 14,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  ingredientItem: {
    marginBottom: 8,
  },
  ingredientText: {
    fontSize: 14,
    lineHeight: 20,
  },
  instructionsText: {
    fontSize: 14,
    lineHeight: 22,
  },
});