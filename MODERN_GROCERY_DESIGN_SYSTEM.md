# 🛒 Modern Grocery Shopping App Design System

A comprehensive, premium design system specifically crafted for multi-store grocery price comparison apps, featuring sophisticated color palettes, modern typography, and premium interaction patterns.

## 🎨 **1. Sophisticated Color System**

### Primary Palette (WCAG AA Compliant)
```typescript
const groceryColors = {
  // Primary Brand Colors - Fresh & Trustworthy
  primary: {
    50: '#F0F9FF',    // Ultra light sky
    100: '#E0F2FE',   // Very light sky  
    200: '#BAE6FD',   // Light sky
    300: '#7DD3FC',   // Medium sky
    400: '#38BDF8',   // Bright sky
    500: '#0EA5E9',   // Primary blue (4.51:1 contrast)
    600: '#0284C7',   // Deep blue
    700: '#0369A1',   // Darker blue
    800: '#075985',   // Very dark blue
    900: '#0C4A6E',   // Ultra dark blue
  },

  // Fresh Green - Success & Savings
  success: {
    50: '#ECFDF5',
    100: '#D1FAE5', 
    500: '#10B981',   // Fresh green (3.36:1 contrast for large text)
    600: '#059669',   // Deep green (4.5:1+ contrast)
    700: '#047857',
  },

  // Warm Coral - Warnings & Sales
  warning: {
    50: '#FEF2F2',
    100: '#FEE2E2',
    500: '#F87171',   // Soft coral
    600: '#EF4444',   // Warning red (4.5:1+ contrast)
    700: '#DC2626',
  },

  // Premium Neutrals - Sophisticated Grays
  neutral: {
    0: '#FFFFFF',     // Pure white
    50: '#FAFAFA',    // Off white
    100: '#F5F5F5',   // Light gray
    200: '#E5E5E5',   // Border gray
    300: '#D4D4D4',   // Muted border
    400: '#A3A3A3',   // Placeholder text
    500: '#737373',   // Secondary text (4.61:1 contrast)
    600: '#525252',   // Primary text support
    700: '#404040',   // Dark text (10.48:1 contrast)
    800: '#262626',   // Darker text
    900: '#171717',   // Ultra dark text (15.29:1 contrast)
  },

  // Store Brand Integration
  stores: {
    woolworths: {
      primary: '#00A651',
      light: '#E8F5E8',
      text: '#FFFFFF',
    },
    newworld: {
      primary: '#E31E24', 
      light: '#FDE8E8',
      text: '#FFFFFF',
    },
    paknsave: {
      primary: '#FFD100',
      light: '#FFF9E6',
      text: '#1F2937',
    },
  },

  // Semantic UI Colors
  semantic: {
    background: '#FAFAFA',
    surface: '#FFFFFF',
    surfaceElevated: '#FFFFFF',
    border: '#E5E5E5',
    borderLight: '#F5F5F5',
    overlay: 'rgba(0, 0, 0, 0.4)',
    overlayLight: 'rgba(0, 0, 0, 0.2)',
  }
};
```

### Dark Theme Palette
```typescript
const darkGroceryColors = {
  primary: {
    500: '#60A5FA',   // Lighter blue for dark bg
    600: '#3B82F6',
  },
  
  neutral: {
    0: '#000000',
    50: '#0A0A0A',    // Ultra dark
    100: '#171717',   // Very dark
    200: '#262626',   // Dark
    300: '#404040',   // Medium dark
    400: '#525252',   // Gray
    500: '#737373',   // Light gray
    600: '#A3A3A3',   // Lighter gray
    700: '#D4D4D4',   // Light text
    800: '#E5E5E5',   // Lighter text
    900: '#F5F5F5',   // Ultra light text
  },

  semantic: {
    background: '#0A0A0A',
    surface: '#171717',
    surfaceElevated: '#262626',
    border: '#404040',
    borderLight: '#262626',
  }
};
```

## 📝 **2. Modern Typography Scale**

### Font Configuration
```typescript
const groceryTypography = {
  fontFamily: {
    // iOS: SF Pro Display, Android: Roboto
    primary: Platform.select({
      ios: 'SF Pro Display',
      android: 'Roboto',
      default: 'System',
    }),
    // Numbers and prices - tabular figures
    numeric: Platform.select({
      ios: 'SF Pro Display',
      android: 'Roboto',
      default: 'System',
    }),
    // Monospace for codes/SKUs
    mono: Platform.select({
      ios: 'SF Mono',
      android: 'Roboto Mono', 
      default: 'monospace',
    }),
  },

  // Modern scale (1.2 ratio)
  fontSize: {
    xs: 12,     // Micro text, disclaimers
    sm: 14,     // Support text, labels
    base: 16,   // Body text, comfortable reading
    lg: 18,     // Emphasis text, card headers
    xl: 20,     // Section headers
    '2xl': 24,  // Page headers  
    '3xl': 28,  // Large headers
    '4xl': 32,  // Hero text
    '5xl': 40,  // Display text
  },

  fontWeight: {
    light: '300',
    normal: '400',
    medium: '500',    // iOS preferred
    semibold: '600',  // Headers, emphasis
    bold: '700',      // Strong emphasis
    heavy: '800',     // Rare use
  },

  lineHeight: {
    xs: 16,     // 1.33 ratio
    sm: 20,     // 1.43 ratio  
    base: 24,   // 1.5 ratio (accessibility)
    lg: 28,     // 1.56 ratio
    xl: 28,     // 1.4 ratio
    '2xl': 32,  // 1.33 ratio
    '3xl': 36,  // 1.29 ratio
    '4xl': 40,  // 1.25 ratio
    '5xl': 48,  // 1.2 ratio
  },

  letterSpacing: {
    tight: -0.5,    // Large headers
    normal: 0,      // Body text
    wide: 0.5,      // Buttons, labels
    wider: 1,       // All caps text
  },
};
```

### Text Styles
```typescript
const textStyles = StyleSheet.create({
  // Headers
  heroTitle: {
    fontSize: groceryTypography.fontSize['4xl'],
    fontWeight: groceryTypography.fontWeight.bold,
    lineHeight: groceryTypography.lineHeight['4xl'],
    letterSpacing: groceryTypography.letterSpacing.tight,
    color: groceryColors.neutral[900],
  },

  pageTitle: {
    fontSize: groceryTypography.fontSize['2xl'], 
    fontWeight: groceryTypography.fontWeight.semibold,
    lineHeight: groceryTypography.lineHeight['2xl'],
    color: groceryColors.neutral[900],
  },

  sectionTitle: {
    fontSize: groceryTypography.fontSize.lg,
    fontWeight: groceryTypography.fontWeight.semibold, 
    lineHeight: groceryTypography.lineHeight.lg,
    color: groceryColors.neutral[800],
  },

  // Body Text
  bodyLarge: {
    fontSize: groceryTypography.fontSize.lg,
    fontWeight: groceryTypography.fontWeight.normal,
    lineHeight: groceryTypography.lineHeight.lg,
    color: groceryColors.neutral[700],
  },

  body: {
    fontSize: groceryTypography.fontSize.base,
    fontWeight: groceryTypography.fontWeight.normal,
    lineHeight: groceryTypography.lineHeight.base,
    color: groceryColors.neutral[700],
  },

  bodyMedium: {
    fontSize: groceryTypography.fontSize.base,
    fontWeight: groceryTypography.fontWeight.medium,
    lineHeight: groceryTypography.lineHeight.base,
    color: groceryColors.neutral[700],
  },

  // Supporting Text
  caption: {
    fontSize: groceryTypography.fontSize.sm,
    fontWeight: groceryTypography.fontWeight.normal,
    lineHeight: groceryTypography.lineHeight.sm,
    color: groceryColors.neutral[500],
  },

  captionMedium: {
    fontSize: groceryTypography.fontSize.sm,
    fontWeight: groceryTypography.fontWeight.medium,
    lineHeight: groceryTypography.lineHeight.sm,
    color: groceryColors.neutral[600],
  },

  micro: {
    fontSize: groceryTypography.fontSize.xs,
    fontWeight: groceryTypography.fontWeight.normal,
    lineHeight: groceryTypography.lineHeight.xs,
    color: groceryColors.neutral[500],
  },

  // Price Text (special formatting)
  priceHero: {
    fontSize: groceryTypography.fontSize.xl,
    fontWeight: groceryTypography.fontWeight.bold,
    lineHeight: groceryTypography.lineHeight.xl,
    color: groceryColors.primary[600],
    fontFamily: groceryTypography.fontFamily.numeric,
    fontVariant: ['tabular-nums'], // Align prices nicely
  },

  priceRegular: {
    fontSize: groceryTypography.fontSize.base,
    fontWeight: groceryTypography.fontWeight.semibold,
    lineHeight: groceryTypography.lineHeight.base,
    color: groceryColors.neutral[800],
    fontFamily: groceryTypography.fontFamily.numeric,
  },

  priceSmall: {
    fontSize: groceryTypography.fontSize.sm,
    fontWeight: groceryTypography.fontWeight.medium,
    lineHeight: groceryTypography.lineHeight.sm,
    color: groceryColors.neutral[600],
    fontFamily: groceryTypography.fontFamily.numeric,
  },

  // Interactive Text
  linkText: {
    fontSize: groceryTypography.fontSize.base,
    fontWeight: groceryTypography.fontWeight.medium,
    lineHeight: groceryTypography.lineHeight.base,
    color: groceryColors.primary[600],
  },

  buttonText: {
    fontSize: groceryTypography.fontSize.base,
    fontWeight: groceryTypography.fontWeight.semibold,
    lineHeight: groceryTypography.lineHeight.base,
    letterSpacing: groceryTypography.letterSpacing.wide,
    color: groceryColors.neutral[0],
  },
});
```

## 📐 **3. Spacing & Layout System**

### Consistent 4px Grid System
```typescript
const spacing = {
  // Base scale (4px increments)
  0: 0,
  1: 4,       // 0.25rem
  2: 8,       // 0.5rem  
  3: 12,      // 0.75rem
  4: 16,      // 1rem - base unit
  5: 20,      // 1.25rem
  6: 24,      // 1.5rem
  8: 32,      // 2rem
  10: 40,     // 2.5rem
  12: 48,     // 3rem
  16: 64,     // 4rem
  20: 80,     // 5rem
  24: 96,     // 6rem

  // Semantic spacing
  xs: 4,      // Tight spacing
  sm: 8,      // Small spacing  
  md: 12,     // Medium spacing
  base: 16,   // Standard spacing
  lg: 20,     // Large spacing
  xl: 24,     // Extra large
  '2xl': 32,  // 2x large
  '3xl': 40,  // 3x large
  '4xl': 48,  // 4x large
  '5xl': 64,  // 5x large
};

// Layout constants
const layout = {
  // Touch targets (accessibility)
  minTouchTarget: 44,
  comfortableTouchTarget: 48,
  largeTouchTarget: 56,

  // Container widths
  maxContentWidth: 428,    // iPhone 14 Pro Max width
  tabletBreakpoint: 768,
  cardMaxWidth: 400,

  // Common dimensions
  headerHeight: Platform.select({ ios: 44, android: 56 }),
  tabBarHeight: 80,
  bottomSafeArea: Platform.select({ ios: 34, android: 0 }),
};
```

## 🎨 **4. Component Specifications**

### Modern Product Cards
```typescript
const productCardStyles = StyleSheet.create({
  // Main product card container
  productCard: {
    backgroundColor: groceryColors.semantic.surface,
    borderRadius: 16,
    padding: spacing.base,
    marginBottom: spacing.base,
    // Premium shadow system
    shadowColor: groceryColors.neutral[900],
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.06,
    shadowRadius: 12,
    elevation: 3,
    // Subtle border for depth
    borderWidth: 1,
    borderColor: groceryColors.semantic.borderLight,
  },

  // Grid view variant (2 columns)
  productCardGrid: {
    width: (screenWidth - spacing.base * 3) / 2,
    marginHorizontal: spacing[1],
  },

  // List view variant (full width)
  productCardList: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.base,
  },

  // Product image container
  productImageContainer: {
    width: 80,
    height: 80,
    borderRadius: 12,
    backgroundColor: groceryColors.neutral[50],
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.sm,
    // Subtle inner shadow
    shadowColor: groceryColors.neutral[400],
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },

  productImage: {
    width: 64,
    height: 64,
    borderRadius: 8,
    resizeMode: 'contain',
  },

  // Product info section
  productInfo: {
    flex: 1,
    gap: spacing[1],
  },

  productName: {
    ...textStyles.bodyMedium,
    marginBottom: spacing[1],
  },

  // Price comparison section
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: spacing.sm,
  },

  priceRange: {
    ...textStyles.priceRegular,
  },

  savings: {
    backgroundColor: groceryColors.success[100],
    paddingHorizontal: spacing[2],
    paddingVertical: spacing[1],
    borderRadius: 8,
  },

  savingsText: {
    ...textStyles.micro,
    color: groceryColors.success[700],
    fontWeight: groceryTypography.fontWeight.semibold,
  },

  // Store indicators row
  storeIndicators: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing[1],
    marginBottom: spacing.sm,
  },

  storeIndicator: {
    width: 28,
    height: 28, 
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    // Dynamic background based on store
  },

  storeIndicatorText: {
    fontSize: 10,
    fontWeight: groceryTypography.fontWeight.bold,
    // Dynamic color based on store
  },

  moreStoresIndicator: {
    backgroundColor: groceryColors.neutral[200],
    paddingHorizontal: spacing[2],
    paddingVertical: spacing[1],
    borderRadius: 12,
  },

  moreStoresText: {
    ...textStyles.micro,
    color: groceryColors.neutral[600],
  },

  // Add to list button
  addButton: {
    position: 'absolute',
    bottom: spacing.base,
    right: spacing.base,
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: groceryColors.primary[500],
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: groceryColors.primary[500],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
});
```

### Modern Filter System
```typescript
const filterStyles = StyleSheet.create({
  // Filter container
  filterContainer: {
    paddingVertical: spacing.base,
    paddingHorizontal: spacing.base,
    backgroundColor: groceryColors.semantic.surface,
  },

  // Category pills
  categoryScrollContainer: {
    paddingVertical: spacing.sm,
  },

  categoryPill: {
    paddingHorizontal: spacing.base,
    paddingVertical: spacing.sm,
    marginRight: spacing.sm,
    borderRadius: 24,
    backgroundColor: groceryColors.neutral[100],
    borderWidth: 1,
    borderColor: groceryColors.semantic.border,
    // Smooth transitions
    minWidth: 80,
    alignItems: 'center',
    justifyContent: 'center',
  },

  categoryPillActive: {
    backgroundColor: groceryColors.primary[500],
    borderColor: groceryColors.primary[500],
    // Enhanced shadow for active state
    shadowColor: groceryColors.primary[500],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },

  categoryPillText: {
    ...textStyles.captionMedium,
    color: groceryColors.neutral[700],
  },

  categoryPillTextActive: {
    color: groceryColors.neutral[0],
    fontWeight: groceryTypography.fontWeight.semibold,
  },

  // Store selector
  storeSelector: {
    flexDirection: 'row',
    gap: spacing.sm,
    marginTop: spacing.base,
  },

  storeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: spacing.sm,
    borderRadius: 12,
    backgroundColor: groceryColors.neutral[50],
    borderWidth: 1,
    borderColor: groceryColors.semantic.border,
    gap: spacing[2],
  },

  storeButtonActive: {
    backgroundColor: groceryColors.neutral[0],
    borderColor: groceryColors.primary[300],
    shadowColor: groceryColors.primary[500],
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
});
```

### Search Bar Design
```typescript
const searchStyles = StyleSheet.create({
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: groceryColors.neutral[50],
    borderRadius: 16,
    paddingHorizontal: spacing.base,
    paddingVertical: spacing.sm,
    marginVertical: spacing.base,
    borderWidth: 1,
    borderColor: groceryColors.semantic.borderLight,
  },

  searchContainerFocused: {
    backgroundColor: groceryColors.neutral[0],
    borderColor: groceryColors.primary[300],
    shadowColor: groceryColors.primary[500],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },

  searchIcon: {
    marginRight: spacing[2],
    color: groceryColors.neutral[400],
  },

  searchInput: {
    flex: 1,
    ...textStyles.body,
    padding: 0, // Remove default padding
    color: groceryColors.neutral[900],
  },

  clearButton: {
    padding: spacing[1],
    marginLeft: spacing[1],
  },

  clearIcon: {
    color: groceryColors.neutral[400],
  },
});
```

## 🌊 **5. Shadow & Elevation System**

```typescript
const shadows = {
  // Subtle card shadows
  card: {
    shadowColor: groceryColors.neutral[900],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.04,
    shadowRadius: 8,
    elevation: 2,
  },

  // Medium elevation
  cardElevated: {
    shadowColor: groceryColors.neutral[900], 
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.06,
    shadowRadius: 12,
    elevation: 4,
  },

  // High elevation (modals, floating buttons)
  floating: {
    shadowColor: groceryColors.neutral[900],
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.12,
    shadowRadius: 20,
    elevation: 8,
  },

  // Premium glow effect
  glow: {
    shadowColor: groceryColors.primary[500],
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 6,
  },

  // Pressed state (reduce elevation)
  pressed: {
    shadowColor: groceryColors.neutral[900],
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.02,
    shadowRadius: 4,
    elevation: 1,
  },
};
```

## 🎭 **6. Micro-Interactions & Animations**

```typescript
const animations = {
  // Timing functions
  timing: {
    fast: 150,
    normal: 250,
    slow: 400,
    slower: 600,
  },

  // Easing curves
  easing: {
    ease: [0.25, 0.1, 0.25, 1],
    easeInOut: [0.4, 0, 0.2, 1],
    easeOut: [0, 0, 0.2, 1],
    spring: [0.68, -0.55, 0.265, 1.55],
  },

  // Scale transforms for press states
  pressScale: 0.96,
  tapScale: 0.98,
  
  // Opacity states
  disabledOpacity: 0.4,
  pressedOpacity: 0.8,
};

// Animation helpers
const createPressAnimation = (scale = animations.pressScale) => {
  return Animated.spring(animatedValue, {
    toValue: scale,
    duration: animations.timing.fast,
    useNativeDriver: true,
  });
};

const createHoverAnimation = () => {
  return Animated.timing(animatedValue, {
    toValue: 1.02,
    duration: animations.timing.normal,
    useNativeDriver: true,
  });
};
```

## 📱 **7. Component Implementation Examples**

### Premium Product Card Component
```typescript
const PremiumProductCard: React.FC<ProductCardProps> = ({ 
  product, 
  onPress, 
  onAddToList 
}) => {
  const [pressed, setPressed] = useState(false);
  const scaleValue = useRef(new Animated.Value(1)).current;
  
  const handlePressIn = () => {
    setPressed(true);
    Animated.spring(scaleValue, {
      toValue: animations.pressScale,
      duration: animations.timing.fast,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    setPressed(false);
    Animated.spring(scaleValue, {
      toValue: 1,
      duration: animations.timing.fast,
      useNativeDriver: true,
    }).start();
  };

  return (
    <Animated.View style={[
      productCardStyles.productCard,
      { transform: [{ scale: scaleValue }] }
    ]}>
      <TouchableOpacity
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        onPress={onPress}
        activeOpacity={1}
        style={productCardStyles.cardContent}
      >
        {/* Product Image */}
        <View style={productCardStyles.productImageContainer}>
          <OptimizedImage
            source={{ uri: product.imageUrl }}
            style={productCardStyles.productImage}
            placeholder={<ProductPlaceholder />}
          />
        </View>

        {/* Product Info */}
        <View style={productCardStyles.productInfo}>
          <Text style={textStyles.bodyMedium} numberOfLines={2}>
            {product.name}
          </Text>
          
          {/* Price Range */}
          <View style={productCardStyles.priceContainer}>
            <Text style={textStyles.priceRegular}>
              ${product.lowestPrice.toFixed(2)} - ${product.highestPrice.toFixed(2)}
            </Text>
            
            {product.savings > 0 && (
              <View style={productCardStyles.savings}>
                <Text style={productCardStyles.savingsText}>
                  Save ${product.savings.toFixed(2)}
                </Text>
              </View>
            )}
          </View>

          {/* Store Indicators */}
          <StoreIndicators stores={product.stores} />
        </View>

        {/* Add Button */}
        <TouchableOpacity
          style={productCardStyles.addButton}
          onPress={() => onAddToList(product)}
          hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
        >
          <Ionicons name="add" size={20} color={groceryColors.neutral[0]} />
        </TouchableOpacity>
      </TouchableOpacity>
    </Animated.View>
  );
};
```

## 🎯 **8. Usage Guidelines**

### Do's
- ✅ Use 4px spacing increments for consistency
- ✅ Maintain 4.5:1 contrast ratio for text
- ✅ Apply consistent shadow elevation
- ✅ Use semantic color names
- ✅ Implement smooth micro-interactions
- ✅ Follow 44px minimum touch targets

### Don'ts  
- ❌ Mix different border radius scales
- ❌ Use arbitrary spacing values
- ❌ Overcomplicate shadows
- ❌ Ignore accessibility contrast
- ❌ Animate too many properties
- ❌ Make touch targets too small

## 🚀 **9. Performance Optimizations**

```typescript
// Memoized styles to prevent re-creation
const memoizedStyles = useMemo(() => 
  StyleSheet.create({
    container: {
      backgroundColor: groceryColors.semantic.background,
      // ... other styles
    }
  }), [themeMode]
);

// Optimized shadow styles (use sparingly)
const optimizedShadows = {
  // Use only when needed
  premium: Platform.select({
    ios: shadows.cardElevated,
    android: { elevation: 4 },
  }),
};
```

This design system provides a cohesive, premium experience for your grocery shopping app while maintaining excellent performance and accessibility standards. The sophisticated color palette, modern typography, and refined component designs will elevate your multi-store price comparison features to professional e-commerce standards.