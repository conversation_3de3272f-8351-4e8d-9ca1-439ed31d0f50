/**
 * TEST SIZE NORMALIZATION FIXES
 * 
 * Comprehensive test to verify that the size normalization fixes prevent
 * incorrect grouping and misleading price comparisons.
 */

import { createClient } from '@supabase/supabase-js';
import { normalizeSize, sizesMatch, getSizeGroupingKey } from '../utils/stringMatching';
import { ProductDeduplicationService } from '../services/productDeduplicationService';
import { IProduct } from '../types/shoppingList';

const supabaseUrl = 'https://emjwniuqwhvohjassnrg.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVtanduaXVxd2h2b2hqYXNzbnJnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjQwMTYwNSwiZXhwIjoyMDY3OTc3NjA1fQ.oTowRoNAjlUmk10O9pSMK2BvL0XlyBb5orVRrlEryHs';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testSizeNormalizationFixes() {
  console.log('🧪 TESTING SIZE NORMALIZATION FIXES...\n');

  try {
    // Test 1: Verify enhanced size normalization
    console.log('1. Testing Enhanced Size Normalization...');
    
    const testCases = [
      // Multi-pack handling
      { input: '6 x 330ml', expected: { value: 1980, unit: 'ml' } },
      { input: '12 x 250ml', expected: { value: 3000, unit: 'ml' } },
      { input: '4 x 1L', expected: { value: 4000, unit: 'ml' } },
      
      // Pack count handling
      { input: '12 pack', expected: { value: 12, unit: 'pack' } },
      { input: '6pack', expected: { value: 6, unit: 'pack' } },
      
      // Standard volume/weight
      { input: '1L', expected: { value: 1000, unit: 'ml' } },
      { input: '2L', expected: { value: 2000, unit: 'ml' } },
      { input: '500ml', expected: { value: 500, unit: 'ml' } },
      { input: '1kg', expected: { value: 1000, unit: 'g' } },
      { input: '500g', expected: { value: 500, unit: 'g' } },
    ];

    let passedTests = 0;
    testCases.forEach((test, index) => {
      const result = normalizeSize(test.input);
      const passed = result.valid && 
                    result.value === test.expected.value && 
                    result.unit === test.expected.unit;
      
      console.log(`   ${index + 1}. "${test.input}" → ${result.value}${result.unit} ${passed ? '✅' : '❌'}`);
      if (passed) passedTests++;
    });

    console.log(`   Results: ${passedTests}/${testCases.length} tests passed`);

    // Test 2: Verify strict size matching
    console.log('\n2. Testing Strict Size Matching...');
    
    const matchingTests = [
      // Should match (same size, different formats)
      { size1: '1L', size2: '1000ml', shouldMatch: true },
      { size1: '2L', size2: '2000ml', shouldMatch: true },
      { size1: '500g', size2: '0.5kg', shouldMatch: true },
      { size1: '1kg', size2: '1000g', shouldMatch: true },
      
      // Should NOT match (different sizes)
      { size1: '1L', size2: '2L', shouldMatch: false },
      { size1: '500ml', size2: '1L', shouldMatch: false },
      { size1: '12 pack', size2: '6 pack', shouldMatch: false },
      { size1: '6 x 330ml', size2: '12 x 330ml', shouldMatch: false },
    ];

    let correctMatches = 0;
    matchingTests.forEach((test, index) => {
      const matches = sizesMatch(test.size1, test.size2, true);
      const correct = matches === test.shouldMatch;
      
      console.log(`   ${index + 1}. "${test.size1}" vs "${test.size2}": ${matches ? 'MATCH' : 'NO MATCH'} ${correct ? '✅' : '❌'}`);
      if (correct) correctMatches++;
    });

    console.log(`   Results: ${correctMatches}/${matchingTests.length} matching tests correct`);

    // Test 3: Test size grouping keys
    console.log('\n3. Testing Size Grouping Keys...');
    
    const groupingTests = [
      { size: '1L', expected: '1000ml' },
      { size: '1000ml', expected: '1000ml' },
      { size: '2L', expected: '2000ml' },
      { size: '500g', expected: '500g' },
      { size: '0.5kg', expected: '500g' },
      { size: '12 pack', expected: '12pack' },
    ];

    let correctKeys = 0;
    groupingTests.forEach((test, index) => {
      const key = getSizeGroupingKey(test.size);
      const correct = key === test.expected;
      
      console.log(`   ${index + 1}. "${test.size}" → "${key}" ${correct ? '✅' : '❌'}`);
      if (correct) correctKeys++;
    });

    console.log(`   Results: ${correctKeys}/${groupingTests.length} grouping keys correct`);

    // Test 4: Test with real problematic products
    console.log('\n4. Testing with Real Problematic Products...');
    
    // Create test products that were previously incorrectly grouped
    const testProducts: IProduct[] = [
      {
        id: '1',
        name: 'Pams Value Lite Milk 1L',
        price: 2.98,
        store: 'paknsave',
        brand: 'Pams',
        size: '1L',
        category: 'dairy'
      },
      {
        id: '2', 
        name: 'Pams Value Lite Milk 2L',
        price: 4.55,
        store: 'newworld',
        brand: 'Pams',
        size: '2L',
        category: 'dairy'
      },
      {
        id: '3',
        name: 'Pams Value Lite Milk 3L',
        price: 6.78,
        store: 'woolworths',
        brand: 'Pams',
        size: '3L',
        category: 'dairy'
      }
    ];

    const deduplicationService = new ProductDeduplicationService();
    const result = deduplicationService.deduplicateProducts(testProducts);

    console.log(`   Original products: ${testProducts.length}`);
    console.log(`   After deduplication: ${result.stats.unifiedCount}`);
    console.log(`   Groups created: ${result.stats.groupCount}`);

    // Each size should be in its own group
    const expectedGroups = 3;
    const correctGrouping = result.stats.groupCount === expectedGroups;
    
    console.log(`   Expected ${expectedGroups} groups, got ${result.stats.groupCount} ${correctGrouping ? '✅' : '❌'}`);

    if (result.groups.length > 0) {
      result.groups.forEach((group, index) => {
        const sizes = group.products.map(p => p.product.size);
        const uniqueSizes = [...new Set(sizes)];
        const singleSize = uniqueSizes.length === 1;
        
        console.log(`     Group ${index + 1}: [${sizes.join(', ')}] - Single size: ${singleSize ? '✅' : '❌'}`);
      });
    }

    // Test 5: Fetch real products and verify no incorrect grouping
    console.log('\n5. Testing with Real Database Products...');
    
    const { data: products, error } = await supabase
      .from('products')
      .select('*')
      .ilike('name', '%milk%')
      .not('price', 'is', null)
      .limit(50);

    if (error) {
      console.error('❌ Error fetching products:', error);
      return;
    }

    if (!products || products.length === 0) {
      console.log('   No milk products found for testing');
      return;
    }

    const realResult = deduplicationService.deduplicateProducts(products);
    
    console.log(`   Real products tested: ${products.length}`);
    console.log(`   Groups created: ${realResult.stats.groupCount}`);
    console.log(`   Unified products: ${realResult.stats.unifiedCount}`);

    // Check for any groups with mixed sizes
    let mixedSizeGroups = 0;
    realResult.groups.forEach(group => {
      const sizes = group.products.map(p => p.product.size || p.product.unit || '').filter(s => s !== '');
      const uniqueSizes = [...new Set(sizes)];
      
      if (uniqueSizes.length > 1) {
        mixedSizeGroups++;
        console.log(`     ⚠️  Mixed size group found: [${uniqueSizes.join(', ')}]`);
      }
    });

    console.log(`   Mixed size groups: ${mixedSizeGroups} ${mixedSizeGroups === 0 ? '✅' : '❌'}`);

    console.log('\n✅ Size normalization fixes test completed!');
    
    return {
      normalizationTests: `${passedTests}/${testCases.length}`,
      matchingTests: `${correctMatches}/${matchingTests.length}`,
      groupingTests: `${correctKeys}/${groupingTests.length}`,
      correctGrouping,
      mixedSizeGroups,
      realProductsCount: products.length
    };

  } catch (error) {
    console.error('❌ Size normalization fixes test failed:', error);
    throw error;
  }
}

// Run the test
if (require.main === module) {
  testSizeNormalizationFixes()
    .then((results) => {
      console.log('\n📊 SIZE NORMALIZATION FIXES TEST SUMMARY:');
      console.log('='.repeat(60));
      console.log(`Normalization Tests: ${results?.normalizationTests || '0/0'} passed`);
      console.log(`Matching Tests: ${results?.matchingTests || '0/0'} correct`);
      console.log(`Grouping Tests: ${results?.groupingTests || '0/0'} correct`);
      console.log(`Correct Grouping: ${results?.correctGrouping ? 'YES' : 'NO'}`);
      console.log(`Mixed Size Groups: ${results?.mixedSizeGroups || 0}`);
      console.log(`Real Products Tested: ${results?.realProductsCount || 0}`);
      
      const allTestsPassed = 
        results?.normalizationTests?.endsWith('10/10') &&
        results?.matchingTests?.endsWith('8/8') &&
        results?.groupingTests?.endsWith('6/6') &&
        results?.correctGrouping &&
        (results?.mixedSizeGroups || 0) === 0;
      
      if (allTestsPassed) {
        console.log('\n🎉 SUCCESS: All size normalization fixes working correctly!');
        console.log('   ✅ Different sizes are no longer incorrectly grouped');
        console.log('   ✅ Price comparisons are now size-consistent');
        console.log('   ✅ Multi-pack handling is accurate');
        console.log('   ✅ Size matching is strict and correct');
        console.log('   ✅ Ready for production deployment');
      } else {
        console.log('\n⚠️  ISSUES FOUND: Some fixes need attention');
        if (!results?.correctGrouping) console.log('   - Product grouping still has issues');
        if ((results?.mixedSizeGroups || 0) > 0) console.log('   - Mixed size groups still exist');
      }
      console.log('='.repeat(60));
    })
    .catch((error) => {
      console.error('❌ Test failed:', error);
    });
}

export { testSizeNormalizationFixes };
