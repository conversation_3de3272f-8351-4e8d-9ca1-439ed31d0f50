-- UPDATE SQL NORMALIZATION FOR SIZE-AWARE MATCHING
-- Run this in Supabase SQL Editor to update the normalization functions

-- Drop and recreate the normalized names function with size preservation
DROP FUNCTION IF EXISTS create_normalized_names();

CREATE OR REPLACE FUNCTION create_normalized_names()
RETURNS void AS $$
BEGIN
  -- Update normalized names using enhanced normalization that preserves size info
  UPDATE products 
  SET norm_name = lower(
    regexp_replace(
      regexp_replace(
        regexp_replace(
          regexp_replace(
            regexp_replace(name, '\b(new|sale|special|offer|limited|fresh|premium|select|choice|value)\b', ' ', 'gi'),
            '\b(\d+(?:\.\d+)?)\s*(l|litre|liter)s?\b', '\1l', 'gi'
          ),
          '\b(\d+(?:\.\d+)?)\s*(ml|millilitre|milliliter)s?\b', '\1ml', 'gi'
        ),
        '\b(\d+(?:\.\d+)?)\s*(g|gram)s?\b', '\1g', 'gi'
      ),
      '\b(\d+(?:\.\d+)?)\s*(kg|kilogram)s?\b', '\1kg', 'gi'
    )
  );
  
  -- Create index for better performance
  CREATE INDEX IF NOT EXISTS idx_products_norm_name_gin ON products USING gin(norm_name gin_trgm_ops);
END;
$$ LANGUAGE plpgsql;

-- Update the comprehensive matching function to be more size-aware
DROP FUNCTION IF EXISTS find_comprehensive_matches(FLOAT);

CREATE OR REPLACE FUNCTION find_comprehensive_matches(
  similarity_threshold FLOAT DEFAULT 0.5
)
RETURNS TABLE(
  pakn_id TEXT,
  neww_id TEXT,
  wool_id TEXT,
  pakn_name TEXT,
  neww_name TEXT,
  wool_name TEXT,
  pakn_price FLOAT,
  neww_price FLOAT,
  wool_price FLOAT,
  pakn_size TEXT,
  neww_size TEXT,
  wool_size TEXT,
  avg_similarity FLOAT
) AS $$
BEGIN
  -- Set similarity threshold for trigram matching
  PERFORM set_limit(similarity_threshold);
  
  RETURN QUERY
  SELECT 
    a.id::TEXT as pakn_id,
    b.id::TEXT as neww_id,
    c.id::TEXT as wool_id,
    a.name as pakn_name,
    b.name as neww_name,
    c.name as wool_name,
    a.price as pakn_price,
    b.price as neww_price,
    c.price as wool_price,
    COALESCE(a.size, a.unit, '') as pakn_size,
    COALESCE(b.size, b.unit, '') as neww_size,
    COALESCE(c.size, c.unit, '') as wool_size,
    (similarity(a.norm_name, b.norm_name) + similarity(a.norm_name, c.norm_name)) / 2 as avg_similarity
  FROM products a
  JOIN products b ON a.norm_name % b.norm_name 
    AND b.store = 'newworld'
    -- Size matching: only match if sizes are equivalent or both empty
    AND (
      (COALESCE(a.size, a.unit, '') = COALESCE(b.size, b.unit, '')) OR
      (COALESCE(a.size, a.unit, '') = '' AND COALESCE(b.size, b.unit, '') = '')
    )
  JOIN products c ON a.norm_name % c.norm_name 
    AND c.store = 'woolworths'
    -- Size matching: only match if sizes are equivalent or both empty
    AND (
      (COALESCE(a.size, a.unit, '') = COALESCE(c.size, c.unit, '')) OR
      (COALESCE(a.size, a.unit, '') = '' AND COALESCE(c.size, c.unit, '') = '')
    )
  WHERE a.store = 'paknsave'
    AND similarity(a.norm_name, b.norm_name) >= similarity_threshold
    AND similarity(a.norm_name, c.norm_name) >= similarity_threshold
  ORDER BY avg_similarity DESC;
END;
$$ LANGUAGE plpgsql;

-- Update the product matches table creation to be size-aware
DROP FUNCTION IF EXISTS create_product_matches_table();

CREATE OR REPLACE FUNCTION create_product_matches_table()
RETURNS void AS $$
BEGIN
  -- Drop existing table if it exists
  DROP TABLE IF EXISTS product_matches;
  
  -- Create comprehensive product matches table with size awareness
  CREATE TABLE product_matches AS
  SELECT 
    a.id as pakn_id,
    b.id as neww_id,
    c.id as wool_id,
    a.name as pakn_name,
    b.name as neww_name,
    c.name as wool_name,
    a.price as pakn_price,
    b.price as neww_price,
    c.price as wool_price,
    COALESCE(a.size, a.unit, '') as pakn_size,
    COALESCE(b.size, b.unit, '') as neww_size,
    COALESCE(c.size, c.unit, '') as wool_size,
    similarity(a.norm_name, b.norm_name) as neww_similarity,
    similarity(a.norm_name, c.norm_name) as wool_similarity,
    LEAST(a.price, COALESCE(b.price, a.price), COALESCE(c.price, a.price)) as best_price,
    GREATEST(a.price, COALESCE(b.price, a.price), COALESCE(c.price, a.price)) - 
    LEAST(a.price, COALESCE(b.price, a.price), COALESCE(c.price, a.price)) as max_savings,
    now() as created_at
  FROM products a
  LEFT JOIN products b ON a.norm_name % b.norm_name 
    AND b.store = 'newworld' 
    AND similarity(a.norm_name, b.norm_name) >= 0.5
    -- Size matching: only match if sizes are equivalent or both empty
    AND (
      (COALESCE(a.size, a.unit, '') = COALESCE(b.size, b.unit, '')) OR
      (COALESCE(a.size, a.unit, '') = '' AND COALESCE(b.size, b.unit, '') = '')
    )
  LEFT JOIN products c ON a.norm_name % c.norm_name 
    AND c.store = 'woolworths' 
    AND similarity(a.norm_name, c.norm_name) >= 0.5
    -- Size matching: only match if sizes are equivalent or both empty
    AND (
      (COALESCE(a.size, a.unit, '') = COALESCE(c.size, c.unit, '')) OR
      (COALESCE(a.size, a.unit, '') = '' AND COALESCE(c.size, c.unit, '') = '')
    )
  WHERE a.store = 'paknsave'
    AND (b.id IS NOT NULL OR c.id IS NOT NULL); -- Only include products with matches
  
  -- Create indexes for better performance
  CREATE INDEX idx_product_matches_pakn_id ON product_matches(pakn_id);
  CREATE INDEX idx_product_matches_neww_id ON product_matches(neww_id);
  CREATE INDEX idx_product_matches_wool_id ON product_matches(wool_id);
  CREATE INDEX idx_product_matches_best_price ON product_matches(best_price);
  CREATE INDEX idx_product_matches_max_savings ON product_matches(max_savings DESC);
  CREATE INDEX idx_product_matches_sizes ON product_matches(pakn_size, neww_size, wool_size);
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT EXECUTE ON FUNCTION create_normalized_names() TO authenticated;
GRANT EXECUTE ON FUNCTION find_comprehensive_matches(FLOAT) TO authenticated;
GRANT EXECUTE ON FUNCTION create_product_matches_table() TO authenticated;

-- Refresh the data with new size-aware matching
SELECT create_normalized_names();
SELECT create_product_matches_table();
