import React from 'react';
import { 
  TouchableOpacity, 
  TouchableNativeFeedback, 
  View, 
  StyleSheet,
  ViewStyle,
  TouchableOpacityProps
} from 'react-native';
import { isAndroid, hapticFeedback, androidRipple } from '../utils/platformUtils';

interface PlatformTouchableProps extends TouchableOpacityProps {
  children: React.ReactNode;
  style?: ViewStyle;
  rippleColor?: string;
  hapticType?: 'light' | 'medium' | 'heavy' | 'selection' | 'success' | 'warning' | 'error';
  borderless?: boolean;
  disabled?: boolean;
}

export const PlatformTouchable: React.FC<PlatformTouchableProps> = ({
  children,
  style,
  onPress,
  rippleColor = 'rgba(0, 0, 0, 0.1)',
  hapticType = 'light',
  borderless = false,
  disabled = false,
  ...props
}) => {
  const handlePress = async (event: any) => {
    if (disabled) return;
    
    // Trigger haptic feedback on iOS
    if (hapticType) {
      await hapticFeedback[hapticType]();
    }
    
    // Call the original onPress handler
    if (onPress) {
      onPress(event);
    }
  };

  if (isAndroid) {
    return (
      <TouchableNativeFeedback
        onPress={handlePress}
        disabled={disabled}
        background={TouchableNativeFeedback.Ripple(rippleColor, borderless)}
        {...props}
      >
        <View style={[style, disabled && styles.disabled]}>
          {children}
        </View>
      </TouchableNativeFeedback>
    );
  }

  // iOS
  return (
    <TouchableOpacity
      style={[style, disabled && styles.disabled]}
      onPress={handlePress}
      disabled={disabled}
      activeOpacity={0.7}
      {...props}
    >
      {children}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  disabled: {
    opacity: 0.6,
  },
}); 