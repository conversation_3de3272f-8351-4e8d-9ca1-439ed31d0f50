import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
} from 'react-native';
import { useTheme } from '../context/ThemeContext';
import { theme } from '../styles';

interface StorePrice {
  store: 'woolworths' | 'newworld' | 'paknsave';
  price?: number;
  available: boolean;
  brand?: string;
  size?: string;
  specialPrice?: number;
  isSpecial?: boolean;
  imageUrl?: string;
}

interface CleanPriceComparisonProps {
  productName: string;
  storePrices: StorePrice[];
  onItemPress?: () => void;
  showPrices?: boolean;
  compact?: boolean;
  productImage?: string;
  showTwoPricesOnly?: boolean;
}

export const CleanPriceComparison: React.FC<CleanPriceComparisonProps> = ({
  productName,
  storePrices,
  onItemPress,
  showPrices = true,
  compact = false,
  productImage,
  showTwoPricesOnly = false,
}) => {
  const { colors } = useTheme();

  // Store configuration with icons and colors
  const storeConfig = {
    woolworths: {
      name: 'Woolworths',
      icon: '🍎',
      logo: require('../../assets/images/supermarkets/woolworths.webp'),
      color: '#00A651',
      backgroundColor: '#E8F5E8',
    },
    paknsave: {
      name: "Pak'nSave",
      icon: '💰',
      logo: require('../../assets/images/supermarkets/paknsave.png'),
      color: '#FFD100',
      backgroundColor: '#FFF9E6',
    },
    newworld: {
      name: 'New World',
      icon: '🛒',
      logo: require('../../assets/images/supermarkets/newworld.png'),
      color: '#E31E24',
      backgroundColor: '#FFE8E8',
    },
  };

  // Get prices for each store, ensuring all stores are represented
  const getStorePrice = (store: 'woolworths' | 'newworld' | 'paknsave') => {
    return storePrices.find(sp => sp.store === store);
  };

  // Find the lowest available price (considering special prices)
  const availablePrices = storePrices.filter(sp => sp.available && (sp.specialPrice || sp.price));
  const lowestPrice = availablePrices.length > 0 
    ? Math.min(...availablePrices.map(sp => sp.specialPrice || sp.price!))
    : null;

  // Get top 2 cheapest stores if showTwoPricesOnly is enabled
  const displayStores = showTwoPricesOnly 
    ? availablePrices
        .sort((a, b) => (a.specialPrice || a.price!) - (b.specialPrice || b.price!))
        .slice(0, 2)
        .map(sp => sp.store)
    : ['woolworths', 'paknsave', 'newworld'] as const;

  const renderStorePrice = (store: 'woolworths' | 'newworld' | 'paknsave') => {
    const storePrice = getStorePrice(store);
    const config = storeConfig[store];
    const displayPrice = storePrice?.specialPrice || storePrice?.price;
    const isLowestPrice = displayPrice === lowestPrice && lowestPrice !== null;
    const isAvailable = storePrice?.available && displayPrice;
    const hasSpecialPrice = storePrice?.isSpecial && storePrice?.specialPrice;

    return (
      <View key={store} style={[
        styles.storeSection,
        compact && styles.storeSectionCompact
      ]}>
        {/* Store Icon */}
        <View style={[
          styles.storeIconContainer,
          { 
            backgroundColor: isLowestPrice ? config.backgroundColor : colors.backgroundSecondary,
            borderColor: isLowestPrice ? config.color : 'transparent',
          },
          compact && styles.storeIconContainerCompact
        ]}>
          {config.logo ? (
            <Image
              source={config.logo}
              style={[
                styles.storeLogoImage,
                compact && styles.storeLogoImageCompact
              ]}
              resizeMode="contain"
            />
          ) : (
            <Text style={[
              styles.storeIcon,
              compact && styles.storeIconCompact
            ]}>
              {config.icon}
            </Text>
          )}
        </View>

        {/* Store Name */}
        <Text style={[
          styles.storeName,
          { color: colors.textSecondary },
          compact && styles.storeNameCompact
        ]}>
          {config.name}
        </Text>

        {/* Price */}
        {showPrices && (
          <View style={styles.priceContainer}>
            {isAvailable ? (
              <View style={styles.priceSection}>
                {/* Special/Main Price */}
                <Text style={[
                  styles.price,
                  { 
                    color: isLowestPrice ? config.color : (hasSpecialPrice ? '#E31E24' : colors.text),
                    fontWeight: isLowestPrice ? '600' : '500',
                  },
                  compact && styles.priceCompact
                ]}>
                  ${displayPrice!.toFixed(2)}
                </Text>
                
                {/* Original Price (if on special) */}
                {hasSpecialPrice && storePrice?.price && (
                  <Text style={[
                    styles.originalPrice,
                    { color: colors.textTertiary },
                    compact && styles.originalPriceCompact
                  ]}>
                    ${storePrice.price.toFixed(2)}
                  </Text>
                )}
                
                {/* Special indicator */}
                {hasSpecialPrice && !compact && (
                  <View style={[styles.specialBadge, { backgroundColor: '#E31E24' }]}>
                    <Text style={styles.specialText}>Special</Text>
                  </View>
                )}
              </View>
            ) : (
              <Text style={[
                styles.notAvailable,
                { color: colors.textTertiary },
                compact && styles.notAvailableCompact
              ]}>
                Not available
              </Text>
            )}
            
            {/* Lowest price indicator */}
            {isLowestPrice && !compact && (
              <View style={[styles.bestPriceBadge, { backgroundColor: config.color }]}>
                <Text style={styles.bestPriceText}>Best</Text>
              </View>
            )}
          </View>
        )}
      </View>
    );
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        { 
          backgroundColor: colors.surface,
          borderBottomColor: colors.border,
        },
        compact && styles.containerCompact
      ]}
      onPress={onItemPress}
      activeOpacity={0.7}
    >
      {/* Product Name */}
      <View style={styles.productHeader}>
        <Text style={[
          styles.productName,
          { color: colors.text },
          compact && styles.productNameCompact
        ]}>
          {productName}
        </Text>
      </View>

      {/* Price Comparison Section */}
      {showPrices && (
        <View style={[
          styles.priceComparisonSection,
          compact && styles.priceComparisonSectionCompact
        ]}>
          {/* Product Image */}
          {productImage && !compact && (
            <View style={styles.productImageContainer}>
              <Image
                source={{ uri: productImage }}
                style={styles.productImage}
                resizeMode="contain"
                onError={(error) => console.log('Product image failed to load:', productImage, error)}
              />
            </View>
          )}

          {/* Store Price Columns */}
          <View style={styles.storesContainer}>
            {showTwoPricesOnly ? (
              displayStores.map(store => renderStorePrice(store))
            ) : (
              <>
                {renderStorePrice('woolworths')}
                {renderStorePrice('paknsave')}
                {renderStorePrice('newworld')}
              </>
            )}
          </View>

          {/* Summary (non-compact only) */}
          {!compact && lowestPrice && (
            <View style={[styles.summarySection, { borderTopColor: colors.border }]}>
              <Text style={[styles.summaryText, { color: colors.textSecondary }]}>
                Best price: ${lowestPrice.toFixed(2)}
                {availablePrices.length > 1 && (
                  <Text style={{ color: theme.colors.success }}>
                    {' '}• Save up to ${(Math.max(...availablePrices.map(sp => sp.price!)) - lowestPrice).toFixed(2)}
                  </Text>
                )}
              </Text>
            </View>
          )}
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: theme.spacing.base,
    paddingVertical: theme.spacing.sm,
    borderBottomWidth: 1,
  },
  containerCompact: {
    paddingVertical: theme.spacing.xs,
  },
  productHeader: {
    marginBottom: theme.spacing.sm,
  },
  productName: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.medium,
    lineHeight: 22,
  },
  productNameCompact: {
    fontSize: theme.typography.fontSize.sm,
    lineHeight: 18,
  },
  priceComparisonSection: {
    gap: theme.spacing.sm,
  },
  priceComparisonSectionCompact: {
    gap: theme.spacing.xs,
  },
  storesContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: theme.spacing.xs,
  },
  storeSection: {
    flex: 1,
    alignItems: 'center',
    gap: 6,
  },
  storeSectionCompact: {
    gap: 4,
  },
  storeIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
  },
  storeIconContainerCompact: {
    width: 32,
    height: 32,
    borderRadius: 16,
  },
  storeLogoImage: {
    width: 30,
    height: 30,
  },
  storeLogoImageCompact: {
    width: 24,
    height: 24,
  },
  storeIcon: {
    fontSize: 20,
  },
  storeIconCompact: {
    fontSize: 16,
  },
  storeName: {
    fontSize: theme.typography.fontSize.xs,
    fontWeight: theme.typography.fontWeight.medium,
    textAlign: 'center',
  },
  storeNameCompact: {
    fontSize: 10,
  },
  priceContainer: {
    alignItems: 'center',
    minHeight: 40,
    justifyContent: 'center',
  },
  price: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.medium,
    textAlign: 'center',
  },
  priceCompact: {
    fontSize: theme.typography.fontSize.sm,
  },
  notAvailable: {
    fontSize: theme.typography.fontSize.sm,
    fontStyle: 'italic',
    textAlign: 'center',
  },
  notAvailableCompact: {
    fontSize: theme.typography.fontSize.xs,
  },
  bestPriceBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    marginTop: 4,
  },
  bestPriceText: {
    color: 'white',
    fontSize: theme.typography.fontSize.xs,
    fontWeight: theme.typography.fontWeight.semibold,
  },
  summarySection: {
    paddingTop: theme.spacing.sm,
    borderTopWidth: 1,
    alignItems: 'center',
  },
  summaryText: {
    fontSize: theme.typography.fontSize.sm,
    textAlign: 'center',
  },
  productImageContainer: {
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  productImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },
  priceSection: {
    alignItems: 'center',
  },
  originalPrice: {
    fontSize: theme.typography.fontSize.xs,
    textDecorationLine: 'line-through',
    marginTop: 2,
  },
  originalPriceCompact: {
    fontSize: 10,
  },
  specialBadge: {
    paddingHorizontal: 4,
    paddingVertical: 1,
    borderRadius: 6,
    marginTop: 2,
  },
  specialText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
});