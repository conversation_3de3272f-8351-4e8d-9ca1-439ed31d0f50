import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { smartProductService } from '../services/smartProductService';

interface StorePrice {
  store: string;
  price: number;
  available: boolean;
}

interface ProductSuggestion {
  id: string;
  name: string;
  brand: string;
  size: string;
  category: string;
  storePrices: StorePrice[];
  imageUrl?: string;
  bestPrice: {
    price: number;
    store: string;
  };
}

interface SmartProductDropdownProps {
  visible: boolean;
  query: string;
  onSelectProduct: (product: ProductSuggestion) => void;
  maxResults?: number;
}

export const SmartProductDropdown: React.FC<SmartProductDropdownProps> = ({
  visible,
  query,
  onSelectProduct,
  maxResults = 8,
}) => {
  const { colors: themeColors, isDark: isDarkMode } = useTheme();
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Professional color scheme
  const colors = {
    light: {
      background: '#F8FAFC',
      surface: '#FFFFFF',
      primary: '#475569',
      text: '#0F172A',
      textSecondary: '#64748B',
      border: '#E2E8F0',
      shadow: 'rgba(15, 23, 42, 0.1)',
      success: '#10B981',
      warning: '#F59E0B',
    },
    dark: {
      background: '#0F172A',
      surface: '#1E293B',
      primary: '#06B6D4',
      text: '#F8FAFC',
      textSecondary: '#94A3B8',
      border: '#334155',
      shadow: 'rgba(0, 0, 0, 0.3)',
      success: '#059669',
      warning: '#D97706',
    }
  };

  const currentColors = isDarkMode ? colors.dark : colors.light;

  useEffect(() => {
    if (query && query.length >= 2) {
      searchProducts(query);
    } else {
      setSearchResults([]);
    }
  }, [query]);

  const searchProducts = async (searchQuery: string) => {
    setIsLoading(true);
    try {
      const results = smartProductService.searchProducts(searchQuery);
      
      // Transform results into flat list of suggestions
      const suggestions: ProductSuggestion[] = [];
      
      for (const group of results.matches.slice(0, 4)) { // Limit groups
        for (const variant of group.variants.slice(0, 2)) { // Limit variants per group
          const storePrices: StorePrice[] = Object.entries(variant.storePrices)
            .filter(([_, price]) => price && price > 0)
            .map(([store, price]) => ({
              store: store.charAt(0).toUpperCase() + store.slice(1),
              price: price!,
              available: variant.available[store as keyof typeof variant.available],
            }))
            .sort((a, b) => a.price - b.price);

          if (storePrices.length > 0 && suggestions.length < maxResults) {
            suggestions.push({
              id: variant.id,
              name: variant.name,
              brand: variant.brand,
              size: variant.size,
              category: variant.category,
              storePrices,
              imageUrl: variant.imageUrl,
              bestPrice: {
                price: storePrices[0].price,
                store: storePrices[0].store,
              },
            });
          }
        }
      }
      
      setSearchResults(suggestions);
    } catch (error) {
      console.error('Error searching products:', error);
      setSearchResults([]);
    } finally {
      setIsLoading(false);
    }
  };

  const renderProductItem = ({ item }: { item: ProductSuggestion }) => (
    <TouchableOpacity
      style={[
        styles.productItem,
        { 
          backgroundColor: currentColors.surface,
          borderBottomColor: currentColors.border,
        }
      ]}
      onPress={() => onSelectProduct(item)}
      activeOpacity={0.7}
    >
      <View style={styles.productContent}>
        {/* Product Image */}
        <View style={[styles.imageContainer, { backgroundColor: currentColors.background }]}>
          {item.imageUrl ? (
            <Image source={{ uri: item.imageUrl }} style={styles.productImage} />
          ) : (
            <Ionicons name="cube-outline" size={24} color={currentColors.textSecondary} />
          )}
        </View>

        {/* Product Details */}
        <View style={styles.productDetails}>
          <Text style={[styles.productName, { color: currentColors.text }]} numberOfLines={1}>
            {item.name}
          </Text>
          
          <Text style={[styles.productBrand, { color: currentColors.textSecondary }]} numberOfLines={1}>
            {item.brand} • {item.size}
          </Text>

          <View style={styles.categoryBadge}>
            <Text style={[styles.categoryText, { color: currentColors.primary }]}>
              {item.category}
            </Text>
          </View>
        </View>

        {/* Price Information */}
        <View style={styles.priceSection}>
          <View style={[styles.bestPriceContainer, { backgroundColor: currentColors.success + '15' }]}>
            <Text style={[styles.bestPriceText, { color: currentColors.success }]}>
              ${item.bestPrice.price.toFixed(2)}
            </Text>
            <Text style={[styles.bestStoreText, { color: currentColors.success }]}>
              {item.bestPrice.store}
            </Text>
          </View>

          {/* Show additional stores if available */}
          {item.storePrices.length > 1 && (
            <View style={styles.additionalPrices}>
              {item.storePrices.slice(1, 3).map((storePrice, index) => (
                <Text
                  key={index}
                  style={[styles.additionalPriceText, { color: currentColors.textSecondary }]}
                >
                  ${storePrice.price.toFixed(2)}
                </Text>
              ))}
              {item.storePrices.length > 3 && (
                <Text style={[styles.moreStoresText, { color: currentColors.textSecondary }]}>
                  +{item.storePrices.length - 3}
                </Text>
              )}
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={[styles.emptyState, { backgroundColor: currentColors.surface }]}>
      <Ionicons name="search-outline" size={32} color={currentColors.textSecondary} />
      <Text style={[styles.emptyText, { color: currentColors.textSecondary }]}>
        {query.length < 2 
          ? "Type at least 2 characters to search"
          : "No products found for \"" + query + "\""
        }
      </Text>
    </View>
  );

  const renderLoadingState = () => (
    <View style={[styles.loadingState, { backgroundColor: currentColors.surface }]}>
      <View style={styles.loadingContent}>
        <View style={[styles.loadingSpinner, { borderTopColor: currentColors.primary }]} />
        <Text style={[styles.loadingText, { color: currentColors.textSecondary }]}>
          Searching products...
        </Text>
      </View>
    </View>
  );

  if (!visible || !query) {
    return null;
  }

  return (
    <View style={[
      styles.container,
      { 
        backgroundColor: currentColors.surface,
        borderColor: currentColors.border,
        shadowColor: currentColors.shadow,
      }
    ]}>
      {isLoading ? (
        renderLoadingState()
      ) : searchResults.length > 0 ? (
        <>
          <View style={[styles.header, { borderBottomColor: currentColors.border }]}>
            <Text style={[styles.headerText, { color: currentColors.text }]}>
              Found {searchResults.length} products
            </Text>
            <Ionicons name="storefront" size={16} color={currentColors.primary} />
          </View>
          <FlatList
            data={searchResults}
            renderItem={renderProductItem}
            keyExtractor={(item) => item.id}
            style={styles.list}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          />
        </>
      ) : (
        renderEmptyState()
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    maxHeight: 320,
    borderWidth: 1,
    borderRadius: 12,
    overflow: 'hidden',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerText: {
    fontSize: 14,
    fontWeight: '600',
  },
  list: {
    maxHeight: 260,
  },
  productItem: {
    borderBottomWidth: 1,
  },
  productContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
  },
  imageContainer: {
    width: 44,
    height: 44,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  productImage: {
    width: 40,
    height: 40,
    borderRadius: 6,
  },
  productDetails: {
    flex: 1,
    marginRight: 12,
  },
  productName: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  productBrand: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 4,
  },
  categoryBadge: {
    alignSelf: 'flex-start',
  },
  categoryText: {
    fontSize: 10,
    fontWeight: '600',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  priceSection: {
    alignItems: 'flex-end',
  },
  bestPriceContainer: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    alignItems: 'center',
    marginBottom: 4,
  },
  bestPriceText: {
    fontSize: 14,
    fontWeight: '700',
  },
  bestStoreText: {
    fontSize: 10,
    fontWeight: '600',
    textTransform: 'uppercase',
  },
  additionalPrices: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  additionalPriceText: {
    fontSize: 10,
    fontWeight: '500',
  },
  moreStoresText: {
    fontSize: 10,
    fontWeight: '600',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  emptyText: {
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
    marginTop: 12,
    lineHeight: 20,
  },
  loadingState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 32,
  },
  loadingContent: {
    alignItems: 'center',
  },
  loadingSpinner: {
    width: 24,
    height: 24,
    borderWidth: 2,
    borderColor: 'transparent',
    borderRadius: 12,
    marginBottom: 12,
  },
  loadingText: {
    fontSize: 14,
    fontWeight: '500',
  },
});