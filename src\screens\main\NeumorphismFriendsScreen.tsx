import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  Modal,
  TextInput,
  Animated,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';

const { width } = Dimensions.get('window');

interface SharedUser {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  permission: 'view' | 'edit';
  status: 'pending' | 'accepted' | 'declined';
  lastActive: string;
  color: string;
}

interface SharedList {
  id: string;
  listId: string;
  listName: string;
  listEmoji: string;
  owner: string;
  sharedWith: SharedUser[];
  lastModified: string;
  itemCount: number;
}

interface PendingInvitation {
  id: string;
  listId: string;
  listName: string;
  listEmoji: string;
  fromUser: string;
  fromUserName: string;
  permission: 'view' | 'edit';
  sentAt: string;
}

const USER_COLORS = [
  '#475569', '#64748B', '#94A3B8', '#52525B', '#71717A', '#A1A1AA', '#78716C', '#57534E'
];

export const NeumorphismFriendsScreen: React.FC = () => {
  const { colors: themeColors, isDark: isDarkMode } = useTheme();
  const [sharedLists, setSharedLists] = useState<SharedList[]>([]);
  const [pendingInvitations, setPendingInvitations] = useState<PendingInvitation[]>([]);
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [selectedListId, setSelectedListId] = useState<string>('');
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteName, setInviteName] = useState('');
  const [invitePermission, setInvitePermission] = useState<'view' | 'edit'>('view');
  const [activeTab, setActiveTab] = useState<'shared' | 'invitations'>('shared');
  const [animations] = useState({
    scale: new Animated.Value(1),
    opacity: new Animated.Value(1),
  });

  // Professional color scheme with dark mode support
  const colors = {
    light: {
      background: '#F8FAFC',
      surface: '#FFFFFF',
      primary: '#475569',
      text: '#0F172A',
      textSecondary: '#64748B',
      textTertiary: '#94A3B8',
      shadow: '#E2E8F0',
      secondary: '#64748B',
    },
    dark: {
      background: '#0F172A',
      surface: '#1E293B',
      primary: '#06B6D4',
      text: '#F8FAFC',
      textSecondary: '#94A3B8',
      textTertiary: '#64748B',
      shadow: 'rgba(0, 0, 0, 0.3)',
      secondary: '#94A3B8',
    }
  };

  const neuColors = isDarkMode ? colors.dark : colors.light;

  useEffect(() => {
    loadSharedData();
    generateDemoData();
  }, []);

  const generateDemoData = () => {
    const demoLists: SharedList[] = [];
    const demoInvitations: PendingInvitation[] = [];

    setSharedLists(demoLists);
    setPendingInvitations(demoInvitations);
  };

  const loadSharedData = async () => {
    try {
      const [sharedData, invitationsData] = await Promise.all([
        AsyncStorage.getItem('neuro_shared_lists'),
        AsyncStorage.getItem('neuro_pending_invitations')
      ]);

      if (sharedData) {
        setSharedLists(JSON.parse(sharedData));
      }
      if (invitationsData) {
        setPendingInvitations(JSON.parse(invitationsData));
      }
    } catch (error) {
      console.error('Error loading shared data:', error);
    }
  };

  const saveSharedData = async (lists: SharedList[], invitations: PendingInvitation[]) => {
    try {
      await Promise.all([
        AsyncStorage.setItem('neuro_shared_lists', JSON.stringify(lists)),
        AsyncStorage.setItem('neuro_pending_invitations', JSON.stringify(invitations))
      ]);
      setSharedLists(lists);
      setPendingInvitations(invitations);
    } catch (error) {
      console.error('Error saving shared data:', error);
    }
  };

  const handleInviteUser = () => {
    if (!inviteEmail.trim() || !inviteName.trim()) {
      Alert.alert('Error', 'Please enter both name and email');
      return;
    }

    const newSharedUser: SharedUser = {
      id: Date.now().toString(),
      name: inviteName.trim(),
      email: inviteEmail.trim(),
      permission: invitePermission,
      status: 'accepted',
      lastActive: 'Just now',
      color: USER_COLORS[Math.floor(Math.random() * USER_COLORS.length)],
    };

    const updatedSharedLists = [...sharedLists];
    const existingListIndex = updatedSharedLists.findIndex(list => list.listId === selectedListId);

    if (existingListIndex >= 0) {
      updatedSharedLists[existingListIndex].sharedWith.push(newSharedUser);
      updatedSharedLists[existingListIndex].lastModified = new Date().toISOString();
    } else {
      const newSharedList: SharedList = {
        id: Date.now().toString(),
        listId: selectedListId,
        listName: getListName(selectedListId),
        listEmoji: getListEmoji(selectedListId),
        owner: 'current_user',
        sharedWith: [newSharedUser],
        lastModified: new Date().toISOString(),
        itemCount: 0,
      };
      updatedSharedLists.push(newSharedList);
    }

    saveSharedData(updatedSharedLists, pendingInvitations);
    setShowInviteModal(false);
    setInviteEmail('');
    setInviteName('');
    setInvitePermission('view');

    Alert.alert(
      'Invitation Sent!',
      `${inviteName} has been invited to collaborate on your ${getListName(selectedListId)} list.`,
    );
  };

  const getListName = (listId: string): string => {
    const lists = [
      { id: 'home', name: 'Home', emoji: '🏠' },
      { id: 'office', name: 'Office', emoji: '🏢' },
      { id: 'party', name: 'Party', emoji: '🎉' },
      { id: 'grocery', name: 'Grocery', emoji: '🛒' },
    ];
    return lists.find(list => list.id === listId)?.name || 'Unknown';
  };

  const getListEmoji = (listId: string): string => {
    const lists = [
      { id: 'home', name: 'Home', emoji: '🏠' },
      { id: 'office', name: 'Office', emoji: '🏢' },
      { id: 'party', name: 'Party', emoji: '🎉' },
      { id: 'grocery', name: 'Grocery', emoji: '🛒' },
    ];
    return lists.find(list => list.id === listId)?.emoji || '📝';
  };

  const handleRemoveUser = (listId: string, userId: string) => {
    Alert.alert(
      'Remove Collaborator',
      'Are you sure you want to remove this person from the list?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => {
            const updatedLists = sharedLists.map(list => {
              if (list.listId === listId) {
                return {
                  ...list,
                  sharedWith: list.sharedWith.filter(user => user.id !== userId),
                  lastModified: new Date().toISOString(),
                };
              }
              return list;
            });
            saveSharedData(updatedLists, pendingInvitations);
          },
        },
      ]
    );
  };

  const handleAcceptInvitation = (invitationId: string) => {
    Alert.alert('Invitation Accepted', 'You can now collaborate on this list!');
    const updatedInvitations = pendingInvitations.filter(inv => inv.id !== invitationId);
    saveSharedData(sharedLists, updatedInvitations);
  };

  const handleDeclineInvitation = (invitationId: string) => {
    const updatedInvitations = pendingInvitations.filter(inv => inv.id !== invitationId);
    saveSharedData(sharedLists, updatedInvitations);
  };

  const animatePress = (callback: () => void) => {
    Animated.sequence([
      Animated.parallel([
        Animated.timing(animations.scale, {
          toValue: 0.95,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(animations.opacity, {
          toValue: 0.8,
          duration: 100,
          useNativeDriver: true,
        }),
      ]),
      Animated.parallel([
        Animated.timing(animations.scale, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(animations.opacity, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        }),
      ]),
    ]).start(callback);
  };

  const renderNeumorphicButton = (
    onPress: () => void,
    children: React.ReactNode,
    style?: any,
    isPressed?: boolean
  ) => (
    <TouchableOpacity
      onPress={() => animatePress(onPress)}
      style={[
        styles.neumorphicButton,
        { backgroundColor: neuColors.surface },
        isPressed && styles.neumorphicButtonPressed,
        style,
      ]}
      activeOpacity={1}
    >
      <Animated.View
        style={[
          styles.neumorphicButtonContent,
          {
            transform: [{ scale: animations.scale }],
            opacity: animations.opacity,
          },
        ]}
      >
        {children}
      </Animated.View>
    </TouchableOpacity>
  );

  const renderSharedList = (list: SharedList) => (
    <View key={list.id} style={[styles.neumorphicCard, { 
      backgroundColor: neuColors.surface,
      shadowColor: neuColors.shadow,
      borderColor: isDarkMode ? neuColors.textTertiary + '20' : '#F1F5F9'
    }]}>
      <View style={styles.listHeader}>
        <View style={styles.listTitleContainer}>
          <Text style={styles.listEmoji}>{list.listEmoji}</Text>
          <View style={styles.listInfo}>
            <Text style={[styles.listName, { color: neuColors.text }]}>{list.listName}</Text>
            <Text style={[styles.listDetails, { color: neuColors.textSecondary }]}>
              {list.itemCount} items • {list.sharedWith.length} collaborators
            </Text>
          </View>
        </View>
        
        {renderNeumorphicButton(
          () => {
            setSelectedListId(list.listId);
            setShowInviteModal(true);
          },
          <Ionicons name="person-add" size={18} color={neuColors.primary} />,
          styles.addButton
        )}
      </View>

      <View style={styles.collaboratorsList}>
        {list.sharedWith.map((user, index) => (
          <View key={user.id} style={[styles.collaboratorCard, { backgroundColor: neuColors.surface }]}>
            <View style={styles.collaboratorInfo}>
              <View style={[styles.userAvatar, { backgroundColor: user.color }]}>
                <Text style={styles.userInitial}>{user.name.charAt(0).toUpperCase()}</Text>
              </View>
              <View style={styles.userDetails}>
                <Text style={[styles.userName, { color: neuColors.text }]}>{user.name}</Text>
                <Text style={[styles.userEmail, { color: neuColors.textSecondary }]}>{user.email}</Text>
                <Text style={[styles.userStatus, { color: neuColors.textTertiary }]}>
                  Last active: {user.lastActive}
                </Text>
              </View>
            </View>

            <View style={styles.collaboratorActions}>
              <View style={[
                styles.permissionBadge,
                { backgroundColor: user.permission === 'edit' ? neuColors.primary + '15' : neuColors.textSecondary + '15' }
              ]}>
                <Ionicons 
                  name={user.permission === 'edit' ? 'create' : 'eye'} 
                  size={12} 
                  color={user.permission === 'edit' ? neuColors.primary : neuColors.textSecondary} 
                />
                <Text style={[
                  styles.permissionText,
                  { color: user.permission === 'edit' ? neuColors.primary : neuColors.textSecondary }
                ]}>
                  {user.permission === 'edit' ? 'Editor' : 'Viewer'}
                </Text>
              </View>

              {renderNeumorphicButton(
                () => handleRemoveUser(list.listId, user.id),
                <Ionicons name="close" size={16} color={neuColors.secondary} />,
                styles.removeButton
              )}
            </View>
          </View>
        ))}
      </View>
    </View>
  );

  const renderPendingInvitation = (invitation: PendingInvitation) => (
    <View key={invitation.id} style={[styles.invitationCard, { backgroundColor: neuColors.surface }]}>
      <View style={styles.invitationHeader}>
        <Text style={styles.invitationEmoji}>{invitation.listEmoji}</Text>
        <View style={styles.invitationInfo}>
          <Text style={[styles.invitationTitle, { color: neuColors.text }]}>
            {invitation.fromUserName} invited you to collaborate
          </Text>
          <Text style={[styles.invitationSubtitle, { color: neuColors.textSecondary }]}>
            {invitation.listName} • {invitation.permission === 'edit' ? 'Can edit' : 'View only'}
          </Text>
        </View>
      </View>
      
      <View style={styles.invitationActions}>
        {renderNeumorphicButton(
          () => handleAcceptInvitation(invitation.id),
          <Text style={[styles.actionButtonText, { color: neuColors.primary }]}>Accept</Text>,
          [styles.actionButton, { backgroundColor: neuColors.primary + '10' }]
        )}
        {renderNeumorphicButton(
          () => handleDeclineInvitation(invitation.id),
          <Text style={[styles.actionButtonText, { color: neuColors.textSecondary }]}>Decline</Text>,
          [styles.actionButton, { backgroundColor: neuColors.textSecondary + '10' }]
        )}
      </View>
    </View>
  );

  const renderEmptyState = (type: 'shared' | 'invitations') => (
    <View style={[styles.emptyState, { backgroundColor: neuColors.surface }]}>
      <View style={[styles.emptyIconContainer, { backgroundColor: neuColors.surface }]}>
        <Ionicons 
          name={type === 'shared' ? 'people-outline' : 'mail-outline'} 
          size={48} 
          color={neuColors.primary} 
        />
      </View>
      <Text style={[styles.emptyTitle, { color: neuColors.text }]}>
        {type === 'shared' ? 'No Shared Lists' : 'No Pending Invitations'}
      </Text>
      <Text style={[styles.emptyText, { color: neuColors.textSecondary }]}>
        {type === 'shared' 
          ? 'Start collaborating by sharing your lists with friends and family'
          : 'When someone invites you to collaborate, invitations will appear here'
        }
      </Text>
      {type === 'shared' && renderNeumorphicButton(
        () => {
          setSelectedListId('grocery');
          setShowInviteModal(true);
        },
        <>
          <Ionicons name="share-outline" size={16} color="white" style={{ marginRight: 8 }} />
          <Text style={styles.startSharingText}>Start Collaborating</Text>
        </>,
        [styles.startSharingButton, { backgroundColor: neuColors.primary }]
      )}
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: neuColors.background }]}>
      <SafeAreaView style={styles.safeArea}>
        {/* Neumorphic Header */}
        <View style={[styles.header, { backgroundColor: neuColors.surface }]}>
          <View style={styles.headerContent}>
            <Text style={[styles.title, { color: neuColors.text }]}>👥 Collaboration</Text>
            <Text style={[styles.subtitle, { color: neuColors.textSecondary }]}>
              Share and collaborate on lists
            </Text>
          </View>
        </View>

        {/* Neumorphic Tab Container */}
        <View style={[styles.tabContainer, { backgroundColor: neuColors.surface }]}>
          {renderNeumorphicButton(
            () => setActiveTab('shared'),
            <Text style={[
              styles.tabText,
              { color: activeTab === 'shared' ? neuColors.primary : neuColors.textSecondary }
            ]}>
              Shared Lists
            </Text>,
            [styles.tab, activeTab === 'shared' && [styles.activeTab, { backgroundColor: neuColors.textSecondary + '15' }]]
          )}
          {renderNeumorphicButton(
            () => setActiveTab('invitations'),
            <Text style={[
              styles.tabText,
              { color: activeTab === 'invitations' ? neuColors.primary : neuColors.textSecondary }
            ]}>
              Invitations {pendingInvitations.length > 0 && `(${pendingInvitations.length})`}
            </Text>,
            [styles.tab, activeTab === 'invitations' && [styles.activeTab, { backgroundColor: neuColors.textSecondary + '15' }]]
          )}
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {activeTab === 'shared' ? (
            <>
              {sharedLists.length === 0 ? (
                renderEmptyState('shared')
              ) : (
                sharedLists.map(renderSharedList)
              )}
            </>
          ) : (
            <>
              {pendingInvitations.length === 0 ? (
                renderEmptyState('invitations')
              ) : (
                pendingInvitations.map(renderPendingInvitation)
              )}
            </>
          )}
        </ScrollView>

        {/* Neumorphic Invite Modal */}
        <Modal
          visible={showInviteModal}
          animationType="slide"
          transparent={true}
          onRequestClose={() => setShowInviteModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={[styles.modalContainer, { backgroundColor: neuColors.surface }]}>
              <View style={styles.modalHeader}>
                <Text style={[styles.modalTitle, { color: neuColors.text }]}>Invite Collaborator</Text>
                {renderNeumorphicButton(
                  () => setShowInviteModal(false),
                  <Ionicons name="close" size={24} color={neuColors.textSecondary} />,
                  styles.closeButton
                )}
              </View>

              <View style={styles.modalBody}>
                <Text style={[styles.inputLabel, { color: neuColors.textSecondary }]}>Name</Text>
                <TextInput
                  style={[styles.neumorphicInput, { 
                    backgroundColor: neuColors.surface, 
                    color: neuColors.text 
                  }]}
                  value={inviteName}
                  onChangeText={setInviteName}
                  placeholder="Enter full name"
                  placeholderTextColor={neuColors.textTertiary}
                />

                <Text style={[styles.inputLabel, { color: neuColors.textSecondary }]}>Email Address</Text>
                <TextInput
                  style={[styles.neumorphicInput, { 
                    backgroundColor: neuColors.surface, 
                    color: neuColors.text 
                  }]}
                  value={inviteEmail}
                  onChangeText={setInviteEmail}
                  placeholder="Enter email address"
                  placeholderTextColor={neuColors.textTertiary}
                  keyboardType="email-address"
                  autoCapitalize="none"
                />

                <Text style={[styles.inputLabel, { color: neuColors.textSecondary }]}>Permission Level</Text>
                <View style={styles.permissionContainer}>
                  {renderNeumorphicButton(
                    () => setInvitePermission('view'),
                    <>
                      <Ionicons 
                        name="eye-outline" 
                        size={20} 
                        color={invitePermission === 'view' ? neuColors.primary : neuColors.textSecondary} 
                      />
                      <Text style={[
                        styles.permissionOptionText,
                        { color: invitePermission === 'view' ? neuColors.primary : neuColors.textSecondary }
                      ]}>
                        View Only
                      </Text>
                    </>,
                    [
                      styles.permissionOption,
                      { backgroundColor: neuColors.surface },
                      invitePermission === 'view' && { backgroundColor: neuColors.primary + '15' }
                    ]
                  )}
                  {renderNeumorphicButton(
                    () => setInvitePermission('edit'),
                    <>
                      <Ionicons 
                        name="create-outline" 
                        size={20} 
                        color={invitePermission === 'edit' ? neuColors.primary : neuColors.textSecondary} 
                      />
                      <Text style={[
                        styles.permissionOptionText,
                        { color: invitePermission === 'edit' ? neuColors.primary : neuColors.textSecondary }
                      ]}>
                        Can Edit
                      </Text>
                    </>,
                    [
                      styles.permissionOption,
                      { backgroundColor: neuColors.surface },
                      invitePermission === 'edit' && { backgroundColor: neuColors.primary + '15' }
                    ]
                  )}
                </View>
              </View>

              <View style={styles.modalActions}>
                {renderNeumorphicButton(
                  () => setShowInviteModal(false),
                  <Text style={[styles.cancelButtonText, { color: neuColors.textSecondary }]}>Cancel</Text>,
                  [styles.modalButton, { backgroundColor: neuColors.surface }]
                )}
                {renderNeumorphicButton(
                  handleInviteUser,
                  <Text style={styles.inviteButtonText}>Send Invitation</Text>,
                  [styles.modalButton, { backgroundColor: neuColors.primary }]
                )}
              </View>
            </View>
          </View>
        </Modal>
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 24,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  headerContent: {
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    fontWeight: '500',
  },
  tabContainer: {
    flexDirection: 'row',
    margin: 20,
    borderRadius: 16,
    padding: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 12,
  },
  activeTab: {
    // Applied inline with dynamic colors
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  neumorphicCard: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowOffset: { width: -3, height: -3 },
    shadowOpacity: 0.8,
    shadowRadius: 6,
    elevation: 3,
    borderWidth: 1,
    // shadowColor and borderColor applied inline with dynamic colors
  },
  neumorphicButton: {
    borderRadius: 12,
    shadowColor: '#E2E8F0',
    shadowOffset: { width: -2, height: -2 },
    shadowOpacity: 0.8,
    shadowRadius: 4,
    elevation: 2,
  },
  neumorphicButtonPressed: {
    shadowColor: '#CBD5E1',
    shadowOffset: { width: 1, height: 1 },
    shadowOpacity: 0.5,
    shadowRadius: 2,
  },
  neumorphicButtonContent: {
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  listHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  listTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  listEmoji: {
    fontSize: 24,
    marginRight: 12,
  },
  listInfo: {
    flex: 1,
  },
  listName: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  listDetails: {
    fontSize: 14,
    fontWeight: '500',
  },
  addButton: {
    width: 44,
    height: 44,
  },
  collaboratorsList: {
    gap: 12,
  },
  collaboratorCard: {
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    shadowColor: '#E2E8F0',
    shadowOffset: { width: -1, height: -1 },
    shadowOpacity: 0.5,
    shadowRadius: 3,
    elevation: 1,
    borderWidth: 1,
    borderColor: '#F1F5F9',
  },
  collaboratorInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  userAvatar: {
    width: 44,
    height: 44,
    borderRadius: 22,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  userInitial: {
    color: 'white',
    fontSize: 18,
    fontWeight: '700',
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  userEmail: {
    fontSize: 13,
    fontWeight: '500',
    marginBottom: 2,
  },
  userStatus: {
    fontSize: 12,
    fontWeight: '500',
  },
  collaboratorActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  permissionBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    gap: 4,
  },
  permissionText: {
    fontSize: 12,
    fontWeight: '600',
  },
  removeButton: {
    width: 32,
    height: 32,
    padding: 8,
  },
  invitationCard: {
    borderRadius: 20,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#D1D5DB',
    shadowOffset: { width: -5, height: -5 },
    shadowOpacity: 1,
    shadowRadius: 10,
    elevation: 5,
  },
  invitationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  invitationEmoji: {
    fontSize: 24,
    marginRight: 12,
  },
  invitationInfo: {
    flex: 1,
  },
  invitationTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  invitationSubtitle: {
    fontSize: 14,
    fontWeight: '500',
  },
  invitationActions: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 12,
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
    borderRadius: 20,
    shadowColor: '#D1D5DB',
    shadowOffset: { width: -5, height: -5 },
    shadowOpacity: 1,
    shadowRadius: 10,
    elevation: 5,
  },
  emptyIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: '#D1D5DB',
    shadowOffset: { width: -3, height: -3 },
    shadowOpacity: 0.5,
    shadowRadius: 6,
    elevation: 3,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  startSharingButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 12,
  },
  startSharingText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalContainer: {
    width: width * 0.9,
    borderRadius: 20,
    shadowColor: '#D1D5DB',
    shadowOffset: { width: -5, height: -5 },
    shadowOpacity: 1,
    shadowRadius: 10,
    elevation: 10,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  closeButton: {
    width: 40,
    height: 40,
    padding: 8,
  },
  modalBody: {
    padding: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
    marginTop: 16,
  },
  neumorphicInput: {
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    shadowColor: '#D1D5DB',
    shadowOffset: { width: -3, height: -3 },
    shadowOpacity: 0.5,
    shadowRadius: 6,
    elevation: 3,
  },
  permissionContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  permissionOption: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    gap: 8,
  },
  permissionOptionText: {
    fontSize: 14,
    fontWeight: '600',
  },
  modalActions: {
    flexDirection: 'row',
    gap: 12,
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 12,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  inviteButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
});