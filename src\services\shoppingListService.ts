import AsyncStorage from '@react-native-async-storage/async-storage';

export interface ShoppingListItem {
  id: string;
  productName: string;
  quantity: number;
  unit?: string;
  price: number;
  originalPrice?: number;
  store: string;
  brand?: string;
  category?: string;
  imageUrl?: string;
  isOnSale?: boolean;
  promotionText?: string;
  notes?: string;
  isCompleted: boolean;
  addedAt: string;
  updatedAt: string;
}

export interface ShoppingList {
  id: string;
  name: string;
  description?: string;
  items: ShoppingListItem[];
  createdAt: string;
  updatedAt: string;
  isDefault: boolean;
  color: string;
  icon: string;
  totalItems: number;
  totalPrice: number;
  completedItems: number;
}

export interface ProductToAdd {
  id: string;
  name: string;
  price: number;
  originalPrice?: number;
  store: string;
  brand?: string;
  category?: string;
  imageUrl?: string;
  unit?: string;
  isOnSale?: boolean;
  promotionText?: string;
}

const STORAGE_KEY = 'shopping_lists';
const DEFAULT_LISTS_KEY = 'default_shopping_lists';

// Default list templates
const DEFAULT_LISTS: Omit<ShoppingList, 'id' | 'createdAt' | 'updatedAt' | 'totalItems' | 'totalPrice' | 'completedItems'>[] = [
  {
    name: 'Weekly Groceries',
    description: 'Regular weekly shopping list',
    items: [],
    isDefault: true,
    color: '#3B82F6',
    icon: 'cart',
  },
  {
    name: 'Quick Essentials',
    description: 'Essential items for quick trips',
    items: [],
    isDefault: false,
    color: '#10B981',
    icon: 'flash',
  },
  {
    name: 'Party Supplies',
    description: 'Items for special occasions',
    items: [],
    isDefault: false,
    color: '#F59E0B',
    icon: 'wine',
  },
];

class ShoppingListService {
  private lists: ShoppingList[] = [];
  private initialized = false;

  // Initialize the service
  async initialize(): Promise<void> {
    if (this.initialized) return;
    
    try {
      await this.loadLists();
      
      // Create default lists if none exist
      if (this.lists.length === 0) {
        await this.createDefaultLists();
      }
      
      this.initialized = true;
    } catch (error) {
      console.error('Error initializing shopping list service:', error);
      this.initialized = true; // Mark as initialized even on error
    }
  }

  // Create default lists
  private async createDefaultLists(): Promise<void> {
    const now = new Date().toISOString();
    
    for (const defaultList of DEFAULT_LISTS) {
      const newList: ShoppingList = {
        ...defaultList,
        id: this.generateId(),
        createdAt: now,
        updatedAt: now,
        totalItems: 0,
        totalPrice: 0,
        completedItems: 0,
      };
      
      this.lists.push(newList);
    }
    
    await this.saveLists();
  }

  // Generate unique ID
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  // Load lists from storage
  private async loadLists(): Promise<void> {
    try {
      const storedLists = await AsyncStorage.getItem(STORAGE_KEY);
      if (storedLists) {
        this.lists = JSON.parse(storedLists);
      }
    } catch (error) {
      console.error('Error loading shopping lists:', error);
      this.lists = [];
    }
  }

  // Save lists to storage
  private async saveLists(): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(this.lists));
    } catch (error) {
      console.error('Error saving shopping lists:', error);
    }
  }

  // Calculate list totals
  private calculateTotals(list: ShoppingList): void {
    list.totalItems = list.items.length;
    list.completedItems = list.items.filter(item => item.isCompleted).length;
    list.totalPrice = list.items.reduce((total, item) => total + (item.price * item.quantity), 0);
  }

  // Get all shopping lists
  async getAllLists(): Promise<ShoppingList[]> {
    await this.initialize();
    return [...this.lists];
  }

  // Get a specific list by ID
  async getListById(id: string): Promise<ShoppingList | null> {
    await this.initialize();
    return this.lists.find(list => list.id === id) || null;
  }

  // Get the default list
  async getDefaultList(): Promise<ShoppingList | null> {
    await this.initialize();
    return this.lists.find(list => list.isDefault) || this.lists[0] || null;
  }

  // Create a new shopping list
  async createList(name: string, description?: string, color?: string, icon?: string): Promise<ShoppingList> {
    await this.initialize();
    
    const now = new Date().toISOString();
    const newList: ShoppingList = {
      id: this.generateId(),
      name,
      description,
      items: [],
      createdAt: now,
      updatedAt: now,
      isDefault: this.lists.length === 0, // First list is default
      color: color || '#3B82F6',
      icon: icon || 'list',
      totalItems: 0,
      totalPrice: 0,
      completedItems: 0,
    };
    
    this.lists.push(newList);
    await this.saveLists();
    
    return newList;
  }

  // Update a shopping list
  async updateList(id: string, updates: Partial<Omit<ShoppingList, 'id' | 'createdAt' | 'totalItems' | 'totalPrice' | 'completedItems'>>): Promise<ShoppingList | null> {
    await this.initialize();
    
    const listIndex = this.lists.findIndex(list => list.id === id);
    if (listIndex === -1) return null;
    
    const updatedList = {
      ...this.lists[listIndex],
      ...updates,
      updatedAt: new Date().toISOString(),
    };
    
    this.calculateTotals(updatedList);
    this.lists[listIndex] = updatedList;
    await this.saveLists();
    
    return updatedList;
  }

  // Delete a shopping list
  async deleteList(id: string): Promise<boolean> {
    await this.initialize();
    
    const listIndex = this.lists.findIndex(list => list.id === id);
    if (listIndex === -1) return false;
    
    // Don't delete the last list
    if (this.lists.length === 1) return false;
    
    const wasDefault = this.lists[listIndex].isDefault;
    this.lists.splice(listIndex, 1);
    
    // If we deleted the default list, make the first remaining list default
    if (wasDefault && this.lists.length > 0) {
      this.lists[0].isDefault = true;
    }
    
    await this.saveLists();
    return true;
  }

  // Set a list as default
  async setDefaultList(id: string): Promise<boolean> {
    await this.initialize();
    
    const targetList = this.lists.find(list => list.id === id);
    if (!targetList) return false;
    
    // Remove default from all lists
    this.lists.forEach(list => {
      list.isDefault = false;
    });
    
    // Set new default
    targetList.isDefault = true;
    
    await this.saveLists();
    return true;
  }

  // Add item to a specific list
  async addItemToList(listId: string, product: ProductToAdd, quantity: number = 1, notes?: string): Promise<ShoppingListItem | null> {
    await this.initialize();
    
    const list = this.lists.find(l => l.id === listId);
    if (!list) return null;
    
    const now = new Date().toISOString();
    const newItem: ShoppingListItem = {
      id: this.generateId(),
      productName: product.name,
      quantity,
      unit: product.unit,
      price: product.price,
      originalPrice: product.originalPrice,
      store: product.store,
      brand: product.brand,
      category: product.category,
      imageUrl: product.imageUrl,
      isOnSale: product.isOnSale,
      promotionText: product.promotionText,
      notes,
      isCompleted: false,
      addedAt: now,
      updatedAt: now,
    };
    
    // Check if item already exists
    const existingItemIndex = list.items.findIndex(item => 
      item.productName === product.name && item.store === product.store
    );
    
    if (existingItemIndex !== -1) {
      // Update existing item quantity
      list.items[existingItemIndex].quantity += quantity;
      list.items[existingItemIndex].updatedAt = now;
    } else {
      // Add new item
      list.items.push(newItem);
    }
    
    list.updatedAt = now;
    this.calculateTotals(list);
    await this.saveLists();
    
    return newItem;
  }

  // Remove item from list
  async removeItemFromList(listId: string, itemId: string): Promise<boolean> {
    await this.initialize();
    
    const list = this.lists.find(l => l.id === listId);
    if (!list) return false;
    
    const itemIndex = list.items.findIndex(item => item.id === itemId);
    if (itemIndex === -1) return false;
    
    list.items.splice(itemIndex, 1);
    list.updatedAt = new Date().toISOString();
    this.calculateTotals(list);
    await this.saveLists();
    
    return true;
  }

  // Update item in list
  async updateItemInList(listId: string, itemId: string, updates: Partial<Omit<ShoppingListItem, 'id' | 'addedAt'>>): Promise<ShoppingListItem | null> {
    await this.initialize();
    
    const list = this.lists.find(l => l.id === listId);
    if (!list) return null;
    
    const itemIndex = list.items.findIndex(item => item.id === itemId);
    if (itemIndex === -1) return null;
    
    const updatedItem = {
      ...list.items[itemIndex],
      ...updates,
      updatedAt: new Date().toISOString(),
    };
    
    list.items[itemIndex] = updatedItem;
    list.updatedAt = new Date().toISOString();
    this.calculateTotals(list);
    await this.saveLists();
    
    return updatedItem;
  }

  // Toggle item completion
  async toggleItemCompletion(listId: string, itemId: string): Promise<boolean> {
    await this.initialize();
    
    const list = this.lists.find(l => l.id === listId);
    if (!list) return false;
    
    const item = list.items.find(i => i.id === itemId);
    if (!item) return false;
    
    item.isCompleted = !item.isCompleted;
    item.updatedAt = new Date().toISOString();
    
    list.updatedAt = new Date().toISOString();
    this.calculateTotals(list);
    await this.saveLists();
    
    return true;
  }

  // Clear completed items from list
  async clearCompletedItems(listId: string): Promise<boolean> {
    await this.initialize();
    
    const list = this.lists.find(l => l.id === listId);
    if (!list) return false;
    
    list.items = list.items.filter(item => !item.isCompleted);
    list.updatedAt = new Date().toISOString();
    this.calculateTotals(list);
    await this.saveLists();
    
    return true;
  }

  // Get list statistics
  async getListStats(listId: string): Promise<{
    totalItems: number;
    completedItems: number;
    totalPrice: number;
    averagePrice: number;
    storeBreakdown: { [store: string]: number };
    categoryBreakdown: { [category: string]: number };
  } | null> {
    await this.initialize();
    
    const list = this.lists.find(l => l.id === listId);
    if (!list) return null;
    
    const storeBreakdown: { [store: string]: number } = {};
    const categoryBreakdown: { [category: string]: number } = {};
    
    list.items.forEach(item => {
      // Store breakdown
      if (!storeBreakdown[item.store]) {
        storeBreakdown[item.store] = 0;
      }
      storeBreakdown[item.store] += item.quantity;
      
      // Category breakdown
      const category = item.category || 'Other';
      if (!categoryBreakdown[category]) {
        categoryBreakdown[category] = 0;
      }
      categoryBreakdown[category] += item.quantity;
    });
    
    return {
      totalItems: list.totalItems,
      completedItems: list.completedItems,
      totalPrice: list.totalPrice,
      averagePrice: list.totalItems > 0 ? list.totalPrice / list.totalItems : 0,
      storeBreakdown,
      categoryBreakdown,
    };
  }

  // Search items across all lists
  async searchItems(query: string): Promise<{ list: ShoppingList; items: ShoppingListItem[] }[]> {
    await this.initialize();
    
    const results: { list: ShoppingList; items: ShoppingListItem[] }[] = [];
    const lowercaseQuery = query.toLowerCase();
    
    this.lists.forEach(list => {
      const matchingItems = list.items.filter(item =>
        item.productName.toLowerCase().includes(lowercaseQuery) ||
        (item.brand && item.brand.toLowerCase().includes(lowercaseQuery)) ||
        (item.category && item.category.toLowerCase().includes(lowercaseQuery)) ||
        (item.notes && item.notes.toLowerCase().includes(lowercaseQuery))
      );
      
      if (matchingItems.length > 0) {
        results.push({ list, items: matchingItems });
      }
    });
    
    return results;
  }
}

export const shoppingListService = new ShoppingListService();
