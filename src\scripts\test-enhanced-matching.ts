/**
 * TEST: Enhanced Product Matching
 * 
 * Tests the enhanced matching algorithms based on recommendations from
 * Matching_Products_Across_Supermarkets.txt document.
 */

import { createClient } from '@supabase/supabase-js';
import { ProductDeduplicationService } from '../services/productDeduplicationService';
import { sqlFuzzyMatchingService } from '../services/sqlFuzzyMatchingService';
import { 
  calculateFuzzySimilarity, 
  advancedNormalizeString, 
  calculateJaccardSimilarity 
} from '../utils/stringMatching';
import { IProduct } from '../types/shoppingList';

const supabaseUrl = 'https://emjwniuqwhvohjassnrg.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVtanduaXVxd2h2b2hqYXNzbnJnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjQwMTYwNSwiZXhwIjoyMDY3OTc3NjA1fQ.oTowRoNAjlUmk10O9pSMK2BvL0XlyBb5orVRrlEryHs';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testEnhancedMatching() {
  console.log('🧪 Testing Enhanced Product Matching Algorithms...\n');
  console.log('📖 Based on recommendations from Matching_Products_Across_Supermarkets.txt\n');

  const deduplicationService = new ProductDeduplicationService();

  try {
    // Test 1: Compare old vs new normalization
    console.log('1. Testing Advanced Normalization...');
    
    const testNames = [
      'Anchor Blue Milk 2L Premium Fresh',
      'ANCHOR BLUE MILK 2 LITRE PREMIUM FRESH',
      'Anchor Blue Milk 2000ml Premium',
      'Pams Fresh Bread White 700g Sliced',
      'PAMS FRESH WHITE BREAD 700 GRAM SLICED',
      'Tip Top Ice Cream Vanilla 2L Tub'
    ];

    testNames.forEach((name, index) => {
      const normalized = advancedNormalizeString(name);
      console.log(`   ${index + 1}. "${name}"`);
      console.log(`      → "${normalized}"`);
    });

    // Test 2: Compare similarity algorithms
    console.log('\n2. Testing Enhanced Similarity Algorithms...');
    
    const testPairs = [
      ['Anchor Blue Milk 2L', 'ANCHOR BLUE MILK 2 LITRE'],
      ['Pams White Bread 700g', 'PAMS FRESH WHITE BREAD 700 GRAM'],
      ['Tip Top Vanilla Ice Cream', 'TIP TOP VANILLA ICECREAM 2L'],
      ['Woolworths Select Bananas', 'Woolworths Bananas Premium'],
      ['New World Chicken Breast', 'Chicken Breast Fillets New World']
    ];

    testPairs.forEach(([name1, name2], index) => {
      const oldSimilarity = calculateFuzzySimilarity(name1, name2, 0.5);
      const jaccardSimilarity = calculateJaccardSimilarity(name1, name2);
      
      console.log(`   ${index + 1}. "${name1}" vs "${name2}"`);
      console.log(`      Enhanced similarity: ${oldSimilarity.similarity.toFixed(3)} (${oldSimilarity.algorithm})`);
      console.log(`      Jaccard similarity: ${jaccardSimilarity.toFixed(3)}`);
      console.log(`      Match: ${oldSimilarity.isMatch ? '✅' : '❌'}`);
    });

    // Test 3: Fetch real products for testing
    console.log('\n3. Testing with Real Product Data...');
    
    const { data: products, error } = await supabase
      .from('products')
      .select('*')
      .not('price', 'is', null)
      .limit(200);

    if (error) {
      console.error('❌ Error fetching products:', error);
      return;
    }

    if (!products || products.length === 0) {
      console.error('❌ No products found');
      return;
    }

    console.log(`✅ Fetched ${products.length} products for testing`);

    // Test 4: Compare standard vs enhanced deduplication
    console.log('\n4. Comparing Standard vs Enhanced Deduplication...');
    
    const standardStart = Date.now();
    const standardResult = deduplicationService.deduplicateProducts(products);
    const standardTime = Date.now() - standardStart;

    console.log(`📊 Standard Deduplication Results:`);
    console.log(`   Processing time: ${standardTime}ms`);
    console.log(`   Original products: ${standardResult.stats.originalCount}`);
    console.log(`   Unified products: ${standardResult.stats.unifiedCount}`);
    console.log(`   Reduction: ${((1 - standardResult.stats.unifiedCount / standardResult.stats.originalCount) * 100).toFixed(1)}%`);
    console.log(`   Cross-store matches: ${standardResult.groups.filter(g => g.stores.length > 1).length}`);

    // Test enhanced deduplication (if SQL functions are available)
    try {
      const enhancedStart = Date.now();
      const enhancedResult = await deduplicationService.deduplicateProductsEnhanced(products);
      const enhancedTime = Date.now() - enhancedStart;

      console.log(`\n📊 Enhanced Deduplication Results:`);
      console.log(`   Processing time: ${enhancedTime}ms`);
      console.log(`   Original products: ${enhancedResult.stats.originalCount}`);
      console.log(`   Unified products: ${enhancedResult.stats.unifiedCount}`);
      console.log(`   Reduction: ${((1 - enhancedResult.stats.unifiedCount / enhancedResult.stats.originalCount) * 100).toFixed(1)}%`);
      console.log(`   Cross-store matches: ${enhancedResult.groups.filter(g => g.stores.length > 1).length}`);

      // Compare results
      const standardReduction = (1 - standardResult.stats.unifiedCount / standardResult.stats.originalCount) * 100;
      const enhancedReduction = (1 - enhancedResult.stats.unifiedCount / enhancedResult.stats.originalCount) * 100;
      const improvement = enhancedReduction - standardReduction;

      console.log(`\n📈 Comparison:`);
      console.log(`   Improvement in reduction: ${improvement.toFixed(1)} percentage points`);
      console.log(`   Speed difference: ${enhancedTime - standardTime}ms`);
      console.log(`   Enhanced is ${improvement > 0 ? 'BETTER' : improvement < 0 ? 'WORSE' : 'SAME'} at finding matches`);

    } catch (enhancedError) {
      console.log(`\n⚠️ Enhanced deduplication not available (SQL functions may not be installed)`);
      console.log(`   This is expected if SQL functions haven't been added to Supabase yet`);
    }

    // Test 5: SQL-based matching (if available)
    console.log('\n5. Testing SQL-based Fuzzy Matching...');
    
    try {
      await sqlFuzzyMatchingService.enableFuzzyExtensions();
      const sqlBenchmark = await sqlFuzzyMatchingService.benchmarkSQLMatching();
      
      console.log(`📊 SQL Matching Performance:`);
      console.log(`   Trigram matches: ${sqlBenchmark.trigram.matches} in ${sqlBenchmark.trigram.time}ms`);
      console.log(`   Levenshtein matches: ${sqlBenchmark.levenshtein.matches} in ${sqlBenchmark.levenshtein.time}ms`);
      console.log(`   Comprehensive matches: ${sqlBenchmark.comprehensive.matches} in ${sqlBenchmark.comprehensive.time}ms`);
      
    } catch (sqlError) {
      console.log(`   ⚠️ SQL fuzzy matching not available (extensions may not be installed)`);
      console.log(`   To enable: Run the SQL functions in sql/fuzzy-matching-functions.sql`);
    }

    // Test 6: Analyze specific improvements
    console.log('\n6. Analyzing Specific Improvements...');
    
    const sampleProducts = products.slice(0, 10);
    sampleProducts.forEach((product, index) => {
      const oldNormalized = product.name.toLowerCase().replace(/[^\w\s]/g, ' ').replace(/\s+/g, ' ').trim();
      const newNormalized = advancedNormalizeString(product.name);
      
      console.log(`   ${index + 1}. "${product.name}" (${product.store})`);
      console.log(`      Old: "${oldNormalized}"`);
      console.log(`      New: "${newNormalized}"`);
      console.log(`      Improvement: ${oldNormalized !== newNormalized ? '✅ Enhanced' : '➖ Same'}`);
    });

    console.log('\n✅ Enhanced matching test completed successfully!');
    
    return {
      standardReduction: (1 - standardResult.stats.unifiedCount / standardResult.stats.originalCount) * 100,
      standardTime,
      testProductsCount: products.length,
      crossStoreMatches: standardResult.groups.filter(g => g.stores.length > 1).length
    };

  } catch (error) {
    console.error('❌ Enhanced matching test failed:', error);
    throw error;
  }
}

// Run the test
if (require.main === module) {
  testEnhancedMatching()
    .then((results) => {
      console.log('\n📊 ENHANCED MATCHING TEST SUMMARY:');
      console.log('='.repeat(60));
      console.log(`Test Products: ${results?.testProductsCount || 0}`);
      console.log(`Standard Reduction: ${results?.standardReduction?.toFixed(1) || 0}%`);
      console.log(`Processing Time: ${results?.standardTime || 0}ms`);
      console.log(`Cross-Store Matches: ${results?.crossStoreMatches || 0}`);
      
      console.log('\n🎯 RECOMMENDATIONS FROM DOCUMENT IMPLEMENTED:');
      console.log('   ✅ Advanced string normalization');
      console.log('   ✅ Multiple similarity algorithms (Jaro-Winkler + Jaccard)');
      console.log('   ✅ Token-based matching');
      console.log('   ✅ SQL-based fuzzy matching framework');
      console.log('   ✅ Performance benchmarking');
      
      console.log('\n📖 BASED ON: Matching_Products_Across_Supermarkets.txt');
      console.log('   - Enhanced normalization removes "fluff" words');
      console.log('   - Jaccard similarity for token-based matching');
      console.log('   - PostgreSQL extensions for large-scale matching');
      console.log('   - Weighted combination of multiple algorithms');
      
      console.log('='.repeat(60));
    })
    .catch((error) => {
      console.error('❌ Test failed:', error);
    });
}

export { testEnhancedMatching };
