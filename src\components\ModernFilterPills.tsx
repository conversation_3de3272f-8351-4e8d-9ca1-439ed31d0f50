/**
 * Modern Filter Pills - Premium Design
 * 
 * Sophisticated filter pill components with smooth animations,
 * haptic feedback, and premium visual effects for category selection.
 */

import React, { useRef, useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Animated,
  StyleSheet,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
// import { BlurView } from 'expo-blur'; // Optional dependency
import { getGroceryTheme, ThemeMode } from '../styles/modernGroceryTheme';

interface FilterOption {
  id: string;
  label: string;
  count?: number;
  icon?: string;
}

interface ModernFilterPillsProps {
  options: FilterOption[];
  selectedId: string;
  onSelectionChange: (id: string) => void;
  themeMode?: ThemeMode;
  showCounts?: boolean;
  scrollable?: boolean;
  style?: any;
}

export const ModernFilterPills: React.FC<ModernFilterPillsProps> = ({
  options,
  selectedId,
  onSelectionChange,
  themeMode = 'light',
  showCounts = true,
  scrollable = true,
  style,
}) => {
  const theme = getGroceryTheme(themeMode);
  const [pressedId, setPressedId] = useState<string | null>(null);

  // Animation refs for each pill
  const animationRefs = useRef<{ [key: string]: Animated.Value }>({});

  // Initialize animation values
  useEffect(() => {
    options.forEach(option => {
      if (!animationRefs.current[option.id]) {
        animationRefs.current[option.id] = new Animated.Value(0);
      }
    });
  }, [options]);

  // Animate selection change
  useEffect(() => {
    options.forEach(option => {
      const animValue = animationRefs.current[option.id];
      if (animValue) {
        Animated.spring(animValue, {
          toValue: selectedId === option.id ? 1 : 0,
          tension: 100,
          friction: 8,
          useNativeDriver: false,
        }).start();
      }
    });
  }, [selectedId, options]);

  // Handle pill press
  const handlePress = (optionId: string) => {
    if (optionId === selectedId) return;

    // Haptic feedback (optional)
    try {
      if (Platform.OS === 'ios') {
        const { HapticFeedback } = require('expo-haptics');
        HapticFeedback.selectionAsync();
      }
    } catch (error) {
      // Graceful fallback if expo-haptics is not installed
    }

    onSelectionChange(optionId);
  };

  // Handle press in/out for visual feedback
  const handlePressIn = (optionId: string) => {
    setPressedId(optionId);
  };

  const handlePressOut = () => {
    setPressedId(null);
  };

  // Render individual pill
  const renderPill = (option: FilterOption) => {
    const isSelected = selectedId === option.id;
    const isPressed = pressedId === option.id;
    const animValue = animationRefs.current[option.id];

    // Animated styles
    const animatedBackgroundColor = animValue?.interpolate({
      inputRange: [0, 1],
      outputRange: [theme.colors.neutral[100], theme.colors.primary[500]],
    }) || theme.colors.neutral[100];

    const animatedBorderColor = animValue?.interpolate({
      inputRange: [0, 1],
      outputRange: [theme.colors.semantic.border, theme.colors.primary[500]],
    }) || theme.colors.semantic.border;

    const animatedTextColor = animValue?.interpolate({
      inputRange: [0, 1],
      outputRange: [theme.colors.neutral[700], theme.colors.neutral[0]],
    }) || theme.colors.neutral[700];

    const animatedCountColor = animValue?.interpolate({
      inputRange: [0, 1],
      outputRange: [theme.colors.neutral[500], 'rgba(255,255,255,0.8)'],
    }) || theme.colors.neutral[500];

    const animatedShadowOpacity = animValue?.interpolate({
      inputRange: [0, 1],
      outputRange: [0, 0.2],
    }) || 0;

    return (
      <TouchableOpacity
        key={option.id}
        onPress={() => handlePress(option.id)}
        onPressIn={() => handlePressIn(option.id)}
        onPressOut={handlePressOut}
        activeOpacity={0.8}
        style={styles.pillTouchable}
      >
        <Animated.View
          style={[
            styles.pill,
            {
              backgroundColor: animatedBackgroundColor,
              borderColor: animatedBorderColor,
              shadowOpacity: animatedShadowOpacity,
              shadowColor: theme.colors.primary[500],
              transform: [
                {
                  scale: isPressed ? 0.95 : 1,
                },
              ],
            },
          ]}
        >
          {/* Icon */}
          {option.icon && (
            <Animated.View style={styles.iconContainer}>
              <Ionicons
                name={option.icon as any}
                size={16}
                color={animatedTextColor as any}
              />
            </Animated.View>
          )}

          {/* Label */}
          <Animated.Text
            style={[
              styles.pillText,
              {
                color: animatedTextColor,
                fontFamily: theme.typography.fontFamily.primary,
                fontWeight: isSelected 
                  ? theme.typography.fontWeight.semibold 
                  : theme.typography.fontWeight.medium,
              },
            ]}
          >
            {option.label}
          </Animated.Text>

          {/* Count Badge */}
          {showCounts && option.count !== undefined && (
            <Animated.View
              style={[
                styles.countBadge,
                {
                  backgroundColor: isSelected
                    ? 'rgba(255,255,255,0.2)'
                    : theme.colors.neutral[200],
                },
              ]}
            >
              <Animated.Text
                style={[
                  styles.countText,
                  {
                    color: animatedCountColor,
                    fontFamily: theme.typography.fontFamily.numeric,
                  },
                ]}
              >
                {option.count}
              </Animated.Text>
            </Animated.View>
          )}

          {/* Premium glow effect for selected state */}
          {isSelected && Platform.OS === 'ios' && (
            <Animated.View
              style={[
                styles.glowEffect,
                {
                  opacity: animatedShadowOpacity,
                  shadowColor: theme.colors.primary[400],
                },
              ]}
            />
          )}
        </Animated.View>
      </TouchableOpacity>
    );
  };

  const Container = scrollable ? ScrollView : View;
  const containerProps = scrollable ? {
    horizontal: true,
    showsHorizontalScrollIndicator: false,
    contentContainerStyle: styles.scrollContent,
  } : {
    style: styles.staticContent,
  };

  return (
    <Container
      style={[styles.container, style]}
      {...containerProps}
    >
      {options.map(renderPill)}
    </Container>
  );
};

// Store Filter Pills Component
interface StoreFilterPillsProps {
  selectedStores: string[];
  onStoreToggle: (store: string) => void;
  themeMode?: ThemeMode;
}

export const StoreFilterPills: React.FC<StoreFilterPillsProps> = ({
  selectedStores,
  onStoreToggle,
  themeMode = 'light',
}) => {
  const theme = getGroceryTheme(themeMode);

  const stores = [
    { id: 'woolworths', name: 'Woolworths', abbr: 'W' },
    { id: 'newworld', name: 'New World', abbr: 'NW' },
    { id: 'paknsave', name: "Pak'nSave", abbr: 'PS' },
  ];

  const renderStorePill = (store: any) => {
    const isSelected = selectedStores.includes(store.id);
    const storeConfig = theme.colors.stores[store.id as keyof typeof theme.colors.stores];

    return (
      <TouchableOpacity
        key={store.id}
        onPress={() => onStoreToggle(store.id)}
        style={[
          styles.storePill,
          {
            backgroundColor: isSelected
              ? storeConfig.primary
              : theme.colors.neutral[100],
            borderColor: isSelected
              ? storeConfig.primary
              : theme.colors.semantic.border,
          },
        ]}
        activeOpacity={0.8}
      >
        {/* Store Logo */}
        <View
          style={[
            styles.storeLogo,
            {
              backgroundColor: isSelected
                ? 'rgba(255,255,255,0.2)'
                : storeConfig.primary,
            },
          ]}
        >
          <Text
            style={[
              styles.storeLogoText,
              {
                color: isSelected
                  ? storeConfig.contrast
                  : storeConfig.contrast,
                fontFamily: theme.typography.fontFamily.primary,
              },
            ]}
          >
            {store.abbr}
          </Text>
        </View>

        {/* Store Name */}
        <Text
          style={[
            styles.storeNameText,
            {
              color: isSelected
                ? storeConfig.contrast
                : theme.colors.neutral[700],
              fontFamily: theme.typography.fontFamily.primary,
              fontWeight: isSelected
                ? theme.typography.fontWeight.semibold
                : theme.typography.fontWeight.medium,
            },
          ]}
        >
          {store.name}
        </Text>

        {/* Selection Indicator */}
        {isSelected && (
          <View style={styles.selectionIndicator}>
            <Ionicons
              name="checkmark"
              size={14}
              color={storeConfig.contrast}
            />
          </View>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.storeContainer}>
      <Text style={[styles.storeTitle, { color: theme.colors.neutral[700] }]}>
        Select Stores:
      </Text>
      <View style={styles.storeGrid}>
        {stores.map(renderStorePill)}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 8,
  },

  scrollContent: {
    paddingHorizontal: 16,
    paddingRight: 32, // Extra padding for last item
  },

  staticContent: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 16,
    gap: 8,
  },

  pillTouchable: {
    marginRight: 8,
  },

  pill: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 24,
    borderWidth: 1.5,
    flexDirection: 'row',
    alignItems: 'center',
    minWidth: 80,
    justifyContent: 'center',
    // Shadow
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 8,
    elevation: 4,
  },

  iconContainer: {
    marginRight: 6,
  },

  pillText: {
    fontSize: 14,
    letterSpacing: 0.5,
  },

  countBadge: {
    marginLeft: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    minWidth: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },

  countText: {
    fontSize: 11,
    fontWeight: '600',
  },

  glowEffect: {
    position: 'absolute',
    top: -2,
    left: -2,
    right: -2,
    bottom: -2,
    borderRadius: 26,
    shadowOffset: { width: 0, height: 0 },
    shadowRadius: 8,
    elevation: 8,
  },

  // Store filter styles
  storeContainer: {
    padding: 16,
  },

  storeTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },

  storeGrid: {
    flexDirection: 'row',
    gap: 8,
  },

  storePill: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 12,
    borderWidth: 1.5,
    gap: 8,
    minHeight: 48,
  },

  storeLogo: {
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },

  storeLogoText: {
    fontSize: 10,
    fontWeight: 'bold',
    letterSpacing: 0.5,
  },

  storeNameText: {
    fontSize: 12,
    flex: 1,
    textAlign: 'center',
  },

  selectionIndicator: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: 'rgba(255,255,255,0.3)',
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default ModernFilterPills;