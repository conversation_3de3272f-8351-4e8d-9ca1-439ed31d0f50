import React, { useState, useCallback, useMemo, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Dimensions,
  SafeAreaView,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';
import { productFetchService, Product } from '../../services/productFetchService';
import { ProductTestComponent } from '../../components/ProductTestComponent';
import { ConsolidatedProductCard } from '../../components/ConsolidatedProductCard';
import { consolidatedProductService } from '../../services/consolidatedProductService';
import { ConsolidatedProduct } from '../../types/deduplication';

const { width } = Dimensions.get('window');

// Mock data removed - now using live Supabase data

// Categories now generated dynamically from Supabase data

export const AllProductsScreenFixed: React.FC = () => {
  const { colors, mode } = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [refreshing, setRefreshing] = useState(false);
  const [products, setProducts] = useState<Product[]>([]);
  const [consolidatedProducts, setConsolidatedProducts] = useState<ConsolidatedProduct[]>([]);
  const [loading, setLoading] = useState(true);
  const [categories, setCategories] = useState<any[]>([]);
  const [deduplicationEnabled, setDeduplicationEnabled] = useState(true);
  const [consolidationStats, setConsolidationStats] = useState<any>(null);

  // Fetch products from Supabase
  const fetchProducts = useCallback(async () => {
    try {
      setLoading(true);
      const result = await productFetchService.fetchProducts({
        maxResults: 10000, // Show all available products (10,309 in database)
        sortBy: 'name'
      });
      
      setProducts(result.products);

      // Apply deduplication if enabled
      if (deduplicationEnabled) {
        console.log('🔄 Applying product deduplication...');
        const consolidated = await consolidatedProductService.consolidateProducts(result.products);
        setConsolidatedProducts(consolidated);

        // Calculate and log consolidation stats
        const stats = consolidatedProductService.getConsolidationStats(result.products, consolidated);
        setConsolidationStats(stats);
        console.log('📊 Consolidation Stats:', stats);

        // Build categories from consolidated data
        const categoryCount: Record<string, number> = {};
        consolidated.forEach(product => {
          const category = product.category || 'Uncategorized';
          categoryCount[category] = (categoryCount[category] || 0) + 1;
        });

        const dynamicCategories = [
          { id: 'All', label: 'All Categories', icon: 'apps', count: consolidated.length },
          ...Object.entries(categoryCount).map(([cat, count]) => ({
            id: cat,
            label: cat.charAt(0).toUpperCase() + cat.slice(1).replace(/-/g, ' & '),
            icon: 'pricetag',
            count
          }))
        ];

        setCategories(dynamicCategories);
      } else {
        // Convert individual products to consolidated format for consistency
        const individual = result.products.map(product => ({
          id: `single_${product.id}`,
          name: product.name,
          brand: product.brand,
          category: product.category,
          imageUrl: product.image_url,
          size: product.size,
          unit: product.unit,
          storePrices: { [product.store]: product.price },
          allStores: [product],
          lowestPrice: product.price,
          highestPrice: product.price,
          maxSavings: 0,
          availableStores: [product.store],
          confidence: 1.0,
          consolidatedAt: new Date(),
        }));
        setConsolidatedProducts(individual);
        setConsolidationStats(null);

        // Build categories from original data
        const categoryCount: Record<string, number> = {};
        result.products.forEach(product => {
          categoryCount[product.category] = (categoryCount[product.category] || 0) + 1;
        });

        const dynamicCategories = [
          { id: 'All', label: 'All Categories', icon: 'apps', count: result.products.length },
          ...Object.entries(categoryCount).map(([cat, count]) => ({
            id: cat,
            label: cat.charAt(0).toUpperCase() + cat.slice(1).replace(/-/g, ' & '),
            icon: 'pricetag',
            count
          }))
        ];

        setCategories(dynamicCategories);
      }
    } catch (error) {
      console.error('Failed to fetch products:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchProducts();
  }, [fetchProducts]);

  // Filter consolidated products based on search and category
  const filteredProducts = useMemo(() => {
    let filtered = consolidatedProducts;

    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(query) ||
        (product.brand && product.brand.toLowerCase().includes(query))
      );
    }

    if (selectedCategory !== 'All') {
      filtered = filtered.filter(product =>
        product.category === selectedCategory
      );
    }

    return filtered;
  }, [consolidatedProducts, searchQuery, selectedCategory]);

  // Toggle deduplication
  const toggleDeduplication = useCallback(() => {
    setDeduplicationEnabled(!deduplicationEnabled);
    // Refetch products with new deduplication setting
    fetchProducts();
  }, [deduplicationEnabled, fetchProducts]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await fetchProducts();
    setRefreshing(false);
  }, [fetchProducts]);

  const getStoreColor = (store: string) => {
    switch (store) {
      case 'woolworths': return '#00A651';
      case 'newworld': return '#E31E24';
      case 'paknsave': return '#FFD100';
      default: return colors.primary;
    }
  };

  // Handle product press
  const handleProductPress = useCallback((product: ConsolidatedProduct) => {
    console.log('Product pressed:', product.name);
    // Could navigate to product detail screen
  }, []);

  // Handle adding product to shopping list
  const handleAddToList = useCallback((product: ConsolidatedProduct, store: string) => {
    console.log('Adding to list:', product.name, 'from', store);
    Alert.alert(
      'Added to Shopping List',
      `"${product.name}" from ${store} has been added to your shopping list.`
    );
  }, []);

  const renderConsolidatedProductCard = useCallback(({ item }: { item: ConsolidatedProduct }) => (
    <ConsolidatedProductCard
      product={item}
      viewMode={viewMode}
      onPress={handleProductPress}
      onAddToList={handleAddToList}
    />
  ), [viewMode, handleProductPress, handleAddToList]);

  const renderHeader = () => (
    <View style={[styles.header, { backgroundColor: colors.background }]}>
      <View style={styles.headerTop}>
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          All Products
        </Text>
        <View style={styles.headerButtons}>
          <TouchableOpacity
            style={[styles.headerButton, { backgroundColor: colors.surface }]}
            onPress={toggleDeduplication}
          >
            <Ionicons
              name={deduplicationEnabled ? "layers" : "copy"}
              size={20}
              color={deduplicationEnabled ? colors.success : colors.textSecondary}
            />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.headerButton, { backgroundColor: colors.surface }]}
            onPress={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
          >
            <Ionicons
              name={viewMode === 'grid' ? 'list' : 'grid'}
              size={20}
              color={colors.text}
            />
          </TouchableOpacity>
        </View>
      </View>

      {/* Deduplication Stats */}
      {deduplicationEnabled && consolidationStats && (
        <View style={[styles.statsContainer, { backgroundColor: colors.surface }]}>
          <Text style={[styles.statsText, { color: colors.success }]}>
            ✨ Showing {consolidationStats.consolidatedCount} products
            (reduced from {consolidationStats.originalCount} • {consolidationStats.reductionPercentage.toFixed(1)}% less clutter)
          </Text>
          {consolidationStats.crossStoreProducts > 0 && (
            <Text style={[styles.statsSubText, { color: colors.textSecondary }]}>
              🛒 {consolidationStats.crossStoreProducts} products available at multiple stores
            </Text>
          )}
        </View>
      )}

      {/* Simple Search */}
      <View style={[styles.searchContainer, { backgroundColor: colors.surface, borderColor: colors.border }]}>
        <Ionicons name="search" size={20} color={colors.textSecondary} />
        <Text style={[styles.searchPlaceholder, { color: colors.textSecondary }]}>
          Search products... (Demo Mode)
        </Text>
      </View>

      {/* Category Pills */}
      <View style={styles.categoriesContainer}>
        {categories.map(category => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryPill,
              {
                backgroundColor: selectedCategory === category.id ? colors.primary : colors.surface,
                borderColor: colors.border,
              }
            ]}
            onPress={() => setSelectedCategory(category.id)}
          >
            <Text style={[
              styles.categoryText,
              { 
                color: selectedCategory === category.id ? colors.background : colors.text 
              }
            ]}>
              {category.label} ({category.count})
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <Text style={[styles.resultsText, { color: colors.textSecondary }]}>
        {filteredProducts.length} products found (Demo Mode)
      </Text>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, styles.centered, { backgroundColor: colors.background }]}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
          Loading products from database...
        </Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <FlatList
        data={filteredProducts}
        renderItem={renderConsolidatedProductCard}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        numColumns={1} // Always use list view for consolidated cards
        key={`consolidated-${deduplicationEnabled}`}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
              No products found. Try refreshing or check your database connection.
            </Text>
            <TouchableOpacity 
              style={[styles.button, { backgroundColor: colors.primary, marginTop: 16 }]}
              onPress={() => {
                // Temporarily show the diagnostic component
                console.log('🔧 For debugging, temporarily replace this screen content with <ProductTestComponent />');
              }}
            >
              <Text style={styles.buttonText}>Run Diagnostics</Text>
            </TouchableOpacity>
          </View>
        }
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
  },
  listContainer: {
    padding: 16,
  },
  header: {
    marginBottom: 16,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerButton: {
    padding: 8,
    borderRadius: 8,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 16,
  },
  searchPlaceholder: {
    marginLeft: 12,
    fontSize: 16,
  },
  categoriesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  categoryPill: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '500',
  },
  resultsText: {
    fontSize: 14,
    marginTop: 8,
  },
  productCard: {
    flex: 1,
    margin: 4,
    padding: 12,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  productImagePlaceholder: {
    height: 80,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  productPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  productSize: {
    fontSize: 12,
    marginBottom: 8,
  },
  storeIndicators: {
    flexDirection: 'row',
    gap: 4,
  },
  storeIndicator: {
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  storeText: {
    fontSize: 10,
    fontWeight: 'bold',
  },
  button: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
});