/**
 * Enhanced Shopping List Component
 * 
 * This component displays shopping list items with combined pricing from multiple stores,
 * special offers, loyalty card prices, and product images.
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { theme } from '../styles';
import { CleanShoppingListItem } from './CleanShoppingListItem';
import { enhancedProductService, EnhancedShoppingItem } from '../services/enhancedProductService';

interface EnhancedShoppingListProps {
  items: string[]; // Array of product names to search for
  onItemToggle?: (itemId: string) => void;
  onItemDelete?: (itemId: string) => void;
  onItemQuantityChange?: (itemId: string, quantity: number) => void;
  showTwoPricesOnly?: boolean;
  compact?: boolean;
  refreshing?: boolean;
  onRefresh?: () => void;
}

export const EnhancedShoppingList: React.FC<EnhancedShoppingListProps> = ({
  items,
  onItemToggle,
  onItemDelete,
  onItemQuantityChange,
  showTwoPricesOnly = false,
  compact = false,
  refreshing = false,
  onRefresh,
}) => {
  const { colors } = useTheme();
  const [enhancedItems, setEnhancedItems] = useState<EnhancedShoppingItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load enhanced items when component mounts or items change
  useEffect(() => {
    loadEnhancedItems();
  }, [items]);

  const loadEnhancedItems = async () => {
    if (items.length === 0) {
      setEnhancedItems([]);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      console.log('🔍 Loading enhanced items for:', items);
      
      // Search for each item using the enhanced service
      const searchPromises = items.map(itemName => 
        enhancedProductService.searchCombinedProducts(itemName, {
          maxResults: 1,
          sortBy: 'price',
          showTwoPricesOnly
        })
      );

      const searchResults = await Promise.all(searchPromises);
      
      // Extract the first result for each item
      const enhanced = searchResults
        .map((results, index) => {
          if (results.length > 0) {
            return {
              ...results[0],
              id: `enhanced-${index}`, // Ensure unique IDs
              name: items[index] // Use original search term as name
            };
          }
          
          // Create a placeholder item if no results found
          return {
            id: `placeholder-${index}`,
            name: items[index],
            category: 'Unknown',
            storePrices: [
              { store: 'woolworths' as const, available: false },
              { store: 'newworld' as const, available: false },
              { store: 'paknsave' as const, available: false }
            ],
            hasSpecialOffers: false,
            quantity: 1,
            checked: false
          } as EnhancedShoppingItem;
        });

      setEnhancedItems(enhanced);
      console.log(`✅ Loaded ${enhanced.length} enhanced items`);

    } catch (err) {
      console.error('❌ Failed to load enhanced items:', err);
      setError('Failed to load product information. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    if (onRefresh) {
      onRefresh();
    }
    loadEnhancedItems();
  };

  const convertToStorePrice = (enhancedItem: EnhancedShoppingItem) => {
    return enhancedItem.storePrices.map(sp => ({
      store: sp.store,
      price: sp.price,
      specialPrice: sp.specialPrice,
      isSpecial: sp.isSpecial,
      available: sp.available,
      brand: sp.brand,
      size: sp.size,
      imageUrl: sp.imageUrl
    }));
  };

  const renderEnhancedItem = ({ item, index }: { item: EnhancedShoppingItem; index: number }) => {
    return (
      <CleanShoppingListItem
        id={item.id}
        name={item.name}
        checked={item.checked}
        quantity={item.quantity}
        unit={item.unit}
        category={item.category}
        storePrices={convertToStorePrice(item)}
        onToggleChecked={onItemToggle || (() => {})}
        onQuantityChange={onItemQuantityChange}
        onDelete={onItemDelete || (() => {})}
        showPrices={true}
        compact={compact}
        productImage={item.bestImage}
        showTwoPricesOnly={showTwoPricesOnly}
      />
    );
  };

  const renderHeader = () => {
    if (enhancedItems.length === 0) return null;

    const totalSavings = enhancedItems.reduce((sum, item) => sum + (item.savings || 0), 0);
    const specialOffers = enhancedItems.filter(item => item.hasSpecialOffers).length;
    const availableItems = enhancedItems.filter(item => 
      item.storePrices.some(sp => sp.available)
    ).length;

    return (
      <View style={[styles.summaryCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
        <View style={styles.summaryRow}>
          <View style={styles.summaryItem}>
            <Text style={[styles.summaryNumber, { color: theme.colors.success }]}>
              ${totalSavings.toFixed(2)}
            </Text>
            <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>
              Total Savings
            </Text>
          </View>
          
          <View style={styles.summaryItem}>
            <Text style={[styles.summaryNumber, { color: theme.colors.warning }]}>
              {specialOffers}
            </Text>
            <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>
              Special Offers
            </Text>
          </View>
          
          <View style={styles.summaryItem}>
            <Text style={[styles.summaryNumber, { color: colors.text }]}>
              {availableItems}/{enhancedItems.length}
            </Text>
            <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>
              Available
            </Text>
          </View>
        </View>

        {showTwoPricesOnly && (
          <View style={[styles.modeIndicator, { backgroundColor: theme.colors.primary }]}>
            <Ionicons name="flash" size={16} color="white" />
            <Text style={styles.modeText}>Quick Compare Mode</Text>
          </View>
        )}
      </View>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="basket-outline" size={64} color={colors.border} />
      <Text style={[styles.emptyTitle, { color: colors.text }]}>
        No Items Yet
      </Text>
      <Text style={[styles.emptySubtext, { color: colors.textSecondary }]}>
        Add items to your shopping list to see price comparisons
      </Text>
    </View>
  );

  const renderError = () => (
    <View style={styles.errorState}>
      <Ionicons name="warning-outline" size={48} color={theme.colors.error} />
      <Text style={[styles.errorTitle, { color: theme.colors.error }]}>
        Failed to Load Prices
      </Text>
      <Text style={[styles.errorSubtext, { color: colors.textSecondary }]}>
        {error}
      </Text>
      <TouchableOpacity
        style={[styles.retryButton, { backgroundColor: theme.colors.primary }]}
        onPress={loadEnhancedItems}
      >
        <Text style={styles.retryButtonText}>Try Again</Text>
      </TouchableOpacity>
    </View>
  );

  if (loading && enhancedItems.length === 0) {
    return (
      <View style={styles.loadingState}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
          Loading price comparisons...
        </Text>
      </View>
    );
  }

  if (error && enhancedItems.length === 0) {
    return renderError();
  }

  if (enhancedItems.length === 0) {
    return renderEmptyState();
  }

  return (
    <FlatList
      data={enhancedItems}
      renderItem={renderEnhancedItem}
      keyExtractor={(item) => item.id}
      ListHeaderComponent={renderHeader}
      style={[styles.container, { backgroundColor: colors.background }]}
      showsVerticalScrollIndicator={false}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={handleRefresh}
          colors={[theme.colors.primary]}
          tintColor={theme.colors.primary}
        />
      }
    />
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  summaryCard: {
    margin: theme.spacing.base,
    padding: theme.spacing.base,
    borderRadius: theme.radius.lg,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryNumber: {
    fontSize: theme.typography.fontSize.xl,
    fontWeight: theme.typography.fontWeight.bold,
  },
  summaryLabel: {
    fontSize: theme.typography.fontSize.xs,
    marginTop: 4,
  },
  modeIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    paddingHorizontal: theme.spacing.sm,
    borderRadius: theme.radius.sm,
    alignSelf: 'center',
  },
  modeText: {
    color: 'white',
    fontSize: theme.typography.fontSize.xs,
    fontWeight: theme.typography.fontWeight.semibold,
    marginLeft: 4,
  },
  loadingState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.xl,
  },
  loadingText: {
    marginTop: theme.spacing.base,
    fontSize: theme.typography.fontSize.base,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.xl,
  },
  emptyTitle: {
    fontSize: theme.typography.fontSize.xl,
    fontWeight: theme.typography.fontWeight.semibold,
    marginTop: theme.spacing.base,
  },
  emptySubtext: {
    fontSize: theme.typography.fontSize.base,
    textAlign: 'center',
    marginTop: theme.spacing.sm,
  },
  errorState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.xl,
  },
  errorTitle: {
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.semibold,
    marginTop: theme.spacing.base,
  },
  errorSubtext: {
    fontSize: theme.typography.fontSize.base,
    textAlign: 'center',
    marginTop: theme.spacing.sm,
    marginBottom: theme.spacing.base,
  },
  retryButton: {
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.base,
    borderRadius: theme.radius.sm,
  },
  retryButtonText: {
    color: 'white',
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.semibold,
  },
});