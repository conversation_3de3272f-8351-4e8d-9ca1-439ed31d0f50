import React, { useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView, StyleSheet } from 'react-native';
import { designSystem, createStyle } from '../styles/designSystem';

// 🎨 LIVE STYLE EDITOR - Like Figma's Properties Panel
export const StyleEditor: React.FC = () => {
  const [selectedCard, setSelectedCard] = useState<'small' | 'medium' | 'large'>('medium');
  const [selectedButton, setSelectedButton] = useState<'primary' | 'secondary'>('primary');
  const [selectedColor, setSelectedColor] = useState(designSystem.colors.primary[500]);

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>🎨 Live Style Editor</Text>
      
      {/* COLOR PICKER SECTION */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Colors</Text>
        <View style={styles.colorGrid}>
          {Object.entries(designSystem.colors.primary).map(([key, color]) => (
            <TouchableOpacity
              key={key}
              style={[styles.colorSwatch, { backgroundColor: color }]}
              onPress={() => setSelectedColor(color)}
            >
              <Text style={styles.colorLabel}>{key}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* SPACING SECTION */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Spacing</Text>
        <View style={styles.spacingGrid}>
          {Object.entries(designSystem.spacing).map(([key, value]) => (
            <View key={key} style={styles.spacingItem}>
              <View style={[styles.spacingBox, { width: value, height: value }]} />
              <Text style={styles.spacingLabel}>{key}: {value}px</Text>
            </View>
          ))}
        </View>
      </View>

      {/* COMPONENT PREVIEW SECTION */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Component Variants</Text>
        
        {/* Card Variants */}
        <Text style={styles.subsectionTitle}>Cards</Text>
        <View style={styles.variantGrid}>
          {(['small', 'medium', 'large'] as const).map((variant) => (
            <TouchableOpacity
              key={variant}
              style={[createStyle.card(variant), { backgroundColor: selectedColor }]}
              onPress={() => setSelectedCard(variant)}
            >
              <Text style={styles.variantText}>{variant}</Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Button Variants */}
        <Text style={styles.subsectionTitle}>Buttons</Text>
        <View style={styles.variantGrid}>
          {(['primary', 'secondary'] as const).map((variant) => (
            <TouchableOpacity
              key={variant}
              style={createStyle.button(variant, 'medium')}
              onPress={() => setSelectedButton(variant)}
            >
              <Text style={[styles.buttonText, { color: variant === 'primary' ? 'white' : 'black' }]}>
                {variant}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* TYPOGRAPHY SECTION */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Typography</Text>
        {Object.entries(designSystem.typography.fontSize).map(([key, size]) => (
          // @ts-ignore
          <Text key={key} style={[createStyle.text(key as any, 'medium' as any), { marginBottom: 8 }]}>
            {key}: {size}px - Sample Text
          </Text>
        ))}
      </View>

      {/* SHADOW PREVIEW */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Shadows</Text>
        <View style={styles.shadowGrid}>
          {Object.entries(designSystem.shadows).map(([key, shadow]) => (
            <View key={key} style={[styles.shadowBox, shadow]}>
              <Text style={styles.shadowLabel}>{key}</Text>
            </View>
          ))}
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: designSystem.colors.background.primary,
    padding: designSystem.spacing.lg,
  },
  title: {
    ...createStyle.text('3xl', 'bold'),
    marginBottom: designSystem.spacing.xl,
    textAlign: 'center',
  },
  section: {
    marginBottom: designSystem.spacing['3xl'],
  },
  sectionTitle: {
    ...createStyle.text('xl', 'semibold'),
    marginBottom: designSystem.spacing.md,
  },
  subsectionTitle: {
    ...createStyle.text('lg', 'medium'),
    marginBottom: designSystem.spacing.sm,
    marginTop: designSystem.spacing.lg,
  },
  colorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: designSystem.spacing.sm,
  },
  colorSwatch: {
    width: 60,
    height: 60,
    borderRadius: designSystem.borderRadius.md,
    justifyContent: 'center',
    alignItems: 'center',
    ...designSystem.shadows.sm,
  },
  colorLabel: {
    color: 'white',
    fontWeight: 'bold' as any,
    fontSize: 10,
  },
  spacingGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: designSystem.spacing.lg,
  },
  spacingItem: {
    alignItems: 'center',
    gap: designSystem.spacing.xs,
  },
  spacingBox: {
    backgroundColor: designSystem.colors.primary[500],
    borderRadius: designSystem.borderRadius.sm,
  },
  // @ts-ignore
  spacingLabel: {
    // @ts-ignore
    ...createStyle.text('xs' as any, 'medium' as any),
  },
  variantGrid: {
    flexDirection: 'row',
    gap: designSystem.spacing.md,
    marginBottom: designSystem.spacing.lg,
  },
  // @ts-ignore
  variantText: {
    // @ts-ignore
    ...createStyle.text('sm' as any, 'medium' as any),
    color: 'white',
  },
  // @ts-ignore
  buttonText: {
    // @ts-ignore
    ...createStyle.text('md' as any, 'semibold' as any),
  },
  shadowGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: designSystem.spacing.lg,
  },
  shadowBox: {
    width: 80,
    height: 60,
    backgroundColor: 'white',
    borderRadius: designSystem.borderRadius.md,
    justifyContent: 'center',
    alignItems: 'center',
  },
  // @ts-ignore
  shadowLabel: {
    // @ts-ignore
    ...createStyle.text('xs', 'medium'),
  },
});

export default StyleEditor;
