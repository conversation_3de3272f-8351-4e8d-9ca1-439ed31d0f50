import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  Animated,
  TouchableOpacity,
  StatusBar,
  Platform,
} from 'react-native';
import { 
  GestureHandlerRootView, 
  PanGestureHandler, 
  PanGestureHandlerGestureEvent 
} from 'react-native-gesture-handler';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '../context/ThemeContext';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface ModernSwipeableContainerProps {
  shoppingListComponent: React.ReactNode;
  allProductsComponent: React.ReactNode;
  cookbookComponent: React.ReactNode;
  onOpenDrawer: () => void;
}

export const ModernSwipeableContainer: React.FC<ModernSwipeableContainerProps> = ({
  shoppingListComponent,
  allProductsComponent,
  cookbookComponent,
  onOpenDrawer,
}) => {
  const { colors: themeColors, isDark: isDarkMode } = useTheme();
  const [currentScreen, setCurrentScreen] = useState<'shopping' | 'products' | 'cookbook'>('shopping');
  const translateX = useRef(new Animated.Value(0)).current;
  const gestureRef = useRef<PanGestureHandler>(null);
  const buttonScale = useRef(new Animated.Value(1)).current;

  // Modern color scheme
  const colors = {
    light: {
      background: '#F8FAFC',
      surface: '#FFFFFF',
      primary: '#475569',
      text: '#0F172A',
      textSecondary: '#64748B',
      navBackground: 'rgba(255, 255, 255, 0.95)',
      navBorder: 'rgba(71, 85, 105, 0.1)',
      shadow: 'rgba(15, 23, 42, 0.1)',
    },
    dark: {
      background: '#0F172A',
      surface: '#1E293B',
      primary: '#06B6D4',
      text: '#F8FAFC',
      textSecondary: '#94A3B8',
      navBackground: 'rgba(30, 41, 59, 0.95)',
      navBorder: 'rgba(148, 163, 184, 0.1)',
      shadow: 'rgba(0, 0, 0, 0.3)',
    }
  };

  const currentColors = isDarkMode ? colors.dark : colors.light;

  const handleGesture = Animated.event(
    [{ nativeEvent: { translationX: translateX } }],
    { useNativeDriver: true }
  );

  const handleGestureEnd = (event: any) => {
    const { translationX, velocityX } = event.nativeEvent;
    
    // Simplified thresholds for better compatibility
    const swipeThreshold = SCREEN_WIDTH * 0.3;
    const velocityThreshold = 500;
    const minSwipeDistance = 50;
    
    let newScreen = currentScreen;
    let targetTranslateX = 0;
    
    // Only process if swipe meets minimum distance requirement
    if (Math.abs(translationX) >= minSwipeDistance) {
      if (currentScreen === 'shopping') {
        if (translationX < -swipeThreshold || velocityX < -velocityThreshold) {
          newScreen = 'products';
          targetTranslateX = -SCREEN_WIDTH;
        }
      } else if (currentScreen === 'products') {
        if (translationX < -swipeThreshold || velocityX < -velocityThreshold) {
          newScreen = 'cookbook';
          targetTranslateX = -SCREEN_WIDTH * 2;
        } else if (translationX > swipeThreshold || velocityX > velocityThreshold) {
          newScreen = 'shopping';
          targetTranslateX = 0;
        } else {
          targetTranslateX = -SCREEN_WIDTH;
        }
      } else {
        if (translationX > swipeThreshold || velocityX > velocityThreshold) {
          newScreen = 'products';
          targetTranslateX = -SCREEN_WIDTH;
        } else {
          targetTranslateX = -SCREEN_WIDTH * 2;
        }
      }
    } else {
      // Swipe too small, return to current position
      if (currentScreen === 'shopping') {
        targetTranslateX = 0;
      } else if (currentScreen === 'products') {
        targetTranslateX = -SCREEN_WIDTH;
      } else {
        targetTranslateX = -SCREEN_WIDTH * 2;
      }
    }

    Animated.spring(translateX, {
      toValue: targetTranslateX,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start();

    if (newScreen !== currentScreen) {
      setCurrentScreen(newScreen);
    }
  };

  const switchToScreen = (screen: 'shopping' | 'products' | 'cookbook') => {
    // Animate button press
    Animated.sequence([
      Animated.timing(buttonScale, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(buttonScale, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();

    setCurrentScreen(screen);
    let targetTranslateX = 0;
    
    if (screen === 'shopping') {
      targetTranslateX = 0;
    } else if (screen === 'products') {
      targetTranslateX = -SCREEN_WIDTH;
    } else {
      targetTranslateX = -SCREEN_WIDTH * 2;
    }
    
    Animated.spring(translateX, {
      toValue: targetTranslateX,
      useNativeDriver: true,
      tension: 80,
      friction: 7,
    }).start();
  };

  const renderFloatingNav = () => (
    <View style={[styles.floatingNavContainer, { bottom: Platform.OS === 'ios' ? 34 : 20 }]}>
      <View style={styles.floatingNavBlur}>
        <View style={[styles.floatingNav, { 
          backgroundColor: currentColors.navBackground,
          borderColor: currentColors.navBorder,
          shadowColor: currentColors.shadow 
        }]}>
          {/* Shopping Button */}
          <TouchableOpacity
            style={[
              styles.navButton,
              currentScreen === 'shopping' && [styles.activeNavButton, { backgroundColor: currentColors.primary + '15' }]
            ]}
            onPress={() => switchToScreen('shopping')}
            hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
            activeOpacity={0.7}
          >
            <Animated.View style={{ transform: [{ scale: currentScreen === 'shopping' ? buttonScale : 1 }] }}>
              <Ionicons 
                name="basket" 
                size={22} 
                color={currentScreen === 'shopping' ? currentColors.primary : currentColors.textSecondary} 
              />
            </Animated.View>
            {currentScreen === 'shopping' && (
              <View style={[styles.activeIndicator, { backgroundColor: currentColors.primary }]} />
            )}
          </TouchableOpacity>

          {/* Products Button */}
          <TouchableOpacity
            style={[
              styles.navButton,
              currentScreen === 'products' && [styles.activeNavButton, { backgroundColor: currentColors.primary + '15' }]
            ]}
            onPress={() => switchToScreen('products')}
            hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
            activeOpacity={0.7}
          >
            <Animated.View style={{ transform: [{ scale: currentScreen === 'products' ? buttonScale : 1 }] }}>
              <Ionicons 
                name="storefront" 
                size={22} 
                color={currentScreen === 'products' ? currentColors.primary : currentColors.textSecondary} 
              />
            </Animated.View>
            {currentScreen === 'products' && (
              <View style={[styles.activeIndicator, { backgroundColor: currentColors.primary }]} />
            )}
          </TouchableOpacity>

          {/* Cookbook Button */}
          <TouchableOpacity
            style={[
              styles.navButton,
              currentScreen === 'cookbook' && [styles.activeNavButton, { backgroundColor: currentColors.primary + '15' }]
            ]}
            onPress={() => switchToScreen('cookbook')}
            hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
            activeOpacity={0.7}
          >
            <Animated.View style={{ transform: [{ scale: currentScreen === 'cookbook' ? buttonScale : 1 }] }}>
              <Ionicons 
                name="restaurant" 
                size={22} 
                color={currentScreen === 'cookbook' ? currentColors.primary : currentColors.textSecondary} 
              />
            </Animated.View>
            {currentScreen === 'cookbook' && (
              <View style={[styles.activeIndicator, { backgroundColor: currentColors.primary }]} />
            )}
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  const renderDrawerButton = () => (
    <TouchableOpacity 
      style={[styles.drawerButton, { 
        backgroundColor: currentColors.surface,
        shadowColor: currentColors.shadow,
        borderColor: currentColors.navBorder
      }]} 
      onPress={onOpenDrawer}
    >
      <Ionicons name="menu" size={20} color={currentColors.primary} />
    </TouchableOpacity>
  );

  return (
    <GestureHandlerRootView style={[styles.container, { backgroundColor: currentColors.background }]}>
      <StatusBar 
        barStyle={isDarkMode ? "light-content" : "dark-content"} 
        backgroundColor={currentColors.background} 
      />
      <SafeAreaView style={styles.safeArea}>
        {/* Drawer Button - Top Left */}
        {renderDrawerButton()}

        {/* Swipeable Content - Full Screen */}
        <PanGestureHandler
          ref={gestureRef}
          onGestureEvent={handleGesture}
          onEnded={handleGestureEnd}
          activeOffsetX={[-20, 20]}
          failOffsetY={[-50, 50]}
          shouldCancelWhenOutside={true}
          minPointers={1}
          maxPointers={1}
          enabled={true}
        >
          <Animated.View style={styles.contentContainer}>
            <Animated.View
              style={[
                styles.screensContainer,
                {
                  transform: [{ translateX }],
                },
              ]}
            >
              {/* Shopping List Screen */}
              <View style={[styles.screen, { backgroundColor: currentColors.background }]}>
                {shoppingListComponent}
              </View>
              
              {/* All Products Screen */}
              <View style={[styles.screen, { backgroundColor: currentColors.background }]}>
                {allProductsComponent}
              </View>
              
              {/* Cookbook Screen */}
              <View style={[styles.screen, { backgroundColor: currentColors.background }]}>
                {cookbookComponent}
              </View>
            </Animated.View>
          </Animated.View>
        </PanGestureHandler>

        {/* Floating Navigation */}
        {renderFloatingNav()}
      </SafeAreaView>
    </GestureHandlerRootView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  drawerButton: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 15 : 25,
    left: 20,
    width: 44,
    height: 44,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1000,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    borderWidth: 1,
  },
  contentContainer: {
    flex: 1,
  },
  screensContainer: {
    flexDirection: 'row',
    width: SCREEN_WIDTH * 3,
    height: '100%',
  },
  screen: {
    width: SCREEN_WIDTH,
    height: '100%',
  },
  floatingNavContainer: {
    position: 'absolute',
    alignSelf: 'center',
    zIndex: 1000,
  },
  floatingNavBlur: {
    borderRadius: 28,
    overflow: 'hidden',
  },
  floatingNav: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 8,
    borderRadius: 28,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
    borderWidth: 1,
  },
  navButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 4,
    position: 'relative',
  },
  activeNavButton: {
    borderRadius: 24,
  },
  activeIndicator: {
    position: 'absolute',
    bottom: 6,
    width: 4,
    height: 4,
    borderRadius: 2,
  },
});