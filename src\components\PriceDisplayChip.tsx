import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { useTheme } from '../context/ThemeContext';
import { theme } from '../styles';

interface PriceDisplayChipProps {
  price: number;
  store: 'woolworths' | 'newworld' | 'paknsave';
  storeName: string;
  isLowestPrice?: boolean;
  potentialSavings?: number;
  onTap?: () => void;
  size?: 'small' | 'medium' | 'large';
  showStore?: boolean;
  showSavings?: boolean;
  disabled?: boolean;
}

export const PriceDisplayChip: React.FC<PriceDisplayChipProps> = ({
  price,
  store,
  storeName,
  isLowestPrice = false,
  potentialSavings,
  onTap,
  size = 'medium',
  showStore = true,
  showSavings = true,
  disabled = false,
}) => {
  const { colors } = useTheme();

  const getStoreColor = (store: string): string => {
    const storeColors = {
      woolworths: '#00A651',
      newworld: '#E31E24',
      paknsave: '#FFD100',
    };
    return storeColors[store as keyof typeof storeColors] || theme.colors.primary;
  };

  const getStoreEmoji = (store: string): string => {
    const storeEmojis = {
      woolworths: '🍎',
      newworld: '🛒',
      paknsave: '💰',
    };
    return storeEmojis[store as keyof typeof storeEmojis] || '🏪';
  };

  const formatPrice = (price: number): string => {
    return price > 0 ? `$${price.toFixed(2)}` : 'N/A';
  };

  const getChipStyle = () => {
    const baseStyle = {
      backgroundColor: isLowestPrice ? '#E8F5E8' : colors.surface,
      borderColor: isLowestPrice ? '#00A651' : colors.border,
      borderWidth: isLowestPrice ? 2 : 1,
    };

    if (disabled) {
      return {
        ...baseStyle,
        backgroundColor: colors.backgroundSecondary,
        borderColor: colors.border,
        opacity: 0.6,
      };
    }

    return baseStyle;
  };

  const getPriceTextStyle = () => {
    if (disabled) {
      return { color: colors.textTertiary };
    }
    return { color: isLowestPrice ? '#00A651' : colors.text };
  };

  const getStoreTextStyle = () => {
    if (disabled) {
      return { color: colors.textTertiary };
    }
    return { color: getStoreColor(store) };
  };

  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          container: { paddingHorizontal: 8, paddingVertical: 4 },
          price: { fontSize: theme.typography.fontSize.sm },
          store: { fontSize: theme.typography.fontSize.xs },
          emoji: { fontSize: 12 },
          savings: { fontSize: theme.typography.fontSize.xs },
        };
      case 'large':
        return {
          container: { paddingHorizontal: 16, paddingVertical: 12 },
          price: { fontSize: theme.typography.fontSize.xl },
          store: { fontSize: theme.typography.fontSize.base },
          emoji: { fontSize: 20 },
          savings: { fontSize: theme.typography.fontSize.sm },
        };
      default: // medium
        return {
          container: { paddingHorizontal: 12, paddingVertical: 8 },
          price: { fontSize: theme.typography.fontSize.base },
          store: { fontSize: theme.typography.fontSize.sm },
          emoji: { fontSize: 16 },
          savings: { fontSize: theme.typography.fontSize.xs },
        };
    }
  };

  const sizeStyles = getSizeStyles();
  const chipStyle = getChipStyle();
  const priceTextStyle = getPriceTextStyle();
  const storeTextStyle = getStoreTextStyle();

  const ChipContent = () => (
    <View style={[
      styles.chip,
      chipStyle,
      sizeStyles.container,
      onTap && !disabled && styles.touchable
    ]}>
      {/* Best Price Indicator */}
      {isLowestPrice && !disabled && (
        <View style={styles.bestPriceIndicator}>
          <Text style={styles.bestPriceText}>⭐</Text>
        </View>
      )}

      {/* Main Content */}
      <View style={styles.mainContent}>
        {/* Store Info */}
        {showStore && (
          <View style={styles.storeRow}>
            <Text style={[styles.storeEmoji, sizeStyles.emoji]}>
              {getStoreEmoji(store)}
            </Text>
            <Text style={[
              styles.storeName, 
              sizeStyles.store,
              storeTextStyle,
              { fontWeight: theme.typography.fontWeight.semibold }
            ]}>
              {storeName}
            </Text>
          </View>
        )}

        {/* Price */}
        <View style={styles.priceRow}>
          <Text style={[
            styles.price,
            sizeStyles.price,
            priceTextStyle,
            { fontWeight: theme.typography.fontWeight.bold }
          ]}>
            {formatPrice(price)}
          </Text>
          
          {isLowestPrice && !disabled && (
            <Text style={[styles.lowestLabel, { color: '#00A651' }]}>
              BEST
            </Text>
          )}
        </View>

        {/* Savings */}
        {showSavings && potentialSavings && potentialSavings > 0 && !disabled && (
          <Text style={[
            styles.savings,
            sizeStyles.savings,
            { color: '#00A651' }
          ]}>
            Save ${potentialSavings.toFixed(2)}
          </Text>
        )}
      </View>

      {/* Tap Indicator */}
      {onTap && !disabled && (
        <View style={styles.tapIndicator}>
          <Text style={[styles.tapText, { color: colors.textTertiary }]}>
            ⋯
          </Text>
        </View>
      )}
    </View>
  );

  if (onTap && !disabled) {
    return (
      <TouchableOpacity onPress={onTap} activeOpacity={0.8}>
        <ChipContent />
      </TouchableOpacity>
    );
  }

  return <ChipContent />;
};

export const PriceComparisonChips: React.FC<{
  prices: Array<{
    store: 'woolworths' | 'newworld' | 'paknsave';
    storeName: string;
    price: number;
    available: boolean;
  }>;
  onStoreSelect?: (store: string) => void;
  layout?: 'horizontal' | 'vertical';
  size?: 'small' | 'medium' | 'large';
}> = ({
  prices,
  onStoreSelect,
  layout = 'horizontal',
  size = 'small',
}) => {
  const availablePrices = prices.filter(p => p.available && p.price > 0);
  
  if (availablePrices.length === 0) {
    return (
      <View style={styles.noPricesContainer}>
        <Text style={styles.noPricesText}>No prices available</Text>
      </View>
    );
  }

  const lowestPrice = Math.min(...availablePrices.map(p => p.price));

  const containerStyle = layout === 'horizontal' 
    ? styles.horizontalContainer 
    : styles.verticalContainer;

  return (
    <View style={containerStyle}>
      {availablePrices.map((priceInfo, index) => (
        <PriceDisplayChip
          key={`${priceInfo.store}-${index}`}
          price={priceInfo.price}
          store={priceInfo.store}
          storeName={priceInfo.storeName}
          isLowestPrice={priceInfo.price === lowestPrice}
          onTap={onStoreSelect ? () => onStoreSelect(priceInfo.store) : undefined}
          size={size}
          showStore={true}
          showSavings={false}
        />
      ))}
    </View>
  );
};

export const LoadingPriceChip: React.FC<{
  size?: 'small' | 'medium' | 'large';
}> = ({ size = 'medium' }) => {
  const { colors } = useTheme();
  
  return (
    <View style={[
      styles.chip,
      { 
        backgroundColor: colors.backgroundSecondary,
        borderColor: colors.border,
        borderWidth: 1,
        paddingHorizontal: 12,
        paddingVertical: 8,
      }
    ]}>
      <View style={styles.loadingContent}>
        <View style={[styles.loadingBar, { backgroundColor: colors.border }]} />
        <View style={[styles.loadingBar, styles.loadingBarShort, { backgroundColor: colors.border }]} />
      </View>
    </View>
  );
};

export const NoPriceChip: React.FC<{
  reason?: string;
  size?: 'small' | 'medium' | 'large';
  onTap?: () => void;
}> = ({ reason = 'Select brand', size = 'medium', onTap }) => {
  const { colors } = useTheme();
  
  const ChipContent = () => (
    <View style={[
      styles.chip,
      { 
        backgroundColor: colors.backgroundSecondary,
        borderColor: colors.border,
        borderWidth: 1,
        borderStyle: 'dashed',
        paddingHorizontal: 12,
        paddingVertical: 8,
      }
    ]}>
      <View style={styles.noPriceContent}>
        <Text style={[styles.noPriceText, { color: colors.textSecondary }]}>
          {reason}
        </Text>
        {onTap && (
          <Text style={[styles.tapHint, { color: colors.textTertiary }]}>
            Tap to choose
          </Text>
        )}
      </View>
    </View>
  );

  if (onTap) {
    return (
      <TouchableOpacity onPress={onTap} activeOpacity={0.8}>
        <ChipContent />
      </TouchableOpacity>
    );
  }

  return <ChipContent />;
};

const styles = StyleSheet.create({
  chip: {
    borderRadius: theme.radius.lg,
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
    minHeight: 36,
  },
  touchable: {
    // Add subtle shadow for touchable chips
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  bestPriceIndicator: {
    position: 'absolute',
    top: -4,
    right: -4,
    zIndex: 1,
  },
  bestPriceText: {
    fontSize: 12,
  },
  mainContent: {
    flex: 1,
    alignItems: 'center',
  },
  storeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  storeEmoji: {
    marginRight: 4,
  },
  storeName: {
    textAlign: 'center',
  },
  priceRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  price: {
    textAlign: 'center',
  },
  lowestLabel: {
    fontSize: theme.typography.fontSize.xs,
    fontWeight: theme.typography.fontWeight.bold,
    marginLeft: 4,
  },
  savings: {
    textAlign: 'center',
    fontWeight: theme.typography.fontWeight.semibold,
    marginTop: 2,
  },
  tapIndicator: {
    position: 'absolute',
    right: 4,
    top: '50%',
    transform: [{ translateY: -6 }],
  },
  tapText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  horizontalContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing.xs,
  },
  verticalContainer: {
    gap: theme.spacing.xs,
  },
  noPricesContainer: {
    padding: theme.spacing.sm,
    alignItems: 'center',
  },
  noPricesText: {
    fontSize: theme.typography.fontSize.sm,
    color: '#999',
  },
  loadingContent: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingBar: {
    height: 8,
    borderRadius: 4,
    marginVertical: 2,
    width: '80%',
  },
  loadingBarShort: {
    width: '60%',
  },
  noPriceContent: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  noPriceText: {
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.medium,
    textAlign: 'center',
  },
  tapHint: {
    fontSize: theme.typography.fontSize.xs,
    marginTop: 2,
    textAlign: 'center',
  },
});