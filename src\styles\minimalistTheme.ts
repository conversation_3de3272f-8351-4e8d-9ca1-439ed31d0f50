/**
 * Minimalist 2-Color Theme System
 * 
 * A clean, modern design using only two primary colors
 * for a sophisticated and minimal aesthetic.
 */

import { Platform } from 'react-native';

// Minimalist 2-color palette
const COLORS = {
  // Primary color - Deep charcoal for text and main elements
  primary: '#2D3748',        // Deep charcoal/dark gray
  primaryLight: '#4A5568',   // Lighter variant for secondary elements
  primaryDark: '#1A202C',    // Darker variant for emphasis
  
  // Secondary color - Clean white/light gray for backgrounds
  secondary: '#FFFFFF',      // Pure white
  secondaryGray: '#F7FAFC',  // Very light gray
  secondaryDark: '#EDF2F7',  // Light gray for subtle contrast
  
  // Semantic states using tints of primary colors
  success: '#38A169',        // Muted green
  warning: '#D69E2E',        // Muted yellow
  error: '#E53E3E',          // Muted red
  
  // Minimalist theme colors
  light: {
    background: '#FFFFFF',           // Pure white background
    backgroundSecondary: '#F7FAFC',  // Very light gray for cards
    surface: '#FFFFFF',              // Pure white surfaces
    
    text: '#2D3748',                 // Deep charcoal text
    textSecondary: '#4A5568',        // Medium gray text
    textTertiary: '#718096',         // Light gray text
    textInverse: '#FFFFFF',          // White text on dark backgrounds
    
    border: '#E2E8F0',               // Light gray borders
    borderSecondary: '#EDF2F7',      // Very light borders
    
    shadow: 'rgba(45, 55, 72, 0.1)', // Subtle shadows
    overlay: 'rgba(45, 55, 72, 0.6)', // Dark overlay for modals
  },
  
  // Dark theme variant (optional)
  dark: {
    background: '#1A202C',           // Dark background
    backgroundSecondary: '#2D3748',  // Lighter dark for cards
    surface: '#2D3748',              // Dark surfaces
    
    text: '#FFFFFF',                 // White text
    textSecondary: '#CBD5E0',        // Light gray text
    textTertiary: '#A0AEC0',         // Medium gray text
    textInverse: '#2D3748',          // Dark text on light backgrounds
    
    border: '#4A5568',               // Medium gray borders
    borderSecondary: '#2D3748',      // Dark borders
    
    shadow: 'rgba(0, 0, 0, 0.3)',    // Darker shadows
    overlay: 'rgba(0, 0, 0, 0.8)',   // Black overlay for modals
  },
};

// Typography - Clean and minimal
const TYPOGRAPHY = {
  fontFamily: {
    regular: Platform.select({
      ios: 'SF Pro Display',
      android: 'Roboto',
      default: 'System',
    }),
    medium: Platform.select({
      ios: 'SF Pro Display Medium',
      android: 'Roboto Medium',
      default: 'System',
    }),
    semibold: Platform.select({
      ios: 'SF Pro Display Semibold',
      android: 'Roboto Medium',
      default: 'System',
    }),
  },
  fontSize: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 28,
    '4xl': 32,
  },
  lineHeight: {
    xs: 16,
    sm: 20,
    base: 24,
    lg: 28,
    xl: 32,
    '2xl': 36,
    '3xl': 40,
    '4xl': 44,
  },
  fontWeight: {
    normal: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: '700' as const,
  },
};

// Spacing - Clean 8px grid system
const SPACING = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  '2xl': 48,
  '3xl': 64,
};

// Component styles - Minimal and clean
const COMPONENTS = {
  // Button styles
  button: {
    height: 48,
    borderRadius: 8,
    paddingHorizontal: 24,
  },
  
  // Input styles
  input: {
    height: 48,
    borderRadius: 8,
    paddingHorizontal: 16,
    borderWidth: 1,
  },
  
  // Card styles
  card: {
    borderRadius: 12,
    padding: 16,
    shadowRadius: 8,
    shadowOpacity: 0.1,
  },
  
  // Header styles
  header: {
    height: 56,
    paddingHorizontal: 16,
  },
};

// Shadow system - Subtle and minimal
const SHADOWS = {
  sm: {
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  md: {
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  lg: {
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
};

// Export minimalist theme
export const minimalistTheme = {
  colors: COLORS,
  typography: TYPOGRAPHY,
  spacing: SPACING,
  components: COMPONENTS,
  shadows: SHADOWS,
};

// Helper function to get theme colors based on mode
export const getMinimalistThemeColors = (isDark: boolean = false) => {
  return isDark ? COLORS.dark : COLORS.light;
};

export default minimalistTheme;