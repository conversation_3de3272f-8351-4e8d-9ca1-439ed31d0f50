import React, { useState, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Dimensions,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';

const { width } = Dimensions.get('window');

// Simple mock data to avoid infinite loops
const mockProducts = [
  {
    id: '1',
    name: 'Organic Bananas',
    lowestPrice: 3.99,
    highestPrice: 5.49,
    stores: ['woolworths', 'newworld'],
    category: 'fruit-veg',
    imageUrl: null,
    size: '1kg',
    unit: 'kg',
    storePrices: { woolworths: 3.99, newworld: 5.49 },
    allStores: ['woolworths', 'newworld']
  },
  {
    id: '2',
    name: 'Free Range Eggs',
    lowestPrice: 6.99,
    highestPrice: 8.49,
    stores: ['woolworths', 'paknsave'],
    category: 'fridge-deli',
    imageUrl: null,
    size: '12 pack',
    unit: 'pack',
    storePrices: { woolworths: 8.49, paknsave: 6.99 },
    allStores: ['woolworths', 'paknsave']
  },
  {
    id: '3',
    name: 'Whole Milk',
    lowestPrice: 3.19,
    highestPrice: 3.89,
    stores: ['newworld', 'paknsave'],
    category: 'fridge-deli',
    imageUrl: null,
    size: '2L',
    unit: 'L',
    storePrices: { newworld: 3.89, paknsave: 3.19 },
    allStores: ['newworld', 'paknsave']
  },
];

const CATEGORIES = [
  { id: 'All', label: 'All Categories', icon: 'apps', count: 3 },
  { id: 'fruit-veg', label: 'Fruit & Veg', icon: 'leaf', count: 1 },
  { id: 'fridge-deli', label: 'Fridge & Deli', icon: 'snow', count: 2 },
];

export const AllProductsScreenFixed: React.FC = () => {
  const { colors, mode } = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [refreshing, setRefreshing] = useState(false);

  // Filter products based on search and category
  const filteredProducts = useMemo(() => {
    let filtered = mockProducts;
    
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(query)
      );
    }
    
    if (selectedCategory !== 'All') {
      filtered = filtered.filter(product =>
        product.category === selectedCategory
      );
    }
    
    return filtered;
  }, [searchQuery, selectedCategory]);

  const handleRefresh = useCallback(() => {
    setRefreshing(true);
    // Simulate refresh
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  }, []);

  const renderProductCard = useCallback(({ item }: { item: any }) => (
    <View style={[styles.productCard, { backgroundColor: colors.background }]}>
      <View style={[styles.productImagePlaceholder, { backgroundColor: colors.border }]}>
        <Ionicons name="image-outline" size={32} color={colors.textSecondary} />
      </View>
      <View style={styles.productInfo}>
        <Text style={[styles.productName, { color: colors.text }]} numberOfLines={2}>
          {item.name}
        </Text>
        <Text style={[styles.productPrice, { color: colors.primary }]}>
          ${item.lowestPrice.toFixed(2)}
          {item.lowestPrice !== item.highestPrice && ` - $${item.highestPrice.toFixed(2)}`}
        </Text>
        <Text style={[styles.productSize, { color: colors.textSecondary }]}>
          {item.size}
        </Text>
        <View style={styles.storeIndicators}>
          {item.allStores.map((store: string) => (
            <View key={store} style={[styles.storeIndicator, { backgroundColor: colors.primary }]}>
              <Text style={[styles.storeText, { color: colors.background }]}>
                {store.charAt(0).toUpperCase()}
              </Text>
            </View>
          ))}
        </View>
      </View>
    </View>
  ), [colors]);

  const renderHeader = () => (
    <View style={[styles.header, { backgroundColor: colors.background }]}>
      <View style={styles.headerTop}>
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          All Products
        </Text>
        <TouchableOpacity
          style={[styles.headerButton, { backgroundColor: colors.surface }]}
          onPress={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
        >
          <Ionicons
            name={viewMode === 'grid' ? 'list' : 'grid'}
            size={20}
            color={colors.text}
          />
        </TouchableOpacity>
      </View>

      {/* Simple Search */}
      <View style={[styles.searchContainer, { backgroundColor: colors.surface, borderColor: colors.border }]}>
        <Ionicons name="search" size={20} color={colors.textSecondary} />
        <Text style={[styles.searchPlaceholder, { color: colors.textSecondary }]}>
          Search products... (Demo Mode)
        </Text>
      </View>

      {/* Category Pills */}
      <View style={styles.categoriesContainer}>
        {CATEGORIES.map(category => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryPill,
              {
                backgroundColor: selectedCategory === category.id ? colors.primary : colors.surface,
                borderColor: colors.border,
              }
            ]}
            onPress={() => setSelectedCategory(category.id)}
          >
            <Text style={[
              styles.categoryText,
              { 
                color: selectedCategory === category.id ? colors.background : colors.text 
              }
            ]}>
              {category.label} ({category.count})
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <Text style={[styles.resultsText, { color: colors.textSecondary }]}>
        {filteredProducts.length} products found (Demo Mode)
      </Text>
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <FlatList
        data={filteredProducts}
        renderItem={renderProductCard}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        numColumns={viewMode === 'grid' ? 2 : 1}
        key={viewMode}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  listContainer: {
    padding: 16,
  },
  header: {
    marginBottom: 16,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerButton: {
    padding: 8,
    borderRadius: 8,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 16,
  },
  searchPlaceholder: {
    marginLeft: 12,
    fontSize: 16,
  },
  categoriesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  categoryPill: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '500',
  },
  resultsText: {
    fontSize: 14,
    marginTop: 8,
  },
  productCard: {
    flex: 1,
    margin: 4,
    padding: 12,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  productImagePlaceholder: {
    height: 80,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  productPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  productSize: {
    fontSize: 12,
    marginBottom: 8,
  },
  storeIndicators: {
    flexDirection: 'row',
    gap: 4,
  },
  storeIndicator: {
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  storeText: {
    fontSize: 10,
    fontWeight: 'bold',
  },
});