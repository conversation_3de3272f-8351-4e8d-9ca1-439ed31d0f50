import { Recipe } from './storage';
import { getRecipes } from './storage';
import { supabase } from '../supabase/client';

// Simple recipe interface for compatibility
interface SupabaseRecipe {
  id: string;
  title: string;
  ingredients: string[];
  instructions: string[];
  imageUrl?: string;
  description?: string;
  cookingTime?: string;
  difficulty?: string;
  servings?: number;
  category?: string;
  cuisine?: string;
  tags?: string[];
}

/**
 * Check if a recipe has all required fields to be considered complete
 */
export const isRecipeComplete = (recipe: Recipe | SupabaseRecipe): boolean => {
  // Basic validation - must have title, ingredients, instructions
  // Temporarily allowing recipes without images to test
  const hasTitle = !!(recipe.title && recipe.title.trim() && recipe.title !== 'Untitled Recipe');
  const hasIngredients = recipe.ingredients && recipe.ingredients.length >= 1 && recipe.ingredients.some(ingredient => ingredient.trim());
  const hasInstructions = recipe.instructions && recipe.instructions.length >= 1 && recipe.instructions.some(instruction => instruction.trim());
  const hasValidImage = !!(recipe.imageUrl && recipe.imageUrl.trim() && recipe.imageUrl !== '' && recipe.imageUrl.startsWith('http'));
  
  // Log which recipes have images for debugging
  if (hasValidImage) {
    console.log(`✅ Recipe with image: "${recipe.title}" - ${recipe.imageUrl}`);
  }
  
  return hasTitle && hasIngredients && hasInstructions; // Temporarily removed: && hasValidImage;
};

/**
 * Get all complete recipes from Supabase
 */
export const getCompleteRecipesFromSupabase = async (options?: {
  category?: string;
  cuisine?: string;
  maxCookingTime?: number;
  difficulty?: string;
  tags?: string[];
  dietary?: string[];
  mealType?: string;
  limit?: number;
  query?: string;
}): Promise<SupabaseRecipe[]> => {
  try {
    console.log('🔍 Getting complete recipes from Supabase...');
    
    let query = supabase
      .from('recipes')
      .select('*');

    // Add filters based on options
    if (options?.category) {
      query = query.eq('category', options.category);
    }
    
    if (options?.cuisine) {
      query = query.eq('cuisine', options.cuisine);
    }
    
    if (options?.difficulty) {
      query = query.eq('difficulty', options.difficulty);
    }
    
    if (options?.mealType) {
      query = query.eq('mealType', options.mealType);
    }
    
    if (options?.query) {
      query = query.ilike('title', `%${options.query}%`);
    }
    
    // Limit results
    if (options?.limit) {
      query = query.limit(options.limit);
    } else {
      query = query.limit(100);
    }
    
    const { data: recipes, error } = await query;
    
    if (error) {
      console.error('❌ Supabase query error:', error);
      return [];
    }
    
    if (!recipes) {
      console.log('❌ No recipes found in Supabase');
      return [];
    }
    
    // Filter to ensure only complete recipes
    const completeRecipes = recipes.filter(isRecipeComplete);
    
    console.log(`✅ Found ${completeRecipes.length} complete recipes from Supabase`);
    return completeRecipes as SupabaseRecipe[];
    
  } catch (error) {
    console.error('❌ Failed to get complete recipes from Supabase:', error);
    return [];
  }
};

/**
 * Get all complete recipes from local app storage
 */
export const getCompleteRecipesFromStorage = async (): Promise<Recipe[]> => {
  try {
    console.log('📱 Getting complete recipes from local storage...');
    
    // Get all recipes from local storage
    const allLocalRecipes = await getRecipes();
    
    // Filter to only complete recipes
    const completeLocalRecipes = allLocalRecipes.filter(isRecipeComplete);
    
    console.log(`✅ Found ${completeLocalRecipes.length} complete recipes in local storage`);
    return completeLocalRecipes;
    
  } catch (error) {
    console.error('❌ Failed to get complete recipes from storage:', error);
    return [];
  }
};

/**
 * Get all complete recipes from both Supabase and local storage
 */
export const getAllCompleteRecipes = async (options?: {
  category?: string;
  cuisine?: string;
  maxCookingTime?: number;
  difficulty?: string;
  tags?: string[];
  dietary?: string[];
  mealType?: string;
  limit?: number;
  query?: string;
  sourcePreference?: 'supabase' | 'local' | 'both';
}): Promise<{
  supabaseRecipes: SupabaseRecipe[];
  localRecipes: Recipe[];
  totalCount: number;
}> => {
  const sourcePreference = options?.sourcePreference || 'both';
  
  try {
    let supabaseRecipes: SupabaseRecipe[] = [];
    let localRecipes: Recipe[] = [];
    
    // Get recipes based on source preference
    if (sourcePreference === 'supabase' || sourcePreference === 'both') {
      supabaseRecipes = await getCompleteRecipesFromSupabase(options);
    }
    
    if (sourcePreference === 'local' || sourcePreference === 'both') {
      localRecipes = await getCompleteRecipesFromStorage();
    }
    
    const totalCount = supabaseRecipes.length + localRecipes.length;
    
    console.log(`✅ Retrieved complete recipes:`, {
      supabase: supabaseRecipes.length,
      local: localRecipes.length,
      total: totalCount
    });
    
    return {
      supabaseRecipes,
      localRecipes,
      totalCount
    };
    
  } catch (error) {
    console.error('❌ Failed to get all complete recipes:', error);
    return {
      supabaseRecipes: [],
      localRecipes: [],
      totalCount: 0
    };
  }
};

/**
 * Get complete recipes by category from both sources
 */
export const getCompleteRecipesByCategory = async (category: string, limit?: number): Promise<{
  supabaseRecipes: SupabaseRecipe[];
  localRecipes: Recipe[];
  totalCount: number;
}> => {
  return await getAllCompleteRecipes({
    category,
    limit: limit
  });
};

/**
 * Search complete recipes with query
 */
export const searchCompleteRecipes = async (query: string, options?: {
  category?: string;
  cuisine?: string;
  maxCookingTime?: number;
  difficulty?: string;
  tags?: string[];
  dietary?: string[];
  mealType?: string;
  limit?: number;
  sourcePreference?: 'supabase' | 'local' | 'both';
}): Promise<{
  supabaseRecipes: SupabaseRecipe[];
  localRecipes: Recipe[];
  totalCount: number;
}> => {
  const supabaseRecipes = await getCompleteRecipesFromSupabase({
    ...options,
    query
  });
  
  // For local search, filter by query in title, ingredients, or description
  const localRecipes = await getCompleteRecipesFromStorage();
  const filteredLocalRecipes = localRecipes.filter(recipe => {
    const searchText = `${recipe.title} ${recipe.description || ''} ${recipe.ingredients.join(' ')} ${recipe.tags?.join(' ') || ''}`.toLowerCase();
    return searchText.includes(query.toLowerCase());
  });
  
  return {
    supabaseRecipes,
    localRecipes: filteredLocalRecipes,
    totalCount: supabaseRecipes.length + filteredLocalRecipes.length
  };
};