# Recipe Import & Categorization Guide

## Overview
This guide helps you import your scraped recipes into the app with automatic categorization by:
- **Cuisine** (Italian, Mexican, Chinese, Thai, etc.)
- **Meal Type** (Breakfast, Lunch, Dinner, Snack, Dessert)
- **Dietary** (Vegetarian, Vegan, Gluten-Free, Dairy-Free, etc.)

## Quick Start

```bash
# Process your recipes
node categorize-scraped-recipes.js your-recipes.json
```

## Supported JSON Formats

The script is flexible and accepts various JSON structures. Here are common formats:

### Format 1: Standard Recipe Format
```json
{
  "title": "Recipe Name",
  "description": "Recipe description",
  "ingredients": ["ingredient 1", "ingredient 2"],
  "instructions": ["step 1", "step 2"],
  "cookingTime": "30 minutes",
  "servings": 4
}
```

### Format 2: Alternative Field Names
```json
{
  "name": "Recipe Name",
  "summary": "Recipe description", 
  "ingredients": ["ingredient 1", "ingredient 2"],
  "directions": "Single string of directions",
  "prep_time": "15 minutes",
  "cook_time": "30 minutes",
  "yield": "4 servings"
}
```

### Format 3: With Nutrition Data
```json
{
  "title": "Recipe Name",
  "ingredients": ["ingredient 1", "ingredient 2"],
  "instructions": ["step 1", "step 2"],
  "nutrition": {
    "calories": 350,
    "protein": 25,
    "carbohydrates": 30,
    "fat": 15
  }
}
```

## Automatic Categorization

The script automatically categorizes recipes based on keywords in:
- Title
- Description
- Ingredients
- Tags

### Cuisine Detection
- **Italian**: pasta, pizza, risotto, marinara, alfredo
- **Mexican**: taco, burrito, enchilada, salsa, guacamole
- **Chinese**: stir-fry, soy sauce, wok, dim sum, noodle
- **Thai**: pad thai, curry, coconut milk, lemongrass
- **Indian**: curry, masala, tandoori, naan, biryani
- **Japanese**: sushi, teriyaki, ramen, miso, tempura
- **Mediterranean**: greek, feta, olive, hummus, tzatziki
- **American**: burger, bbq, sandwich, mac and cheese
- **French**: croissant, crepe, quiche, ratatouille
- **Korean**: kimchi, bulgogi, bibimbap, gochujang
- **Spanish**: paella, tapas, gazpacho, sangria
- **Middle Eastern**: shawarma, kebab, falafel, tabbouleh

### Meal Type Detection
- **Breakfast**: breakfast, pancake, waffle, egg, omelette, cereal, oatmeal
- **Lunch**: lunch, sandwich, salad, wrap, soup
- **Dinner**: dinner, main course, entree, roast, casserole
- **Snack**: snack, appetizer, finger food, dip
- **Dessert**: dessert, cake, cookie, pie, chocolate, ice cream
- **Beverage**: drink, smoothie, juice, tea, coffee

### Dietary Detection
- **Vegetarian**: No meat/fish + contains vegetables
- **Vegan**: No animal products + plant-based keywords
- **Gluten-Free**: No wheat/flour/pasta + gluten-free keywords
- **Dairy-Free**: No milk/cheese/butter + dairy-free keywords
- **Low-Carb**: No pasta/bread/rice + low-carb keywords
- **Healthy**: Contains salad, grilled, steamed, quinoa, kale

## Processing Large Recipe Collections

### Step 1: Prepare Your JSON File
Ensure your recipes are in a JSON array:
```json
[
  { "title": "Recipe 1", ... },
  { "title": "Recipe 2", ... },
  { "title": "Recipe 3", ... }
]
```

### Step 2: Run the Categorization Script
```bash
node categorize-scraped-recipes.js my-recipes.json
```

### Step 3: Review the Output
The script will:
1. Show categorization statistics
2. Save a processed file: `my-recipes-processed.json`
3. Upload to Algolia automatically

## Filtering Recipes in Your App

Once uploaded, users can find recipes by:

### By Cuisine
```javascript
// In CategoryRecipesScreen
navigation.navigate('CategoryRecipes', { 
  filterType: 'cuisine',
  filterValue: 'italian' 
});
```

### By Meal Type
```javascript
navigation.navigate('CategoryRecipes', { 
  filterType: 'mealType',
  filterValue: 'breakfast' 
});
```

### By Dietary Preference
```javascript
navigation.navigate('CategoryRecipes', { 
  filterType: 'dietary',
  filterValue: 'vegan' 
});
```

## Bulk Import Tips

### 1. Clean Your Data First
```javascript
// Example cleaning script
const cleanRecipes = recipes.map(recipe => ({
  ...recipe,
  ingredients: recipe.ingredients || [],
  instructions: recipe.instructions || recipe.directions || [],
  title: recipe.title || recipe.name || 'Untitled'
}));
```

### 2. Test with Small Batches
Start with 10-20 recipes to verify categorization accuracy.

### 3. Monitor Algolia Dashboard
Check your Algolia dashboard to see:
- Total recipes indexed
- Search performance
- Facet distribution

## Troubleshooting

### Issue: Recipes not categorizing correctly
**Solution**: Add more keywords to the detection arrays in the script

### Issue: Upload fails
**Solution**: Check your Algolia API key has write permissions

### Issue: Duplicate recipes
**Solution**: Ensure each recipe has a unique ID before uploading

## Example: Import from Popular Recipe Sites

### From AllRecipes Format
```json
{
  "name": "Chicken Parmesan",
  "description": "Classic Italian-American dish",
  "ingredients": {
    "amount": ["2", "1 cup", "2 cups"],
    "name": ["chicken breasts", "breadcrumbs", "marinara sauce"]
  }
}
```

Transform to:
```javascript
const transformed = {
  title: recipe.name,
  description: recipe.description,
  ingredients: recipe.ingredients.amount.map((amt, i) => 
    `${amt} ${recipe.ingredients.name[i]}`
  )
};
```

### From Food Network Format
```json
{
  "title": "Bobby's Burger",
  "categories": ["American", "Grilling"],
  "ingredients": ["ground beef", "buns", "cheese"],
  "directions": ["Form patties", "Grill 5 minutes per side"]
}
```

Already compatible! Just ensure it's in an array.

## Next Steps

1. **Prepare your JSON file** with scraped recipes
2. **Run the categorization script**
3. **Check your app** - recipes will appear in appropriate categories
4. **Customize categories** by editing the script if needed

## Support

If you have recipes in a different format, you can:
1. Transform them to match supported formats
2. Modify the `formatRecipeForAlgolia` function in the script
3. Share a sample for custom processing logic 

## Overview
This guide helps you import your scraped recipes into the app with automatic categorization by:
- **Cuisine** (Italian, Mexican, Chinese, Thai, etc.)
- **Meal Type** (Breakfast, Lunch, Dinner, Snack, Dessert)
- **Dietary** (Vegetarian, Vegan, Gluten-Free, Dairy-Free, etc.)

## Quick Start

```bash
# Process your recipes
node categorize-scraped-recipes.js your-recipes.json
```

## Supported JSON Formats

The script is flexible and accepts various JSON structures. Here are common formats:

### Format 1: Standard Recipe Format
```json
{
  "title": "Recipe Name",
  "description": "Recipe description",
  "ingredients": ["ingredient 1", "ingredient 2"],
  "instructions": ["step 1", "step 2"],
  "cookingTime": "30 minutes",
  "servings": 4
}
```

### Format 2: Alternative Field Names
```json
{
  "name": "Recipe Name",
  "summary": "Recipe description", 
  "ingredients": ["ingredient 1", "ingredient 2"],
  "directions": "Single string of directions",
  "prep_time": "15 minutes",
  "cook_time": "30 minutes",
  "yield": "4 servings"
}
```

### Format 3: With Nutrition Data
```json
{
  "title": "Recipe Name",
  "ingredients": ["ingredient 1", "ingredient 2"],
  "instructions": ["step 1", "step 2"],
  "nutrition": {
    "calories": 350,
    "protein": 25,
    "carbohydrates": 30,
    "fat": 15
  }
}
```

## Automatic Categorization

The script automatically categorizes recipes based on keywords in:
- Title
- Description
- Ingredients
- Tags

### Cuisine Detection
- **Italian**: pasta, pizza, risotto, marinara, alfredo
- **Mexican**: taco, burrito, enchilada, salsa, guacamole
- **Chinese**: stir-fry, soy sauce, wok, dim sum, noodle
- **Thai**: pad thai, curry, coconut milk, lemongrass
- **Indian**: curry, masala, tandoori, naan, biryani
- **Japanese**: sushi, teriyaki, ramen, miso, tempura
- **Mediterranean**: greek, feta, olive, hummus, tzatziki
- **American**: burger, bbq, sandwich, mac and cheese
- **French**: croissant, crepe, quiche, ratatouille
- **Korean**: kimchi, bulgogi, bibimbap, gochujang
- **Spanish**: paella, tapas, gazpacho, sangria
- **Middle Eastern**: shawarma, kebab, falafel, tabbouleh

### Meal Type Detection
- **Breakfast**: breakfast, pancake, waffle, egg, omelette, cereal, oatmeal
- **Lunch**: lunch, sandwich, salad, wrap, soup
- **Dinner**: dinner, main course, entree, roast, casserole
- **Snack**: snack, appetizer, finger food, dip
- **Dessert**: dessert, cake, cookie, pie, chocolate, ice cream
- **Beverage**: drink, smoothie, juice, tea, coffee

### Dietary Detection
- **Vegetarian**: No meat/fish + contains vegetables
- **Vegan**: No animal products + plant-based keywords
- **Gluten-Free**: No wheat/flour/pasta + gluten-free keywords
- **Dairy-Free**: No milk/cheese/butter + dairy-free keywords
- **Low-Carb**: No pasta/bread/rice + low-carb keywords
- **Healthy**: Contains salad, grilled, steamed, quinoa, kale

## Processing Large Recipe Collections

### Step 1: Prepare Your JSON File
Ensure your recipes are in a JSON array:
```json
[
  { "title": "Recipe 1", ... },
  { "title": "Recipe 2", ... },
  { "title": "Recipe 3", ... }
]
```

### Step 2: Run the Categorization Script
```bash
node categorize-scraped-recipes.js my-recipes.json
```

### Step 3: Review the Output
The script will:
1. Show categorization statistics
2. Save a processed file: `my-recipes-processed.json`
3. Upload to Algolia automatically

## Filtering Recipes in Your App

Once uploaded, users can find recipes by:

### By Cuisine
```javascript
// In CategoryRecipesScreen
navigation.navigate('CategoryRecipes', { 
  filterType: 'cuisine',
  filterValue: 'italian' 
});
```

### By Meal Type
```javascript
navigation.navigate('CategoryRecipes', { 
  filterType: 'mealType',
  filterValue: 'breakfast' 
});
```

### By Dietary Preference
```javascript
navigation.navigate('CategoryRecipes', { 
  filterType: 'dietary',
  filterValue: 'vegan' 
});
```

## Bulk Import Tips

### 1. Clean Your Data First
```javascript
// Example cleaning script
const cleanRecipes = recipes.map(recipe => ({
  ...recipe,
  ingredients: recipe.ingredients || [],
  instructions: recipe.instructions || recipe.directions || [],
  title: recipe.title || recipe.name || 'Untitled'
}));
```

### 2. Test with Small Batches
Start with 10-20 recipes to verify categorization accuracy.

### 3. Monitor Algolia Dashboard
Check your Algolia dashboard to see:
- Total recipes indexed
- Search performance
- Facet distribution

## Troubleshooting

### Issue: Recipes not categorizing correctly
**Solution**: Add more keywords to the detection arrays in the script

### Issue: Upload fails
**Solution**: Check your Algolia API key has write permissions

### Issue: Duplicate recipes
**Solution**: Ensure each recipe has a unique ID before uploading

## Example: Import from Popular Recipe Sites

### From AllRecipes Format
```json
{
  "name": "Chicken Parmesan",
  "description": "Classic Italian-American dish",
  "ingredients": {
    "amount": ["2", "1 cup", "2 cups"],
    "name": ["chicken breasts", "breadcrumbs", "marinara sauce"]
  }
}
```

Transform to:
```javascript
const transformed = {
  title: recipe.name,
  description: recipe.description,
  ingredients: recipe.ingredients.amount.map((amt, i) => 
    `${amt} ${recipe.ingredients.name[i]}`
  )
};
```

### From Food Network Format
```json
{
  "title": "Bobby's Burger",
  "categories": ["American", "Grilling"],
  "ingredients": ["ground beef", "buns", "cheese"],
  "directions": ["Form patties", "Grill 5 minutes per side"]
}
```

Already compatible! Just ensure it's in an array.

## Next Steps

1. **Prepare your JSON file** with scraped recipes
2. **Run the categorization script**
3. **Check your app** - recipes will appear in appropriate categories
4. **Customize categories** by editing the script if needed

## Support

If you have recipes in a different format, you can:
1. Transform them to match supported formats
2. Modify the `formatRecipeForAlgolia` function in the script
3. Share a sample for custom processing logic 