/**
 * Fixed Smart Search Service
 * 
 * Core search service that provides intelligent product search capabilities
 * Fixed to work with current database schema (no missing columns)
 */

import { supabase } from '../supabase/client';

export interface SmartSearchResult {
  id: string;
  name: string;
  brand: string;
  category: string;
  store: string;
  price: number;
  image_url?: string;
  relevance_score: number;
  match_type: 'exact' | 'alias' | 'fuzzy';
  shopping_aliases?: string[];
  tags?: string[];
  unit?: string;
  isCommonItem?: boolean;
}

export interface SearchOptions {
  maxResults?: number;
  storeFilter?: 'woolworths' | 'newworld' | 'paknsave';
  categoryFilter?: string;
  minPrice?: number;
  maxPrice?: number;
  includeImages?: boolean;
  preferredStore?: 'woolworths' | 'newworld' | 'paknsave';
  fuzzyTolerance?: number;
}

export interface SearchSuggestion {
  query: string;
  category: string;
  popularity: number;
  type: 'product' | 'brand' | 'alias' | 'category';
}

export interface PopularTerm {
  query: string;
  search_count: number;
  category?: string;
}

class SmartSearchService {
  private cache = new Map<string, { data: any; timestamp: number }>();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  /**
   * Perform intelligent product search
   */
  async searchProducts(query: string, options: SearchOptions = {}): Promise<SmartSearchResult[]> {
    const {
      maxResults = 100, // Increased from 20 to show more search results
      storeFilter,
      categoryFilter,
      minPrice,
      maxPrice
    } = options;

    if (!query.trim()) {
      return [];
    }

    const cacheKey = `search:${query}:${JSON.stringify(options)}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      let supabaseQuery = supabase
        .from('products')
        .select('id, name, brand, category, store, price, image_url, description')
        .not('price', 'is', null);

      // Add text search
      supabaseQuery = supabaseQuery.textSearch('name', query, { type: 'websearch' });

      // Add filters
      if (storeFilter) {
        supabaseQuery = supabaseQuery.eq('store', storeFilter);
      }
      if (categoryFilter) {
        supabaseQuery = supabaseQuery.eq('category', categoryFilter);
      }
      if (minPrice) {
        supabaseQuery = supabaseQuery.gte('price', minPrice);
      }
      if (maxPrice) {
        supabaseQuery = supabaseQuery.lte('price', maxPrice);
      }

      // Order and limit
      supabaseQuery = supabaseQuery
        .order('price', { ascending: true })
        .limit(maxResults);

      const { data, error } = await supabaseQuery;

      if (error) {
        console.error('Search error:', error);
        return [];
      }

      const results: SmartSearchResult[] = (data || []).map(item => ({
        id: item.id,
        name: item.name,
        brand: item.brand || this.extractBrand(item.name) || '',
        category: item.category || '',
        store: item.store,
        price: item.price,
        image_url: item.image_url,
        relevance_score: this.calculateRelevanceScore(item.name, query),
        match_type: 'exact' as const
      }));

      this.setCachedData(cacheKey, results);
      return results;

    } catch (error) {
      console.error('Search service error:', error);
      return [];
    }
  }

  /**
   * Get autocomplete suggestions
   */
  async getSuggestions(partialQuery: string, maxSuggestions: number = 10): Promise<SearchSuggestion[]> {
    if (!partialQuery.trim()) {
      return await this.getPopularSuggestions(maxSuggestions);
    }

    const cacheKey = `suggestions:${partialQuery}:${maxSuggestions}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const { data, error } = await supabase
        .from('products')
        .select('name, category')
        .ilike('name', `${partialQuery}%`)
        .not('price', 'is', null)
        .limit(maxSuggestions * 2); // Get more to deduplicate

      if (error) {
        console.error('Suggestions error:', error);
        return [];
      }

      // Deduplicate and format
      const uniqueNames = new Map<string, { name: string; category: string }>();
      (data || []).forEach(item => {
        if (!uniqueNames.has(item.name)) {
          uniqueNames.set(item.name, { name: item.name, category: item.category || '' });
        }
      });

      const suggestions: SearchSuggestion[] = Array.from(uniqueNames.values())
        .slice(0, maxSuggestions)
        .map(item => ({
          query: item.name,
          category: item.category,
          popularity: 0,
          type: 'product' as const
        }));

      this.setCachedData(cacheKey, suggestions);
      return suggestions;

    } catch (error) {
      console.error('Suggestions service error:', error);
      return [];
    }
  }

  /**
   * Get popular search terms
   */
  async getPopularTerms(maxTerms: number = 10): Promise<PopularTerm[]> {
    try {
      // Since we don't have search analytics yet, return common product names
      const { data, error } = await supabase
        .from('products')
        .select('name')
        .not('price', 'is', null)
        .order('name')
        .limit(maxTerms);

      if (error) {
        console.error('Popular terms error:', error);
        return [];
      }

      return (data || []).map(item => ({
        query: item.name,
        search_count: 1
      }));

    } catch (error) {
      console.error('Popular terms service error:', error);
      return [];
    }
  }

  /**
   * Get spelling corrections for a query
   */
  async getSpellingCorrections(query: string): Promise<string[]> {
    if (!query.trim()) {
      return [];
    }

    try {
      // Use trigram similarity to find similar product names
      const { data, error } = await supabase
        .from('products')
        .select('name')
        .textSearch('name', query, { type: 'websearch' })
        .limit(5);

      if (error) {
        console.error('Spelling corrections error:', error);
        return [];
      }

      // Extract unique corrected terms
      const corrections = new Set<string>();
      (data || []).forEach(item => {
        if (item.name && item.name.toLowerCase() !== query.toLowerCase()) {
          corrections.add(item.name);
        }
      });

      return Array.from(corrections).slice(0, 3);

    } catch (error) {
      console.error('Spelling corrections service error:', error);
      return [];
    }
  }

  /**
   * Record search analytics (simplified version)
   */
  async recordSearchAnalytics(
    query: string,
    resultsCount: number,
    selectedProductId?: string,
    searchType: 'text' | 'fuzzy' | 'alias' | 'category' = 'text',
    responseTimeMs?: number
  ): Promise<void> {
    // For now, just log analytics (table creation is optional)
    console.log('Search analytics:', {
      query,
      resultsCount,
      selectedProductId,
      searchType,
      responseTimeMs
    });
  }

  /**
   * Get popular products by category
   */
  async getPopularProductsByCategory(category: string, maxResults: number = 20): Promise<SmartSearchResult[]> {
    const cacheKey = `popular_category:${category}:${maxResults}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const { data, error } = await supabase
        .from('products')
        .select('id, name, brand, category, store, price, image_url')
        .eq('category', category)
        .not('price', 'is', null)
        .order('price', { ascending: true })
        .limit(maxResults);

      if (error) {
        console.error('Popular products error:', error);
        return [];
      }

      const results: SmartSearchResult[] = (data || []).map(item => ({
        id: item.id,
        name: item.name,
        brand: item.brand || this.extractBrand(item.name) || '',
        category: category,
        store: item.store,
        price: item.price,
        image_url: item.image_url,
        relevance_score: 0.8,
        match_type: 'exact' as const
      }));

      this.setCachedData(cacheKey, results);
      return results;

    } catch (error) {
      console.error('Popular products service error:', error);
      return [];
    }
  }

  /**
   * Clear search cache
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * Extract brand from product name
   */
  private extractBrand(productName: string): string | null {
    const words = productName.trim().split(' ');
    if (words.length < 2) return null;
    
    // Known NZ brands for instant recognition
    const knownBrands = [
      'Nescafe', 'Moccona', 'Greggs', 'Robert Harris', 'Avalanche',
      'Anchor', 'Mainland', 'Tararua', 'Lewis Road', 'Meadow Fresh',
      'Tip Top', 'Molenberg', 'Burgen', 'Vogels', 'Wonder White',
      'Cadbury', 'Whittakers', 'Nestle', 'Mars', 'Snickers',
      'Bluebird', 'ETA', 'Watties', 'Maggi', 'Continental',
      'Pams', 'Homebrand', 'Essentials', 'Budget', 'Value'
    ];
    
    const firstWord = words[0];
    const firstTwoWords = words.slice(0, 2).join(' ');
    
    // Check known brands first
    if (knownBrands.some(brand => brand.toLowerCase() === firstWord.toLowerCase())) {
      return firstWord;
    }
    if (knownBrands.some(brand => brand.toLowerCase() === firstTwoWords.toLowerCase())) {
      return firstTwoWords;
    }
    
    // Fallback to first word if it looks like a brand
    const commonWords = ['fresh', 'organic', 'premium', 'select', 'choice', 'best', 'super'];
    if (!commonWords.includes(firstWord.toLowerCase()) && /^[A-Z]/.test(firstWord)) {
      return firstWord;
    }
    
    return null;
  }

  /**
   * Calculate relevance score
   */
  private calculateRelevanceScore(name: string, query: string): number {
    const lowerName = name.toLowerCase();
    const lowerQuery = query.toLowerCase();
    
    if (lowerName === lowerQuery) return 1.0;
    if (lowerName.startsWith(lowerQuery)) return 0.9;
    if (lowerName.includes(lowerQuery)) return 0.8;
    return 0.5;
  }

  /**
   * Get cached data if still valid
   */
  private getCachedData(key: string): any | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.data;
    }
    this.cache.delete(key);
    return null;
  }

  /**
   * Set cached data
   */
  private setCachedData(key: string, data: any): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  /**
   * Get popular suggestions for empty queries
   */
  private async getPopularSuggestions(maxSuggestions: number): Promise<SearchSuggestion[]> {
    try {
      const { data, error } = await supabase
        .from('products')
        .select('name, category')
        .not('price', 'is', null)
        .order('name')
        .limit(maxSuggestions);

      if (error) {
        console.error('Popular suggestions error:', error);
        return [];
      }

      return (data || []).map(item => ({
        query: item.name,
        category: item.category || '',
        popularity: 0,
        type: 'product' as const
      }));

    } catch (error) {
      console.error('Popular suggestions service error:', error);
      return [];
    }
  }
}

export const smartSearchService = new SmartSearchService();
export { SmartSearchService };