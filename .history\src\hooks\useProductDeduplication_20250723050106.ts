
import { useState, useEffect, useMemo } from 'react';
import { IProduct, IUnifiedProduct } from '../types/shoppingList';
import { productDeduplicationService } from '../services/productDeduplicationService';
import { useOptimizedProducts } from './useOptimizedProducts';

interface IUseProductDeduplication {
  unifiedProducts: IUnifiedProduct[];
  loading: boolean;
  error: Error | null;
  totalProducts: number;
  storeCount: number;
  averageSavings: number;
  refresh: () => Promise<void>;
}

export const useProductDeduplication = (options: {
  pageSize?: number;
  searchQuery?: string;
  selectedCategory?: string;
  selectedStores?: string[];
  sortBy?: string;
  enableCache?: boolean;
} = {}): IUseProductDeduplication => {
  const {
    pageSize = 200,
    searchQuery = '',
    selectedCategory = 'All',
    selectedStores = ['woolworths', 'newworld', 'paknsave'],
    sortBy = 'relevance',
    enableCache = true,
  } = options;
  
  const { products, loadingState, refresh: refreshProducts } = useOptimizedProducts({
    pageSize,
    searchQuery,
    selectedCategory,
    selectedStores,
    sortBy,
    enableCache,
  });
  
  const [unifiedProducts, setUnifiedProducts] = useState<IUnifiedProduct[]>([]);
  const [processingError, setProcessingError] = useState<Error | null>(null);

  // Process products into unified format
  const processProducts = useMemo(() => {
    return (rawProducts: IProduct[]) => {
      if (!rawProducts || rawProducts.length === 0) {
        return [];
      }
      
      console.log(`🔄 Product deduplication: Processing ${rawProducts.length} raw products`);
      
      try {
        // Filter out products with invalid prices
        const validProducts = rawProducts.filter(product => 
          product.price && product.price > 0 && product.price < 1000
        );
        
        console.log(`📊 Valid products for deduplication: ${validProducts.length}/${rawProducts.length}`);
        
        // Use enhanced deduplication with strict size matching
        const deduplicationResult = productDeduplicationService.deduplicateProducts(validProducts, {
          requireSizeMatch: true,
          enableFuzzySizeMatching: true,
          nameSimilarityThreshold: 0.85,
          minimumConfidence: 0.7
        });

        const deduplicated = deduplicationResult.unifiedProducts;
        
        console.log(`✅ Product deduplication: Created ${deduplicated.length} unified products`);
        console.log(`📈 Average savings per product: $${deduplicated.reduce((sum, p) => sum + p.maxSavings, 0) / deduplicated.length || 0}`);
        
        setProcessingError(null);
        return deduplicated;
      } catch (error) {
        console.error('❌ Product deduplication failed:', error);
        setProcessingError(error instanceof Error ? error : new Error('Deduplication failed'));
        
        // Return empty array on error rather than fallback
        return [];
      }
    };
  }, []);

  useEffect(() => {
    if (products.length > 0) {
      const processed = processProducts(products);
      setUnifiedProducts(processed);
    } else if (!loadingState.initial) {
      console.log('⚠️ No products found to deduplicate');
      setUnifiedProducts([]);
    }
  }, [products, processProducts, loadingState.initial]);

  // Calculate statistics
  const stats = useMemo(() => {
    const totalProducts = products.length;
    const storeCount = new Set(products.map(p => p.store)).size;
    const averageSavings = unifiedProducts.length > 0 
      ? unifiedProducts.reduce((sum, p) => sum + p.maxSavings, 0) / unifiedProducts.length
      : 0;
      
    return { totalProducts, storeCount, averageSavings };
  }, [products, unifiedProducts]);

  const refresh = async () => {
    if (refreshProducts) {
      refreshProducts();
    }
  };

  return {
    unifiedProducts,
    loading: loadingState.initial || loadingState.loadingMore,
    error: processingError || (loadingState.error ? new Error(loadingState.error) : null),
    totalProducts: stats.totalProducts,
    storeCount: stats.storeCount,
    averageSavings: stats.averageSavings,
    refresh
  };
};
