/**
 * String Matching Utilities for Product Deduplication
 * Story 1.1: Product Deduplication Algorithm
 */

import { StringMatchResult, NormalizedSize } from '../types/deduplication';

/**
 * Calculate Levenshtein distance between two strings
 */
function levenshteinDistance(str1: string, str2: string): number {
  const matrix: number[][] = [];
  
  // Initialize matrix
  for (let i = 0; i <= str2.length; i++) {
    matrix[i] = [i];
  }
  for (let j = 0; j <= str1.length; j++) {
    matrix[0][j] = j;
  }
  
  // Calculate distances
  for (let i = 1; i <= str2.length; i++) {
    for (let j = 1; j <= str1.length; j++) {
      if (str2[i - 1] === str1[j - 1]) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1, // substitution
          matrix[i][j - 1] + 1,     // insertion
          matrix[i - 1][j] + 1      // deletion
        );
      }
    }
  }
  
  return matrix[str2.length][str1.length];
}

/**
 * Calculate Jaro-Winkler similarity between two strings
 * More accurate for product names than simple Levenshtein
 */
function jaroWinklerSimilarity(str1: string, str2: string): number {
  if (str1 === str2) return 1.0;
  
  const len1 = str1.length;
  const len2 = str2.length;
  
  if (len1 === 0 || len2 === 0) return 0.0;
  
  // Maximum allowed distance for matching characters
  const matchDistance = Math.floor(Math.max(len1, len2) / 2) - 1;
  if (matchDistance < 0) return 0.0;
  
  // Arrays to track matches
  const str1Matches = new Array(len1).fill(false);
  const str2Matches = new Array(len2).fill(false);
  
  let matches = 0;
  let transpositions = 0;
  
  // Find matches
  for (let i = 0; i < len1; i++) {
    const start = Math.max(0, i - matchDistance);
    const end = Math.min(i + matchDistance + 1, len2);
    
    for (let j = start; j < end; j++) {
      if (str2Matches[j] || str1[i] !== str2[j]) continue;
      str1Matches[i] = true;
      str2Matches[j] = true;
      matches++;
      break;
    }
  }
  
  if (matches === 0) return 0.0;
  
  // Count transpositions
  let k = 0;
  for (let i = 0; i < len1; i++) {
    if (!str1Matches[i]) continue;
    while (!str2Matches[k]) k++;
    if (str1[i] !== str2[k]) transpositions++;
    k++;
  }
  
  // Calculate Jaro similarity
  const jaro = (matches / len1 + matches / len2 + (matches - transpositions / 2) / matches) / 3;
  
  // Calculate Jaro-Winkler similarity
  if (jaro < 0.7) return jaro;
  
  // Common prefix length (up to 4 characters)
  let prefixLength = 0;
  for (let i = 0; i < Math.min(len1, len2, 4); i++) {
    if (str1[i] === str2[i]) prefixLength++;
    else break;
  }
  
  return jaro + (0.1 * prefixLength * (1 - jaro));
}

/**
 * Normalize string for comparison by removing special characters and extra whitespace
 * Enhanced based on industry best practices for product matching
 */
export function normalizeString(input: string): string {
  return input
    .toLowerCase()
    .replace(/[^\w\s]/g, ' ') // Replace special chars with spaces
    .replace(/\s+/g, ' ')     // Normalize whitespace
    .trim();
}

/**
 * Advanced normalization for product matching
 * Based on best practices from Matching_Products_Across_Supermarkets.txt
 */
export function advancedNormalizeString(input: string): string {
  return input
    .toLowerCase()
    // Remove common "fluff" words that don't help matching
    .replace(/\b(new|sale|special|offer|limited|fresh|premium|select|choice|value|pack)\b/gi, ' ')
    // Normalize units consistently
    .replace(/\b(\d+)\s*(l|litre|liter)s?\b/gi, '$1l')
    .replace(/\b(\d+)\s*(ml|millilitre|milliliter)s?\b/gi, '$1ml')
    .replace(/\b(\d+)\s*(g|gram)s?\b/gi, '$1g')
    .replace(/\b(\d+)\s*(kg|kilogram)s?\b/gi, '$1kg')
    // Remove all non-alphanumeric characters except spaces
    .replace(/[^a-z0-9\s]/g, '')
    // Normalize whitespace
    .replace(/\s+/g, ' ')
    .trim();
}

/**
 * Token-based Jaccard similarity as recommended in the document
 * Treats names as sets of words and computes |intersection|/|union|
 */
export function calculateJaccardSimilarity(str1: string, str2: string): number {
  const tokens1 = new Set(advancedNormalizeString(str1).split(' ').filter(token => token.length > 1));
  const tokens2 = new Set(advancedNormalizeString(str2).split(' ').filter(token => token.length > 1));

  const intersection = new Set([...tokens1].filter(token => tokens2.has(token)));
  const union = new Set([...tokens1, ...tokens2]);

  return union.size === 0 ? 0 : intersection.size / union.size;
}

/**
 * Calculate fuzzy string similarity using multiple algorithms
 * Enhanced with industry best practices from product matching research
 */
export function calculateFuzzySimilarity(str1: string, str2: string, threshold: number = 0.85): StringMatchResult {
  // Normalize inputs using advanced normalization
  const normalized1 = advancedNormalizeString(str1);
  const normalized2 = advancedNormalizeString(str2);

  // Exact match check first
  if (normalized1 === normalized2) {
    return {
      similarity: 1.0,
      isMatch: true,
      algorithm: 'exact'
    };
  }

  // Calculate multiple similarity metrics as recommended
  const jaroSimilarity = jaroWinklerSimilarity(normalized1, normalized2);

  // Levenshtein-based similarity
  const maxLength = Math.max(normalized1.length, normalized2.length);
  const levenshteinSim = maxLength > 0 ? 1 - (levenshteinDistance(normalized1, normalized2) / maxLength) : 0;

  // Token-based Jaccard similarity (recommended in document)
  const jaccardSimilarity = calculateJaccardSimilarity(str1, str2);

  // Use weighted combination of metrics for better accuracy
  const weightedSimilarity = (
    jaroSimilarity * 0.4 +      // Good for character-level similarity
    jaccardSimilarity * 0.4 +   // Good for token-level similarity
    levenshteinSim * 0.2        // Fallback for edit distance
  );

  // Use the best score from all methods
  const similarity = Math.max(jaroSimilarity, jaccardSimilarity, levenshteinSim, weightedSimilarity);

  // Determine which algorithm performed best
  let bestAlgorithm = 'fuzzy';
  if (jaccardSimilarity === similarity) bestAlgorithm = 'jaccard';
  else if (levenshteinSim === similarity) bestAlgorithm = 'levenshtein';
  else if (weightedSimilarity === similarity) bestAlgorithm = 'weighted';

  return {
    similarity: Math.round(similarity * 1000) / 1000, // Round to 3 decimal places
    isMatch: similarity >= threshold,
    algorithm: bestAlgorithm
  };
}

/**
 * Check if two brands are equivalent (exact match, case-insensitive)
 */
export function brandsMatch(brand1: string | undefined, brand2: string | undefined): boolean {
  if (!brand1 && !brand2) return true; // Both undefined
  if (!brand1 || !brand2) return false; // One undefined
  
  return normalizeString(brand1) === normalizeString(brand2);
}

/**
 * Enhanced size/quantity normalization patterns
 * Fixed to handle per-kg pricing and complex size formats
 */
const SIZE_PATTERNS = {
  // Per-unit pricing patterns (critical for Pak'nSave)
  perkg: /per\s*kg/i,
  per100g: /per\s*100\s*g/i,
  perlitre: /per\s*(?:l|litre|liter)/i,
  per100ml: /per\s*100\s*ml/i,

  // Multi-pack patterns (handle first to avoid confusion)
  multipack: /(\d+)\s*x\s*(\d+(?:\.\d+)?)\s*(ml|l|litre|liter|g|gram|kg|kilogram)/i,
  packcount: /(\d+)\s*pack/i,

  // Liquid volumes
  ml: /(\d+(?:\.\d+)?)\s*(?:ml|millilitre|milliliter)s?/i,
  litre: /(\d+(?:\.\d+)?)\s*(?:l|litre|liter)s?/i,

  // Weights
  gram: /(\d+(?:\.\d+)?)\s*g(?:ram)?s?/i,
  kilogram: /(\d+(?:\.\d+)?)\s*kg/i,

  // Counts
  count: /(\d+)\s*(?:count|ct|pieces?)/i,

  // Generic numeric
  numeric: /(\d+(?:\.\d+)?)/
};

/**
 * Enhanced size/quantity normalization for accurate comparison
 * Fixed to handle multi-pack and prevent incorrect grouping
 */
export function normalizeSize(sizeStr: string): NormalizedSize {
  if (!sizeStr) {
    return {
      original: '',
      value: 0,
      unit: '',
      valid: false
    };
  }

  const normalized = sizeStr.toLowerCase().trim();

  // Handle per-unit pricing first (critical for Pak'nSave)
  if (normalized.match(SIZE_PATTERNS.perkg)) {
    return {
      original: sizeStr,
      value: 1000, // 1kg = 1000g for comparison
      unit: 'per-kg',
      valid: true
    };
  }

  if (normalized.match(SIZE_PATTERNS.per100g)) {
    return {
      original: sizeStr,
      value: 100,
      unit: 'per-100g',
      valid: true
    };
  }

  if (normalized.match(SIZE_PATTERNS.perlitre)) {
    return {
      original: sizeStr,
      value: 1000, // 1L = 1000ml for comparison
      unit: 'per-litre',
      valid: true
    };
  }

  if (normalized.match(SIZE_PATTERNS.per100ml)) {
    return {
      original: sizeStr,
      value: 100,
      unit: 'per-100ml',
      valid: true
    };
  }

  // Handle multi-pack patterns (e.g., "6 x 330ml", "12pack")
  let match = normalized.match(SIZE_PATTERNS.multipack);
  if (match) {
    const packCount = parseInt(match[1]);
    const unitValue = parseFloat(match[2]);
    const unitType = match[3].toLowerCase();

    // Calculate total volume/weight for multi-packs
    let totalValue = packCount * unitValue;
    let baseUnit = unitType;

    // Normalize to base units
    if (unitType.match(/^(l|litre|liter)$/)) {
      totalValue = totalValue * 1000; // Convert to ml
      baseUnit = 'ml';
    } else if (unitType.match(/^(kg|kilogram)$/)) {
      totalValue = totalValue * 1000; // Convert to g
      baseUnit = 'g';
    } else if (unitType.match(/^(g|gram)$/)) {
      baseUnit = 'g';
    } else if (unitType.match(/^(ml|millilitre|milliliter)$/)) {
      baseUnit = 'ml';
    }

    return {
      original: sizeStr,
      value: totalValue,
      unit: baseUnit,
      valid: true
    };
  }

  // Handle pack count (e.g., "12 pack", "6pack")
  match = normalized.match(SIZE_PATTERNS.packcount);
  if (match) {
    return {
      original: sizeStr,
      value: parseInt(match[1]),
      unit: 'pack',
      valid: true
    };
  }

  // Try liquid volumes (convert to ml for consistency)
  match = normalized.match(SIZE_PATTERNS.litre);
  if (match) {
    return {
      original: sizeStr,
      value: parseFloat(match[1]) * 1000, // Convert to ml
      unit: 'ml',
      valid: true
    };
  }

  match = normalized.match(SIZE_PATTERNS.ml);
  if (match) {
    return {
      original: sizeStr,
      value: parseFloat(match[1]),
      unit: 'ml',
      valid: true
    };
  }

  // Try weights (convert to grams for consistency)
  match = normalized.match(SIZE_PATTERNS.kilogram);
  if (match) {
    return {
      original: sizeStr,
      value: parseFloat(match[1]) * 1000, // Convert to grams
      unit: 'g',
      valid: true
    };
  }

  match = normalized.match(SIZE_PATTERNS.gram);
  if (match) {
    return {
      original: sizeStr,
      value: parseFloat(match[1]),
      unit: 'g',
      valid: true
    };
  }

  // Try counts
  match = normalized.match(SIZE_PATTERNS.count);
  if (match) {
    return {
      original: sizeStr,
      value: parseInt(match[1]),
      unit: 'count',
      valid: true
    };
  }

  // Fallback to numeric extraction with unknown unit
  match = normalized.match(SIZE_PATTERNS.numeric);
  if (match) {
    return {
      original: sizeStr,
      value: parseFloat(match[1]),
      unit: 'unknown',
      valid: true
    };
  }

  return {
    original: sizeStr,
    value: 0,
    unit: '',
    valid: false
  };
}

/**
 * Enhanced size matching with strict equivalence checking
 * Prevents different sizes from being incorrectly grouped
 */
export function sizesMatch(size1: string | undefined, size2: string | undefined, fuzzyMatching = true): boolean {
  if (!size1 && !size2) return true; // Both undefined
  if (!size1 || !size2) return false; // One undefined

  if (!fuzzyMatching) {
    return normalizeString(size1) === normalizeString(size2);
  }

  const norm1 = normalizeSize(size1);
  const norm2 = normalizeSize(size2);

  // Both must be valid for comparison
  if (!norm1.valid || !norm2.valid) {
    // Fallback to string comparison for invalid normalizations
    return normalizeString(size1) === normalizeString(size2);
  }

  // Must have same unit type
  if (norm1.unit !== norm2.unit) {
    return false;
  }

  // CRITICAL: Per-unit pricing must match exactly (no cross-matching with fixed sizes)
  if (norm1.unit.startsWith('per-') || norm2.unit.startsWith('per-')) {
    return norm1.unit === norm2.unit && norm1.value === norm2.value;
  }

  // For pack/count units, must be exact match
  if (norm1.unit === 'pack' || norm1.unit === 'count') {
    return norm1.value === norm2.value;
  }

  // For weight/volume units, allow small floating point differences
  const tolerance = 0.001;
  const difference = Math.abs(norm1.value - norm2.value);

  return difference < tolerance;
}

/**
 * Get normalized size for product grouping key generation
 * Returns a consistent string representation for identical sizes
 */
export function getSizeGroupingKey(size: string | undefined): string {
  if (!size) return 'no-size';

  const normalized = normalizeSize(size);
  if (!normalized.valid) return `raw-${normalizeString(size)}`;

  // Create consistent grouping key
  return `${normalized.value}${normalized.unit}`;
}

/**
 * Utility function to get human-readable similarity explanation
 */
export function explainSimilarity(str1: string, str2: string): string {
  const result = calculateFuzzySimilarity(str1, str2);
  
  return `"${str1}" vs "${str2}": ${(result.similarity * 100).toFixed(1)}% similarity using ${result.algorithm} matching${result.isMatch ? ' (MATCH)' : ' (NO MATCH)'}`;
}