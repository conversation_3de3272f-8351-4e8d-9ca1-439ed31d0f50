# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Development
- `npm run start` - Start Expo development server
- `npm run android` - Run on Android device/emulator
- `npm run ios` - Run on iOS device/simulator 
- `npm run web` - Run on web browser
- `npm run ts:check` - Run TypeScript type checking

### Data Management
- `npm run supabase:migrate` - Run Supabase database migrations
- `npm run products:sync` - Sync product data to Supabase

### Testing & Quality
Run `npm run ts:check` before committing changes to ensure TypeScript compliance.

## Project Architecture

### Core Framework
This is a React Native Expo app (v51.0.39) with TypeScript, targeting iOS, Android, and web platforms. It uses React Navigation for routing and Supabase as the backend database.

### Key Architecture Patterns

#### Context-Based State Management
- **ShoppingContext** (`src/context/ShoppingContext.tsx`) - Central state for shopping lists and products with AsyncStorage persistence
- **AuthContext** (`src/context/AuthContext.tsx`) - Authentication state management
- **ThemeContext** (`src/context/ThemeContext.tsx`) - Theme and styling state

#### Service Layer Architecture
- **Price Comparison Service** (`src/services/priceComparisonService.ts`) - Queries Supabase products table for multi-store price comparisons across Woolworths, New World, and Pak'nSave
- **Enhanced Product Service** (`src/services/enhancedProductService.ts`) - Product data enrichment and management
- **Shopping List Services** - Multiple services for different shopping list implementations

#### Theme System
Centralized theme system in `src/styles/`:
- `theme.ts` - Core design tokens (colors, typography, spacing)
- `commonStyles.ts` - Reusable component styles
- `designSystem.ts` - Complete design system
- Always import from `src/styles` index file

### Database Integration

#### Supabase Setup
- Products stored in Supabase `products` table with fields: objectID, name, price, store, unit, category, brand, imageUrl, availability
- Store identifiers: 'woolworths', 'newworld', 'paknsave'
- Client configured in `src/supabase/client.ts`

#### Data Types
Key interfaces defined in `src/context/ShoppingContext.tsx`:
- `Product` - Core product model with price, store, and metadata
- `ShoppingList` - Shopping list container with items, totals, and metadata

### Performance Optimizations
- Optimized components: `OptimizedProductCard`, `OptimizedImage`
- Custom hooks: `useOptimizedProducts`, `useProductCache`
- Skeleton loading states for improved perceived performance

### Security Requirements
Follow security rules in `.cursor/rules/security-and-firebase.md`:
- Never commit API keys to git
- Use Firebase Cloud Functions for external API calls (Gemini, Algolia)
- No secrets in `EXPO_PUBLIC_` environment variables
- All external APIs must go through secure Firebase backend

### Navigation Structure
- Main tab navigation with screens for shopping lists, products, recipes
- Auth flow with login/register screens
- Modal-based detail views for products and price comparisons

### Code Organization Patterns
- Components in `src/components/` with clear naming conventions
- Services in `src/services/` for business logic
- Custom hooks in `src/hooks/` for reusable stateful logic
- Types in `src/types/` for TypeScript definitions
- Utils in `src/utils/` for helper functions

### Development Notes
- Uses React Native UI Lib for base components
- Implements Expo Router for navigation
- Firebase integration for cloud functions and real-time features
- Supabase for product data storage and queries
- TypeScript strict mode enabled

## Coding Standards & Conventions

### TypeScript Configuration
- Strict mode enabled (`strict: true` in tsconfig.json)
- ES2020 target with ESNext modules
- All code must pass `npm run ts:check` before commits

### Component Architecture

#### Component Naming
- PascalCase for component files and exports
- Descriptive names indicating functionality (e.g., `CleanShoppingListItem`, `ModernProductCard`)
- Prefix pattern-based naming for related components (e.g., `Clean*`, `Modern*`, `Optimized*`)

#### Component Structure
```typescript
// Standard component structure
interface ComponentProps {
  // Props definition with clear types
}

export const ComponentName: React.FC<ComponentProps> = ({
  // Destructured props
}) => {
  // Component logic
  
  return (
    // JSX with proper styling
  );
};
```

#### Props and Interface Design
- Use descriptive interface names ending with `Props`
- Optional props marked with `?`
- Default values provided in destructuring
- Complex objects defined as separate interfaces

### Styling Conventions

#### Theme Integration
- Always import theme from `src/styles` index
- Use theme tokens for colors, spacing, typography
- Leverage `useTheme()` hook for dynamic theming
- Follow design system patterns from `designSystem.ts`

#### StyleSheet Patterns
```typescript
const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.surface,
    padding: theme.spacing.base,
    borderRadius: theme.radius.lg,
  },
});
```

#### Responsive Design
- Use theme spacing scale (xs: 4, sm: 8, base: 16, etc.)
- Implement proper shadow elevation for cards
- Support both light and dark themes

### State Management

#### Context Usage
- Use `useShopping()` for shopping list state
- Use `useTheme()` for theme and styling
- Keep context lean and focused

#### Local State
- Prefer `useState` for component-specific state
- Use `useEffect` with proper dependency arrays
- Implement cleanup for subscriptions

### Service Layer Patterns

#### Service Structure
- Services in `src/services/` directory
- Clear separation of concerns
- Async/await for all promises
- Proper error handling with try/catch

#### API Integration
- Supabase client for database operations
- Type-safe queries with Database types
- Error boundaries for API failures

### File Organization

#### Import Conventions
```typescript
// 1. React and React Native imports
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

// 2. Third-party libraries
import { useNavigation } from '@react-navigation/native';

// 3. Internal imports (contexts, services, utils)
import { useTheme } from '../context/ThemeContext';
import { theme } from '../styles';

// 4. Type imports (separated)
import type { Product, ShoppingList } from '../types';
```

#### Directory Structure
- Group related files in logical directories
- Use index files for barrel exports
- Keep utilities separate from business logic

## UI/UX Design Principles

### Design System

#### Color Palette
- Primary: `#74B9FF` (gentle sky blue)
- Secondary: `#FDCB6E` (soft golden yellow)
- Accent: `#A29BFE` (soft lavender)
- Semantic colors for success, warning, error states

#### Typography Scale
- Font sizes: xs(12), sm(14), base(16), lg(18), xl(20), 2xl(24), etc.
- Font weights: normal(400), medium(500), semibold(600), bold(700)
- Platform-specific font families (SF Pro Display/Roboto)

#### Spacing System
- 8px grid system (xs: 4, sm: 8, md: 12, base: 16, lg: 20, xl: 24, etc.)
- Consistent padding and margins across components

#### Component Variants
- Button sizes: sm(36px), base(48px), lg(56px), xl(64px)
- Card styles with multiple elevation levels
- Input height standardized at 52px

### Interaction Patterns

#### Touch Targets
- Minimum 44px touch targets for accessibility
- Proper feedback states (pressed, disabled)
- Swipe gestures for delete actions

#### Navigation
- Tab-based main navigation
- Modal presentations for detailed views
- Consistent back button behavior

#### Loading States
- Skeleton loaders for perceived performance
- Progressive loading for large datasets
- Error boundaries with retry mechanisms

### Visual Hierarchy

#### Cards and Containers
- Elevated surfaces with subtle shadows
- Rounded corners (8px-24px radius scale)
- Clear content separation

#### Lists and Grids
- Consistent item spacing
- Visual indicators for completion states
- Price comparison highlighting

## Testing Standards

### Type Safety
- TypeScript strict mode enforced
- All props and state properly typed
- Database queries use generated types

### Code Quality
- No testing framework currently configured
- Type checking serves as primary quality gate
- Manual testing on multiple platforms required

### Performance Testing
- Monitor bundle size and load times
- Test on lower-end devices
- Optimize image loading and caching

## Security & Data Privacy

### API Security
- No direct external API calls from client
- All secrets handled through Firebase Cloud Functions
- Environment variables properly secured

### Data Handling
- Local storage for shopping lists only
- No sensitive user data in client storage
- Supabase handles user authentication

### Code Security
- No hardcoded secrets in source code
- Proper input validation on all forms
- Secure Supabase client configuration

## Firebase Integration

### Cloud Functions
- External API proxying (Gemini AI, Algolia)
- Secure environment variable handling
- Proper CORS configuration

### Authentication
- Firebase Auth for user management
- Secure token handling
- Logout and session management

## Development Workflow

### Code Standards
- Use descriptive variable and function names
- Comment complex business logic
- Follow established patterns for new features

### Git Practices
- Run `npm run ts:check` before commits
- Use descriptive commit messages
- Keep commits focused and atomic

### Platform Testing
- Test on iOS, Android, and web
- Verify responsive behavior
- Check theme switching functionality

## Performance Guidelines

### Component Optimization
- Use `React.memo()` for expensive components
- Implement proper key props for lists
- Lazy load heavy components

### Image Handling
- Optimize image sizes and formats
- Implement proper caching strategies
- Use placeholder loading states

### Bundle Optimization
- Tree-shake unused dependencies
- Optimize import statements
- Monitor bundle analyzer output

## Deployment Considerations

### Environment Setup
- Expo CLI for development builds
- EAS Build for production releases
- Proper environment variable configuration

### Platform-Specific Notes
- iOS requires proper provisioning profiles
- Android requires signed APK/AAB
- Web builds need proper static hosting

This document serves as the definitive guide for development practices in this AI Recipe Planner / Shopping List application. All team members and contributors should follow these standards to maintain code quality and consistency.