/**
 * TypeScript interfaces for Product Deduplication Service
 * Story 1.1: Product Deduplication Algorithm
 */

import { IProduct, IUnifiedProduct } from './shoppingList';

// Configuration interfaces for matching thresholds
export interface DeduplicationConfig {
  /** Minimum similarity score for product name matching (0.0 to 1.0) */
  nameSimilarityThreshold: number;
  
  /** Whether to require exact brand matching */
  requireExactBrandMatch: boolean;
  
  /** Whether to require exact category matching */
  requireExactCategoryMatch: boolean;
  
  /** Whether to require size/quantity matching */
  requireSizeMatch: boolean;
  
  /** Minimum confidence score to consider products as matches (0.0 to 1.0) */
  minimumConfidence: number;
  
  /** Whether to enable fuzzy size matching (e.g., 1L = 1 Litre) */
  enableFuzzySizeMatching: boolean;
}

// Default configuration values - Updated based on data analysis
export const DEFAULT_DEDUPLICATION_CONFIG: DeduplicationConfig = {
  nameSimilarityThreshold: 0.85, // 85% similarity as per story requirements
  requireExactBrandMatch: false, // Changed: Most products don't have brand field populated
  requireExactCategoryMatch: false, // Changed: Many products have null/empty categories
  requireSizeMatch: false, // Changed: Size matching is helpful but not required
  minimumConfidence: 0.7,
  enableFuzzySizeMatching: true,
};

// Matching result interfaces
export interface ProductMatch {
  /** The original product */
  product: IProduct;
  
  /** Matching confidence score (0.0 to 1.0) */
  confidence: number;
  
  /** Breakdown of matching criteria */
  matchBreakdown: {
    nameMatch: number;
    brandMatch: boolean;
    categoryMatch: boolean;
    sizeMatch: boolean;
  };
}

export interface ProductGroup {
  /** Unique identifier for the group */
  groupId: string;
  
  /** All products in this group */
  products: ProductMatch[];
  
  /** Overall confidence for the group */
  groupConfidence: number;
  
  /** Representative product (highest scoring) */
  representative: IProduct;
  
  /** Stores represented in this group */
  stores: string[];
}

// Deduplication results
export interface DeduplicationResult {
  /** Original input products */
  originalProducts: IProduct[];
  
  /** Grouped products with matching scores */
  groups: ProductGroup[];
  
  /** Unified products for display */
  unifiedProducts: IUnifiedProduct[];
  
  /** Processing statistics */
  stats: {
    originalCount: number;
    groupCount: number;
    unifiedCount: number;
    averageConfidence: number;
    processingTimeMs: number;
  };
  
  /** Configuration used for this deduplication */
  config: DeduplicationConfig;
}

// String matching types
export interface StringMatchResult {
  /** Similarity score (0.0 to 1.0) */
  similarity: number;
  
  /** Whether the strings match based on threshold */
  isMatch: boolean;
  
  /** Algorithm used for matching */
  algorithm: 'exact' | 'fuzzy' | 'normalized';
}

// Size normalization types
export interface NormalizedSize {
  /** Original size string */
  original: string;
  
  /** Normalized numeric value */
  value: number;
  
  /** Normalized unit */
  unit: string;
  
  /** Whether normalization was successful */
  valid: boolean;
}

// Error types
export class DeduplicationError extends Error {
  constructor(
    message: string,
    public code: 'CONFIG_ERROR' | 'MATCHING_ERROR' | 'PROCESSING_ERROR',
    public originalError?: Error
  ) {
    super(message);
    this.name = 'DeduplicationError';
  }
}

// Service interface for dependency injection and testing
export interface IProductDeduplicationService {
  /** Main deduplication function as required by story */
  deduplicateProducts(products: IProduct[], config?: Partial<DeduplicationConfig>): DeduplicationResult;
  
  /** Configure matching thresholds as required by story */
  configureThresholds(config: Partial<DeduplicationConfig>): void;
  
  /** Get matching confidence between two products as required by story */
  getMatchingConfidence(product1: IProduct, product2: IProduct): number;
  
  /** Get current configuration */
  getConfiguration(): DeduplicationConfig;
  
  /** Reset configuration to defaults */
  resetConfiguration(): void;
}