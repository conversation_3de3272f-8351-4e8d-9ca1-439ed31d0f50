import { useState, useEffect, useCallback } from 'react';
import { supabase } from '../supabase/client';

interface Recipe {
  id: string;
  title: string;
  description?: string;
  ingredients: string[];
  instructions: string;
  image_url?: string;
  prep_time?: number;
  cook_time?: number;
  servings?: number;
  difficulty?: string;
  cuisine?: string;
  tags?: string[];
  is_public?: boolean;
  user_id: string;
  created_at?: string;
  updated_at?: string;
}

interface UseRecipeDataProps {
  searchQuery?: string;
  category?: string;
  cuisine?: string;
  limit?: number;
}

interface UseRecipeDataReturn {
  recipes: Recipe[];
  isLoading: boolean;
  isRefreshing: boolean;
  error: string | null;
  refresh: () => void;
  loadMore: () => void;
  hasMore: boolean;
}

export const useRecipeData = ({
  searchQuery = '',
  category = 'All',
  cuisine = 'All',
  limit = 20,
}: UseRecipeDataProps): UseRecipeDataReturn => {
  const [recipes, setRecipes] = useState<Recipe[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [offset, setOffset] = useState(0);

  const loadRecipes = useCallback(async (
    isRefresh = false,
    loadMore = false
  ) => {
    try {
      if (isRefresh) {
        setIsRefreshing(true);
        setOffset(0);
      } else if (!loadMore) {
        setIsLoading(true);
      }

      setError(null);

      // Build query
      let query = supabase
        .from('recipes')
        .select('*')
        .eq('is_public', true) // Only get public recipes
        .order('created_at', { ascending: false });

      // Apply search filter
      if (searchQuery.trim()) {
        query = query.or(`title.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%`);
      }

      // Apply category filter
      if (category !== 'All') {
        query = query.contains('tags', [category.toLowerCase()]);
      }

      // Apply cuisine filter
      if (cuisine !== 'All') {
        query = query.eq('cuisine', cuisine);
      }

      // Apply pagination
      const currentOffset = isRefresh ? 0 : offset;
      query = query.range(currentOffset, currentOffset + limit - 1);

      const { data, error: queryError } = await query;

      if (queryError) {
        throw queryError;
      }

      // Process the data
      const processedRecipes = (data || []).map(recipe => ({
        ...recipe,
        ingredients: Array.isArray(recipe.ingredients) 
          ? recipe.ingredients 
          : typeof recipe.ingredients === 'string'
          ? JSON.parse(recipe.ingredients)
          : [],
        tags: Array.isArray(recipe.tags) 
          ? recipe.tags 
          : typeof recipe.tags === 'string'
          ? JSON.parse(recipe.tags)
          : [],
      }));

      if (isRefresh) {
        setRecipes(processedRecipes);
        setOffset(processedRecipes.length);
      } else if (loadMore) {
        setRecipes(prev => [...prev, ...processedRecipes]);
        setOffset(prev => prev + processedRecipes.length);
      } else {
        setRecipes(processedRecipes);
        setOffset(processedRecipes.length);
      }

      setHasMore(processedRecipes.length === limit);

    } catch (err: any) {
      console.error('Failed to load recipes:', err);
      setError(err.message || 'Failed to load recipes');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, [searchQuery, category, cuisine, limit, offset]);

  // Initial load and when filters change
  useEffect(() => {
    setOffset(0);
    loadRecipes(false, false);
  }, [searchQuery, category, cuisine]);

  // Refresh function
  const refresh = useCallback(() => {
    loadRecipes(true, false);
  }, [loadRecipes]);

  // Load more function
  const loadMoreRecipes = useCallback(() => {
    if (hasMore && !isLoading && !isRefreshing) {
      loadRecipes(false, true);
    }
  }, [hasMore, isLoading, isRefreshing, loadRecipes]);

  return {
    recipes,
    isLoading,
    isRefreshing,
    error,
    refresh,
    loadMore: loadMoreRecipes,
    hasMore,
  };
};

// Mock data for development/testing
export const getMockRecipes = (): Recipe[] => [
  {
    id: '1',
    title: 'Classic Spaghetti Carbonara',
    description: 'A traditional Italian pasta dish with eggs, cheese, and pancetta',
    ingredients: [
      '400g spaghetti',
      '200g pancetta, diced',
      '4 large eggs',
      '100g Parmesan cheese, grated',
      '2 cloves garlic, minced',
      'Black pepper to taste',
      'Salt for pasta water'
    ],
    instructions: '1. Cook spaghetti in salted boiling water until al dente. 2. While pasta cooks, fry pancetta until crispy. 3. Beat eggs with Parmesan and black pepper. 4. Drain pasta, reserving pasta water. 5. Quickly mix hot pasta with pancetta, then remove from heat and stir in egg mixture. 6. Add pasta water if needed to create creamy sauce. 7. Serve immediately.',
    image_url: 'https://example.com/carbonara.jpg',
    prep_time: 10,
    cook_time: 15,
    servings: 4,
    difficulty: 'Medium',
    cuisine: 'Italian',
    tags: ['dinner', 'pasta', 'italian'],
    is_public: true,
    user_id: 'system',
    created_at: new Date().toISOString(),
  },
  {
    id: '2',
    title: 'Green Smoothie Bowl',
    description: 'A healthy and refreshing breakfast bowl packed with nutrients',
    ingredients: [
      '1 frozen banana',
      '1 cup spinach',
      '1/2 avocado',
      '1 cup almond milk',
      '1 tbsp chia seeds',
      '1 tbsp honey',
      'Granola for topping',
      'Fresh berries for topping'
    ],
    instructions: '1. Blend banana, spinach, avocado, almond milk, and honey until smooth. 2. Pour into a bowl. 3. Top with chia seeds, granola, and fresh berries. 4. Serve immediately.',
    image_url: 'https://example.com/smoothie-bowl.jpg',
    prep_time: 5,
    cook_time: 0,
    servings: 1,
    difficulty: 'Easy',
    cuisine: 'Healthy',
    tags: ['breakfast', 'healthy', 'smoothie'],
    is_public: true,
    user_id: 'system',
    created_at: new Date().toISOString(),
  },
  {
    id: '3',
    title: 'Chocolate Chip Cookies',
    description: 'Classic homemade chocolate chip cookies that are crispy on the outside and chewy on the inside',
    ingredients: [
      '2 1/4 cups all-purpose flour',
      '1 tsp baking soda',
      '1 tsp salt',
      '1 cup butter, softened',
      '3/4 cup granulated sugar',
      '3/4 cup brown sugar',
      '2 large eggs',
      '2 tsp vanilla extract',
      '2 cups chocolate chips'
    ],
    instructions: '1. Preheat oven to 375°F. 2. Mix flour, baking soda, and salt in a bowl. 3. Cream butter and sugars until fluffy. 4. Beat in eggs and vanilla. 5. Gradually add flour mixture. 6. Stir in chocolate chips. 7. Drop spoonfuls onto baking sheets. 8. Bake 9-11 minutes until golden. 9. Cool on sheets for 2 minutes, then transfer to wire rack.',
    image_url: 'https://example.com/cookies.jpg',
    prep_time: 15,
    cook_time: 11,
    servings: 24,
    difficulty: 'Easy',
    cuisine: 'American',
    tags: ['dessert', 'cookies', 'baking'],
    is_public: true,
    user_id: 'system',
    created_at: new Date().toISOString(),
  },
];

// Development hook that uses mock data if no real data is available
export const useRecipeDataWithFallback = (props: UseRecipeDataProps): UseRecipeDataReturn => {
  const result = useRecipeData(props);
  const [useMockData, setUseMockData] = useState(false);

  useEffect(() => {
    // If we have an error or no recipes after loading, use mock data
    if (!result.isLoading && result.recipes.length === 0 && !result.error) {
      setUseMockData(true);
    }
  }, [result.isLoading, result.recipes.length, result.error]);

  if (useMockData) {
    return {
      recipes: getMockRecipes(),
      isLoading: false,
      isRefreshing: false,
      error: null,
      refresh: () => {},
      loadMore: () => {},
      hasMore: false,
    };
  }

  return result;
};