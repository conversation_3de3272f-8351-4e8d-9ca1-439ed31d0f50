/**
 * Price Comparison Service - Supabase Products Database
 * 
 * This service uses Supabase to fetch products from all stores for price comparison.
 * Products are stored in the Supabase 'products' table with store information.
 * 
 * Expected product structure in Supabase:
 * {
 *   objectID: "unique-product-id",
 *   name: "Product Name",
 *   price: 12.99,
 *   store: "woolworths" | "newworld" | "paknsave",
 *   unit: "each" | "kg" | "L" | etc,
 *   category: "Category Name",
 *   brand: "Brand Name",
 *   description: "Product description",
 *   imageUrl: "https://...",
 *   availability: "in_stock" | "low_stock" | "out_of_stock",
 *   promotions: ["Special offer"],
 *   nutritionInfo: { calories: 100, protein: 5, ... }
 * }
 * 
 * The service searches the Supabase database with store filters to get products per store.
 */

import { supabase } from '../supabase/client';

export interface SupermarketProduct {
  objectID: string;
  id?: string;
  name: string;
  price: number;
  specialPrice?: number;
  isSpecial?: boolean;
  unit?: string;
  description?: string;
  category?: string;
  brand?: string;
  imageUrl?: string;
  availability?: 'in_stock' | 'low_stock' | 'out_of_stock';
  promotions?: string[];
  nutritionInfo?: {
    calories?: number;
    protein?: number;
    carbs?: number;
    fat?: number;
  };
  isCommonItem?: boolean;
}

export interface StoreSearchResult {
  store: 'woolworths' | 'newworld' | 'paknsave';
  storeName: string;
  product: SupermarketProduct;
  searchQuery: string;
  relevance_score?: number;
}

export interface ShoppingListOptimization {
  totalCost: number;
  itemCount: number;
  storeBreakdown: {
    store: 'woolworths' | 'newworld' | 'paknsave';
    storeName: string;
    items: Array<{
      query: string;
      product: SupermarketProduct;
      savings?: number;
    }>;
    subtotal: number;
    itemCount: number;
  }[];
  unavailableItems: string[];
  totalSavings: number;
  recommendedStrategy: 'single_store' | 'multi_store' | 'split_shopping';
}

export interface PriceComparisonResult {
  query: string;
  results: StoreSearchResult[];
  cheapestOption?: StoreSearchResult;
  priceRange?: {
    min: number;
    max: number;
    savings: number;
  };
  averagePrice?: number;
}

class PriceComparisonError extends Error {
  constructor(message: string, public statusCode?: number) {
    super(message);
    this.name = 'PriceComparisonError';
  }
}

const getSupabaseClient = () => {
  return supabase;
};

// Helper function to extract price from product data based on store
const extractPrice = (hit: any, storeKey: string): number => {
  let price: number;
  
  if (storeKey === 'woolworths') {
    // Woolworths uses currentPrice or unitPrice
    price = hit.currentPrice || hit.unitPrice || hit.price;
  } else {
    // New World and PakNSave use price field
    price = hit.price;
  }
  
  const parsedPrice = typeof price === 'number' ? price : parseFloat(price);
  
  console.log(`💰 ${storeKey} price extraction: ${hit.name} - raw: ${price} (${typeof price}) -> parsed: ${parsedPrice}`);
  
  return parsedPrice;
};

// Helper function to extract special/loyalty prices
const extractSpecialPrice = (hit: any, storeKey: string): { specialPrice?: number; isSpecial: boolean } => {
  let specialPrice: number | undefined;
  let isSpecial = false;
  
  if (storeKey === 'woolworths') {
    // Check for Woolworths Rewards/loyalty pricing
    specialPrice = hit.rewardsPrice || hit.memberPrice || hit.loyaltyPrice || hit.specialPrice;
    isSpecial = !!(specialPrice && specialPrice < (hit.currentPrice || hit.unitPrice || hit.price));
  } else if (storeKey === 'paknsave') {
    // Check for Pak'nSave club card pricing
    specialPrice = hit.clubPrice || hit.memberPrice || hit.specialPrice;
    isSpecial = !!(specialPrice && specialPrice < hit.price);
  } else if (storeKey === 'newworld') {
    // Check for New World club card pricing
    specialPrice = hit.clubPrice || hit.memberPrice || hit.specialPrice;
    isSpecial = !!(specialPrice && specialPrice < hit.price);
  }
  
  if (isSpecial && specialPrice) {
    console.log(`🏷️ ${storeKey} special price: ${hit.name} - regular: $${hit.price} -> special: $${specialPrice}`);
  }
  
  return { specialPrice, isSpecial };
};

// Helper function to extract image URL based on store
const extractImageUrl = (hit: any, storeKey: string): string => {
  let imageUrl = '';
  
  if (storeKey === 'woolworths') {
    // Woolworths image URLs - try multiple formats
    imageUrl = hit.image_url || hit.imageUrl || hit.image || hit.mediumImageFile || hit.largeImageFile;
    
    // Fix Woolworths CDN URLs if they're relative
    if (imageUrl && !imageUrl.startsWith('http')) {
      if (imageUrl.startsWith('/')) {
        imageUrl = `https://cdn0.woolworths.com.au${imageUrl}`;
      } else {
        imageUrl = `https://cdn0.woolworths.com.au/wowproductimages/medium/${imageUrl}`;
      }
    }
    
    // Replace any broken CDN references
    if (imageUrl && imageUrl.includes('undefined')) {
      imageUrl = '';
    }
  } else {
    // New World and Pak'nSave use standard image_url field
    imageUrl = hit.image_url || hit.imageUrl || hit.image || '';
  }
  
  console.log(`🖼️ ${storeKey} image extraction: ${hit.name} - ${imageUrl || 'No image'}`);
  
  return imageUrl;
};

const STORE_CONFIG = {
  woolworths: {
    indexName: 'products',
    storeFilter: 'woolworths',
    displayName: 'Woolworths',
    color: '#00A651',
    logo: '🍎'
  },
  newworld: {
    indexName: 'products', 
    storeFilter: 'newworld',
    displayName: 'New World',
    color: '#E31E24',
    logo: '🛒'
  },
  paknsave: {
    indexName: 'products',
    storeFilter: 'paknsave',
    displayName: "Pak'nSave",
    color: '#FFD100',
    logo: '💰'
  }
} as const;

export const priceComparisonService = {
  /**
   * Search for a product across all supermarkets using Supabase
   */
  searchProductAcrossStores: async (
    query: string,
    options: {
      maxResults?: number;
      category?: string;
      sortBy?: 'price' | 'relevance';
      includeOutOfStock?: boolean;
      brand?: string; // Filter by specific brand
      store?: string; // Filter by specific store
    } = {}
  ): Promise<PriceComparisonResult> => {
    try {
      console.log(`🔍 Fetching products from Supabase with options:`, {
        searchQuery: query,
        maxResults: options.maxResults || 5,
        category: options.category,
        brand: options.brand,
        store: options.store,
        database: 'Supabase products table'
      });
      
      const client = getSupabaseClient();
      const maxResults = options.maxResults || 5;
      const allResults: StoreSearchResult[] = [];

      // Build Supabase query
      let supabaseQuery = client
        .from('products')
        .select('*');

      // Add search conditions
      if (query.trim()) {
        supabaseQuery = supabaseQuery.ilike('name', `%${query}%`);
      }

      // Add filters
      if (options.store) {
        supabaseQuery = supabaseQuery.eq('store', options.store);
      }

      if (options.category) {
        supabaseQuery = supabaseQuery.eq('category', options.category);
      }

      if (options.brand) {
        supabaseQuery = supabaseQuery.eq('brand', options.brand);
      }

      if (!options.includeOutOfStock) {
        supabaseQuery = supabaseQuery.eq('availability', 'in_stock');
      }

      // Add sorting
      if (options.sortBy === 'price') {
        supabaseQuery = supabaseQuery.order('price', { ascending: true });
      } else {
        supabaseQuery = supabaseQuery.order('name', { ascending: true });
      }

      // Limit results
      supabaseQuery = supabaseQuery.limit(maxResults * 3); // Get more to account for filtering

      const { data: products, error } = await supabaseQuery;

      if (error) {
        console.error('Supabase query error:', error);
        return {
          query,
          results: [],
          cheapestOption: undefined,
          priceRange: undefined,
          averagePrice: 0
        };
      }

      if (!products || products.length === 0) {
        console.log(`❌ No products found for query: "${query}"`);
        return {
          query,
          results: [],
          cheapestOption: undefined,
          priceRange: undefined,
          averagePrice: 0
        };
      }

      console.log(`📊 Found ${products.length} products from Supabase`);

      // Convert to StoreSearchResult format
      for (const product of products) {
        if (allResults.length >= maxResults) break;

        try {
          const price = extractPrice(product, product.store);
          if (!isNaN(price) && price > 0 && price < 10000) {
            const storeConfig = STORE_CONFIG[product.store as keyof typeof STORE_CONFIG];
            
            const specialPriceInfo = extractSpecialPrice(product, product.store);
            const imageUrl = extractImageUrl(product, product.store);
            
            allResults.push({
              store: product.store as 'woolworths' | 'newworld' | 'paknsave',
              storeName: storeConfig ? storeConfig.displayName : product.store,
              product: {
                objectID: product.objectid || product.id || `${product.store}-${product.name}`,
                name: product.name || 'Unknown Product',
                price: price,
                specialPrice: specialPriceInfo.specialPrice,
                isSpecial: specialPriceInfo.isSpecial,
                unit: product.unit || 'each',
                description: product.description || '',
                category: product.category || '',
                brand: product.brand || '',
                imageUrl: imageUrl,
                availability: product.availability || 'in_stock',
                promotions: Array.isArray(product.promotions) ? product.promotions : [],
                nutritionInfo: product.nutritionInfo || {}
              },
              searchQuery: query,
              relevance_score: 1.0
            });
          }
        } catch (productError) {
          console.warn(`Failed to process product: ${product.name}`, productError);
        }
      }

      console.log(`✅ Product fetch complete from Supabase:`, {
        totalProducts: allResults.length,
        database: 'Supabase products table'
      });

      // Sort results
      if (options.sortBy === 'price') {
        allResults.sort((a, b) => a.product.price - b.product.price);
      }

      // Calculate price statistics
      const prices = allResults.map(r => r.product.price).filter(p => p > 0);
      const minPrice = prices.length > 0 ? Math.min(...prices) : 0;
      const maxPrice = prices.length > 0 ? Math.max(...prices) : 0;
      const avgPrice = prices.length > 0 ? prices.reduce((a, b) => a + b, 0) / prices.length : 0;

      const cheapestOption = allResults.find(r => r.product.price === minPrice);

      return {
        query,
        results: allResults,
        cheapestOption,
        priceRange: prices.length > 0 ? {
          min: minPrice,
          max: maxPrice,
          savings: maxPrice - minPrice
        } : undefined,
        averagePrice: avgPrice
      };

    } catch (error) {
      console.error('Product search failed:', error);
      throw new PriceComparisonError(`Failed to search for "${query}": ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  /**
   * Optimize an entire shopping list across stores
   */
  optimizeShoppingList: async (
    shoppingItems: string[],
    strategy: 'cheapest_total' | 'single_store_convenience' | 'balanced' = 'cheapest_total'
  ): Promise<ShoppingListOptimization> => {
    try {
      console.log(`Optimizing shopping list with ${shoppingItems.length} items using ${strategy} strategy`);

      // Search for each item across all stores
      const itemSearchPromises = shoppingItems.map(async (item) => {
        const comparison = await priceComparisonService.searchProductAcrossStores(item, {
          maxResults: 3,
          sortBy: 'price',
          includeOutOfStock: false
        });
        return { item, comparison };
      });

      const itemComparisons = await Promise.all(itemSearchPromises);
      const unavailableItems: string[] = [];
      const availableComparisons = itemComparisons.filter(({ item, comparison }) => {
        if (comparison.results.length === 0) {
          unavailableItems.push(item);
          return false;
        }
        return true;
      });

      if (strategy === 'cheapest_total') {
        return optimizeForCheapestTotal(availableComparisons, unavailableItems);
      } else if (strategy === 'single_store_convenience') {
        return optimizeForSingleStore(availableComparisons, unavailableItems);
      } else {
        return optimizeBalanced(availableComparisons, unavailableItems);
      }

    } catch (error) {
      console.error('Shopping list optimization failed:', error);
      throw new PriceComparisonError('Failed to optimize shopping list');
    }
  },

  /**
   * Get store information
   */
  getStoreInfo: () => STORE_CONFIG,

  /**
   * Check if service is configured
   */
  isConfigured: (): boolean => {
    return !!supabase;
  },

  /**
   * Explore the actual structure of the products table in Supabase
   * This helps understand what fields and data are actually available
   */
  exploreSupabaseStructure: async (): Promise<void> => {
    try {
      const client = getSupabaseClient();
      
      console.log('🔍 Exploring Supabase products table structure...');
      
      // Get some sample products to understand the structure
      const { data: products, error } = await client
        .from('products')
        .select('*')
        .limit(10);

      if (error) {
        console.error('❌ Failed to fetch products from Supabase:', error);
        return;
      }

      if (products && products.length > 0) {
        console.log('📊 Supabase Table Statistics:', {
          sampleSize: products.length,
          totalAvailable: 'Use count(*) query for exact total'
        });
        
        // Show structure of first few products
        console.log('📝 Sample Product Structures:');
        products.slice(0, 3).forEach((product: any, index: number) => {
          console.log(`Product ${index + 1}:`, {
            objectID: product.objectid,
            availableFields: Object.keys(product),
            sampleData: {
              name: product.name,
              category: product.category,
              store: product.store,
              price: product.price,
              brand: product.brand,
              description: product.description
            }
          });
        });

        // Test searches for common categories
        const categoriesToTest = ['chips', 'frozen-vegetables', 'rice', 'pasta-pizza-sauce', 'crackers'];
        
        console.log('🧪 Testing category searches...');
        for (const category of categoriesToTest) {
          const { data: categoryResults } = await client
            .from('products')
            .select('name, category, store, price, brand')
            .ilike('name', `%${category}%`)
            .limit(5);
          
          console.log(`🔍 Search for "${category}": Found ${categoryResults?.length || 0} products`);
          if (categoryResults && categoryResults.length > 0) {
            console.log(`   Sample results:`, categoryResults.slice(0, 2));
          }
        }
      } else {
        console.warn('⚠️ No products found in Supabase table');
      }
    } catch (error) {
      console.error('❌ Failed to explore Supabase structure:', error);
    }
  },

  /**
   * Test the Supabase products table setup
   * This function helps verify that the products table contains data for all stores
   */
  testSupabaseProducts: async (): Promise<{
    tableExists: boolean;
    storeBreakdown: Record<string, number>;
    totalProducts: number;
    sampleProducts: any[];
  }> => {
    try {
      const client = getSupabaseClient();
      
      // First explore the structure
      await priceComparisonService.exploreSupabaseStructure();
      
      // Get product count by store
      const { data: products, error } = await client
        .from('products')
        .select('name, store, price, brand, category')
        .limit(100);

      if (error) {
        console.error('❌ Failed to test Supabase products:', error);
        return {
          tableExists: false,
          storeBreakdown: {},
          totalProducts: 0,
          sampleProducts: []
        };
      }

      const storeBreakdown: Record<string, number> = {};
      if (products) {
        products.forEach((product: any) => {
          const store = product.store || 'unknown';
          storeBreakdown[store] = (storeBreakdown[store] || 0) + 1;
        });
      }

      console.log('🧪 Supabase Products Test Results:', {
        totalProducts: products?.length || 0,
        storeBreakdown
      });

      return {
        tableExists: true,
        storeBreakdown,
        totalProducts: products?.length || 0,
        sampleProducts: products?.slice(0, 5) || []
      };
    } catch (error) {
      console.error('❌ Supabase products test failed:', error);
      return {
        tableExists: false,
        storeBreakdown: {},
        totalProducts: 0,
        sampleProducts: []
      };
    }
  }
};

// Optimization strategies
function optimizeForCheapestTotal(
  comparisons: Array<{ item: string; comparison: PriceComparisonResult }>,
  unavailableItems: string[]
): ShoppingListOptimization {
  const storeBreakdown: { [key: string]: any } = {};
  let totalCost = 0;
  let totalSavings = 0;

  // For each item, pick the cheapest option
  comparisons.forEach(({ item, comparison }) => {
    const cheapest = comparison.cheapestOption;
    if (cheapest) {
      const storeKey = cheapest.store;
      if (!storeBreakdown[storeKey]) {
        storeBreakdown[storeKey] = {
          store: storeKey,
          storeName: cheapest.storeName,
          items: [],
          subtotal: 0,
          itemCount: 0
        };
      }

      const avgPrice = comparison.averagePrice || cheapest.product.price;
      const savings = avgPrice - cheapest.product.price;

      storeBreakdown[storeKey].items.push({
        query: item,
        product: cheapest.product,
        savings: savings > 0 ? savings : undefined
      });
      storeBreakdown[storeKey].subtotal += cheapest.product.price;
      storeBreakdown[storeKey].itemCount += 1;
      totalCost += cheapest.product.price;
      totalSavings += savings;
    }
  });

  const stores = Object.values(storeBreakdown);
  const recommendedStrategy = stores.length === 1 ? 'single_store' : 'multi_store';

  return {
    totalCost,
    itemCount: comparisons.length,
    storeBreakdown: stores,
    unavailableItems,
    totalSavings,
    recommendedStrategy
  };
}

function optimizeForSingleStore(
  comparisons: Array<{ item: string; comparison: PriceComparisonResult }>,
  unavailableItems: string[]
): ShoppingListOptimization {
  const storeTotals: { [key: string]: { total: number; items: number; results: any[] } } = {};

  // Calculate total cost per store
  Object.keys(STORE_CONFIG).forEach(storeKey => {
    storeTotals[storeKey] = { total: 0, items: 0, results: [] };
  });

  comparisons.forEach(({ item, comparison }) => {
    Object.keys(STORE_CONFIG).forEach(storeKey => {
      const storeResult = comparison.results.find(r => r.store === storeKey);
      if (storeResult) {
        storeTotals[storeKey].total += storeResult.product.price;
        storeTotals[storeKey].items += 1;
        storeTotals[storeKey].results.push({ item, result: storeResult });
      }
    });
  });

  // Find the store with the best combination of price and availability
  let bestStore = '';
  let bestScore = Infinity;

  Object.entries(storeTotals).forEach(([storeKey, data]) => {
    if (data.items > 0) {
      // Score based on total cost and item availability
      const availabilityRatio = data.items / comparisons.length;
      const score = data.total + (1 - availabilityRatio) * 1000; // Penalty for missing items
      
      if (score < bestScore) {
        bestScore = score;
        bestStore = storeKey;
      }
    }
  });

  const storeConfig = STORE_CONFIG[bestStore as keyof typeof STORE_CONFIG];
  const storeData = storeTotals[bestStore];

  return {
    totalCost: storeData.total,
    itemCount: storeData.items,
    storeBreakdown: [{
      store: bestStore as any,
      storeName: storeConfig?.displayName || bestStore,
      items: storeData.results.map(({ item, result }) => ({
        query: item,
        product: result.product
      })),
      subtotal: storeData.total,
      itemCount: storeData.items
    }],
    unavailableItems: [
      ...unavailableItems,
      ...comparisons
        .filter(({ comparison }) => !comparison.results.find(r => r.store === bestStore))
        .map(({ item }) => item)
    ],
    totalSavings: 0,
    recommendedStrategy: 'single_store'
  };
}

function optimizeBalanced(
  comparisons: Array<{ item: string; comparison: PriceComparisonResult }>,
  unavailableItems: string[]
): ShoppingListOptimization {
  // Balance between price and convenience
  // If most items are cheapest at one store, use that store
  // Otherwise, use cheapest total strategy
  
  const storeScores: { [key: string]: number } = {};
  Object.keys(STORE_CONFIG).forEach(key => storeScores[key] = 0);

  comparisons.forEach(({ comparison }) => {
    if (comparison.cheapestOption) {
      storeScores[comparison.cheapestOption.store] += 1;
    }
  });

  const dominantStore = Object.entries(storeScores).reduce((a, b) => 
    storeScores[a[0]] > storeScores[b[0]] ? a : b
  );

  // If one store has majority of cheapest items, optimize for single store
  if (dominantStore[1] >= comparisons.length * 0.6) {
    return optimizeForSingleStore(comparisons, unavailableItems);
  } else {
    return optimizeForCheapestTotal(comparisons, unavailableItems);
  }
}