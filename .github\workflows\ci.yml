name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18.x'

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Use Node.js ${{ env.NODE_VERSION }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm test
      env:
        EXPO_PUBLIC_GEMINI_API_KEY: ${{ secrets.EXPO_PUBLIC_GEMINI_API_KEY }}
        EXPO_PUBLIC_ALGOLIA_APP_ID: ${{ secrets.EXPO_PUBLIC_ALGOLIA_APP_ID }}
        EXPO_PUBLIC_ALGOLIA_API_KEY: ${{ secrets.EXPO_PUBLIC_ALGOLIA_API_KEY }}
        EXPO_PUBLIC_ALGOLIA_INDEX_NAME: ${{ secrets.EXPO_PUBLIC_ALGOLIA_INDEX_NAME }}
        ALGOLIA_ADMIN_KEY: ${{ secrets.ALGOLIA_ADMIN_KEY }}
        SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
        SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}

  build:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Use Node.js ${{ env.NODE_VERSION }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Setup Expo
      uses: expo/expo-github-action@v8
      with:
        expo-version: latest
        token: ${{ secrets.EXPO_TOKEN }}
    
    - name: Build for web
      run: npx expo export --platform web
      env:
        EXPO_PUBLIC_GEMINI_API_KEY: ${{ secrets.EXPO_PUBLIC_GEMINI_API_KEY }}
        EXPO_PUBLIC_ALGOLIA_APP_ID: ${{ secrets.EXPO_PUBLIC_ALGOLIA_APP_ID }}
        EXPO_PUBLIC_ALGOLIA_API_KEY: ${{ secrets.EXPO_PUBLIC_ALGOLIA_API_KEY }}
        EXPO_PUBLIC_ALGOLIA_INDEX_NAME: ${{ secrets.EXPO_PUBLIC_ALGOLIA_INDEX_NAME }}
        ALGOLIA_ADMIN_KEY: ${{ secrets.ALGOLIA_ADMIN_KEY }}
        SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
        SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}

  deploy:
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Use Node.js ${{ env.NODE_VERSION }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Setup Expo
      uses: expo/expo-github-action@v8
      with:
        expo-version: latest
        token: ${{ secrets.EXPO_TOKEN }}
    
    - name: Build and publish
      run: |
        npx expo export --platform web
        # Add your deployment steps here (e.g., deploy to Netlify, Vercel, etc.)
      env:
        EXPO_PUBLIC_GEMINI_API_KEY: ${{ secrets.EXPO_PUBLIC_GEMINI_API_KEY }}
        EXPO_PUBLIC_ALGOLIA_APP_ID: ${{ secrets.EXPO_PUBLIC_ALGOLIA_APP_ID }}
        EXPO_PUBLIC_ALGOLIA_API_KEY: ${{ secrets.EXPO_PUBLIC_ALGOLIA_API_KEY }}
        EXPO_PUBLIC_ALGOLIA_INDEX_NAME: ${{ secrets.EXPO_PUBLIC_ALGOLIA_INDEX_NAME }}
        ALGOLIA_ADMIN_KEY: ${{ secrets.ALGOLIA_ADMIN_KEY }}
        SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
        SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
