/**
 * Enhanced Minimalist Theme
 * 
 * Beautiful, subtle colors that maintain minimalism while adding
 * visual warmth and sophistication to the user interface.
 */

import { Platform } from 'react-native';

// Enhanced minimalist color palette with subtle warmth
const COLORS = {
  // Primary colors - Warm, sophisticated tones
  primary: '#2D3436',           // Rich charcoal with subtle warmth
  primaryLight: '#636E72',      // Soft gray-blue
  primaryAccent: '#74B9FF',     // Gentle sky blue for highlights
  
  // Secondary colors - Warm neutrals
  secondary: '#FDCB6E',         // Soft golden yellow for accents
  secondaryLight: '#FFF5E6',    // Very light warm cream
  secondaryMuted: '#E17055',    // Muted coral for subtle warmth
  
  // Tertiary colors - Cool accents
  tertiary: '#A29BFE',          // Soft lavender
  tertiaryLight: '#F3F2FF',     // Very light purple tint
  tertiaryMuted: '#81ECEC',     // Soft mint green
  
  // Neutral base colors with warmth
  white: '#FEFEFE',             // Slightly warm white
  cream: '#FDFBF7',             // Warm cream background
  lightGray: '#F8F9FA',         // Cool light gray
  mediumGray: '#E9ECEF',        // Medium gray with subtle warmth
  darkGray: '#495057',          // Rich dark gray
  
  // Semantic colors - Muted and sophisticated
  success: '#00B894',           // Muted teal green
  successLight: '#E8F8F5',      // Very light success background
  warning: '#FDCB6E',           // Warm golden yellow
  warningLight: '#FFF9E6',      // Very light warning background
  error: '#E17055',             // Muted coral red
  errorLight: '#FDF2EF',        // Very light error background
  info: '#74B9FF',              // Soft sky blue
  infoLight: '#EBF4FF',         // Very light info background
  
  // Enhanced light theme
  light: {
    // Backgrounds with subtle warmth
    background: '#FEFEFE',              // Slightly warm white
    backgroundSecondary: '#FDFBF7',     // Warm cream
    backgroundTertiary: '#F8F9FA',      // Cool light gray
    surface: '#FFFFFF',                 // Pure white for cards
    surfaceSecondary: '#FDFBF7',        // Warm cream for secondary cards
    surfaceElevated: '#FFFFFF',         // White with enhanced shadow
    
    // Text colors with improved contrast
    text: '#2D3436',                    // Rich charcoal
    textSecondary: '#636E72',           // Soft gray-blue
    textTertiary: '#74B9FF',            // Gentle blue for links
    textMuted: '#B2BEC3',               // Light gray for muted text
    textInverse: '#FFFFFF',             // White text
    textAccent: '#FDCB6E',              // Golden accent text
    
    // Interactive colors
    primary: '#74B9FF',                 // Gentle sky blue
    primaryHover: '#0984E3',            // Deeper blue for hover
    primaryPressed: '#0984E3',          // Pressed state
    secondary: '#FDCB6E',               // Soft golden yellow
    secondaryHover: '#E17055',          // Coral for hover
    accent: '#A29BFE',                  // Soft lavender
    
    // Borders and separators
    border: '#E9ECEF',                  // Light gray border
    borderSecondary: '#DEE2E6',         // Medium gray border
    borderAccent: '#74B9FF',            // Blue accent border
    separator: '#F8F9FA',               // Very light separator
    
    // Status and feedback
    success: '#00B894',                 // Muted teal
    successBackground: '#E8F8F5',       // Light success background
    warning: '#FDCB6E',                 // Golden yellow
    warningBackground: '#FFF9E6',       // Light warning background
    error: '#E17055',                   // Muted coral
    errorBackground: '#FDF2EF',         // Light error background
    info: '#74B9FF',                    // Soft blue
    infoBackground: '#EBF4FF',          // Light info background
    
    // Shadows and overlays
    shadow: 'rgba(45, 52, 54, 0.08)',   // Subtle warm shadow
    shadowMedium: 'rgba(45, 52, 54, 0.12)', // Medium shadow
    shadowStrong: 'rgba(45, 52, 54, 0.16)', // Strong shadow
    overlay: 'rgba(45, 52, 54, 0.4)',   // Modal overlay
    overlayLight: 'rgba(253, 251, 247, 0.9)', // Light overlay
    
    // Glass morphism effects
    glass: 'rgba(255, 255, 255, 0.8)',
    glassBorder: 'rgba(116, 185, 255, 0.2)',
    glassStrong: 'rgba(255, 255, 255, 0.9)',
  },
  
  // Enhanced dark theme with warm undertones
  dark: {
    // Dark backgrounds with subtle warmth
    background: '#1A1D23',              // Warm dark blue-gray
    backgroundSecondary: '#2D3436',     // Rich charcoal
    backgroundTertiary: '#363A3F',      // Medium dark gray
    surface: '#2D3436',                 // Charcoal for cards
    surfaceSecondary: '#363A3F',        // Lighter dark for secondary cards
    surfaceElevated: '#495057',         // Elevated dark surface
    
    // Dark theme text
    text: '#FEFEFE',                    // Warm white
    textSecondary: '#DDD6FE',           // Light purple tint
    textTertiary: '#81ECEC',            // Soft mint
    textMuted: '#74B9FF',               // Soft blue
    textInverse: '#2D3436',             // Dark text
    textAccent: '#FDCB6E',              // Golden accent
    
    // Dark interactive colors
    primary: '#74B9FF',                 // Soft blue
    primaryHover: '#A29BFE',            // Lavender hover
    primaryPressed: '#6C5CE7',          // Deeper purple pressed
    secondary: '#FDCB6E',               // Golden yellow
    secondaryHover: '#E17055',          // Coral hover
    accent: '#81ECEC',                  // Mint accent
    
    // Dark borders
    border: '#495057',                  // Medium dark border
    borderSecondary: '#6C757D',         // Lighter dark border
    borderAccent: '#74B9FF',            // Blue accent border
    separator: '#363A3F',               // Dark separator
    
    // Dark status colors
    success: '#00B894',                 // Teal success
    successBackground: '#1A3A32',       // Dark success background
    warning: '#FDCB6E',                 // Golden warning
    warningBackground: '#3A3320',       // Dark warning background
    error: '#E17055',                   // Coral error
    errorBackground: '#3A251F',         // Dark error background
    info: '#74B9FF',                    // Blue info
    infoBackground: '#1F2937',          // Dark info background
    
    // Dark shadows and overlays
    shadow: 'rgba(0, 0, 0, 0.3)',       // Dark shadow
    shadowMedium: 'rgba(0, 0, 0, 0.4)',  // Medium dark shadow
    shadowStrong: 'rgba(0, 0, 0, 0.5)',  // Strong dark shadow
    overlay: 'rgba(0, 0, 0, 0.7)',      // Dark overlay
    overlayLight: 'rgba(45, 52, 54, 0.9)', // Dark light overlay
    
    // Dark glass effects
    glass: 'rgba(45, 52, 54, 0.8)',
    glassBorder: 'rgba(116, 185, 255, 0.3)',
    glassStrong: 'rgba(45, 52, 54, 0.9)',
  },
};

// Enhanced typography with better hierarchy
const TYPOGRAPHY = {
  fontFamily: {
    regular: Platform.select({
      ios: 'SF Pro Display',
      android: 'Roboto',
      default: 'System',
    }),
    medium: Platform.select({
      ios: 'SF Pro Display Medium',
      android: 'Roboto Medium',
      default: 'System',
    }),
    semibold: Platform.select({
      ios: 'SF Pro Display Semibold',
      android: 'Roboto Bold',
      default: 'System',
    }),
    bold: Platform.select({
      ios: 'SF Pro Display Bold',
      android: 'Roboto Black',
      default: 'System',
    }),
  },
  fontSize: {
    xs: 11,
    sm: 13,
    base: 15,
    md: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 28,
    '4xl': 32,
    '5xl': 40,
  },
  lineHeight: {
    xs: 16,
    sm: 18,
    base: 22,
    md: 24,
    lg: 26,
    xl: 28,
    '2xl': 32,
    '3xl': 36,
    '4xl': 40,
    '5xl': 48,
  },
  fontWeight: {
    light: '300' as const,
    normal: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: '700' as const,
    black: '800' as const,
  },
  letterSpacing: {
    tight: -0.5,
    normal: 0,
    wide: 0.5,
    wider: 1,
  },
};

// Enhanced spacing system
const SPACING = {
  xs: 4,
  sm: 8,
  md: 12,
  base: 16,
  lg: 20,
  xl: 24,
  '2xl': 32,
  '3xl': 40,
  '4xl': 48,
  '5xl': 64,
  '6xl': 80,
};

// Modern border radius
const RADIUS = {
  none: 0,
  xs: 4,
  sm: 6,
  base: 8,
  md: 10,
  lg: 12,
  xl: 16,
  '2xl': 20,
  '3xl': 24,
  full: 9999,
};

// Enhanced shadow system
const SHADOWS = {
  none: {
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  xs: {
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  sm: {
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  base: {
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.12,
    shadowRadius: 8,
    elevation: 4,
  },
  lg: {
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.16,
    shadowRadius: 16,
    elevation: 8,
  },
  xl: {
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.20,
    shadowRadius: 24,
    elevation: 12,
  },
  // Special effects
  glow: {
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 8,
  },
  colored: {
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 6,
  },
};

// Enhanced component styles
const COMPONENTS = {
  // Modern button styles
  button: {
    height: {
      sm: 36,
      base: 44,
      lg: 52,
      xl: 60,
    },
    padding: {
      sm: { paddingHorizontal: 16, paddingVertical: 8 },
      base: { paddingHorizontal: 20, paddingVertical: 12 },
      lg: { paddingHorizontal: 24, paddingVertical: 16 },
      xl: { paddingHorizontal: 32, paddingVertical: 20 },
    },
    borderRadius: RADIUS.lg,
  },
  
  // Enhanced input styles
  input: {
    height: 48,
    padding: SPACING.base,
    borderRadius: RADIUS.lg,
    borderWidth: 1.5,
  },
  
  // Modern card styles
  card: {
    borderRadius: RADIUS.xl,
    padding: SPACING.lg,
    ...SHADOWS.base,
  },
  
  // Premium card styles
  cardPremium: {
    borderRadius: RADIUS['2xl'],
    padding: SPACING.xl,
    ...SHADOWS.lg,
  },
  
  // Modern header
  header: {
    height: Platform.OS === 'ios' ? 44 : 56,
    paddingHorizontal: SPACING.lg,
  },
  
  // Glass morphism
  glass: {
    borderRadius: RADIUS.xl,
    borderWidth: 1,
    backdropFilter: 'blur(20px)',
  },
};

// Recipe-specific colors with enhanced palette
const RECIPE_COLORS = {
  // Difficulty levels
  difficulty: {
    easy: '#00B894',      // Soft teal
    medium: '#FDCB6E',    // Golden yellow
    hard: '#E17055',      // Muted coral
  },
  
  // Meal types
  mealType: {
    breakfast: '#FDCB6E',  // Golden yellow
    lunch: '#00B894',      // Teal green
    dinner: '#74B9FF',     // Sky blue
    snack: '#E17055',      // Coral
    dessert: '#A29BFE',    // Lavender
  },
  
  // Dietary info
  dietary: {
    vegetarian: '#00B894',  // Teal
    vegan: '#81ECEC',       // Mint
    glutenFree: '#FDCB6E',  // Golden
    dairyFree: '#74B9FF',   // Blue
    keto: '#A29BFE',        // Lavender
    paleo: '#E17055',       // Coral
  },
  
  // Nutrition
  nutrition: {
    calories: '#E17055',    // Coral
    protein: '#74B9FF',     // Blue
    carbs: '#00B894',       // Teal
    fat: '#FDCB6E',         // Golden
    fiber: '#A29BFE',       // Lavender
  },
};

// Beautiful gradients
const GRADIENTS = {
  primary: ['#74B9FF', '#0984E3'] as const,
  secondary: ['#FDCB6E', '#E17055'] as const,
  accent: ['#A29BFE', '#6C5CE7'] as const,
  warm: ['#FDCB6E', '#E17055'] as const,
  cool: ['#74B9FF', '#81ECEC'] as const,
  sunset: ['#FDCB6E', '#A29BFE'] as const,
  ocean: ['#74B9FF', '#00B894'] as const,
  lavender: ['#A29BFE', '#DDD6FE'] as const,
  mint: ['#81ECEC', '#00B894'] as const,
};

// Animation settings
const ANIMATIONS = {
  duration: {
    fast: 200,
    normal: 300,
    slow: 500,
  },
  easing: {
    ease: 'ease-out',
    spring: 'spring',
  },
};

// Create enhanced minimalist theme
export const enhancedMinimalistTheme = {
  colors: COLORS,
  typography: TYPOGRAPHY,
  spacing: SPACING,
  radius: RADIUS,
  shadows: SHADOWS,
  components: COMPONENTS,
  recipeColors: RECIPE_COLORS,
  gradients: GRADIENTS,
  animations: ANIMATIONS,
} as const;

// Theme mode type
export type ThemeMode = 'light' | 'dark';

// Get theme colors based on mode
export const getEnhancedThemeColors = (mode: ThemeMode = 'light') => {
  return {
    ...COLORS,
    ...COLORS[mode],
    gradients: GRADIENTS,
    recipe: RECIPE_COLORS,
  };
};

// Utility to create themed styles
export const createEnhancedThemedStyles = <T extends Record<string, any>>(
  stylesFn: (theme: typeof enhancedMinimalistTheme, colors: ReturnType<typeof getEnhancedThemeColors>) => T
) => {
  return (mode: ThemeMode = 'light') => {
    const colors = getEnhancedThemeColors(mode);
    return stylesFn(enhancedMinimalistTheme, colors);
  };
};

// Individual exports for compatibility
export const colors = COLORS;
export const typography = TYPOGRAPHY;
export const spacing = SPACING;
export const radius = RADIUS;
export const shadows = SHADOWS;
export const components = COMPONENTS;
export const recipeColors = RECIPE_COLORS;
export const gradients = GRADIENTS;

export default enhancedMinimalistTheme; 