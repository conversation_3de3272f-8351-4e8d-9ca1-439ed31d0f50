# 🛒 Price Comparison & Shopping Optimization Guide

This guide explains how to use the new price comparison and shopping list optimization features in your AI Recipe Planner app.

## 🎯 Overview

The price comparison system allows users to:
- **Compare prices** across Woolworths, New World, and Pak'nSave
- **Optimize shopping lists** to find the cheapest total cost
- **Smart shopping recommendations** for single-store vs multi-store shopping
- **Real-time price lookup** with Algolia search integration

## 🏗️ Architecture

### Data Sources
- **Woolworths**: Algolia index `woolworths`
- **New World**: Algolia index `newworld` 
- **Pak'nSave**: Algolia index `products`

### Key Components
1. **PriceComparisonService** (`src/services/priceComparisonService.ts`)
2. **PriceComparisonModal** (`src/components/PriceComparisonModal.tsx`)
3. **EnhancedShoppingListScreen** (`src/screens/main/EnhancedShoppingListScreen.tsx`)

## 🚀 Getting Started

### 1. Setup Requirements

Ensure your Algolia indices contain product data with these fields:
```json
{
  "objectID": "product-123",
  "name": "Milk 2L",
  "price": 3.50,
  "unit": "2L",
  "description": "Fresh milk 2 litre",
  "category": "Dairy",
  "brand": "Anchor", 
  "imageUrl": "https://example.com/milk.jpg",
  "availability": "in_stock",
  "promotions": ["Buy 2 Get 1 Free"]
}
```

### 2. Integration Steps

#### Replace the existing ShoppingListScreen:

```typescript
// In your navigation configuration
import { EnhancedShoppingListScreen } from '../screens/main/EnhancedShoppingListScreen';

// Replace ShoppingListScreen with EnhancedShoppingListScreen
```

#### Update your environment variables:
```env
EXPO_PUBLIC_ALGOLIA_APP_ID=your_algolia_app_id
EXPO_PUBLIC_ALGOLIA_API_KEY=your_search_api_key
```

## 🎮 Features & Usage

### 1. Individual Product Price Search

Users can search for individual products across all stores:

```typescript
import { priceComparisonService } from '../services/priceComparisonService';

// Search for a product across all stores
const comparison = await priceComparisonService.searchProductAcrossStores('milk', {
  maxResults: 5,
  sortBy: 'price',
  includeOutOfStock: false
});

console.log('Cheapest option:', comparison.cheapestOption);
console.log('Price range:', comparison.priceRange);
```

### 2. Shopping List Optimization

Optimize entire shopping lists for best prices:

```typescript
const shoppingItems = ['milk', 'bread', 'eggs', 'butter'];

// Find cheapest total across all stores
const optimization = await priceComparisonService.optimizeShoppingList(
  shoppingItems, 
  'cheapest_total'
);

console.log('Total cost:', optimization.totalCost);
console.log('Store breakdown:', optimization.storeBreakdown);
console.log('Recommended strategy:', optimization.recommendedStrategy);
```

### 3. Optimization Strategies

- **`cheapest_total`**: Find the absolute cheapest option for each item (may require multiple stores)
- **`single_store_convenience`**: Optimize for shopping at only one store
- **`balanced`**: Balance between price and convenience

## 🎨 UI Components

### PriceComparisonModal

Shows price comparison results with:
- Store logos and colors
- Price rankings with "LOWEST" badges
- Savings calculations
- Promotional offers
- Sort by price or relevance

### Enhanced Shopping List

Features include:
- **💡 Optimize button**: Finds best prices for entire list
- **🔍 Find prices**: Individual item price lookup
- **Price display**: Shows store, price, and savings
- **Store indicators**: Emoji indicators for each store
- **Total estimate**: Running total of estimated costs

## 📊 Data Structures

### Store Configuration
```typescript
const STORE_CONFIG = {
  woolworths: {
    indexName: 'woolworths',
    displayName: 'Woolworths',
    color: '#00A651',
    logo: '🍎'
  },
  newworld: {
    indexName: 'newworld', 
    displayName: 'New World',
    color: '#E31E24',
    logo: '🛒'
  },
  paknsave: {
    indexName: 'products',
    displayName: "Pak'nSave",
    color: '#FFD100',
    logo: '💰'
  }
};
```

### Shopping Item with Price Info
```typescript
interface ShoppingItem {
  id: string;
  name: string;
  quantity?: string;
  category: string;
  checked: boolean;
  recipeId?: string;
  priceInfo?: {
    store: 'woolworths' | 'newworld' | 'paknsave';
    storeName: string;
    price: number;
    unit?: string;
    lastUpdated: string;
  };
}
```

## 🔧 Customization

### Adding New Stores

1. Update `STORE_CONFIG` in `priceComparisonService.ts`:
```typescript
const STORE_CONFIG = {
  // existing stores...
  countdown: {
    indexName: 'countdown_products',
    displayName: 'Countdown',
    color: '#FF6B00',
    logo: '🛍️'
  }
};
```

2. Update TypeScript types:
```typescript
export type StoreType = 'woolworths' | 'newworld' | 'paknsave' | 'countdown';
```

### Custom Search Filters

Add product filtering options:
```typescript
const comparison = await priceComparisonService.searchProductAcrossStores('milk', {
  maxResults: 10,
  category: 'Dairy',
  sortBy: 'price',
  includeOutOfStock: false
});
```

## 🎯 Best Practices

### 1. Error Handling
```typescript
try {
  const comparison = await priceComparisonService.searchProductAcrossStores(query);
  // Handle results
} catch (error) {
  if (error instanceof PriceComparisonError) {
    Alert.alert('Search Error', error.message);
  }
}
```

### 2. Loading States
Always show loading indicators for price searches:
```typescript
const [loading, setLoading] = useState(false);

const searchPrices = async () => {
  setLoading(true);
  try {
    // Search logic
  } finally {
    setLoading(false);
  }
};
```

### 3. Cache Management
Price data is cached in the shopping item's `priceInfo` field. Consider:
- Showing last updated timestamps
- Refreshing stale price data
- Validating price accuracy

## 🚦 Performance Tips

1. **Batch requests**: When optimizing lists, the service automatically batches requests
2. **Limit results**: Use `maxResults` parameter to control response size
3. **Relevant searches**: Use specific product names for better matching
4. **Store availability**: Filter out-of-stock items for better UX

## 🧪 Testing

### Test individual product search:
```typescript
// Test price comparison
const result = await priceComparisonService.searchProductAcrossStores('test product');
expect(result.results.length).toBeGreaterThan(0);
expect(result.cheapestOption).toBeDefined();
```

### Test shopping list optimization:
```typescript
// Test optimization
const optimization = await priceComparisonService.optimizeShoppingList(['milk', 'bread']);
expect(optimization.totalCost).toBeGreaterThan(0);
expect(optimization.storeBreakdown.length).toBeGreaterThan(0);
```

## 🔮 Future Enhancements

1. **Price history tracking**: Track price changes over time
2. **Geolocation**: Factor in store distance and travel costs
3. **Promotional alerts**: Notify users of special offers
4. **Barcode scanning**: Quick product lookup via camera
5. **Recipe cost calculation**: Estimate total recipe costs
6. **Budget tracking**: Compare spending against user budgets

## 📞 Support

If you encounter issues:
1. Check Algolia index configuration
2. Verify API credentials in environment variables
3. Ensure product data contains required fields (`name`, `price`)
4. Check network connectivity for Algolia requests

---

## 🎉 Example Usage

Here's a complete example of integrating the price comparison system:

```typescript
import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Alert } from 'react-native';
import { priceComparisonService } from '../services/priceComparisonService';

const SmartShoppingExample = () => {
  const [optimization, setOptimization] = useState(null);
  const [loading, setLoading] = useState(false);

  const optimizeList = async () => {
    setLoading(true);
    try {
      const result = await priceComparisonService.optimizeShoppingList([
        'milk 2L',
        'bread white',
        'eggs dozen',
        'butter 500g',
        'cheese cheddar'
      ], 'balanced');
      
      setOptimization(result);
      
      Alert.alert(
        'Optimization Complete!',
        `Total cost: $${result.totalCost.toFixed(2)}\n` +
        `Stores needed: ${result.storeBreakdown.length}\n` +
        `Strategy: ${result.recommendedStrategy}`
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to optimize shopping list');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View>
      <TouchableOpacity onPress={optimizeList} disabled={loading}>
        <Text>{loading ? 'Optimizing...' : 'Optimize Shopping List'}</Text>
      </TouchableOpacity>
      
      {optimization && (
        <View>
          <Text>Total: ${optimization.totalCost.toFixed(2)}</Text>
          {optimization.storeBreakdown.map(store => (
            <Text key={store.store}>
              {store.storeName}: ${store.subtotal.toFixed(2)} ({store.itemCount} items)
            </Text>
          ))}
        </View>
      )}
    </View>
  );
};
```

This completes your comprehensive price comparison and shopping optimization system! 🎉