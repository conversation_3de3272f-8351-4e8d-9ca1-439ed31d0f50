/**
 * TEST: Consolidated Product Card Integration
 * 
 * Tests the complete integration of Story 1.1 (Deduplication) + Story 1.2 (Consolidated Cards)
 * Verifies that the ConsolidatedProductCard component works with real deduplication data.
 */

import { createClient } from '@supabase/supabase-js';
import { consolidatedProductService } from '../services/consolidatedProductService';
import { IProduct } from '../types/shoppingList';

const supabaseUrl = 'https://emjwniuqwhvohjassnrg.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVtanduaXVxd2h2b2hqYXNzbnJnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjQwMTYwNSwiZXhwIjoyMDY3OTc3NjA1fQ.oTowRoNAjlUmk10O9pSMK2BvL0XlyBb5orVRrlEryHs';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testConsolidatedProductCard() {
  console.log('🧪 Testing Consolidated Product Card Integration...\n');

  try {
    // Test 1: Fetch sample products for testing
    console.log('1. Fetching sample products for testing...');
    
    const { data: products, error } = await supabase
      .from('products')
      .select('*')
      .not('price', 'is', null)
      .limit(500);

    if (error) {
      console.error('❌ Error fetching products:', error);
      return;
    }

    if (!products || products.length === 0) {
      console.error('❌ No products found');
      return;
    }

    console.log(`✅ Fetched ${products.length} products for testing`);

    // Test 2: Apply consolidation
    console.log('\n2. Applying product consolidation...');
    
    const startTime = Date.now();
    const consolidatedProducts = await consolidatedProductService.consolidateProducts(products);
    const endTime = Date.now();

    console.log(`✅ Consolidation completed in ${endTime - startTime}ms`);
    console.log(`📊 Results:`);
    console.log(`   Original products: ${products.length}`);
    console.log(`   Consolidated products: ${consolidatedProducts.length}`);
    console.log(`   Reduction: ${((1 - consolidatedProducts.length / products.length) * 100).toFixed(1)}%`);

    // Test 3: Analyze consolidated products for UI display
    console.log('\n3. Analyzing consolidated products for UI display...');
    
    const stats = consolidatedProductService.getConsolidationStats(products, consolidatedProducts);
    console.log(`📈 Consolidation Statistics:`);
    console.log(`   Reduction percentage: ${stats.reductionPercentage.toFixed(1)}%`);
    console.log(`   Cross-store products: ${stats.crossStoreProducts}`);
    console.log(`   Total savings opportunities: $${stats.totalSavingsOpportunities.toFixed(2)}`);
    console.log(`   Average confidence: ${stats.averageConfidence.toFixed(3)}`);

    // Test 4: Examine specific consolidated products
    console.log('\n4. Examining specific consolidated products...');
    
    const multiStoreProducts = consolidatedProducts.filter(p => p.availableStores.length > 1);
    console.log(`🛒 Found ${multiStoreProducts.length} products available at multiple stores:`);

    multiStoreProducts.slice(0, 5).forEach((product, index) => {
      console.log(`\n   ${index + 1}. "${product.name}"`);
      console.log(`      Stores: [${product.availableStores.join(', ')}]`);
      console.log(`      Price range: $${product.lowestPrice.toFixed(2)} - $${product.highestPrice.toFixed(2)}`);
      console.log(`      Max savings: $${product.maxSavings.toFixed(2)}`);
      console.log(`      Confidence: ${product.confidence.toFixed(3)}`);
      
      // Show store prices
      Object.entries(product.storePrices).forEach(([store, price]) => {
        const isBest = price === product.lowestPrice;
        console.log(`        ${store}: $${price.toFixed(2)}${isBest ? ' (BEST)' : ''}`);
      });
    });

    // Test 5: Test UI data structure compatibility
    console.log('\n5. Testing UI data structure compatibility...');
    
    const sampleProduct = consolidatedProducts[0];
    console.log(`📱 Sample product for UI rendering:`);
    console.log(`   ID: ${sampleProduct.id}`);
    console.log(`   Name: ${sampleProduct.name}`);
    console.log(`   Brand: ${sampleProduct.brand || 'N/A'}`);
    console.log(`   Image URL: ${sampleProduct.imageUrl ? 'Available' : 'Not available'}`);
    console.log(`   Store prices: ${Object.keys(sampleProduct.storePrices).length} stores`);
    console.log(`   All stores data: ${sampleProduct.allStores.length} variants`);

    // Verify required fields for ConsolidatedProductCard
    const requiredFields = ['id', 'name', 'storePrices', 'allStores', 'lowestPrice', 'highestPrice', 'availableStores'];
    const missingFields = requiredFields.filter(field => !(field in sampleProduct));
    
    if (missingFields.length === 0) {
      console.log(`   ✅ All required fields present for ConsolidatedProductCard`);
    } else {
      console.log(`   ❌ Missing fields: ${missingFields.join(', ')}`);
    }

    // Test 6: Performance analysis for UI
    console.log('\n6. Performance analysis for UI rendering...');
    
    const avgProductSize = JSON.stringify(sampleProduct).length;
    const totalMemoryUsage = (consolidatedProducts.length * avgProductSize) / (1024 * 1024);
    
    console.log(`💾 Memory usage analysis:`);
    console.log(`   Average product size: ${avgProductSize} bytes`);
    console.log(`   Total memory for ${consolidatedProducts.length} products: ${totalMemoryUsage.toFixed(2)} MB`);
    console.log(`   Memory efficiency: ${totalMemoryUsage < 10 ? 'EXCELLENT' : totalMemoryUsage < 25 ? 'GOOD' : 'NEEDS OPTIMIZATION'}`);

    // Test 7: Simulate user interactions
    console.log('\n7. Simulating user interactions...');
    
    const testProduct = multiStoreProducts[0];
    if (testProduct) {
      console.log(`🖱️  Simulating user interactions with "${testProduct.name}":`);
      
      // Simulate store selection
      const bestStore = Object.entries(testProduct.storePrices)
        .reduce((best, [store, price]) => price < best.price ? { store, price } : best, 
                { store: '', price: Infinity });
      
      console.log(`   User selects best price store: ${bestStore.store} ($${bestStore.price.toFixed(2)})`);
      console.log(`   Savings vs highest price: $${(testProduct.highestPrice - bestStore.price).toFixed(2)}`);
      
      // Simulate adding to shopping list
      const selectedProduct = testProduct.allStores.find(p => p.store === bestStore.store);
      if (selectedProduct) {
        console.log(`   Adding to shopping list: "${selectedProduct.name}" from ${selectedProduct.store}`);
        console.log(`   Product details: $${selectedProduct.price}, ${selectedProduct.unit || 'N/A'}`);
      }
    }

    console.log('\n✅ Consolidated Product Card integration test completed successfully!');
    
    return {
      originalCount: products.length,
      consolidatedCount: consolidatedProducts.length,
      reductionPercentage: stats.reductionPercentage,
      crossStoreProducts: stats.crossStoreProducts,
      totalSavings: stats.totalSavingsOpportunities,
      memoryUsage: totalMemoryUsage,
      processingTime: endTime - startTime
    };

  } catch (error) {
    console.error('❌ Consolidated Product Card test failed:', error);
    throw error;
  }
}

// Run the test
if (require.main === module) {
  testConsolidatedProductCard()
    .then((results) => {
      console.log('\n📊 CONSOLIDATED PRODUCT CARD TEST SUMMARY:');
      console.log('='.repeat(60));
      console.log(`Original Products: ${results?.originalCount || 0}`);
      console.log(`Consolidated Products: ${results?.consolidatedCount || 0}`);
      console.log(`Reduction: ${results?.reductionPercentage?.toFixed(1) || 0}%`);
      console.log(`Cross-Store Products: ${results?.crossStoreProducts || 0}`);
      console.log(`Total Savings Opportunities: $${results?.totalSavings?.toFixed(2) || 0}`);
      console.log(`Memory Usage: ${results?.memoryUsage?.toFixed(2) || 0} MB`);
      console.log(`Processing Time: ${results?.processingTime || 0}ms`);
      
      const isSuccess = (results?.reductionPercentage || 0) > 20 && 
                       (results?.crossStoreProducts || 0) > 0 && 
                       (results?.memoryUsage || 0) < 25;
      
      if (isSuccess) {
        console.log('\n🎉 SUCCESS: Consolidated Product Card is ready for production!');
        console.log('   ✅ Significant reduction in visual clutter');
        console.log('   ✅ Cross-store price comparison working');
        console.log('   ✅ Memory usage is efficient');
        console.log('   ✅ Integration between Story 1.1 and 1.2 is complete');
        console.log('   ✅ Ready for user testing and deployment');
      } else {
        console.log('\n⚠️  PARTIAL SUCCESS: Some optimizations may be needed');
        if ((results?.reductionPercentage || 0) <= 20) console.log('   - Consider adjusting deduplication criteria');
        if ((results?.crossStoreProducts || 0) === 0) console.log('   - No cross-store matches found');
        if ((results?.memoryUsage || 0) >= 25) console.log('   - Consider memory optimizations');
      }
      console.log('='.repeat(60));
    })
    .catch((error) => {
      console.error('❌ Test failed:', error);
    });
}

export { testConsolidatedProductCard };
