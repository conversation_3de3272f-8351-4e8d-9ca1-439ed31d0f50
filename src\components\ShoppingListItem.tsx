import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Animated,
} from 'react-native';
import { Swipeable } from 'react-native-gesture-handler';
import { useTheme } from '../context/ThemeContext';
import { theme } from '../styles';
import { PriceDisplayChip, NoPriceChip, LoadingPriceChip } from './PriceDisplayChip';
import { BrandSelectorDropdown } from './BrandSelectorDropdown';
import { ShoppingListItem as ShoppingListItemType } from '../types/shoppingList';

interface ShoppingListItemProps {
  item: ShoppingListItemType;
  onToggleChecked: (itemId: string) => void;
  onBrandSelect: (itemId: string, brand: string, size: string, rejectedOptions: Array<{ brand: string; size: string; price: number }>) => void;
  onQuantityChange?: (itemId: string, quantity: number) => void;
  onDelete: (itemId: string) => void;
  showPrices?: boolean;
  compact?: boolean;
}

export const ShoppingListItem: React.FC<ShoppingListItemProps> = ({
  item,
  onToggleChecked,
  onBrandSelect,
  onQuantityChange,
  onDelete,
  showPrices = true,
  compact = false,
}) => {
  const { colors } = useTheme();
  const [showBrandSelector, setShowBrandSelector] = useState(false);
  const [brandSelectorLoading, setBrandSelectorLoading] = useState(false);

  const handleCheckboxPress = () => {
    onToggleChecked(item.id);
  };

  const handleBrandSelectorOpen = () => {
    setShowBrandSelector(true);
  };

  const handleBrandSelection = async (
    brand: string, 
    size: string, 
    rejectedOptions: Array<{ brand: string; size: string; price: number }>
  ) => {
    setBrandSelectorLoading(true);
    try {
      await onBrandSelect(item.id, brand, size, rejectedOptions);
      setShowBrandSelector(false);
    } catch (error) {
      Alert.alert('Error', 'Failed to update brand selection. Please try again.');
    } finally {
      setBrandSelectorLoading(false);
    }
  };

  const handleQuantityPress = () => {
    if (!onQuantityChange) return;
    
    Alert.alert(
      'Change Quantity',
      `Current quantity: ${item.quantity || 1}`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: '1', onPress: () => onQuantityChange(item.id, 1) },
        { text: '2', onPress: () => onQuantityChange(item.id, 2) },
        { text: '3', onPress: () => onQuantityChange(item.id, 3) },
        { text: '4', onPress: () => onQuantityChange(item.id, 4) },
        { text: '5', onPress: () => onQuantityChange(item.id, 5) },
      ]
    );
  };

  const renderRightAction = () => (
    <Animated.View style={[styles.deleteAction, { backgroundColor: '#FF6B6B' }]}>
      <TouchableOpacity
        style={styles.deleteButton}
        onPress={() => onDelete(item.id)}
      >
        <Text style={styles.deleteText}>🗑️</Text>
        <Text style={[styles.deleteLabel, { color: 'white' }]}>Delete</Text>
      </TouchableOpacity>
    </Animated.View>
  );

  const renderPriceSection = () => {
    if (!showPrices) return null;

    // Loading state
    if (brandSelectorLoading) {
      return <LoadingPriceChip size={compact ? 'small' : 'medium'} />;
    }

    // No brand selected
    if (!item.selectedBrand || !item.selectedSize) {
      return (
        <NoPriceChip
          reason="Select brand"
          size={compact ? 'small' : 'medium'}
          onTap={handleBrandSelectorOpen}
        />
      );
    }

    // Brand selected but no price data
    if (!item.priceData) {
      return (
        <NoPriceChip
          reason="Price unavailable"
          size={compact ? 'small' : 'medium'}
          onTap={handleBrandSelectorOpen}
        />
      );
    }

    try {
      // Safely calculate price information
      const allStorePrices = item.priceData.allStorePrices || [];
      const availablePrices = allStorePrices
        .filter(p => p && p.available && typeof p.price === 'number' && !isNaN(p.price));
      
      const prices = availablePrices.map(p => p.price).filter((p): p is number => typeof p === 'number' && !isNaN(p));
      const lowestPrice = prices.length > 0 ? Math.min(...prices) : 0;
      
      const selectedPrice = item.priceData.selectedPrice;
      const isValidPrice = typeof selectedPrice === 'number' && !isNaN(selectedPrice) && selectedPrice > 0;
      
      if (!isValidPrice) {
        return (
          <NoPriceChip
            reason="Invalid price"
            size={compact ? 'small' : 'medium'}
            onTap={handleBrandSelectorOpen}
          />
        );
      }

      const isLowestPrice = Math.abs(selectedPrice - lowestPrice) < 0.01;
      const potentialSavings = item.priceData.potentialSavings || 0;

      return (
        <PriceDisplayChip
          price={selectedPrice}
          store={item.priceData.selectedStore as any}
          storeName={item.priceData.selectedStoreName || item.priceData.selectedStore || ''}
          isLowestPrice={isLowestPrice}
          potentialSavings={potentialSavings}
          onTap={handleBrandSelectorOpen}
          size={compact ? 'small' : 'medium'}
          showStore={!compact}
          showSavings={!compact && potentialSavings > 0}
        />
      );
    } catch (error) {
      console.warn('Error rendering price section:', error);
      return (
        <NoPriceChip
          reason="Price error"
          size={compact ? 'small' : 'medium'}
          onTap={handleBrandSelectorOpen}
        />
      );
    }
  };

  const renderBrandInfo = () => {
    if (!item.selectedBrand) return null;

    return (
      <View style={styles.brandInfo}>
        <Text style={[styles.brandText, { color: colors.textSecondary }]}>
          {item.selectedBrand}
          {item.selectedSize && ` (${item.selectedSize})`}
        </Text>
        {item.brandSelectionStatus === 'selected' && (
          <Text style={[styles.autoSelectedIndicator, { color: theme.colors.primary }]}>
            ⭐ Auto-selected
          </Text>
        )}
      </View>
    );
  };

  const renderQuantityInfo = () => {
    if (!item.quantity || item.quantity === 1) return null;

    return (
      <TouchableOpacity
        style={[styles.quantityBadge, { backgroundColor: theme.colors.primary }]}
        onPress={handleQuantityPress}
      >
        <Text style={styles.quantityText}>
          {item.quantity}
          {item.unit && ` ${item.unit}`}
        </Text>
      </TouchableOpacity>
    );
  };

  const renderCategoryBadge = () => {
    if (compact || !item.category || item.category === 'Groceries') return null;

    return (
      <View style={[styles.categoryBadge, { backgroundColor: colors.backgroundSecondary }]}>
        <Text style={[styles.categoryText, { color: colors.textTertiary }]}>
          {item.category}
        </Text>
      </View>
    );
  };

  return (
    <>
      <Swipeable renderRightActions={renderRightAction}>
        <View style={[
          styles.container,
          { 
            backgroundColor: colors.surface,
            borderBottomColor: colors.border,
          },
          item.checked && styles.checkedContainer,
          compact && styles.compactContainer
        ]}>
          {/* Left side - Checkbox and Item Info */}
          <View style={styles.leftSection}>
            {/* Checkbox */}
            <TouchableOpacity
              style={[
                styles.checkbox,
                {
                  backgroundColor: item.checked ? theme.colors.primary : 'transparent',
                  borderColor: item.checked ? theme.colors.primary : colors.border,
                }
              ]}
              onPress={handleCheckboxPress}
            >
              {item.checked && (
                <Text style={styles.checkmark}>✓</Text>
              )}
            </TouchableOpacity>

            {/* Item Details */}
            <View style={styles.itemDetails}>
              {/* Main row */}
              <View style={styles.mainRow}>
                <View style={styles.nameSection}>
                  <Text style={[
                    styles.itemName,
                    { color: colors.text },
                    item.checked && styles.checkedText
                  ]}>
                    {item.name}
                  </Text>
                  {renderQuantityInfo()}
                </View>
              </View>

              {/* Secondary info */}
              {!compact && (
                <View style={styles.secondaryRow}>
                  {renderBrandInfo()}
                  {renderCategoryBadge()}
                </View>
              )}
            </View>
          </View>

          {/* Right side - Price and Brand Selection */}
          <View style={styles.rightSection}>
            {renderPriceSection()}
            
            {/* Brand selection button */}
            {!compact && (
              <TouchableOpacity
                style={[styles.brandButton, { borderColor: colors.border }]}
                onPress={handleBrandSelectorOpen}
              >
                <Text style={[styles.brandButtonText, { color: colors.textSecondary }]}>
                  Brand ▼
                </Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
      </Swipeable>

      {/* Brand Selector Modal */}
      <BrandSelectorDropdown
        visible={showBrandSelector}
        onClose={() => setShowBrandSelector(false)}
        productName={item.name}
        baseProduct={item.baseProduct || ''}
        currentSelection={item.selectedBrand && item.selectedSize ? {
          brand: item.selectedBrand,
          size: item.selectedSize
        } : undefined}
        onBrandSelect={handleBrandSelection}
      />
    </>
  );
};

export const ShoppingListItemSeparator: React.FC = () => {
  const { colors } = useTheme();
  
  return (
    <View style={[styles.separator, { backgroundColor: colors.border }]} />
  );
};

export const ShoppingListItemSkeleton: React.FC<{ compact?: boolean }> = ({ compact = false }) => {
  const { colors } = useTheme();
  
  return (
    <View style={[
      styles.container,
      { backgroundColor: colors.surface, borderBottomColor: colors.border },
      compact && styles.compactContainer
    ]}>
      <View style={styles.leftSection}>
        <View style={[styles.skeleton, styles.checkboxSkeleton, { backgroundColor: colors.border }]} />
        <View style={styles.itemDetails}>
          <View style={[styles.skeleton, styles.nameSkeleton, { backgroundColor: colors.border }]} />
          {!compact && (
            <View style={[styles.skeleton, styles.brandSkeleton, { backgroundColor: colors.border }]} />
          )}
        </View>
      </View>
      <View style={styles.rightSection}>
        <View style={[styles.skeleton, styles.priceSkeleton, { backgroundColor: colors.border }]} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.base,
    paddingVertical: theme.spacing.sm,
    borderBottomWidth: 1,
    minHeight: 68,
  },
  compactContainer: {
    paddingVertical: theme.spacing.xs,
    minHeight: 48,
  },
  checkedContainer: {
    opacity: 0.6,
  },
  leftSection: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 6,
    borderWidth: 2,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: theme.spacing.sm,
  },
  checkmark: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  itemDetails: {
    flex: 1,
  },
  mainRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  nameSection: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemName: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.medium,
    flex: 1,
  },
  checkedText: {
    textDecorationLine: 'line-through',
  },
  quantityBadge: {
    paddingHorizontal: theme.spacing.xs,
    paddingVertical: 2,
    borderRadius: theme.radius.sm,
    marginLeft: theme.spacing.xs,
  },
  quantityText: {
    color: 'white',
    fontSize: theme.typography.fontSize.xs,
    fontWeight: theme.typography.fontWeight.semibold,
  },
  secondaryRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
    gap: theme.spacing.xs,
  },
  brandInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.xs,
  },
  brandText: {
    fontSize: theme.typography.fontSize.sm,
  },
  autoSelectedIndicator: {
    fontSize: theme.typography.fontSize.xs,
    fontWeight: theme.typography.fontWeight.semibold,
  },
  categoryBadge: {
    paddingHorizontal: theme.spacing.xs,
    paddingVertical: 2,
    borderRadius: theme.radius.sm,
  },
  categoryText: {
    fontSize: theme.typography.fontSize.xs,
  },
  rightSection: {
    alignItems: 'flex-end',
    gap: theme.spacing.xs,
  },
  brandButton: {
    paddingHorizontal: theme.spacing.xs,
    paddingVertical: 4,
    borderRadius: theme.radius.sm,
    borderWidth: 1,
  },
  brandButtonText: {
    fontSize: theme.typography.fontSize.xs,
    fontWeight: theme.typography.fontWeight.medium,
  },
  deleteAction: {
    justifyContent: 'center',
    alignItems: 'center',
    width: 80,
  },
  deleteButton: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    width: '100%',
  },
  deleteText: {
    fontSize: 24,
    marginBottom: 4,
  },
  deleteLabel: {
    fontSize: theme.typography.fontSize.xs,
    fontWeight: theme.typography.fontWeight.semibold,
  },
  separator: {
    height: 1,
    marginHorizontal: theme.spacing.base,
  },
  skeleton: {
    borderRadius: 4,
  },
  checkboxSkeleton: {
    width: 24,
    height: 24,
    borderRadius: 6,
    marginRight: theme.spacing.sm,
  },
  nameSkeleton: {
    height: 16,
    width: '70%',
    marginBottom: 4,
  },
  brandSkeleton: {
    height: 12,
    width: '50%',
  },
  priceSkeleton: {
    height: 32,
    width: 80,
    borderRadius: theme.radius.lg,
  },
});