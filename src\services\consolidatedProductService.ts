/**
 * Consolidated Product Service
 * Converts deduplication results to consolidated products for UI display
 * Integrates Story 1.1 (Deduplication) with Story 1.2 (Consolidated Product Cards)
 */

import { IProduct, IUnifiedProduct } from '../types/shoppingList';
import { 
  DeduplicationResult, 
  ProductGroup, 
  ConsolidatedProduct 
} from '../types/deduplication';
import { ProductDeduplicationService } from './productDeduplicationService';

export class ConsolidatedProductService {
  private deduplicationService: ProductDeduplicationService;

  constructor() {
    this.deduplicationService = new ProductDeduplicationService();
  }

  /**
   * Convert raw products to consolidated products using deduplication
   */
  async consolidateProducts(products: IProduct[]): Promise<ConsolidatedProduct[]> {
    try {
      // Run deduplication
      const deduplicationResult = this.deduplicationService.deduplicateProducts(products);
      
      // Convert groups to consolidated products
      const consolidatedProducts = deduplicationResult.groups.map(group => 
        this.convertGroupToConsolidatedProduct(group)
      );

      // Add ungrouped products as individual consolidated products
      const groupedProductIds = new Set(
        deduplicationResult.groups.flatMap(group => 
          group.products.map(match => match.product.id)
        )
      );

      const ungroupedProducts = products
        .filter(product => !groupedProductIds.has(product.id))
        .map(product => this.convertSingleProductToConsolidated(product));

      return [...consolidatedProducts, ...ungroupedProducts];
    } catch (error) {
      console.error('Error consolidating products:', error);
      // Fallback: convert all products individually
      return products.map(product => this.convertSingleProductToConsolidated(product));
    }
  }

  /**
   * Convert a product group to a consolidated product
   */
  private convertGroupToConsolidatedProduct(group: ProductGroup): ConsolidatedProduct {
    const allProducts = group.products.map(match => match.product);
    const representative = group.representative;

    // Calculate store prices
    const storePrices: Record<string, number> = {};
    allProducts.forEach(product => {
      storePrices[product.store] = product.price;
    });

    // Calculate price statistics
    const prices = Object.values(storePrices).filter(price => price > 0);
    const lowestPrice = prices.length > 0 ? Math.min(...prices) : 0;
    const highestPrice = prices.length > 0 ? Math.max(...prices) : 0;
    const maxSavings = highestPrice - lowestPrice;

    // Get best image (prefer products with images)
    const bestImage = this.getBestImage(allProducts);

    // Get best brand (prefer non-empty brands)
    const bestBrand = this.getBestBrand(allProducts);

    // Get best size/unit
    const bestSizeUnit = this.getBestSizeUnit(allProducts);

    return {
      id: group.groupId,
      name: representative.name,
      brand: bestBrand,
      category: representative.category,
      imageUrl: bestImage,
      size: bestSizeUnit.size,
      unit: bestSizeUnit.unit,
      storePrices,
      allStores: allProducts,
      lowestPrice,
      highestPrice,
      maxSavings,
      availableStores: group.stores,
      confidence: group.groupConfidence,
      consolidatedAt: new Date(),
    };
  }

  /**
   * Convert a single product to a consolidated product (for ungrouped products)
   */
  private convertSingleProductToConsolidated(product: IProduct): ConsolidatedProduct {
    return {
      id: `single_${product.id}`,
      name: product.name,
      brand: product.brand,
      category: product.category,
      imageUrl: product.image_url,
      size: product.size,
      unit: product.unit,
      storePrices: { [product.store]: product.price },
      allStores: [product],
      lowestPrice: product.price,
      highestPrice: product.price,
      maxSavings: 0,
      availableStores: [product.store],
      confidence: 1.0,
      consolidatedAt: new Date(),
    };
  }

  /**
   * Get the best image from a group of products
   */
  private getBestImage(products: IProduct[]): string | undefined {
    // Prefer products with images
    const productsWithImages = products.filter(p => p.image_url && p.image_url.trim() !== '');
    
    if (productsWithImages.length > 0) {
      // Prefer higher quality images (longer URLs often indicate better images)
      return productsWithImages
        .sort((a, b) => (b.image_url?.length || 0) - (a.image_url?.length || 0))[0]
        .image_url;
    }

    return undefined;
  }

  /**
   * Get the best brand from a group of products
   */
  private getBestBrand(products: IProduct[]): string | undefined {
    // Prefer non-empty, non-null brands
    const validBrands = products
      .map(p => p.brand)
      .filter(brand => brand && brand.trim() !== '' && brand !== 'N/A');

    if (validBrands.length > 0) {
      // Return the most common brand, or the first valid one
      const brandCounts = validBrands.reduce((acc, brand) => {
        acc[brand!] = (acc[brand!] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      return Object.entries(brandCounts)
        .sort(([, a], [, b]) => b - a)[0][0];
    }

    return undefined;
  }

  /**
   * Get the best size and unit from a group of products
   */
  private getBestSizeUnit(products: IProduct[]): { size?: string; unit?: string } {
    // Prefer products with both size and unit
    const productsWithSize = products.filter(p => p.size && p.size.trim() !== '');
    const productsWithUnit = products.filter(p => p.unit && p.unit.trim() !== '');

    if (productsWithSize.length > 0) {
      const bestSizeProduct = productsWithSize[0];
      return {
        size: bestSizeProduct.size,
        unit: bestSizeProduct.unit || productsWithUnit[0]?.unit,
      };
    }

    if (productsWithUnit.length > 0) {
      return {
        size: undefined,
        unit: productsWithUnit[0].unit,
      };
    }

    return { size: undefined, unit: undefined };
  }

  /**
   * Convert consolidated product to IUnifiedProduct for compatibility
   */
  convertToUnifiedProduct(consolidated: ConsolidatedProduct): IUnifiedProduct {
    return {
      id: consolidated.id,
      name: consolidated.name,
      brand: consolidated.brand,
      category: consolidated.category,
      imageUrl: consolidated.imageUrl,
      size: consolidated.size,
      unit: consolidated.unit,
      storePrices: consolidated.storePrices,
      allStores: consolidated.allStores,
      lowestPrice: consolidated.lowestPrice,
      highestPrice: consolidated.highestPrice,
      maxSavings: consolidated.maxSavings,
      availableStores: consolidated.availableStores,
      confidence: consolidated.confidence,
      consolidatedAt: consolidated.consolidatedAt.toISOString(),
    };
  }

  /**
   * Get statistics about consolidation
   */
  getConsolidationStats(
    originalProducts: IProduct[], 
    consolidatedProducts: ConsolidatedProduct[]
  ) {
    const originalCount = originalProducts.length;
    const consolidatedCount = consolidatedProducts.length;
    const reductionCount = originalCount - consolidatedCount;
    const reductionPercentage = originalCount > 0 ? (reductionCount / originalCount) * 100 : 0;

    const crossStoreProducts = consolidatedProducts.filter(p => p.availableStores.length > 1);
    const totalSavingsOpportunities = consolidatedProducts
      .filter(p => p.maxSavings > 0)
      .reduce((sum, p) => sum + p.maxSavings, 0);

    return {
      originalCount,
      consolidatedCount,
      reductionCount,
      reductionPercentage,
      crossStoreProducts: crossStoreProducts.length,
      totalSavingsOpportunities,
      averageConfidence: consolidatedProducts.reduce((sum, p) => sum + p.confidence, 0) / consolidatedProducts.length,
    };
  }
}

// Export singleton instance
export const consolidatedProductService = new ConsolidatedProductService();
