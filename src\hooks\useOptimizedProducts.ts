import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { supabase } from '../supabase/client';
import { useProductCache } from './useProductCache';
import { productDeduplicationService, IUnifiedProduct } from '../services/productDeduplicationService';

interface Product {
  id: string;
  name: string;
  price: number;
  current_price?: number;
  original_price?: number;
  store: string;
  category?: string;
  brand?: string;
  unit?: string;
  size?: string;
  image_url?: string;
  is_available?: boolean;
  is_on_sale?: boolean;
  last_updated?: string;
}

// Use the enhanced ProductGroup from deduplication service
interface ProductGroup extends IUnifiedProduct {}

interface UseOptimizedProductsProps {
  pageSize?: number;
  searchQuery?: string;
  selectedCategory?: string;
  selectedStores?: string[];
  sortBy?: string;
  enableCache?: boolean;
}

interface LoadingState {
  initial: boolean;
  loadingMore: boolean;
  refreshing: boolean;
  error: string | null;
}

export const useOptimizedProducts = ({
  pageSize = 100, // Increased from 20 to 100 to show more products per page
  searchQuery = '',
  selectedCategory = 'All',
  selectedStores = ['woolworths', 'newworld', 'paknsave'],
  sortBy = 'relevance',
  enableCache = true,
}: UseOptimizedProductsProps) => {
  const [products, setProducts] = useState<Product[]>([]);
  const [productGroups, setProductGroups] = useState<ProductGroup[]>([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loadingState, setLoadingState] = useState<LoadingState>({
    initial: true,
    loadingMore: false,
    refreshing: false,
    error: null,
  });

  const { getCachedData, setCachedData } = useProductCache();
  const abortControllerRef = useRef<AbortController | null>(null);
  const lastRequestRef = useRef<string>('');

  // Debounced search to prevent excessive API calls
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState(searchQuery);
  const searchTimeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    
    searchTimeoutRef.current = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 300); // 300ms debounce

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [searchQuery]);

  // Generate request signature for deduplication
  const getRequestSignature = useCallback(() => {
    return JSON.stringify({
      searchQuery: debouncedSearchQuery,
      selectedCategory,
      selectedStores: selectedStores.sort(),
      sortBy,
      page,
    });
  }, [debouncedSearchQuery, selectedCategory, selectedStores, sortBy, page]);

  // Enhanced product grouping using deduplication service
  const groupedProducts = useMemo(() => {
    if (products.length === 0) return [];

    // Filter products first
    let filteredProducts = products;

    // Apply search filter
    if (debouncedSearchQuery.trim()) {
      const query = debouncedSearchQuery.toLowerCase();
      filteredProducts = filteredProducts.filter(product => 
        product.name.toLowerCase().includes(query) ||
        (product.brand && product.brand.toLowerCase().includes(query)) ||
        (product.category && product.category.toLowerCase().includes(query))
      );
    }

    // Apply category filter
    if (selectedCategory !== 'All') {
      filteredProducts = filteredProducts.filter(product => 
        product.category === selectedCategory
      );
    }

    // Apply store filter
    filteredProducts = filteredProducts.filter(product => 
      selectedStores.includes(product.store)
    );

    // Use the enhanced deduplication service
    let result = productDeduplicationService.groupProducts(filteredProducts);

    // Apply sorting to grouped results
    switch (sortBy) {
      case 'price_asc':
        result.sort((a, b) => a.lowestPrice - b.lowestPrice);
        break;
      case 'price_desc':
        result.sort((a, b) => b.lowestPrice - a.lowestPrice);
        break;
      case 'name_asc':
        result.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 'name_desc':
        result.sort((a, b) => b.name.localeCompare(a.name));
        break;
      default:
        // relevance - use default sorting from deduplication service
        break;
    }

    return result;
  }, [products, debouncedSearchQuery, selectedCategory, selectedStores, sortBy]);

  // Update product groups when groupedProducts changes
  useEffect(() => {
    setProductGroups(groupedProducts);
  }, [groupedProducts]);

  // Optimized product loading with caching and request deduplication
  // Removed old loadProducts function - using loadProductsStable instead



  // Track if initial load has happened
  const initialLoadRef = useRef(false);

  // Store current values in refs to avoid stale closures
  const currentValuesRef = useRef({
    debouncedSearchQuery,
    pageSize,
  });

  // Update refs on every render
  currentValuesRef.current = {
    debouncedSearchQuery,
    pageSize,
  };

  // Create a completely stable load function using refs - NO useCallback
  const loadProducts = async (pageNum: number, append: boolean = false) => {
    try {
      setLoadingState(prev => ({
        ...prev,
        initial: pageNum === 1 && !append,
        loadingMore: pageNum > 1,
        error: null,
      }));

      // Get current values from refs to avoid stale closures
      const { debouncedSearchQuery: currentSearchQuery, pageSize: currentPageSize } = currentValuesRef.current;

      // Query products with price data from the prices table
      const { data, error } = await supabase
        .from('products')
        .select(`
          *,
          prices!inner (
            price,
            store_id,
            recorded_at,
            stores!inner (
              name,
              display_name
            )
          )
        `)
        .ilike('name', `%${currentSearchQuery}%`)
        .range((pageNum - 1) * currentPageSize, pageNum * currentPageSize - 1)
        .order('name', { ascending: true }); // Add consistent ordering

      if (error) throw error;

      console.log(`📦 useOptimizedProducts: Loaded ${data?.length || 0} products from Supabase (page ${pageNum}, query: "${currentSearchQuery}")`);

      // Transform the joined data to include price and store information
      const transformedProducts = (data || []).map(item => {
        // Get the latest price record (there might be multiple)
        const latestPrice = item.prices && item.prices.length > 0 
          ? item.prices.reduce((latest: any, current: any) => 
              new Date(current.recorded_at) > new Date(latest.recorded_at) ? current : latest
            )
          : null;

        return {
          ...item,
          // Set current_price from the prices table if not already set
          current_price: item.current_price || latestPrice?.price,
          price: item.price || latestPrice?.price,
          // Set store from the prices table if not already set
          store: item.store || latestPrice?.stores?.name,
          store_name: item.store_name || latestPrice?.stores?.display_name,
          // Remove the nested prices array to match expected Product interface
          prices: undefined
        };
      });

      const products = transformedProducts;
      const hasMoreData = products.length === currentPageSize;

      // Update state
      if (append) {
        setProducts(prev => [...prev, ...products]);
      } else {
        setProducts(products);
      }
      setHasMore(hasMoreData);
      setPage(pageNum);

    } catch (error) {
      console.error('❌ Error loading products:', error);
      setLoadingState(prev => ({ ...prev, error: error instanceof Error ? error.message : 'Unknown error' }));
    } finally {
      setLoadingState(prev => ({
        ...prev,
        initial: false,
        loadingMore: false,
        refreshing: false,
      }));
    }
  };

  // Store current state values in refs to avoid dependency issues
  const currentStateRef = useRef({ page, hasMore, loadingState });
  currentStateRef.current = { page, hasMore, loadingState };

  // Load more products - NO useCallback
  const loadMore = () => {
    const { page: currentPage, hasMore: currentHasMore, loadingState: currentLoadingState } = currentStateRef.current;
    if (!currentLoadingState.loadingMore && currentHasMore && !currentLoadingState.initial) {
      loadProducts(currentPage + 1, true);
    }
  };

  // Refresh products - NO useCallback
  const refresh = () => {
    setLoadingState(prev => ({ ...prev, refreshing: true }));
    setPage(1);
    loadProducts(1, false);
  };

  // Track previous search query to detect changes
  const prevSearchQueryRef = useRef(debouncedSearchQuery);

  // Reset when search query changes - NO function dependencies
  useEffect(() => {
    if (initialLoadRef.current && prevSearchQueryRef.current !== debouncedSearchQuery) {
      prevSearchQueryRef.current = debouncedSearchQuery;
      setProducts([]);
      setPage(1);
      setHasMore(true);
      loadProducts(1, false);
    }
  }, [debouncedSearchQuery]); // Only depend on the search query

  // Initial load - only run once
  useEffect(() => {
    if (!initialLoadRef.current) {
      initialLoadRef.current = true;
      prevSearchQueryRef.current = debouncedSearchQuery;
      loadProducts(1, false);
    }
  }, []); // NO dependencies - run only once

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  return {
    products,
    productGroups,
    loadingState,
    hasMore,
    page,
    loadMore,
    refresh,
    loadProducts,
  };
};
