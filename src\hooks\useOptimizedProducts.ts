import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { supabase } from '../supabase/client';
import { useProductCache } from './useProductCache';
import { productDeduplicationService } from '../services/productDeduplicationService';
import { IUnifiedProduct } from '../types/shoppingList';

// Updated interface to match consolidated product structure
interface ConsolidatedProductData {
  id: string;
  display_name: string;
  normalized_name: string;
  primary_size?: string;
  brand?: { name: string } | null;
  category?: { name: string; parent_id?: string; level: number } | null;
  store_prices?: Array<{
    store: { name: string; logo_url?: string };
    price: number;
    recorded_at: string;
    is_special: boolean;
  }>;
}

// Legacy Product interface for backwards compatibility
interface Product {
  id: string;
  name: string;
  price: number;
  current_price?: number;
  original_price?: number;
  store: string;
  category?: string;
  brand?: string;
  unit?: string;
  size?: string;
  image_url?: string;
  is_available?: boolean;
  is_on_sale?: boolean;
  last_updated?: string;
}

// Use the enhanced ProductGroup from deduplication service
interface ProductGroup extends IUnifiedProduct {}

interface UseOptimizedProductsProps {
  pageSize?: number;
  searchQuery?: string;
  selectedCategory?: string;
  selectedStores?: string[];
  sortBy?: string;
  enableCache?: boolean;
}

interface LoadingState {
  initial: boolean;
  loadingMore: boolean;
  refreshing: boolean;
  error: string | null;
}

export const useOptimizedProducts = ({
  pageSize = 100, // Increased from 20 to 100 to show more products per page
  searchQuery = '',
  selectedCategory = 'All',
  selectedStores = ['woolworths', 'newworld', 'paknsave'],
  sortBy = 'relevance',
  enableCache = true,
}: UseOptimizedProductsProps) => {
  const [products, setProducts] = useState<Product[]>([]);
  const [productGroups, setProductGroups] = useState<ProductGroup[]>([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loadingState, setLoadingState] = useState<LoadingState>({
    initial: true,
    loadingMore: false,
    refreshing: false,
    error: null,
  });

  const { getCachedData, setCachedData } = useProductCache();
  const abortControllerRef = useRef<AbortController | null>(null);
  const lastRequestRef = useRef<string>('');

  // Debounced search to prevent excessive API calls
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState(searchQuery);
  const searchTimeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    
    searchTimeoutRef.current = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 300); // 300ms debounce

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [searchQuery]);

  // Generate request signature for deduplication
  const getRequestSignature = useCallback(() => {
    return JSON.stringify({
      searchQuery: debouncedSearchQuery,
      selectedCategory,
      selectedStores: selectedStores.sort(),
      sortBy,
      page,
    });
  }, [debouncedSearchQuery, selectedCategory, selectedStores, sortBy, page]);

  // Enhanced product grouping using deduplication service
  const groupedProducts = useMemo(() => {
    if (products.length === 0) return [];

    // Filter products first
    let filteredProducts = products;

    // Apply search filter
    if (debouncedSearchQuery.trim()) {
      const query = debouncedSearchQuery.toLowerCase();
      filteredProducts = filteredProducts.filter(product => 
        product.name.toLowerCase().includes(query) ||
        (product.brand && product.brand.toLowerCase().includes(query)) ||
        (product.category && product.category.toLowerCase().includes(query))
      );
    }

    // Apply category filter
    if (selectedCategory !== 'All') {
      filteredProducts = filteredProducts.filter(product => 
        product.category === selectedCategory
      );
    }

    // Apply store filter
    filteredProducts = filteredProducts.filter(product => 
      selectedStores.includes(product.store)
    );

    // Use the enhanced deduplication service
    let result = productDeduplicationService.groupProducts(filteredProducts);

    // Apply sorting to grouped results
    switch (sortBy) {
      case 'price_asc':
        result.sort((a, b) => a.lowestPrice - b.lowestPrice);
        break;
      case 'price_desc':
        result.sort((a, b) => b.lowestPrice - a.lowestPrice);
        break;
      case 'name_asc':
        result.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 'name_desc':
        result.sort((a, b) => b.name.localeCompare(a.name));
        break;
      default:
        // relevance - use default sorting from deduplication service
        break;
    }

    return result;
  }, [products, debouncedSearchQuery, selectedCategory, selectedStores, sortBy]);

  // Update product groups when groupedProducts changes
  useEffect(() => {
    setProductGroups(groupedProducts);
  }, [groupedProducts]);

  // Optimized product loading with caching and request deduplication
  // Removed old loadProducts function - using loadProductsStable instead



  // Track if initial load has happened
  const initialLoadRef = useRef(false);

  // Store current values in refs to avoid stale closures
  const currentValuesRef = useRef({
    debouncedSearchQuery,
    pageSize,
  });

  // Update refs on every render
  currentValuesRef.current = {
    debouncedSearchQuery,
    pageSize,
  };

  // Transform consolidated products to legacy format for backwards compatibility
  const transformConsolidatedToLegacy = (consolidatedProducts: any[]): Product[] => {
    const legacyProducts: Product[] = [];
    
    consolidatedProducts.forEach(consolidatedProduct => {
      const brandName = consolidatedProduct.brands?.name || '';
      const categoryName = consolidatedProduct.category_hierarchy?.name || '';
      
      // Create a product entry for each store that has this product
      if (consolidatedProduct.consolidated_prices) {
        consolidatedProduct.consolidated_prices
          .filter((priceEntry: any) => priceEntry.was_available)
          .forEach((priceEntry: any) => {
            legacyProducts.push({
              id: `${consolidatedProduct.id}_${priceEntry.stores.name}`,
              name: consolidatedProduct.display_name,
              price: priceEntry.price,
              current_price: priceEntry.price,
              store: priceEntry.stores.name.toLowerCase(),
              category: categoryName,
              brand: brandName,
              size: consolidatedProduct.primary_size,
              image_url: consolidatedProduct.image_url,
              is_available: true,
              is_on_sale: priceEntry.is_special,
              last_updated: priceEntry.recorded_at,
            });
          });
      }
    });
    
    return legacyProducts;
  };

  // Updated load function to use consolidated schema
  const loadProducts = async (pageNum: number, append: boolean = false) => {
    try {
      setLoadingState(prev => ({
        ...prev,
        initial: pageNum === 1 && !append,
        loadingMore: pageNum > 1,
        error: null,
      }));

      // Get current values from refs to avoid stale closures
      const { debouncedSearchQuery: currentSearchQuery, pageSize: currentPageSize } = currentValuesRef.current;

      // Try consolidated products first, fallback to legacy products
      let data: any[] = [];
      let error: any = null;
      
      try {
        // Attempt to use consolidated products
        const consolidatedQuery = supabase
          .from('consolidated_products')
          .select(`
            *,
            brands (name),
            category_hierarchy (name, parent_id, level),
            consolidated_prices (
              price,
              recorded_at,
              is_special,
              was_available,
              stores (name, logo_url)
            )
          `)
          .order('display_name', { ascending: true });

        if (currentSearchQuery.trim()) {
          consolidatedQuery.or(`display_name.ilike.%${currentSearchQuery}%,normalized_name.ilike.%${currentSearchQuery}%`);
        }

        consolidatedQuery.range((pageNum - 1) * currentPageSize, pageNum * currentPageSize - 1);

        const consolidatedResult = await consolidatedQuery;
        
        if (consolidatedResult.error) {
          throw consolidatedResult.error;
        }
        
        data = consolidatedResult.data || [];
        console.log(`📦 useOptimizedProducts: Loaded ${data.length} consolidated products from Supabase (page ${pageNum}, query: "${currentSearchQuery}")`);
        
        // Transform consolidated data to legacy format
        const transformedProducts = transformConsolidatedToLegacy(data);
        const hasMoreData = data.length === currentPageSize;
        
        console.log(`🔄 Transformed ${data.length} consolidated products into ${transformedProducts.length} legacy format products`);
        
        // Update state with transformed products
        if (append) {
          setProducts(prev => [...prev, ...transformedProducts]);
        } else {
          setProducts(transformedProducts);
        }
        setHasMore(hasMoreData);
        setPage(pageNum);
        
      } catch (consolidatedError) {
        console.log(`❌ Consolidated products failed, falling back to legacy products:`, consolidatedError);
        
        // Fallback to legacy products table
        const legacyQuery = supabase
          .from('products')
          .select('*')
          .not('price', 'is', null);
        
        if (currentSearchQuery.trim()) {
          legacyQuery.ilike('name', `%${currentSearchQuery}%`);
        }
        
        legacyQuery
          .range((pageNum - 1) * currentPageSize, pageNum * currentPageSize - 1)
          .order('name', { ascending: true });

        const legacyResult = await legacyQuery;
        
        if (legacyResult.error) throw legacyResult.error;
        
        data = legacyResult.data || [];
        const hasMoreData = data.length === currentPageSize;
        
        console.log(`📦 useOptimizedProducts: Loaded ${data.length} legacy products from Supabase (page ${pageNum}, query: "${currentSearchQuery}")`);
        
        // Update state with legacy products directly
        if (append) {
          setProducts(prev => [...prev, ...data]);
        } else {
          setProducts(data as Product[]);
        }
        setHasMore(hasMoreData);
        setPage(pageNum);
      }

    } catch (error) {
      console.error('❌ Error loading consolidated products:', error);
      setLoadingState(prev => ({ ...prev, error: error instanceof Error ? error.message : 'Unknown error' }));
    } finally {
      setLoadingState(prev => ({
        ...prev,
        initial: false,
        loadingMore: false,
        refreshing: false,
      }));
    }
  };

  // Store current state values in refs to avoid dependency issues
  const currentStateRef = useRef({ page, hasMore, loadingState });
  currentStateRef.current = { page, hasMore, loadingState };

  // Load more products - NO useCallback
  const loadMore = () => {
    const { page: currentPage, hasMore: currentHasMore, loadingState: currentLoadingState } = currentStateRef.current;
    if (!currentLoadingState.loadingMore && currentHasMore && !currentLoadingState.initial) {
      loadProducts(currentPage + 1, true);
    }
  };

  // Refresh products - NO useCallback
  const refresh = () => {
    setLoadingState(prev => ({ ...prev, refreshing: true }));
    setPage(1);
    loadProducts(1, false);
  };

  // Track previous search query to detect changes
  const prevSearchQueryRef = useRef(debouncedSearchQuery);

  // Reset when search query changes - NO function dependencies
  useEffect(() => {
    if (initialLoadRef.current && prevSearchQueryRef.current !== debouncedSearchQuery) {
      prevSearchQueryRef.current = debouncedSearchQuery;
      setProducts([]);
      setPage(1);
      setHasMore(true);
      loadProducts(1, false);
    }
  }, [debouncedSearchQuery]); // Only depend on the search query

  // Initial load - only run once
  useEffect(() => {
    if (!initialLoadRef.current) {
      initialLoadRef.current = true;
      prevSearchQueryRef.current = debouncedSearchQuery;
      loadProducts(1, false);
    }
  }, []); // NO dependencies - run only once

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  return {
    products,
    productGroups,
    loadingState,
    hasMore,
    page,
    loadMore,
    refresh,
    loadProducts,
  };
};
