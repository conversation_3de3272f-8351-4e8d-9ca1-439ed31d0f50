/**
 * TEST PAK'NSAVE PRICING FIXES
 * 
 * Comprehensive test to verify that the Pak'nSave pricing fixes resolve
 * the per-kg vs fixed-size matching issues.
 */

import { createClient } from '@supabase/supabase-js';
import { normalizeSize, sizesMatch, getSizeGroupingKey } from '../utils/stringMatching';
import { unitPriceCalculationService } from '../services/unitPriceCalculationService';
import { ProductDeduplicationService } from '../services/productDeduplicationService';
import { IProduct } from '../types/shoppingList';

const supabaseUrl = 'https://emjwniuqwhvohjassnrg.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVtanduaXVxd2h2b2hqYXNzbnJnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjQwMTYwNSwiZXhwIjoyMDY3OTc3NjA1fQ.oTowRoNAjlUmk10O9pSMK2BvL0XlyBb5orVRrlEryHs';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testPaknsaveFixes() {
  console.log('🧪 TESTING PAK\'NSAVE PRICING FIXES...\n');

  try {
    // Test 1: Verify enhanced size normalization handles per-kg
    console.log('1. Testing Enhanced Size Normalization...');
    
    const testCases = [
      { input: 'per kg', expected: { value: 1000, unit: 'per-kg' } },
      { input: 'per 100g', expected: { value: 100, unit: 'per-100g' } },
      { input: 'per litre', expected: { value: 1000, unit: 'per-litre' } },
      { input: '500g', expected: { value: 500, unit: 'g' } },
      { input: '1L', expected: { value: 1000, unit: 'ml' } },
    ];

    let passedTests = 0;
    testCases.forEach((test, index) => {
      const result = normalizeSize(test.input);
      const passed = result.valid && 
                    result.value === test.expected.value && 
                    result.unit === test.expected.unit;
      
      console.log(`   ${index + 1}. "${test.input}" → ${result.value}${result.unit} ${passed ? '✅' : '❌'}`);
      if (passed) passedTests++;
    });

    console.log(`   Results: ${passedTests}/${testCases.length} tests passed`);

    // Test 2: Verify per-kg vs fixed-size matching is prevented
    console.log('\n2. Testing Per-kg vs Fixed-size Matching Prevention...');
    
    const matchingTests = [
      { size1: 'per kg', size2: '500g', shouldMatch: false },
      { size1: 'per kg', size2: 'per kg', shouldMatch: true },
      { size1: '500g', size2: '500g', shouldMatch: true },
      { size1: 'per litre', size2: '1L', shouldMatch: false },
      { size1: 'per litre', size2: 'per litre', shouldMatch: true },
    ];

    let correctMatches = 0;
    matchingTests.forEach((test, index) => {
      const matches = sizesMatch(test.size1, test.size2, true);
      const correct = matches === test.shouldMatch;
      
      console.log(`   ${index + 1}. "${test.size1}" vs "${test.size2}": ${matches ? 'MATCH' : 'NO MATCH'} ${correct ? '✅' : '❌'}`);
      if (correct) correctMatches++;
    });

    console.log(`   Results: ${correctMatches}/${matchingTests.length} matching tests correct`);

    // Test 3: Test unit price calculations
    console.log('\n3. Testing Unit Price Calculations...');
    
    const testProducts: IProduct[] = [
      {
        id: '1',
        name: 'Roasted Salted Pistachios',
        price: 44.90,
        store: 'paknsave',
        size: 'per kg'
      },
      {
        id: '2',
        name: 'Roasted Salted Pistachios',
        price: 8.00,
        store: 'woolworths',
        size: '170g'
      },
      {
        id: '3',
        name: 'Fresh Milk',
        price: 3.50,
        store: 'paknsave',
        size: '1L'
      },
      {
        id: '4',
        name: 'Fresh Milk',
        price: 7.00,
        store: 'newworld',
        size: '2L'
      }
    ];

    testProducts.forEach((product, index) => {
      const unitPrice = unitPriceCalculationService.calculateUnitPrice(product);
      console.log(`   ${index + 1}. ${product.name} (${product.store})`);
      console.log(`      Original: $${product.price} (${product.size})`);
      console.log(`      Unit Price: ${unitPriceCalculationService.formatUnitPrice(unitPrice)}`);
      console.log(`      Comparable: ${unitPrice.isComparable ? '✅' : '❌'}`);
      console.log('');
    });

    // Test 4: Check if problematic matches are detected
    console.log('4. Testing Problematic Match Detection...');
    
    const problematicProducts = [testProducts[0], testProducts[1]]; // per kg vs 170g
    const analysis = unitPriceCalculationService.detectProblematicMatches(problematicProducts);
    
    console.log(`   Has Problems: ${analysis.hasProblems ? '✅ DETECTED' : '❌ NOT DETECTED'}`);
    if (analysis.issues.length > 0) {
      console.log('   Issues Found:');
      analysis.issues.forEach(issue => console.log(`     - ${issue}`));
    }
    if (analysis.recommendations.length > 0) {
      console.log('   Recommendations:');
      analysis.recommendations.forEach(rec => console.log(`     - ${rec}`));
    }

    // Test 5: Test deduplication with problematic products
    console.log('\n5. Testing Deduplication with Problematic Products...');
    
    const deduplicationService = new ProductDeduplicationService();
    const result = deduplicationService.deduplicateProducts(testProducts);
    
    console.log(`   Original products: ${testProducts.length}`);
    console.log(`   After deduplication: ${result.stats.unifiedCount}`);
    console.log(`   Groups created: ${result.stats.groupCount}`);
    
    // Each different size should be in its own group
    const expectedGroups = 4; // per kg, 170g, 1L, 2L should all be separate
    const correctGrouping = result.stats.groupCount === expectedGroups;
    
    console.log(`   Expected ${expectedGroups} groups, got ${result.stats.groupCount} ${correctGrouping ? '✅' : '❌'}`);

    if (result.groups.length > 0) {
      result.groups.forEach((group, index) => {
        const sizes = group.products.map(p => p.product.size);
        const prices = group.products.map(p => p.product.price);
        
        console.log(`     Group ${index + 1}: [${sizes.join(', ')}] - Prices: $${Math.min(...prices).toFixed(2)} - $${Math.max(...prices).toFixed(2)}`);
      });
    }

    console.log('\n✅ Pak\'nSave pricing fixes test completed!');
    
    return {
      normalizationTests: `${passedTests}/${testCases.length}`,
      matchingTests: `${correctMatches}/${matchingTests.length}`,
      correctGrouping,
      problemsDetected: analysis.hasProblems
    };

  } catch (error) {
    console.error('❌ Pak\'nSave fixes test failed:', error);
    throw error;
  }
}

// Run the test
if (require.main === module) {
  testPaknsaveFixes()
    .then((results) => {
      console.log('\n📊 PAK\'NSAVE FIXES TEST SUMMARY:');
      console.log('='.repeat(60));
      console.log(`Normalization Tests: ${results?.normalizationTests || '0/0'} passed`);
      console.log(`Matching Tests: ${results?.matchingTests || '0/0'} correct`);
      console.log(`Correct Grouping: ${results?.correctGrouping ? 'YES' : 'NO'}`);
      console.log(`Problems Detected: ${results?.problemsDetected ? 'YES' : 'NO'}`);
      
      const allTestsPassed = 
        results?.normalizationTests?.endsWith('5/5') &&
        results?.matchingTests?.endsWith('5/5') &&
        results?.correctGrouping &&
        results?.problemsDetected;
      
      if (allTestsPassed) {
        console.log('\n🎉 SUCCESS: All Pak\'nSave pricing fixes working correctly!');
        console.log('   ✅ Per-kg products no longer match with fixed-size products');
        console.log('   ✅ Unit price calculations are accurate');
        console.log('   ✅ Problematic matches are detected and prevented');
        console.log('   ✅ Size-aware grouping prevents misleading price ranges');
        console.log('   ✅ Ready for production deployment');
      } else {
        console.log('\n⚠️  ISSUES FOUND: Some fixes need attention');
        if (!results?.correctGrouping) console.log('   - Product grouping still has issues');
        if (!results?.problemsDetected) console.log('   - Problem detection not working');
      }
      console.log('='.repeat(60));
    })
    .catch((error) => {
      console.error('❌ Test failed:', error);
    });
}

export { testPaknsaveFixes };
