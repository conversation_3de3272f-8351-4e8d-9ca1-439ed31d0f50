import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Modal,
  ScrollView,
  Image,
} from 'react-native';
import { Swipeable } from 'react-native-gesture-handler';
import { useTheme } from '../context/ThemeContext';
import { theme } from '../styles';

interface StorePrice {
  store: 'woolworths' | 'newworld' | 'paknsave';
  price?: number;
  available: boolean;
  brand?: string;
  size?: string;
}

interface SizeVariant {
  size: string;
  storePrices: StorePrice[];
}

interface CompactShoppingListItemProps {
  id: string;
  name: string;
  checked?: boolean;
  quantity?: number;
  unit?: string;
  category?: string;
  selectedBrand?: string;
  selectedSize?: string;
  storePrices: StorePrice[];
  sizeVariants?: SizeVariant[]; // Available size options for the product
  onToggleChecked: (itemId: string) => void;
  onQuantityChange?: (itemId: string, quantity: number) => void;
  onBrandChange?: (itemId: string, brand?: string) => void;
  onSizeChange?: (itemId: string, size: string) => void;
  onEdit?: (itemId: string) => void;
  onDelete: (itemId: string) => void;
  showPrices?: boolean;
}

export const CompactShoppingListItem: React.FC<CompactShoppingListItemProps> = ({
  id,
  name,
  checked = false,
  quantity = 1,
  unit,
  category,
  selectedBrand,
  selectedSize,
  storePrices,
  sizeVariants = [],
  onToggleChecked,
  onQuantityChange,
  onBrandChange,
  onSizeChange,
  onEdit,
  onDelete,
  showPrices = true,
}) => {
  const { colors } = useTheme();
  const [showSizeModal, setShowSizeModal] = useState(false);

  // Store configuration
  const storeConfig = {
    woolworths: {
      name: 'Woolworths',
      icon: '🍎',
      logo: require('../../assets/images/supermarkets/woolworths.webp'),
      color: '#00A651',
      backgroundColor: '#E8F5E8',
    },
    paknsave: {
      name: "Pak'nSave",
      icon: '💰',
      logo: require('../../assets/images/supermarkets/paknsave.png'),
      color: '#FFD100',
      backgroundColor: '#FFF9E6',
    },
    newworld: {
      name: 'New World',
      icon: '🛒',
      logo: require('../../assets/images/supermarkets/newworld.png'),
      color: '#E31E24',
      backgroundColor: '#FFE8E8',
    },
  };

  const handleCheckboxPress = () => {
    onToggleChecked(id);
  };

  const handleQuantityPress = () => {
    if (!onQuantityChange) return;
    const newQuantity = quantity >= 5 ? 1 : quantity + 1;
    onQuantityChange(id, newQuantity);
  };

  // Check if product is a liquid (for size variants)
  const isLiquidProduct = () => {
    const liquidKeywords = ['milk', 'juice', 'water', 'oil', 'vinegar', 'sauce', 'syrup', 'cream'];
    return liquidKeywords.some(keyword => 
      name.toLowerCase().includes(keyword) || 
      selectedSize?.toLowerCase().includes('l') ||
      selectedSize?.toLowerCase().includes('ml')
    );
  };

  // Available brands function removed - using SimpleBrandDropdown instead

  // Find the lowest available price
  const availablePrices = storePrices.filter(sp => sp.available && sp.price);
  const lowestPrice = availablePrices.length > 0 
    ? Math.min(...availablePrices.map(sp => sp.price!))
    : null;
  
  // Debug logging for storePrices
  console.log(`💰 ${name} (${selectedBrand}) storePrices:`, storePrices);

  const renderStorePrice = (store: 'woolworths' | 'newworld' | 'paknsave') => {
    const storePrice = storePrices.find(sp => sp.store === store);
    const config = storeConfig[store];
    const isLowestPrice = storePrice?.price === lowestPrice && lowestPrice !== null;
    const isAvailable = storePrice?.available && storePrice?.price;
    
    // Debug logging
    console.log(`🏪 ${store} store price data:`, {
      storePrice,
      isAvailable,
      price: storePrice?.price,
      available: storePrice?.available
    });

    return (
      <View key={store} style={styles.storeSection}>
        {/* Store Icon */}
        <View style={[
          styles.storeIconContainer,
          { 
            backgroundColor: isLowestPrice ? config.backgroundColor : colors.backgroundSecondary,
            borderColor: isLowestPrice ? config.color : 'transparent',
          }
        ]}>
          {config.logo ? (
            <Image
              source={config.logo}
              style={styles.storeLogoImage}
              resizeMode="contain"
            />
          ) : (
            <Text style={styles.storeIcon}>
              {config.icon}
            </Text>
          )}
        </View>

        {/* Price */}
        {showPrices && (
          <View style={styles.priceContainer}>
            {isAvailable ? (
              <Text style={[
                styles.price,
                { 
                  color: isLowestPrice ? config.color : colors.text,
                  fontWeight: isLowestPrice ? '600' : '500',
                }
              ]}>
                ${storePrice.price!.toFixed(2)}
              </Text>
            ) : (
              <Text style={[styles.notAvailable, { color: colors.textTertiary }]}>
                N/A
              </Text>
            )}
          </View>
        )}
      </View>
    );
  };

  // Brand modal removed - using SimpleBrandDropdown instead

  const renderSizeModal = () => {
    if (!isLiquidProduct() || sizeVariants.length === 0) return null;
    
    return (
      <Modal
        visible={showSizeModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowSizeModal(false)}
      >
        <TouchableOpacity 
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowSizeModal(false)}
        >
          <View style={[styles.modalContent, { backgroundColor: colors.surface }]}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              Select Size
            </Text>
            <ScrollView style={styles.optionsList}>
              {sizeVariants.map((variant) => {
                const hasAvailablePrices = variant.storePrices.some(sp => sp.available && sp.price);
                if (!hasAvailablePrices) return null;
                
                const variantLowestPrice = Math.min(
                  ...variant.storePrices.filter(sp => sp.available && sp.price).map(sp => sp.price!)
                );
                
                return (
                  <TouchableOpacity
                    key={variant.size}
                    style={[
                      styles.optionItem,
                      { borderBottomColor: colors.border },
                      variant.size === selectedSize && { backgroundColor: colors.backgroundSecondary }
                    ]}
                    onPress={() => {
                      onSizeChange?.(id, variant.size);
                      setShowSizeModal(false);
                    }}
                  >
                    <View style={styles.sizeOptionContent}>
                      <Text style={[
                        styles.optionText,
                        { color: colors.text },
                        variant.size === selectedSize && { fontWeight: '600' }
                      ]}>
                        {variant.size}
                      </Text>
                      <Text style={[styles.sizePrice, { color: colors.textSecondary }]}>
                        from ${variantLowestPrice.toFixed(2)}
                      </Text>
                    </View>
                    {variant.size === selectedSize && (
                      <Text style={[styles.checkmark, { color: theme.colors.primary }]}>✓</Text>
                    )}
                  </TouchableOpacity>
                );
              })}
            </ScrollView>
          </View>
        </TouchableOpacity>
      </Modal>
    );
  };

  const renderRightActions = () => (
    <View style={styles.rightActions}>
      {/* Edit Action */}
      {onEdit && (
        <Animated.View style={[styles.editAction, { backgroundColor: '#4A90E2' }]}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => onEdit(id)}
          >
            <Text style={styles.actionIcon}>✏️</Text>
            <Text style={styles.actionLabel}>Edit</Text>
          </TouchableOpacity>
        </Animated.View>
      )}
      
      {/* Delete Action */}
      <Animated.View style={[styles.deleteAction, { backgroundColor: '#FF6B6B' }]}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => onDelete(id)}
        >
          <Text style={styles.actionIcon}>🗑️</Text>
          <Text style={styles.actionLabel}>Delete</Text>
        </TouchableOpacity>
      </Animated.View>
    </View>
  );

  return (
    <>
      <Swipeable renderRightActions={renderRightActions}>
        <View style={[
          styles.container,
          { 
            backgroundColor: colors.surface,
            borderBottomColor: colors.border,
          },
          checked && styles.checkedContainer
        ]}>
          {/* Header Section */}
          <View style={styles.headerSection}>
            {/* Checkbox */}
            <TouchableOpacity
              style={[
                styles.checkbox,
                {
                  backgroundColor: checked ? theme.colors.primary : 'transparent',
                  borderColor: checked ? theme.colors.primary : colors.border,
                }
              ]}
              onPress={handleCheckboxPress}
            >
              {checked && (
                <Text style={styles.checkboxCheck}>✓</Text>
              )}
            </TouchableOpacity>

            {/* Product Info */}
            <View style={styles.productInfo}>
              {/* Product Name */}
              <Text style={[
                styles.productName,
                { color: colors.text },
                checked && styles.checkedText
              ]}>
                {name}
              </Text>
              
              {/* Brand and Controls */}
              <View style={styles.brandRow}>
                <Text style={[styles.brandText, { color: colors.textSecondary }]}>
                  {selectedBrand || 'No brand selected'}
                </Text>
                
                <View style={styles.controlButtons}>
                  {/* Change Brand Button */}
                  <TouchableOpacity
                    style={[styles.changeButton, { borderColor: colors.border }]}
                    onPress={() => {
                      console.log('🎯 Change button pressed for:', name);
                      onBrandChange?.(id);
                    }}
                  >
                    <Text style={[styles.changeButtonText, { color: theme.colors.primary }]}>
                      Change
                    </Text>
                  </TouchableOpacity>
                  
                  {/* Size Button (for liquids) */}
                  {isLiquidProduct() && sizeVariants.length > 0 && (
                    <TouchableOpacity
                      style={[styles.sizeButton, { borderColor: colors.border }]}
                      onPress={() => setShowSizeModal(true)}
                    >
                      <Text style={[styles.sizeButtonText, { color: theme.colors.primary }]}>
                        {selectedSize || 'Size'} ▼
                      </Text>
                    </TouchableOpacity>
                  )}
                </View>
              </View>
            </View>

            {/* Quantity Badge */}
            {quantity > 1 && (
              <TouchableOpacity
                style={[styles.quantityBadge, { backgroundColor: theme.colors.primary }]}
                onPress={handleQuantityPress}
              >
                <Text style={styles.quantityText}>
                  {quantity}
                </Text>
              </TouchableOpacity>
            )}
          </View>

          {/* Price Comparison Section */}
          {showPrices && (
            <View style={styles.priceSection}>
              <View style={styles.storesContainer}>
                {renderStorePrice('woolworths')}
                {renderStorePrice('paknsave')}
                {renderStorePrice('newworld')}
              </View>
            </View>
          )}
        </View>
      </Swipeable>

      {/* Modals */}
      {/* Brand modal removed - using SimpleBrandDropdown instead */}
      {renderSizeModal()}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: theme.spacing.base,
    paddingVertical: theme.spacing.sm,
    borderBottomWidth: 1,
  },
  checkedContainer: {
    opacity: 0.6,
  },
  headerSection: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: theme.spacing.xs,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: theme.spacing.sm,
    marginTop: 2,
  },
  checkboxCheck: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.medium,
    lineHeight: 20,
    marginBottom: 2,
  },
  checkedText: {
    textDecorationLine: 'line-through',
  },
  brandRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  brandText: {
    fontSize: theme.typography.fontSize.sm,
    flex: 1,
  },
  controlButtons: {
    flexDirection: 'row',
    gap: theme.spacing.xs,
  },
  changeButton: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: theme.radius.sm,
    borderWidth: 1,
  },
  changeButtonText: {
    fontSize: theme.typography.fontSize.xs,
    fontWeight: theme.typography.fontWeight.medium,
  },
  sizeButton: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: theme.radius.sm,
    borderWidth: 1,
  },
  sizeButtonText: {
    fontSize: theme.typography.fontSize.xs,
    fontWeight: theme.typography.fontWeight.medium,
  },
  quantityBadge: {
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: theme.spacing.xs,
  },
  quantityText: {
    color: 'white',
    fontSize: theme.typography.fontSize.xs,
    fontWeight: theme.typography.fontWeight.semibold,
  },
  priceSection: {
    paddingTop: theme.spacing.xs,
  },
  storesContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    gap: theme.spacing.xs,
  },
  storeSection: {
    alignItems: 'center',
    gap: 4,
    flex: 1,
  },
  storeIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
  },
  storeLogoImage: {
    width: 24,
    height: 24,
  },
  storeIcon: {
    fontSize: 16,
  },
  priceContainer: {
    alignItems: 'center',
    minHeight: 24,
    justifyContent: 'center',
  },
  price: {
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.medium,
    textAlign: 'center',
  },
  notAvailable: {
    fontSize: theme.typography.fontSize.xs,
    fontStyle: 'italic',
    textAlign: 'center',
  },
  rightActions: {
    flexDirection: 'row',
  },
  editAction: {
    justifyContent: 'center',
    alignItems: 'center',
    width: 60,
  },
  deleteAction: {
    justifyContent: 'center',
    alignItems: 'center',
    width: 60,
  },
  actionButton: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    width: '100%',
  },
  actionIcon: {
    fontSize: 18,
    marginBottom: 2,
  },
  actionLabel: {
    color: 'white',
    fontSize: theme.typography.fontSize.xs,
    fontWeight: theme.typography.fontWeight.semibold,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '80%',
    maxHeight: '60%',
    borderRadius: theme.radius.lg,
    padding: theme.spacing.base,
  },
  modalTitle: {
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.semibold,
    textAlign: 'center',
    marginBottom: theme.spacing.base,
  },
  optionsList: {
    maxHeight: 300,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: theme.spacing.sm,
    borderBottomWidth: 1,
  },
  optionText: {
    fontSize: theme.typography.fontSize.base,
  },
  sizeOptionContent: {
    flex: 1,
  },
  sizePrice: {
    fontSize: theme.typography.fontSize.sm,
    marginTop: 2,
  },
  checkmark: {
    fontSize: 16,
    fontWeight: 'bold',
  },
});