/**
 * Modern Search Bar - Premium Design
 * 
 * Sophisticated search component with smooth animations,
 * micro-interactions, and premium visual effects.
 */

import React, { useRef, useState, useEffect } from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  Animated,
  StyleSheet,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
// import { BlurView } from 'expo-blur'; // Optional dependency
import { getGroceryTheme, ThemeMode } from '../styles/modernGroceryTheme';

interface ModernSearchBarProps {
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  onFocus?: () => void;
  onBlur?: () => void;
  themeMode?: ThemeMode;
  autoFocus?: boolean;
  editable?: boolean;
}

export const ModernSearchBar: React.FC<ModernSearchBarProps> = ({
  value,
  onChangeText,
  placeholder = 'Search products...',
  onFocus,
  onBlur,
  themeMode = 'light',
  autoFocus = false,
  editable = true,
}) => {
  const theme = getGroceryTheme(themeMode);
  const [isFocused, setIsFocused] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  
  // Animation values
  const borderAnimatedValue = useRef(new Animated.Value(0)).current;
  const scaleAnimatedValue = useRef(new Animated.Value(1)).current;
  const shadowAnimatedValue = useRef(new Animated.Value(0)).current;
  const placeholderAnimatedValue = useRef(new Animated.Value(0)).current;

  // Input ref for methods
  const inputRef = useRef<TextInput>(null);

  useEffect(() => {
    // Animate based on focus state
    Animated.parallel([
      Animated.timing(borderAnimatedValue, {
        toValue: isFocused ? 1 : 0,
        duration: theme.animations.timing.normal,
        useNativeDriver: false,
      }),
      Animated.spring(scaleAnimatedValue, {
        toValue: isFocused ? 1.02 : 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
      Animated.timing(shadowAnimatedValue, {
        toValue: isFocused ? 1 : 0,
        duration: theme.animations.timing.normal,
        useNativeDriver: false,
      }),
      Animated.timing(placeholderAnimatedValue, {
        toValue: isFocused || value.length > 0 ? 1 : 0,
        duration: theme.animations.timing.fast,
        useNativeDriver: false,
      }),
    ]).start();
  }, [isFocused, value.length, theme.animations.timing]);

  // Handle focus
  const handleFocus = () => {
    setIsFocused(true);
    onFocus?.();
    
    // Add haptic feedback (optional)
    try {
      if (Platform.OS === 'ios') {
        const { HapticFeedback } = require('expo-haptics');
        HapticFeedback.selectionAsync();
      }
    } catch (error) {
      // Graceful fallback if expo-haptics is not installed
    }
  };

  // Handle blur
  const handleBlur = () => {
    setIsFocused(false);
    onBlur?.();
  };

  // Handle clear
  const handleClear = () => {
    onChangeText('');
    inputRef.current?.focus();
    
    // Add haptic feedback (optional)
    try {
      if (Platform.OS === 'ios') {
        const { HapticFeedback } = require('expo-haptics');
        HapticFeedback.impactAsync(HapticFeedback.ImpactFeedbackStyle.Light);
      }
    } catch (error) {
      // Graceful fallback if expo-haptics is not installed
    }
  };

  // Animated styles
  const animatedBorderColor = borderAnimatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [theme.colors.semantic.borderLight, theme.colors.primary[300]],
  });

  const animatedBackgroundColor = borderAnimatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [theme.colors.neutral[50], theme.colors.neutral[0]],
  });

  const animatedShadowOpacity = shadowAnimatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0.04, 0.1],
  });

  const animatedShadowRadius = shadowAnimatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [4, 12],
  });

  const animatedIconColor = borderAnimatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [theme.colors.neutral[400], theme.colors.primary[500]],
  });

  // Dynamic styles
  const containerStyle = [
    styles.container,
    {
      backgroundColor: animatedBackgroundColor,
      borderColor: animatedBorderColor,
      shadowOpacity: animatedShadowOpacity,
      shadowRadius: animatedShadowRadius,
      shadowColor: theme.colors.primary[500],
      transform: [{ scale: scaleAnimatedValue }],
    },
  ];

  return (
    <Animated.View style={containerStyle}>
      {/* Search Icon */}
      <Animated.View style={styles.iconContainer}>
        <Ionicons
          name="search"
          size={20}
          color={animatedIconColor as any}
        />
      </Animated.View>

      {/* Text Input */}
      <TextInput
        ref={inputRef}
        style={[
          styles.input,
          {
            color: theme.colors.neutral[900],
            fontFamily: theme.typography.fontFamily.primary,
          },
        ]}
        value={value}
        onChangeText={onChangeText}
        placeholder={placeholder}
        placeholderTextColor={theme.colors.neutral[400]}
        onFocus={handleFocus}
        onBlur={handleBlur}
        autoFocus={autoFocus}
        editable={editable}
        autoCorrect={false}
        autoCapitalize="none"
        returnKeyType="search"
        blurOnSubmit={false}
        selectionColor={theme.colors.primary[500]}
        // Enhanced keyboard appearance
        keyboardAppearance={themeMode === 'dark' ? 'dark' : 'light'}
      />

      {/* Clear Button */}
      {value.length > 0 && (
        <TouchableOpacity
          style={styles.clearButton}
          onPress={handleClear}
          hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
        >
          <View style={[styles.clearButtonDefault, { backgroundColor: theme.colors.neutral[200] }]}>
            <Ionicons
              name="close"
              size={16}
              color={theme.colors.neutral[600]}
            />
          </View>
        </TouchableOpacity>
      )}

      {/* Focus Ring Effect (iOS style) */}
      {isFocused && Platform.OS === 'ios' && (
        <Animated.View
          style={[
            styles.focusRing,
            {
              borderColor: theme.colors.primary[300],
              opacity: shadowAnimatedValue,
            },
          ]}
        />
      )}
    </Animated.View>
  );
};

// Compact variant for smaller spaces
export const CompactSearchBar: React.FC<ModernSearchBarProps> = (props) => {
  const theme = getGroceryTheme(props.themeMode);
  
  return (
    <View style={[styles.compactContainer, { backgroundColor: theme.colors.neutral[100] }]}>
      <Ionicons
        name="search"
        size={18}
        color={theme.colors.neutral[500]}
        style={styles.compactIcon}
      />
      <TextInput
        style={[
          styles.compactInput,
          {
            color: theme.colors.neutral[900],
            fontFamily: theme.typography.fontFamily.primary,
          },
        ]}
        placeholder={props.placeholder}
        placeholderTextColor={theme.colors.neutral[400]}
        value={props.value}
        onChangeText={props.onChangeText}
        autoCorrect={false}
        autoCapitalize="none"
        returnKeyType="search"
      />
      {props.value.length > 0 && (
        <TouchableOpacity
          onPress={() => props.onChangeText('')}
          style={styles.compactClearButton}
        >
          <Ionicons
            name="close-circle"
            size={16}
            color={theme.colors.neutral[400]}
          />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1.5,
    marginVertical: 8,
    // Base shadow
    shadowOffset: { width: 0, height: 2 },
    elevation: 3,
    // Prevent layout shift
    minHeight: 52,
  },

  iconContainer: {
    marginRight: 12,
  },

  input: {
    flex: 1,
    fontSize: 16,
    fontWeight: '400',
    lineHeight: 24,
    padding: 0, // Remove default padding
    textAlignVertical: 'center',
  },

  clearButton: {
    marginLeft: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },

  clearButtonBlur: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },

  clearButtonDefault: {
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },

  focusRing: {
    position: 'absolute',
    top: -2,
    left: -2,
    right: -2,
    bottom: -2,
    borderRadius: 18,
    borderWidth: 2,
    pointerEvents: 'none',
  },

  // Compact variant styles
  compactContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginVertical: 4,
    minHeight: 40,
  },

  compactIcon: {
    marginRight: 8,
  },

  compactInput: {
    flex: 1,
    fontSize: 14,
    fontWeight: '400',
    padding: 0,
  },

  compactClearButton: {
    marginLeft: 6,
    padding: 2,
  },
});

export default ModernSearchBar;