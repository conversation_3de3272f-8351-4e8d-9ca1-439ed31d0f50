import React from 'react';
import { View, Text, Image, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';

interface Ingredient {
  name: string;
  quantity: string | number;
  icon?: string; // URL or local asset
}

interface RecipeDetailCardProps {
  imageUrl: string;
  title: string;
  description: string;
  ingredients: Ingredient[];
  instructions: string[];
  onAddToShoppingList?: () => void;
  onClose?: () => void;
}

export const RecipeDetailCard: React.FC<RecipeDetailCardProps> = ({
  imageUrl,
  title,
  description,
  ingredients,
  instructions,
  onAddToShoppingList,
  onClose,
}) => {
  return (
    <View style={styles.card}>
      {/* Close Button */}
      {onClose && (
        <TouchableOpacity style={styles.closeBtn} onPress={onClose}>
          <Text style={{ fontSize: 24, color: '#888' }}>×</Text>
        </TouchableOpacity>
      )}
      {/* Top Image */}
      <View style={styles.imageContainer}>
        <Image source={{ uri: imageUrl }} style={styles.image} />
      </View>
      {/* Content */}
      <View style={styles.content}>
        <Text style={styles.title}>{title}</Text>
        <Text style={styles.description}>{description}</Text>
        <View style={styles.ingredientHeaderRow}>
          <Text style={styles.sectionTitle}>Ingredients <Text style={styles.ingredientCount}>({ingredients.length})</Text></Text>
          {onAddToShoppingList && (
            <TouchableOpacity style={styles.addBtn} onPress={onAddToShoppingList}>
              <Text style={styles.addBtnText}>Add to Shopping List</Text>
            </TouchableOpacity>
          )}
        </View>
        <ScrollView style={styles.ingredientList} contentContainerStyle={{ paddingBottom: 8 }}>
          {ingredients.map((ing, idx) => (
            <View key={idx} style={styles.ingredientRow}>
              {ing.icon ? (
                <Image source={{ uri: ing.icon }} style={styles.ingredientIcon} />
              ) : (
                <View style={styles.ingredientIconPlaceholder} />
              )}
              <Text style={styles.ingredientName}>{ing.name}</Text>
              <Text style={styles.ingredientQty}>{ing.quantity}</Text>
            </View>
          ))}
        </ScrollView>
        <Text style={styles.sectionTitle}>Instructions</Text>
        <ScrollView style={styles.instructionsList} contentContainerStyle={{ paddingBottom: 16 }}>
          {instructions && instructions.length > 0 ? (
            instructions.map((step, idx) => (
              <View key={idx} style={styles.instructionRow}>
                <Text style={styles.instructionIndex}>{idx + 1}.</Text>
                <Text style={styles.instructionText}>{step}</Text>
              </View>
            ))
          ) : (
            <Text style={styles.noInstructions}>No instructions available.</Text>
          )}
        </ScrollView>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#fff',
    borderRadius: 24,
    overflow: 'hidden',
    flex: 1,
    margin: 0,
    minHeight: '100%',
  },
  closeBtn: {
    position: 'absolute',
    top: 18,
    right: 18,
    zIndex: 10,
    backgroundColor: '#f2f2f2',
    borderRadius: 18,
    width: 36,
    height: 36,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 2,
  },
  imageContainer: {
    width: '100%',
    height: 220,
    backgroundColor: '#eee',
  },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  content: {
    padding: 20,
    paddingTop: 16,
    flex: 1,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 6,
    color: '#222',
  },
  description: {
    fontSize: 15,
    color: '#666',
    marginBottom: 16,
  },
  ingredientHeaderRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  addBtn: {
    backgroundColor: '#2ecc71',
    borderRadius: 16,
    paddingVertical: 6,
    paddingHorizontal: 14,
  },
  addBtnText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14,
  },
  sectionTitle: {
    fontWeight: 'bold',
    fontSize: 16,
    marginBottom: 8,
    color: '#222',
  },
  ingredientCount: {
    color: '#2ecc71',
    fontWeight: '600',
    fontSize: 15,
  },
  ingredientList: {
    maxHeight: 180,
    marginBottom: 12,
  },
  ingredientRow: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f7f7f7',
    borderRadius: 12,
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginBottom: 8,
  },
  ingredientIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 10,
    backgroundColor: '#eee',
  },
  ingredientIconPlaceholder: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 10,
    backgroundColor: '#eee',
  },
  ingredientName: {
    flex: 1,
    fontSize: 15,
    color: '#333',
  },
  ingredientQty: {
    fontWeight: 'bold',
    fontSize: 15,
    color: '#222',
    marginLeft: 8,
  },
  instructionsList: {
    maxHeight: 320,
  },
  instructionRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 10,
  },
  instructionIndex: {
    fontWeight: 'bold',
    fontSize: 15,
    color: '#2ecc71',
    marginRight: 8,
    width: 22,
  },
  instructionText: {
    flex: 1,
    fontSize: 15,
    color: '#333',
  },
  noInstructions: {
    color: '#888',
    fontStyle: 'italic',
    marginBottom: 12,
  },
}); 