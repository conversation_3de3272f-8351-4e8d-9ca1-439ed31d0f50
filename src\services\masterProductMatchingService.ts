/**
 * Master Product Matching Service
 * Implements the multi-stage product matching pipeline from the research blueprint:
 * 1. Identifier-based matching (EAN/UPC)
 * 2. Fuzzy string matching (Levenshtein, N-gram)
 * 3. Semantic similarity matching
 * 4. Attribute-based matching and refinement
 */

import { supabase } from '../supabase/client';

export interface ProductMatchResult {
  success: boolean;
  matched_count: number;
  total_processed: number;
  method: 'identifier' | 'fuzzy' | 'semantic' | 'attribute';
  confidence_avg?: number;
  errors?: string[];
}

export interface MasterProduct {
  id: string;
  canonical_name: string;
  brand?: string;
  category?: string;
  image_url?: string;
  ean_upc?: string;
  created_at: string;
  updated_at: string;
}

export interface ScrapedListing {
  id: string;
  store_id: string;
  scraped_product_name: string;
  scraped_description?: string;
  scraped_price?: number;
  scraped_size_info?: string;
  is_matched: boolean;
  matched_product_id?: string;
  match_confidence?: number;
  match_method?: string;
}

export interface ConsolidatedProduct {
  id: string;
  name: string;
  brand?: string;
  category?: string;
  image_url?: string;
  store_prices: Record<string, number>;
  lowest_price: number;
  highest_price: number;
  max_savings: number;
  best_store: string;
  available_stores: string[];
  last_updated: string;
}

class MasterProductMatchingService {
  /**
   * Run the complete multi-stage matching pipeline
   */
  async runFullMatchingPipeline(): Promise<{
    identifier_matches: ProductMatchResult;
    fuzzy_matches: ProductMatchResult;
    total_matched: number;
    execution_time_ms: number;
  }> {
    const startTime = performance.now();
    
    try {
      console.log('🚀 Starting full product matching pipeline...');
      
      // Stage 1: Identifier-based matching (highest confidence)
      const identifierMatches = await this.matchProductsByIdentifier();
      console.log(`✅ Identifier matching: ${identifierMatches.matched_count} matches`);
      
      // Stage 2: Fuzzy string matching
      const fuzzyMatches = await this.matchProductsByFuzzySimilarity();
      console.log(`✅ Fuzzy matching: ${fuzzyMatches.matched_count} matches`);
      
      // Update product store prices from all matches
      await this.updateProductStorePrices();
      console.log('✅ Product store prices updated');
      
      const endTime = performance.now();
      const executionTime = endTime - startTime;
      
      const totalMatched = identifierMatches.matched_count + fuzzyMatches.matched_count;
      
      console.log(`🎉 Matching pipeline completed in ${executionTime.toFixed(2)}ms`);
      console.log(`📊 Total products matched: ${totalMatched}`);
      
      return {
        identifier_matches: identifierMatches,
        fuzzy_matches: fuzzyMatches,
        total_matched: totalMatched,
        execution_time_ms: executionTime
      };
    } catch (error) {
      console.error('❌ Error in matching pipeline:', error);
      throw error;
    }
  }

  /**
   * Stage 1: Match products using unique identifiers (EAN/UPC)
   * This is the most accurate and fastest matching method
   */
  async matchProductsByIdentifier(): Promise<ProductMatchResult> {
    try {
      const { data, error } = await supabase.rpc('match_products_by_identifier');
      
      if (error) {
        console.error('Error in identifier matching:', error);
        return {
          success: false,
          matched_count: 0,
          total_processed: 0,
          method: 'identifier',
          errors: [error.message]
        };
      }

      const matchedCount = data?.[0]?.matched_count || 0;
      
      return {
        success: true,
        matched_count: matchedCount,
        total_processed: matchedCount,
        method: 'identifier',
        confidence_avg: 1.0 // Identifier matches are always high confidence
      };
    } catch (error) {
      console.error('Exception in identifier matching:', error);
      return {
        success: false,
        matched_count: 0,
        total_processed: 0,
        method: 'identifier',
        errors: [error instanceof Error ? error.message : 'Unknown error']
      };
    }
  }

  /**
   * Stage 2: Match products using fuzzy string similarity
   * Handles typographical errors and minor variations
   */
  async matchProductsByFuzzySimilarity(threshold: number = 0.7): Promise<ProductMatchResult> {
    try {
      const { data, error } = await supabase.rpc('match_products_by_fuzzy_similarity', {
        similarity_threshold: threshold
      });
      
      if (error) {
        console.error('Error in fuzzy matching:', error);
        return {
          success: false,
          matched_count: 0,
          total_processed: 0,
          method: 'fuzzy',
          errors: [error.message]
        };
      }

      const matchedCount = data?.[0]?.matched_count || 0;
      
      return {
        success: true,
        matched_count: matchedCount,
        total_processed: matchedCount,
        method: 'fuzzy',
        confidence_avg: threshold // Average confidence is around the threshold
      };
    } catch (error) {
      console.error('Exception in fuzzy matching:', error);
      return {
        success: false,
        matched_count: 0,
        total_processed: 0,
        method: 'fuzzy',
        errors: [error instanceof Error ? error.message : 'Unknown error']
      };
    }
  }

  /**
   * Update product store prices from matched scraped data
   */
  async updateProductStorePrices(): Promise<void> {
    const { error } = await supabase.rpc('update_product_store_prices');
    
    if (error) {
      console.error('Error updating product store prices:', error);
      throw error;
    }
  }

  /**
   * Get consolidated products for display (used by ConsolidatedProductCard)
   */
  async getConsolidatedProducts(options?: {
    limit?: number;
    offset?: number;
    search?: string;
    category?: string;
    stores?: string[];
  }): Promise<ConsolidatedProduct[]> {
    let query = supabase
      .from('consolidated_products')
      .select('*')
      .order('name');

    // Apply filters
    if (options?.search) {
      query = query.ilike('name', `%${options.search}%`);
    }
    
    if (options?.category && options.category !== 'All') {
      query = query.eq('category', options.category);
    }
    
    // Apply pagination
    if (options?.limit) {
      query = query.limit(options.limit);
    }
    
    if (options?.offset) {
      query = query.range(options.offset, (options.offset + (options?.limit || 50)) - 1);
    }

    const { data, error } = await query;
    
    if (error) {
      console.error('Error fetching consolidated products:', error);
      throw error;
    }

    // Filter by available stores if specified
    let filteredData = data || [];
    if (options?.stores && options.stores.length > 0) {
      filteredData = filteredData.filter(product => 
        options.stores!.some(store => 
          product.available_stores?.includes(store)
        )
      );
    }

    return filteredData;
  }

  /**
   * Get a single consolidated product by ID
   */
  async getConsolidatedProductById(productId: string): Promise<ConsolidatedProduct | null> {
    const { data, error } = await supabase
      .from('consolidated_products')
      .select('*')
      .eq('id', productId)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Product not found
      }
      console.error('Error fetching consolidated product:', error);
      throw error;
    }

    return data;
  }

  /**
   * Migrate existing products to master catalog
   * This is a one-time operation to set up the master catalog
   */
  async migrateExistingProducts(): Promise<{ success: boolean; message: string }> {
    try {
      console.log('🔄 Starting migration of existing products to master catalog...');
      
      const { error } = await supabase.rpc('migrate_existing_products_to_master_catalog');
      
      if (error) {
        console.error('Migration error:', error);
        return {
          success: false,
          message: `Migration failed: ${error.message}`
        };
      }

      console.log('✅ Migration completed successfully');
      return {
        success: true,
        message: 'Successfully migrated existing products to master catalog'
      };
    } catch (error) {
      console.error('Migration exception:', error);
      return {
        success: false,
        message: `Migration failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Get matching statistics and health metrics
   */
  async getMatchingStatistics(): Promise<{
    total_scraped: number;
    total_matched: number;
    total_master_products: number;
    match_rate: number;
    unmatched_count: number;
    confidence_distribution: Record<string, number>;
  }> {
    try {
      const [
        scrapedResult,
        matchedResult,
        masterResult,
        confidenceResult
      ] = await Promise.all([
        supabase.from('store_product_listings').select('id', { count: 'exact', head: true }),
        supabase.from('store_product_listings').select('id', { count: 'exact', head: true }).eq('is_matched', true),
        supabase.from('products').select('id', { count: 'exact', head: true }),
        supabase.from('store_product_listings').select('match_method').eq('is_matched', true)
      ]);

      const totalScraped = scrapedResult.count || 0;
      const totalMatched = matchedResult.count || 0;
      const totalMaster = masterResult.count || 0;
      const matchRate = totalScraped > 0 ? (totalMatched / totalScraped) * 100 : 0;

      // Calculate confidence distribution
      const confidenceData = confidenceResult.data || [];
      const confidenceDistribution = confidenceData.reduce((acc, item) => {
        const method = item.match_method || 'unknown';
        acc[method] = (acc[method] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      return {
        total_scraped: totalScraped,
        total_matched: totalMatched,
        total_master_products: totalMaster,
        match_rate: Math.round(matchRate * 100) / 100,
        unmatched_count: totalScraped - totalMatched,
        confidence_distribution: confidenceDistribution
      };
    } catch (error) {
      console.error('Error getting matching statistics:', error);
      throw error;
    }
  }

  /**
   * Create a new master product manually
   * Useful for products that can't be automatically matched
   */
  async createMasterProduct(product: {
    canonical_name: string;
    brand?: string;
    category?: string;
    image_url?: string;
    ean_upc?: string;
  }): Promise<MasterProduct> {
    const { data, error } = await supabase
      .from('products')
      .insert([{
        canonical_name: product.canonical_name,
        brand: product.brand,
        category: product.category,
        image_url: product.image_url,
        ean_upc: product.ean_upc
      }])
      .select()
      .single();

    if (error) {
      console.error('Error creating master product:', error);
      throw error;
    }

    return data;
  }

  /**
   * Manually link a scraped listing to a master product
   * For handling edge cases or manual review corrections
   */
  async linkScrapedListingToMasterProduct(
    scrapedListingId: string,
    masterProductId: string,
    confidence: number = 0.8
  ): Promise<void> {
    const { error } = await supabase
      .from('store_product_listings')
      .update({
        matched_product_id: masterProductId,
        is_matched: true,
        match_method: 'manual',
        match_confidence: confidence
      })
      .eq('id', scrapedListingId);

    if (error) {
      console.error('Error linking scraped listing to master product:', error);
      throw error;
    }

    // Update product store prices
    await this.updateProductStorePrices();
  }
}

// Export singleton instance
export const masterProductMatchingService = new MasterProductMatchingService();