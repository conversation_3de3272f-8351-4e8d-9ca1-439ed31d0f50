/**
 * Test file for Product Deduplication Service
 * Run with: npm test -- productDeduplicationService.test.ts
 */

import { ProductDeduplicationService, Product } from '../productDeduplicationService';

const mockProducts: Product[] = [
  // Same product across 3 stores - should be grouped
  {
    id: 'w1',
    name: 'Tegel Chicken Breast Fillets',
    price: 12.99,
    current_price: 12.99,
    store: 'woolworths',
    brand: 'Tegel',
    size: '1kg',
    unit: 'kg',
    category: 'meat-poultry',
    image_url: 'https://woolworths.com/tegel-chicken-breast-1kg.jpg',
    is_available: true,
  },
  {
    id: 'nw1',
    name: 'Tegel Chicken Breast Fillets',
    price: 13.50,
    current_price: 13.50,
    store: 'newworld',
    brand: 'Tegel',
    size: '1kg',
    unit: 'kg',
    category: 'meat-poultry',
    image_url: 'https://newworld.com/tegel-chicken-breast-1kg-hd.jpg',
    is_available: true,
  },
  {
    id: 'ps1',
    name: 'Tegel Chicken Breast Fillets',
    price: 11.99,
    current_price: 10.99, // On sale
    original_price: 11.99,
    store: 'paknsave',
    brand: 'Tegel',
    size: '1kg',
    unit: 'kg',
    category: 'meat-poultry',
    image_url: 'https://paknsave.com/tegel-chicken-breast-1kg-thumb.jpg',
    is_available: true,
    is_on_sale: true,
  },
  
  // Different sizes - should NOT be grouped
  {
    id: 'w2',
    name: 'Tegel Chicken Breast Fillets',
    price: 7.99,
    current_price: 7.99,
    store: 'woolworths',
    brand: 'Tegel',
    size: '500g',
    unit: 'g',
    category: 'meat-poultry',
    image_url: 'https://woolworths.com/tegel-chicken-breast-500g.jpg',
    is_available: true,
  },
  
  // Different brand - should NOT be grouped
  {
    id: 'w3',
    name: 'Chicken Breast Fillets',
    price: 14.99,
    current_price: 14.99,
    store: 'woolworths',
    brand: 'Inghams',
    size: '1kg',
    unit: 'kg',
    category: 'meat-poultry',
    image_url: 'https://woolworths.com/inghams-chicken-breast-1kg.jpg',
    is_available: true,
  },
  
  // Completely different product
  {
    id: 'w4',
    name: 'Fresh Bananas',
    price: 3.99,
    current_price: 3.99,
    store: 'woolworths',
    brand: undefined,
    size: '1kg',
    unit: 'kg',
    category: 'fruit-veg',
    image_url: 'https://woolworths.com/bananas-1kg.jpg',
    is_available: true,
  },
];

describe('ProductDeduplicationService', () => {
  let service: ProductDeduplicationService;

  beforeEach(() => {
    service = ProductDeduplicationService.getInstance();
  });

  describe('groupProducts', () => {
    test('should group same products from different stores', () => {
      const groups = service.groupProducts(mockProducts);
      
      // Should have 4 groups total:
      // 1. Tegel Chicken Breast 1kg (3 stores)
      // 2. Tegel Chicken Breast 500g (1 store)
      // 3. Inghams Chicken Breast 1kg (1 store)  
      // 4. Fresh Bananas 1kg (1 store)
      expect(groups).toHaveLength(4);
      
      // Find the main Tegel group
      const tegelGroup = groups.find(g => 
        g.name.includes('Tegel') && 
        g.size === '1kg' &&
        g.products.length === 3
      );
      
      expect(tegelGroup).toBeDefined();
      expect(tegelGroup!.stores).toHaveLength(3);
      expect(tegelGroup!.stores).toEqual(expect.arrayContaining(['woolworths', 'newworld', 'paknsave']));
    });

    test('should calculate correct price information', () => {
      const groups = service.groupProducts(mockProducts);
      
      const tegelGroup = groups.find(g => 
        g.name.includes('Tegel') && 
        g.size === '1kg'
      );
      
      expect(tegelGroup!.lowestPrice).toBe(10.99); // Pak'nSave sale price
      expect(tegelGroup!.highestPrice).toBe(13.50); // New World price
      expect(tegelGroup!.bestPrice.store).toBe('paknsave');
      expect(tegelGroup!.isOnSale).toBe(true);
    });

    test('should select best image quality', () => {
      const groups = service.groupProducts(mockProducts);
      
      const tegelGroup = groups.find(g => 
        g.name.includes('Tegel') && 
        g.size === '1kg'
      );
      
      // Should prefer New World's HD image over thumbnail
      expect(tegelGroup!.bestImage).toBe('https://newworld.com/tegel-chicken-breast-1kg-hd.jpg');
    });

    test('should not group products with different sizes', () => {
      const groups = service.groupProducts(mockProducts);
      
      const tegel1kg = groups.find(g => 
        g.name.includes('Tegel') && 
        g.size === '1kg'
      );
      const tegel500g = groups.find(g => 
        g.name.includes('Tegel') && 
        g.size === '500g'
      );
      
      expect(tegel1kg).toBeDefined();
      expect(tegel500g).toBeDefined();
      expect(tegel1kg!.id).not.toBe(tegel500g!.id);
    });

    test('should not group products with different brands', () => {
      const groups = service.groupProducts(mockProducts);
      
      const tegelChicken = groups.find(g => 
        g.brand === 'Tegel' && 
        g.size === '1kg'
      );
      const inghamsChicken = groups.find(g => 
        g.brand === 'Inghams' && 
        g.size === '1kg'
      );
      
      expect(tegelChicken).toBeDefined();
      expect(inghamsChicken).toBeDefined();
      expect(tegelChicken!.id).not.toBe(inghamsChicken!.id);
    });
  });

  describe('getPriceInfo', () => {
    test('should calculate price information correctly', () => {
      const groups = service.groupProducts(mockProducts);
      const tegelGroup = groups.find(g => 
        g.name.includes('Tegel') && 
        g.size === '1kg'
      )!;
      
      const priceInfo = ProductDeduplicationService.getPriceInfo(tegelGroup);
      
      expect(priceInfo.lowestPrice).toBe(10.99);
      expect(priceInfo.highestPrice).toBe(13.50);
      expect(priceInfo.bestStore).toBe('paknsave');
      expect(priceInfo.savingsAmount).toBe(2.51);
      expect(priceInfo.savingsPercentage).toBeCloseTo(18.59, 1);
      expect(priceInfo.priceRange).toBe('$10.99 - $13.50');
    });
  });

  describe('getStorePricing', () => {
    test('should return pricing for each store', () => {
      const groups = service.groupProducts(mockProducts);
      const tegelGroup = groups.find(g => 
        g.name.includes('Tegel') && 
        g.size === '1kg'
      )!;
      
      const storePricing = ProductDeduplicationService.getStorePricing(tegelGroup);
      
      expect(storePricing).toHaveLength(3);
      
      const paknSave = storePricing.find(sp => sp.store === 'paknsave');
      expect(paknSave!.price).toBe(10.99);
      expect(paknSave!.isOnSale).toBe(true);
      expect(paknSave!.isBestPrice).toBe(true);
      
      const newWorld = storePricing.find(sp => sp.store === 'newworld');
      expect(newWorld!.price).toBe(13.50);
      expect(newWorld!.isBestPrice).toBe(false);
    });
  });
});

// Export for manual testing in console
export const testData = {
  mockProducts,
  testGrouping: () => {
    const service = ProductDeduplicationService.getInstance();
    const groups = service.groupProducts(mockProducts);
    
    console.log('🧪 Product Deduplication Test Results:');
    console.log(`📦 ${groups.length} product groups created from ${mockProducts.length} individual products`);
    
    groups.forEach((group, index) => {
      console.log(`\n📋 Group ${index + 1}: ${group.name}`);
      console.log(`   🏪 Stores: ${group.stores.join(', ')}`);
      console.log(`   💰 Price Range: $${group.lowestPrice} - $${group.highestPrice}`);
      console.log(`   🎯 Best Deal: ${group.bestPrice.store} at $${group.bestPrice.current_price || group.bestPrice.price}`);
      console.log(`   📸 Image: ${group.bestImage ? '✅' : '❌'}`);
      console.log(`   🔥 On Sale: ${group.isOnSale ? '✅' : '❌'}`);
    });
    
    return groups;
  }
};