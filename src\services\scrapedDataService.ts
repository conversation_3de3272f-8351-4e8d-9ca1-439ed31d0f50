/**
 * Scraped Data Service
 * 
 * Reads price data from the improved scraper and provides
 * real-time price updates for the shopping list
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { SupermarketProduct, StoreSearchResult } from './priceComparisonService';

const SCRAPED_DATA_PREFIX = 'scraped_data_';
const PRICE_CACHE_PREFIX = 'price_cache_';
const LAST_UPDATE_KEY = 'last_price_update';

export interface ScrapedProduct {
  id: string;
  objectID: string;
  name: string;
  price: number;
  currentPrice: number;
  category: string;
  brand: string;
  unit: string;
  availability: string;
  image?: string;
  imageUrl?: string;
  store: string;
  storeName: string;
  scrapedAt: string;
  isRealData?: boolean;
  isGenerated?: boolean;
  promotions?: string[];
}

export interface PriceChange {
  product: ScrapedProduct;
  previousPrice: number;
  newPrice: number;
  changePercent: number;
  changeType: 'increase' | 'decrease' | 'same';
  isSignificant: boolean; // > 5% change
}

export interface ScrapedDataSnapshot {
  timestamp: string;
  totalProducts: number;
  stores: {
    woolworths: ScrapedProduct[];
    newworld: ScrapedProduct[];
    paknsave: ScrapedProduct[];
  };
  priceChanges: PriceChange[];
  lastUpdateTime: string;
}

class ScrapedDataService {
  private lastUpdateTime: string | null = null;
  private priceCache: Map<string, ScrapedProduct> = new Map();
  private updateListeners: Set<() => void> = new Set();

  constructor() {
    this.initializeCache();
  }

  private async initializeCache() {
    try {
      const lastUpdate = await AsyncStorage.getItem(LAST_UPDATE_KEY);
      this.lastUpdateTime = lastUpdate;
      
      // Load cached price data
      await this.loadCachedData();
      
      // Start monitoring for updates
      this.startMonitoring();
    } catch (error) {
      console.error('Failed to initialize scraped data cache:', error);
    }
  }

  private async loadCachedData() {
    try {
      // Try to load clean scraped data first
      await this.loadCleanScrapedData();
      
      if (this.priceCache.size === 0) {
        // Fallback to loading from AsyncStorage cache
        const stores = ['woolworths', 'newworld', 'paknsave'];
        
        for (const store of stores) {
          const cacheKey = `${SCRAPED_DATA_PREFIX}${store}`;
          const cachedData = await AsyncStorage.getItem(cacheKey);
          
          if (cachedData) {
            const products: ScrapedProduct[] = JSON.parse(cachedData);
            products.forEach(product => {
              this.priceCache.set(`${store}_${product.name.toLowerCase()}`, product);
            });
          }
        }
      }
      
      console.log(`📱 Loaded ${this.priceCache.size} products from cache`);
    } catch (error) {
      console.error('Failed to load cached data:', error);
    }
  }

  private startMonitoring() {
    // Check for updates every 30 seconds
    setInterval(() => {
      this.checkForUpdates();
    }, 30000);
  }

  private async checkForUpdates() {
    try {
      // In a real app, this would check file timestamps or use a file watcher
      // For now, we'll simulate checking for new data
      const hasNewData = await this.hasNewScrapedData();
      
      if (hasNewData) {
        console.log('🔄 New scraped data detected, updating prices...');
        await this.loadLatestScrapedData();
        this.notifyListeners();
      }
    } catch (error) {
      console.error('Error checking for updates:', error);
    }
  }

  private async hasNewScrapedData(): Promise<boolean> {
    // This would typically check file modification times
    // For demo purposes, we'll check every few minutes
    const now = new Date().getTime();
    const lastCheck = this.lastUpdateTime ? new Date(this.lastUpdateTime).getTime() : 0;
    const timeDiff = now - lastCheck;
    
    // Simulate new data every 5 minutes for demo
    return timeDiff > 5 * 60 * 1000;
  }

  private async loadLatestScrapedData() {
    try {
      // Load clean scraped data
      await this.loadCleanScrapedData();
      
      // Generate some price changes for demo
      const priceChanges: PriceChange[] = this.generateDemoPriceChanges();
      
      // Update last update time
      this.lastUpdateTime = new Date().toISOString();
      await AsyncStorage.setItem(LAST_UPDATE_KEY, this.lastUpdateTime);
      
      // Cache price changes for recent activity
      if (priceChanges.length > 0) {
        await AsyncStorage.setItem('recent_price_changes', JSON.stringify(priceChanges));
        console.log(`📈 Detected ${priceChanges.length} price changes`);
      }
      
    } catch (error) {
      console.error('Failed to load scraped data:', error);
    }
  }

  private generateDemoPriceChanges(): PriceChange[] {
    // Generate some demo price changes for testing
    const changes: PriceChange[] = [];
    
    // Simulate some price changes
    const anchorMilk = this.priceCache.get('woolworths_anchor blue milk');
    if (anchorMilk) {
      const oldPrice = anchorMilk.price;
      const newPrice = oldPrice * 0.95; // 5% decrease
      changes.push({
        product: anchorMilk,
        previousPrice: oldPrice,
        newPrice: newPrice,
        changePercent: -5,
        changeType: 'decrease',
        isSignificant: true
      });
    }
    
    return changes;
  }

  private async loadCleanScrapedData(): Promise<void> {
    try {
      // In production, this would read from the actual cleaned scraped data files
      // For now, we'll simulate loading real scraped data
      
      // This would be: const cleanData = require('../../scrapers/scraped-data/latest-clean-products.json');
      const cleanData = this.generateCleanSampleData();
      
      // Clear existing cache
      this.priceCache.clear();
      
             // Load clean data into cache
       ['woolworths', 'newworld', 'paknsave'].forEach(store => {
         const storeData = (cleanData as any)[store];
         if (storeData) {
           storeData.forEach((product: any) => {
            const cacheKey = `${store}_${product.name.toLowerCase()}`;
            this.priceCache.set(cacheKey, {
              id: product.id,
              objectID: product.objectID,
              name: product.name,
              price: product.price,
              currentPrice: product.currentPrice,
              category: product.category,
              brand: product.brand,
              unit: product.unit,
              availability: product.availability,
              imageUrl: product.imageUrl,
              store: product.store,
              storeName: product.storeName,
              scrapedAt: product.scrapedAt,
              isGenerated: product.isGenerated,
              promotions: product.promotions || []
            });
          });
        }
      });
      
      console.log(`📱 Loaded ${this.priceCache.size} clean products from scraped data`);
    } catch (error) {
      console.error('Failed to load clean scraped data:', error);
      // Fallback to old method
      await this.generateRealisticSampleData('woolworths');
    }
  }

  private generateCleanSampleData() {
    // No more mock data - use real scraped data from Supabase
    return {
      woolworths: [],
      newworld: [],
      paknsave: []
    };
  }

  private async generateRealisticSampleData(store: string): Promise<ScrapedProduct[]> {
    // Fallback method - should not be used when clean data is available
    console.log('⚠️ Using fallback sample data generation');
    return [];
  }

  // Public API methods

  /**
   * Search for products across all stores using scraped data
   */
  async searchProducts(query: string, maxResults: number = 5): Promise<StoreSearchResult[]> {
    const normalizedQuery = query.toLowerCase().trim();
    const results: StoreSearchResult[] = [];

    // Search through cached products
    for (const [key, product] of this.priceCache.entries()) {
      if (product.name.includes(normalizedQuery) || 
          product.brand.toLowerCase().includes(normalizedQuery)) {
        
        results.push({
          store: product.store as 'woolworths' | 'newworld' | 'paknsave',
          storeName: product.storeName,
          product: {
            objectID: product.objectID,
            name: product.name,
            price: product.price,
            unit: product.unit,
            description: `${product.brand} ${product.name}`,
            category: product.category,
            brand: product.brand,
            imageUrl: product.imageUrl,
            availability: product.availability as 'in_stock' | 'low_stock' | 'out_of_stock',
            promotions: product.promotions || []
          },
          searchQuery: query,
          relevance_score: this.calculateRelevanceScore(normalizedQuery, product)
        });
      }
    }

    // Sort by relevance and price
    results.sort((a, b) => {
      const relevanceDiff = (b.relevance_score || 0) - (a.relevance_score || 0);
      if (Math.abs(relevanceDiff) > 0.1) return relevanceDiff;
      return a.product.price - b.product.price;
    });

    return results.slice(0, maxResults);
  }

  private calculateRelevanceScore(query: string, product: ScrapedProduct): number {
    let score = 0;
    
    // Exact match gets highest score
    if (product.name === query) score += 100;
    
    // Name contains query
    if (product.name.includes(query)) score += 50;
    
    // Brand match
    if (product.brand.toLowerCase().includes(query)) score += 30;
    
    // Category match
    if (product.category.toLowerCase().includes(query)) score += 20;
    
    // Availability bonus
    if (product.availability === 'in_stock') score += 10;
    
    // Promotion bonus
    if (product.promotions && product.promotions.length > 0) score += 5;
    
    return score;
  }

  /**
   * Get price comparison for a specific product
   */
  async getPriceComparison(productName: string): Promise<{
    results: StoreSearchResult[];
    bestPrice?: StoreSearchResult;
    savings?: number;
  }> {
    const results = await this.searchProducts(productName, 10);
    
    // Group by store and find best price per store
    const storeResults = new Map<string, StoreSearchResult>();
    
    results.forEach(result => {
      const existing = storeResults.get(result.store);
      if (!existing || result.product.price < existing.product.price) {
        storeResults.set(result.store, result);
      }
    });

    const finalResults = Array.from(storeResults.values());
    const bestPrice = finalResults.reduce((best, current) => 
      current.product.price < best.product.price ? current : best
    );

    const worstPrice = finalResults.reduce((worst, current) => 
      current.product.price > worst.product.price ? current : worst
    );

    const savings = worstPrice.product.price - bestPrice.product.price;

    return {
      results: finalResults.sort((a, b) => a.product.price - b.product.price),
      bestPrice,
      savings: savings > 0 ? savings : undefined
    };
  }

  /**
   * Get recent price changes
   */
  async getRecentPriceChanges(): Promise<PriceChange[]> {
    try {
      const changes = await AsyncStorage.getItem('recent_price_changes');
      return changes ? JSON.parse(changes) : [];
    } catch (error) {
      console.error('Failed to get recent price changes:', error);
      return [];
    }
  }

  /**
   * Get data freshness info
   */
  getDataFreshness(): { lastUpdate: string | null; isStale: boolean } {
    const isStale = this.lastUpdateTime ? 
      (Date.now() - new Date(this.lastUpdateTime).getTime()) > 24 * 60 * 60 * 1000 : true;
    
    return {
      lastUpdate: this.lastUpdateTime,
      isStale
    };
  }

  /**
   * Force refresh data
   */
  async forceRefresh(): Promise<void> {
    console.log('🔄 Force refreshing scraped data...');
    await this.loadLatestScrapedData();
    this.notifyListeners();
  }

  /**
   * Subscribe to data updates
   */
  onDataUpdate(callback: () => void): () => void {
    this.updateListeners.add(callback);
    return () => this.updateListeners.delete(callback);
  }

  private notifyListeners() {
    this.updateListeners.forEach(callback => {
      try {
        callback();
      } catch (error) {
        console.error('Error in update listener:', error);
      }
    });
  }

  /**
   * Get the latest scraped data snapshot
   */
  async getLatestData(): Promise<ScrapedDataSnapshot> {
    try {
      const timestamp = new Date().toISOString();
      const woolworthsProducts: ScrapedProduct[] = [];
      const newworldProducts: ScrapedProduct[] = [];
      const paknsaveProducts: ScrapedProduct[] = [];
      
      // Group cached products by store
      for (const [key, product] of this.priceCache.entries()) {
        if (product.store === 'woolworths') {
          woolworthsProducts.push(product);
        } else if (product.store === 'newworld') {
          newworldProducts.push(product);
        } else if (product.store === 'paknsave') {
          paknsaveProducts.push(product);
        }
      }
      
      const totalProducts = woolworthsProducts.length + newworldProducts.length + paknsaveProducts.length;
      const priceChanges = await this.getRecentPriceChanges();
      
      return {
        timestamp,
        totalProducts,
        stores: {
          woolworths: woolworthsProducts,
          newworld: newworldProducts,
          paknsave: paknsaveProducts
        },
        priceChanges,
        lastUpdateTime: this.lastUpdateTime || timestamp
      };
    } catch (error) {
      console.error('Failed to get latest data:', error);
      return {
        timestamp: new Date().toISOString(),
        totalProducts: 0,
        stores: {
          woolworths: [],
          newworld: [],
          paknsave: []
        },
        priceChanges: [],
        lastUpdateTime: new Date().toISOString()
      };
    }
  }

  /**
   * Get quick product suggestions for autocomplete
   */
  getProductSuggestions(query: string, limit: number = 8): string[] {
    const normalizedQuery = query.toLowerCase().trim();
    if (!normalizedQuery) return [];

    const suggestions = new Set<string>();
    
    for (const product of this.priceCache.values()) {
      if (product.name.includes(normalizedQuery) && suggestions.size < limit) {
        suggestions.add(product.name);
      }
    }

    return Array.from(suggestions);
  }
}

export const scrapedDataService = new ScrapedDataService(); 