import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Animated,
  Dimensions,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { minimalistTheme } from '../styles/minimalistTheme';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface BurgerMenuProps {
  visible: boolean;
  onClose: () => void;
  onNavigateToProfile: () => void;
  userProfile?: {
    name: string;
    email: string;
    avatar?: string;
  };
}

export const BurgerMenu: React.FC<BurgerMenuProps> = ({
  visible,
  onClose,
  onNavigateToProfile,
  userProfile = { name: '<PERSON>', email: '<EMAIL>' },
}) => {
  const { colors } = useTheme();

  const menuItems = [
    { id: 'profile', title: 'Profile Settings', icon: 'person-outline', onPress: onNavigateToProfile },
    { id: 'preferences', title: 'Dietary Preferences', icon: 'nutrition-outline', onPress: () => {} },
    { id: 'notifications', title: 'Notifications', icon: 'notifications-outline', onPress: () => {} },
    { id: 'storage', title: 'Storage & Sync', icon: 'cloud-outline', onPress: () => {} },
    { id: 'about', title: 'About', icon: 'information-circle-outline', onPress: () => {} },
    { id: 'help', title: 'Help & Support', icon: 'help-circle-outline', onPress: () => {} },
  ];

  const handleMenuItemPress = (item: typeof menuItems[0]) => {
    onClose();
    setTimeout(() => {
      item.onPress();
    }, 250);
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      statusBarTranslucent
    >
      <View style={styles.overlay}>
        <TouchableOpacity style={styles.backdrop} onPress={onClose} />
        
        <Animated.View style={[styles.menuContainer, { backgroundColor: colors.surface }]}>
          <SafeAreaView style={styles.menuContent}>
            {/* Header */}
            <View style={styles.header}>
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <Ionicons name="close" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>

            {/* User Profile Section */}
            <View style={[styles.profileSection, { borderBottomColor: colors.border }]}>
              <View style={[styles.avatarContainer, { backgroundColor: colors.primary + '20' }]}>
                {userProfile.avatar ? (
                  <Image source={{ uri: userProfile.avatar }} style={styles.avatar} />
                ) : (
                  <Ionicons name="person" size={32} color={colors.primary} />
                )}
              </View>
              <View style={styles.profileInfo}>
                <Text style={[styles.userName, { color: colors.text }]}>
                  {userProfile.name}
                </Text>
                <Text style={[styles.userEmail, { color: colors.textSecondary }]}>
                  {userProfile.email}
                </Text>
              </View>
            </View>

            {/* Menu Items */}
            <View style={styles.menuItems}>
              {menuItems.map((item) => (
                <TouchableOpacity
                  key={item.id}
                  style={[styles.menuItem, { borderBottomColor: colors.border }]}
                  onPress={() => handleMenuItemPress(item)}
                >
                  <View style={styles.menuItemContent}>
                    <Ionicons 
                      name={item.icon as any} 
                      size={20} 
                      color={colors.textSecondary} 
                      style={styles.menuIcon}
                    />
                    <Text style={[styles.menuItemText, { color: colors.text }]}>
                      {item.title}
                    </Text>
                  </View>
                  <Ionicons 
                    name="chevron-forward" 
                    size={16} 
                    color={colors.textTertiary} 
                  />
                </TouchableOpacity>
              ))}
            </View>

            {/* Footer */}
            <View style={styles.footer}>
              <Text style={[styles.appVersion, { color: colors.textTertiary }]}>
                Recipe Planner v1.0.0
              </Text>
            </View>
          </SafeAreaView>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    flexDirection: 'row',
  },
  backdrop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  menuContainer: {
    width: SCREEN_WIDTH * 0.8,
    maxWidth: 320,
    shadowColor: '#000',
    shadowOffset: { width: 2, height: 0 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  menuContent: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingHorizontal: minimalistTheme.spacing.lg,
    paddingTop: minimalistTheme.spacing.lg,
    paddingBottom: minimalistTheme.spacing.md,
  },
  closeButton: {
    padding: minimalistTheme.spacing.xs,
  },
  profileSection: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: minimalistTheme.spacing.lg,
    paddingBottom: minimalistTheme.spacing.lg,
    borderBottomWidth: 1,
    marginBottom: minimalistTheme.spacing.lg,
  },
  avatarContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: minimalistTheme.spacing.md,
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  profileInfo: {
    flex: 1,
  },
  userName: {
    fontSize: minimalistTheme.typography.fontSize.lg,
    fontWeight: minimalistTheme.typography.fontWeight.semibold,
    marginBottom: 2,
  },
  userEmail: {
    fontSize: minimalistTheme.typography.fontSize.sm,
  },
  menuItems: {
    flex: 1,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: minimalistTheme.spacing.lg,
    paddingVertical: minimalistTheme.spacing.md,
    borderBottomWidth: StyleSheet.hairlineWidth,
  },
  menuItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuIcon: {
    marginRight: minimalistTheme.spacing.md,
    width: 20,
  },
  menuItemText: {
    fontSize: minimalistTheme.typography.fontSize.base,
    fontWeight: minimalistTheme.typography.fontWeight.medium,
    flex: 1,
  },
  footer: {
    paddingHorizontal: minimalistTheme.spacing.lg,
    paddingVertical: minimalistTheme.spacing.lg,
    alignItems: 'center',
  },
  appVersion: {
    fontSize: minimalistTheme.typography.fontSize.xs,
  },
});