import { useState, useEffect, useCallback } from 'react';
import { supabase } from '../supabase/client';

interface UseProductBrandsReturn {
  brands: string[];
  isLoading: boolean;
  pick: (brand: string) => Promise<void>;
}

// Extract brand from product name (optimized for NZ products)
const extractBrand = (productName: string): string | null => {
  const words = productName.trim().split(' ');
  if (words.length < 2) return null;
  
  // Known NZ brands for instant recognition
  const knownBrands = [
    'Nescafe', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', 'Avalanche',
    'Anchor', 'Mainland', 'Tara<PERSON><PERSON>', 'Lewis Road', 'Meadow Fresh',
    'Tip Top', '<PERSON><PERSON>berg', '<PERSON>urgen', 'Vogels', 'Wonder White',
    'Cadbury', 'Whittakers', 'Nestle', 'Mars', 'Snickers',
    'Bluebird', 'ETA', 'Watties', 'Maggi', 'Continental',
    'Pams', 'Homebrand', 'Essentials', 'Budget', 'Value',
    'Boss', '<PERSON>', 'Special Blend', 'Pretzels', 'Tostitos'
  ];
  
  const firstWord = words[0];
  const firstTwoWords = words.slice(0, 2).join(' ');
  
  // Check known brands first
  if (knownBrands.some(brand => brand.toLowerCase() === firstWord.toLowerCase())) {
    return firstWord;
  }
  if (knownBrands.some(brand => brand.toLowerCase() === firstTwoWords.toLowerCase())) {
    return firstTwoWords;
  }
  
  // Fallback to first word if it looks like a brand
  const commonWords = ['fresh', 'organic', 'premium', 'select', 'choice', 'best', 'super', 'medium', 'large', 'small'];
  if (!commonWords.includes(firstWord.toLowerCase()) && /^[A-Z]/.test(firstWord)) {
    return firstWord;
  }
  
  return null;
};

export function useProductBrands(productName: string, userId?: string): UseProductBrandsReturn {
  const [brands, setBrands] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const searchBrands = useCallback(async (query: string) => {
    if (!query?.trim() || query.length < 2) {
      setBrands([]);
      return;
    }

    setIsLoading(true);
    try {
      // Search for products matching the query
      const { data, error } = await supabase
        .from('products')
        .select('name, brand')
        .or(`name.ilike.${query}%,name.ilike.%${query}%`)
        .not('price', 'is', null)
        .limit(100); // Get more to extract brands from

      if (error) {
        console.error('Brand search error:', error);
        setBrands([]);
        return;
      }

      // Extract brands from product names and existing brand field
      const extractedBrands = new Set<string>();
      
      (data || []).forEach(product => {
        // Try existing brand field first
        if (product.brand && product.brand.trim()) {
          extractedBrands.add(product.brand.trim());
        } else {
          // Extract from product name
          const brand = extractBrand(product.name);
          if (brand) {
            extractedBrands.add(brand);
          }
        }
      });

      const uniqueBrands = Array.from(extractedBrands).sort();
      setBrands(uniqueBrands);
      
    } catch (error) {
      console.error('Brand search failed:', error);
      setBrands([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const pick = useCallback(async (brand: string) => {
    if (!userId || !productName || !brand) return;
    
    try {
      await supabase
        .from('user_brand_prefs')
        .upsert({ 
          user_id: userId, 
          product_name: productName.toLowerCase().trim(), 
          brand: brand.trim()
        });
    } catch (error) {
      console.error('Failed to save brand preference:', error);
      // Don't throw error, just log it
    }
  }, [userId, productName]);

  useEffect(() => {
    searchBrands(productName);
  }, [productName, searchBrands]);

  return { brands, isLoading, pick };
}