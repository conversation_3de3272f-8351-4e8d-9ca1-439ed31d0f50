// Database types for the unified Supabase schema
export interface Database {
  public: {
    Tables: {
      products: {
        Row: {
          id: string;
          object_id: string | null;
          name: string;
          display_name: string | null;
          description: string | null;
          brand: string | null;
          brand_name: string | null;
          price: number | null;
          current_price: number | null;
          original_price: number | null;
          sale_price: number | null;
          unit_price: number | null;
          original_unit_price: number | null;
          per_unit_price: number | null;
          discount_percentage: number | null;
          category: string | null;
          subcategory: string | null;
          size: string | null;
          weight: string | null;
          unit: string | null;
          unit_name: string | null;
          pack_size: string | null;
          package_size: string | null;
          original_unit_quantity: number | null;
          store: string;
          store_name: string | null;
          store_color: string | null;
          store_logo: string | null;
          source_site: string | null;
          availability: string | null;
          is_available: boolean | null;
          stock_level: number | null;
          barcode: string | null;
          sku: string | null;
          product_code: string | null;
          product_type: string | null;
          supplier: string | null;
          image_url: string | null;
          thumbnail_url: string | null;
          image_urls: string[] | null;
          nutritional_info: any | null;
          nutritional_data: any | null;
          allergens: string[] | null;
          ingredients: string | null;
          product_details: any | null;
          searchable_text: string | null;
          shopping_aliases: string[] | null;
          shopping_priority: number | null;
          is_common_item: boolean | null;
          tags: string[] | null;
          promotion_type: string | null;
          promotion_description: string | null;
          promotions: any | null;
          rating: number | null;
          review_count: number | null;
          seasonal: boolean | null;
          organic: boolean | null;
          gluten_free: boolean | null;
          vegan: boolean | null;
          country_of_origin: string | null;
          created_at: string | null;
          updated_at: string | null;
          last_checked: string | null;
          last_updated: string | null;
          image_uploaded_at: string | null;
        };
        Insert: {
          id: string;
          object_id?: string | null;
          name: string;
          display_name?: string | null;
          description?: string | null;
          brand?: string | null;
          brand_name?: string | null;
          price?: number | null;
          current_price?: number | null;
          original_price?: number | null;
          sale_price?: number | null;
          unit_price?: number | null;
          original_unit_price?: number | null;
          per_unit_price?: number | null;
          discount_percentage?: number | null;
          category?: string | null;
          subcategory?: string | null;
          size?: string | null;
          weight?: string | null;
          unit?: string | null;
          unit_name?: string | null;
          pack_size?: string | null;
          package_size?: string | null;
          original_unit_quantity?: number | null;
          store: string;
          store_name?: string | null;
          store_color?: string | null;
          store_logo?: string | null;
          source_site?: string | null;
          availability?: string | null;
          is_available?: boolean | null;
          stock_level?: number | null;
          barcode?: string | null;
          sku?: string | null;
          product_code?: string | null;
          product_type?: string | null;
          supplier?: string | null;
          image_url?: string | null;
          thumbnail_url?: string | null;
          image_urls?: string[] | null;
          nutritional_info?: any | null;
          nutritional_data?: any | null;
          allergens?: string[] | null;
          ingredients?: string | null;
          product_details?: any | null;
          searchable_text?: string | null;
          shopping_aliases?: string[] | null;
          shopping_priority?: number | null;
          is_common_item?: boolean | null;
          tags?: string[] | null;
          promotion_type?: string | null;
          promotion_description?: string | null;
          promotions?: any | null;
          rating?: number | null;
          review_count?: number | null;
          seasonal?: boolean | null;
          organic?: boolean | null;
          gluten_free?: boolean | null;
          vegan?: boolean | null;
          country_of_origin?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
          last_checked?: string | null;
          last_updated?: string | null;
          image_uploaded_at?: string | null;
        };
        Update: {
          id?: string;
          object_id?: string | null;
          name?: string;
          display_name?: string | null;
          description?: string | null;
          brand?: string | null;
          brand_name?: string | null;
          price?: number | null;
          current_price?: number | null;
          original_price?: number | null;
          sale_price?: number | null;
          unit_price?: number | null;
          original_unit_price?: number | null;
          per_unit_price?: number | null;
          discount_percentage?: number | null;
          category?: string | null;
          subcategory?: string | null;
          size?: string | null;
          weight?: string | null;
          unit?: string | null;
          unit_name?: string | null;
          pack_size?: string | null;
          package_size?: string | null;
          original_unit_quantity?: number | null;
          store?: string;
          store_name?: string | null;
          store_color?: string | null;
          store_logo?: string | null;
          source_site?: string | null;
          availability?: string | null;
          is_available?: boolean | null;
          stock_level?: number | null;
          barcode?: string | null;
          sku?: string | null;
          product_code?: string | null;
          product_type?: string | null;
          supplier?: string | null;
          image_url?: string | null;
          thumbnail_url?: string | null;
          image_urls?: string[] | null;
          nutritional_info?: any | null;
          nutritional_data?: any | null;
          allergens?: string[] | null;
          ingredients?: string | null;
          product_details?: any | null;
          searchable_text?: string | null;
          shopping_aliases?: string[] | null;
          shopping_priority?: number | null;
          is_common_item?: boolean | null;
          tags?: string[] | null;
          promotion_type?: string | null;
          promotion_description?: string | null;
          promotions?: any | null;
          rating?: number | null;
          review_count?: number | null;
          seasonal?: boolean | null;
          organic?: boolean | null;
          gluten_free?: boolean | null;
          vegan?: boolean | null;
          country_of_origin?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
          last_checked?: string | null;
          last_updated?: string | null;
          image_uploaded_at?: string | null;
        };
      };
      recipes: {
        Row: {
          id: string;
          title: string;
          description: string | null;
          ingredients: any;
          instructions: string;
          image_url: string | null;
          prep_time: number | null;
          cook_time: number | null;
          servings: number | null;
          difficulty: string | null;
          cuisine: string | null;
          tags: string[] | null;
          is_public: boolean | null;
          created_at: string | null;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          id?: string;
          title: string;
          description?: string | null;
          ingredients?: any;
          instructions: string;
          image_url?: string | null;
          prep_time?: number | null;
          cook_time?: number | null;
          servings?: number | null;
          difficulty?: string | null;
          cuisine?: string | null;
          tags?: string[] | null;
          is_public?: boolean | null;
          created_at?: string | null;
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          id?: string;
          title?: string;
          description?: string | null;
          ingredients?: any;
          instructions?: string;
          image_url?: string | null;
          prep_time?: number | null;
          cook_time?: number | null;
          servings?: number | null;
          difficulty?: string | null;
          cuisine?: string | null;
          tags?: string[] | null;
          is_public?: boolean | null;
          created_at?: string | null;
          updated_at?: string | null;
          user_id?: string;
        };
      };
      stores: {
        Row: {
          id: number;
          name: string;
          display_name: string;
          color: string | null;
          logo: string | null;
          website_url: string | null;
          api_endpoint: string | null;
          is_active: boolean | null;
          created_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          id?: number;
          name: string;
          display_name: string;
          color?: string | null;
          logo?: string | null;
          website_url?: string | null;
          api_endpoint?: string | null;
          is_active?: boolean | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          id?: number;
          name?: string;
          display_name?: string;
          color?: string | null;
          logo?: string | null;
          website_url?: string | null;
          api_endpoint?: string | null;
          is_active?: boolean | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
      };
      prices: {
        Row: {
          id: number;
          product_id: string;
          store_id: number;
          price: number;
          original_price: number | null;
          discount_percentage: number | null;
          recorded_at: string | null;
          created_at: string | null;
        };
        Insert: {
          id?: number;
          product_id: string;
          store_id: number;
          price: number;
          original_price?: number | null;
          discount_percentage?: number | null;
          recorded_at?: string | null;
          created_at?: string | null;
        };
        Update: {
          id?: number;
          product_id?: string;
          store_id?: number;
          price?: number;
          original_price?: number | null;
          discount_percentage?: number | null;
          recorded_at?: string | null;
          created_at?: string | null;
        };
      };
      shopping_lists: {
        Row: {
          id: string;
          user_id: string;
          name: string;
          is_default: boolean | null;
          created_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          id?: string;
          user_id: string;
          name?: string;
          is_default?: boolean | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          id?: string;
          user_id?: string;
          name?: string;
          is_default?: boolean | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
      };
      shopping_list_items: {
        Row: {
          id: string;
          shopping_list_id: string;
          product_id: string | null;
          custom_name: string | null;
          quantity: number | null;
          unit: string | null;
          notes: string | null;
          is_completed: boolean | null;
          preferred_store: string | null;
          preferred_brand: string | null;
          created_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          id?: string;
          shopping_list_id: string;
          product_id?: string | null;
          custom_name?: string | null;
          quantity?: number | null;
          unit?: string | null;
          notes?: string | null;
          is_completed?: boolean | null;
          preferred_store?: string | null;
          preferred_brand?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          id?: string;
          shopping_list_id?: string;
          product_id?: string | null;
          custom_name?: string | null;
          quantity?: number | null;
          unit?: string | null;
          notes?: string | null;
          is_completed?: boolean | null;
          preferred_store?: string | null;
          preferred_brand?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
      };
    };
    Views: {
      available_products: {
        Row: {
          [key: string]: any;
        };
      };
      price_comparison: {
        Row: {
          name: string | null;
          min_price: number | null;
          max_price: number | null;
          avg_price: number | null;
          store_count: number | null;
          available_stores: string[] | null;
        };
      };
      public_recipes: {
        Row: {
          [key: string]: any;
        };
      };
    };
    Functions: {
      get_cheapest_price: {
        Args: {
          product_name: string;
        };
        Returns: {
          store: string;
          price: number;
          brand: string;
          id: string;
        }[];
      };
      compare_product_prices: {
        Args: {
          product_name: string;
        };
        Returns: {
          store: string;
          price: number;
          brand: string;
          availability: string;
          id: string;
        }[];
      };
      search_products: {
        Args: {
          search_query: string;
        };
        Returns: {
          id: string;
          name: string;
          price: number;
          store: string;
          brand: string;
          availability: string;
        }[];
      };
    };
  };
}