/**
 * INTEGRATION EXAMPLE - How to Use Modern Design System
 * 
 * This file shows how to integrate the modern components
 * into your existing AllProductsScreen.tsx
 */

import React, { useState, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// Import modern design system
import { getGroceryTheme } from './src/styles/modernGroceryTheme';
import { ModernProductCard } from './src/components/ModernProductCard';
import { ModernSearchBar } from './src/components/ModernSearchBar';
import { ModernFilterPills, StoreFilterPills } from './src/components/ModernFilterPills';

// Your existing imports
import { useTheme } from './src/context/ThemeContext';
import { useOptimizedProducts } from './src/hooks/useOptimizedProducts';

const CATEGORIES = [
  { id: 'All', label: 'All Categories', icon: 'apps' },
  { id: 'fruit-veg', label: 'Fruit & Veg', icon: 'leaf' },
  { id: 'meat-poultry', label: 'Meat & Poultry', icon: 'restaurant' },
  { id: 'fish-seafood', label: 'Fish & Seafood', icon: 'fish' },
  { id: 'fridge-deli', label: 'Fridge & Deli', icon: 'snow' },
  { id: 'bakery', label: 'Bakery', icon: 'cafe' },
  { id: 'pantry', label: 'Pantry', icon: 'archive' },
  { id: 'drinks', label: 'Drinks', icon: 'wine' },
  { id: 'household', label: 'Household', icon: 'home' },
];

export const ModernAllProductsScreen: React.FC = () => {
  // Theme setup
  const { isDark } = useTheme();
  const theme = getGroceryTheme(isDark ? 'dark' : 'light');
  
  // State management
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedStores, setSelectedStores] = useState<string[]>([
    'woolworths', 'newworld', 'paknsave'
  ]);

  // Your existing optimized products hook
  const {
    productGroups,
    loadingState,
    hasMore,
    loadMore,
    refresh,
  } = useOptimizedProducts({
    pageSize: 20,
    searchQuery,
    selectedCategory,
    selectedStores,
    sortBy: 'relevance',
    enableCache: true,
  });

  // Memoized styles for performance
  const styles = useMemo(() => createStyles(theme), [theme]);

  // Enhanced category data with counts
  const categoriesWithCounts = useMemo(() => {
    return CATEGORIES.map(category => ({
      ...category,
      count: category.id === 'All' 
        ? productGroups.length
        : productGroups.filter(group => 
            group.products.some(product => product.category === category.id)
          ).length
    }));
  }, [productGroups]);

  // Handlers
  const handleCategoryChange = useCallback((categoryId: string) => {
    setSelectedCategory(categoryId);
  }, []);

  const handleStoreToggle = useCallback((store: string) => {
    setSelectedStores(prev =>
      prev.includes(store)
        ? prev.filter(s => s !== store)
        : [...prev, store]
    );
  }, []);

  const handleProductPress = useCallback((productGroup: any) => {
    // Your existing product detail modal logic
    console.log('Product pressed:', productGroup.name);
  }, []);

  const handleAddToList = useCallback((productGroup: any) => {
    // Your existing add to list logic
    console.log('Add to list:', productGroup.name);
  }, []);

  // Render header with modern components
  const renderModernHeader = () => (
    <View style={styles.headerContainer}>
      {/* Page Title */}
      <View style={styles.titleContainer}>
        <Text style={styles.pageTitle}>All Products</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.viewModeButton}
            onPress={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
          >
            <Ionicons
              name={viewMode === 'grid' ? 'list' : 'grid'}
              size={20}
              color={theme.colors.neutral[600]}
            />
          </TouchableOpacity>
        </View>
      </View>

      {/* Modern Search Bar */}
      <ModernSearchBar
        value={searchQuery}
        onChangeText={setSearchQuery}
        placeholder="Search products..."
        themeMode={theme.mode}
      />

      {/* Modern Category Filter Pills */}
      <ModernFilterPills
        options={categoriesWithCounts}
        selectedId={selectedCategory}
        onSelectionChange={handleCategoryChange}
        themeMode={theme.mode}
        showCounts={true}
        scrollable={true}
      />

      {/* Store Filter Pills */}
      <StoreFilterPills
        selectedStores={selectedStores}
        onStoreToggle={handleStoreToggle}
        themeMode={theme.mode}
      />

      {/* Results Summary */}
      <View style={styles.resultsSummary}>
        <Text style={styles.resultsText}>
          {productGroups.length} products found
        </Text>
        {selectedStores.length < 3 && (
          <Text style={styles.storeFilterText}>
            from {selectedStores.length} store{selectedStores.length !== 1 ? 's' : ''}
          </Text>
        )}
      </View>
    </View>
  );

  // Render product item with modern card
  const renderProductItem = ({ item, index }: { item: any; index: number }) => (
    <ModernProductCard
      item={item}
      viewMode={viewMode}
      onPress={handleProductPress}
      onAddToList={handleAddToList}
      themeMode={theme.mode}
      isLoading={loadingState.loadingMore && index >= unifiedProducts.length - 3}
    />
  );

  // Empty state
  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons 
        name="search" 
        size={64} 
        color={theme.colors.neutral[300]} 
      />
      <Text style={styles.emptyTitle}>No products found</Text>
      <Text style={styles.emptySubtext}>
        Try adjusting your search or filters
      </Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <FlatList
        data={unifiedProducts}
        renderItem={renderProductItem}
        keyExtractor={(item, index) => `${item.name}-${index}`}
        ListHeaderComponent={renderModernHeader}
        ListEmptyComponent={!loadingState.initial ? renderEmptyState : null}
        numColumns={viewMode === 'grid' ? 2 : 1}
        key={viewMode} // Force re-render when view mode changes
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        
        // Performance optimizations
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        windowSize={10}
        initialNumToRender={8}
        updateCellsBatchingPeriod={50}
        
        // Pull to refresh
        refreshControl={
          <RefreshControl
            refreshing={loadingState.refreshing}
            onRefresh={refresh}
            colors={[theme.colors.primary[500]]}
            tintColor={theme.colors.primary[500]}
          />
        }
        
        // Infinite scroll
        onEndReached={loadMore}
        onEndReachedThreshold={0.5}
      />
    </SafeAreaView>
  );
};

// Dynamic styles based on theme
const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.semantic.background,
  },

  listContainer: {
    paddingBottom: theme.spacing['2xl'],
  },

  headerContainer: {
    backgroundColor: theme.colors.semantic.surface,
    paddingHorizontal: theme.spacing.base,
    paddingTop: theme.spacing.base,
    paddingBottom: theme.spacing.sm,
    marginBottom: theme.spacing.base,
    ...theme.shadows.card,
  },

  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.base,
  },

  pageTitle: {
    fontSize: theme.typography.fontSize['2xl'],
    fontWeight: theme.typography.fontWeight.bold,
    color: theme.colors.neutral[900],
    fontFamily: theme.typography.fontFamily.primary,
  },

  headerActions: {
    flexDirection: 'row',
    gap: theme.spacing.sm,
  },

  viewModeButton: {
    width: 40,
    height: 40,
    borderRadius: theme.borderRadius.lg,
    backgroundColor: theme.colors.neutral[100],
    justifyContent: 'center',
    alignItems: 'center',
    ...theme.shadows.card,
  },

  resultsSummary: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: theme.spacing.base,
    paddingTop: theme.spacing.sm,
    borderTopWidth: 1,
    borderTopColor: theme.colors.semantic.borderLight,
  },

  resultsText: {
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.medium,
    color: theme.colors.neutral[600],
    fontFamily: theme.typography.fontFamily.primary,
  },

  storeFilterText: {
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.neutral[500],
    fontFamily: theme.typography.fontFamily.primary,
  },

  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: theme.spacing['5xl'],
    paddingHorizontal: theme.spacing['2xl'],
  },

  emptyTitle: {
    fontSize: theme.typography.fontSize.xl,
    fontWeight: theme.typography.fontWeight.semibold,
    color: theme.colors.neutral[700],
    marginTop: theme.spacing.base,
    marginBottom: theme.spacing.sm,
    fontFamily: theme.typography.fontFamily.primary,
  },

  emptySubtext: {
    fontSize: theme.typography.fontSize.base,
    color: theme.colors.neutral[500],
    textAlign: 'center',
    lineHeight: theme.typography.lineHeight.lg,
    fontFamily: theme.typography.fontFamily.primary,
  },
});

export default ModernAllProductsScreen;