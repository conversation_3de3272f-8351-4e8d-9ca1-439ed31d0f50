/**
 * Tests for ProductDeduplicationService
 * Story 1.1: Product Deduplication Algorithm
 */

import { ProductDeduplicationService } from '../../src/services/productDeduplicationService';
import { IProduct } from '../../src/types/shoppingList';
import { DeduplicationConfig, DEFAULT_DEDUPLICATION_CONFIG } from '../../src/types/deduplication';

describe('ProductDeduplicationService', () => {
  let service: ProductDeduplicationService;
  
  // Mock product data for testing
  const mockProducts: IProduct[] = [
    {
      id: '1',
      name: 'Anchor Milk 1L',
      price: 3.50,
      store: 'woolworths',
      brand: 'Anchor',
      size: '1L',
      category: 'dairy',
      unit: '1 litre',
      is_available: true
    },
    {
      id: '2', 
      name: 'Anchor Milk 1L',
      price: 3.20,
      store: 'newworld',
      brand: 'Anchor',
      size: '1L',
      category: 'dairy',
      unit: '1 litre',
      is_available: true
    },
    {
      id: '3',
      name: 'Anchor Milk 1L',
      price: 2.99,
      store: 'paknsave',
      brand: 'Anchor', 
      size: '1L',
      category: 'dairy',
      unit: '1 litre',
      is_available: true
    },
    {
      id: '4',
      name: 'Kelloggs Cornflakes 500g',
      price: 5.50,
      store: 'woolworths',
      brand: 'Kelloggs',
      size: '500g',
      category: 'breakfast',
      unit: '500 grams',
      is_available: true
    },
    {
      id: '5',
      name: 'Kelloggs Cornflakes 500g',
      price: 5.25,
      store: 'newworld',
      brand: 'Kelloggs',
      size: '500g', 
      category: 'breakfast',
      unit: '500 grams',
      is_available: true
    },
    {
      id: '6',
      name: 'Coca Cola 2L',
      price: 3.99,
      store: 'woolworths',
      brand: 'Coca Cola',
      size: '2L',
      category: 'drinks',
      unit: '2 litres',
      is_available: true
    }
  ];

  beforeEach(() => {
    service = new ProductDeduplicationService();
  });

  describe('Configuration Management', () => {
    test('should initialize with default configuration', () => {
      const config = service.getConfiguration();
      expect(config).toEqual(DEFAULT_DEDUPLICATION_CONFIG);
    });

    test('should allow threshold configuration', () => {
      const newThresholds = {
        nameSimilarityThreshold: 0.9,
        requireExactBrandMatch: false
      };
      
      service.configureThresholds(newThresholds);
      const config = service.getConfiguration();
      
      expect(config.nameSimilarityThreshold).toBe(0.9);
      expect(config.requireExactBrandMatch).toBe(false);
      expect(config.requireExactCategoryMatch).toBe(DEFAULT_DEDUPLICATION_CONFIG.requireExactCategoryMatch); // Should remain unchanged
    });

    test('should validate threshold values', () => {
      expect(() => {
        service.configureThresholds({ nameSimilarityThreshold: 1.5 });
      }).toThrow('nameSimilarityThreshold must be between 0 and 1');

      expect(() => {
        service.configureThresholds({ minimumConfidence: -0.1 });
      }).toThrow('minimumConfidence must be between 0 and 1');
    });

    test('should reset configuration to defaults', () => {
      service.configureThresholds({ nameSimilarityThreshold: 0.9 });
      service.resetConfiguration();
      
      const config = service.getConfiguration();
      expect(config).toEqual(DEFAULT_DEDUPLICATION_CONFIG);
    });
  });

  describe('Product Matching Confidence', () => {
    test('should return 1.0 for identical products', () => {
      const product = mockProducts[0];
      const confidence = service.getMatchingConfidence(product, product);
      expect(confidence).toBe(1.0);
    });

    test('should return high confidence for same product from different stores', () => {
      const anchorMilk1 = mockProducts[0]; // Woolworths
      const anchorMilk2 = mockProducts[1]; // New World
      
      const confidence = service.getMatchingConfidence(anchorMilk1, anchorMilk2);
      expect(confidence).toBeGreaterThan(0.85); // Should meet 85% threshold
    });

    test('should return low confidence for different products', () => {
      const anchorMilk = mockProducts[0];
      const cornflakes = mockProducts[3];
      
      const confidence = service.getMatchingConfidence(anchorMilk, cornflakes);
      expect(confidence).toBeLessThan(0.5); // Should be clearly different
    });

    test('should handle missing brand gracefully', () => {
      const productWithoutBrand: IProduct = {
        ...mockProducts[0],
        id: 'test',
        brand: undefined
      };
      
      const confidence = service.getMatchingConfidence(mockProducts[0], productWithoutBrand);
      expect(confidence).toBeGreaterThan(0); // Should not crash
    });
  });

  describe('Product Deduplication', () => {
    test('should deduplicate identical products from different stores', () => {
      const anchorProducts = mockProducts.slice(0, 3); // 3 Anchor Milk products
      const result = service.deduplicateProducts(anchorProducts);
      
      expect(result.stats.originalCount).toBe(3);
      expect(result.stats.groupCount).toBe(1);
      expect(result.stats.unifiedCount).toBe(1);
      
      const unifiedProduct = result.unifiedProducts[0];
      expect(unifiedProduct.allStores).toHaveLength(3);
      expect(unifiedProduct.storePrices).toHaveProperty('woolworths', 3.50);
      expect(unifiedProduct.storePrices).toHaveProperty('newworld', 3.20);
      expect(unifiedProduct.storePrices).toHaveProperty('paknsave', 2.99);
      expect(unifiedProduct.lowestPrice).toBe(2.99);
      expect(unifiedProduct.bestStore).toBe('paknsave');
    });

    test('should create separate groups for different products', () => {
      const result = service.deduplicateProducts(mockProducts);
      
      expect(result.stats.originalCount).toBe(6);
      expect(result.stats.groupCount).toBe(3); // Anchor Milk, Kelloggs, Coca Cola
      expect(result.stats.unifiedCount).toBe(3);
      
      // Find each product group
      const anchorMilk = result.unifiedProducts.find(p => p.name.includes('Anchor'));
      const cornflakes = result.unifiedProducts.find(p => p.name.includes('Cornflakes'));
      const cocaCola = result.unifiedProducts.find(p => p.name.includes('Coca Cola'));
      
      expect(anchorMilk).toBeDefined();
      expect(cornflakes).toBeDefined();
      expect(cocaCola).toBeDefined();
      
      expect(anchorMilk!.allStores).toHaveLength(3);
      expect(cornflakes!.allStores).toHaveLength(2);
      expect(cocaCola!.allStores).toHaveLength(1);
    });

    test('should handle empty product array', () => {
      const result = service.deduplicateProducts([]);
      
      expect(result.stats.originalCount).toBe(0);
      expect(result.stats.groupCount).toBe(0);
      expect(result.stats.unifiedCount).toBe(0);
      expect(result.unifiedProducts).toHaveLength(0);
    });

    test('should use provided configuration overrides', () => {
      const strictConfig: Partial<DeduplicationConfig> = {
        nameSimilarityThreshold: 0.95,
        minimumConfidence: 0.9
      };
      
      const result = service.deduplicateProducts(mockProducts, strictConfig);
      
      // With stricter matching, should create more groups
      expect(result.stats.groupCount).toBeGreaterThanOrEqual(3);
      expect(result.config.nameSimilarityThreshold).toBe(0.95);
    });

    test('should calculate accurate performance statistics', () => {
      const result = service.deduplicateProducts(mockProducts);
      
      expect(result.stats.processingTimeMs).toBeGreaterThan(0);
      expect(result.stats.averageConfidence).toBeGreaterThan(0);
      expect(result.stats.averageConfidence).toBeLessThanOrEqual(1.0);
    });
  });

  describe('Fuzzy Matching Requirements', () => {
    test('should match products with minor name variations', () => {
      const products: IProduct[] = [
        {
          id: '1',
          name: 'Cadbury Dairy Milk Chocolate 200g',
          price: 4.50,
          store: 'woolworths',
          brand: 'Cadbury',
          size: '200g',
          category: 'confectionery'
        },
        {
          id: '2',
          name: 'Cadbury Dairy Milk Chocolate 200g Block',
          price: 4.25,
          store: 'newworld',
          brand: 'Cadbury',
          size: '200g',
          category: 'confectionery'
        }
      ];
      
      const result = service.deduplicateProducts(products);
      expect(result.stats.groupCount).toBe(1); // Should be grouped as same product
    });

    test('should handle size variations correctly', () => {
      const products: IProduct[] = [
        {
          id: '1',
          name: 'Fresh Milk',
          price: 3.50,
          store: 'woolworths',
          brand: 'Meadow Fresh',
          size: '1L',
          category: 'dairy'
        },
        {
          id: '2',
          name: 'Fresh Milk',
          price: 3.20,
          store: 'newworld',
          brand: 'Meadow Fresh',
          size: '1 Litre',
          category: 'dairy'
        }
      ];
      
      const result = service.deduplicateProducts(products);
      expect(result.stats.groupCount).toBe(1); // Should match despite size format difference
    });
  });

  describe('Edge Cases and Error Handling', () => {
    test('should handle products with missing fields', () => {
      const productsWithMissingFields: IProduct[] = [
        {
          id: '1',
          name: 'Test Product',
          price: 5.00,
          store: 'woolworths'
          // Missing brand, size, category
        },
        {
          id: '2', 
          name: 'Test Product',
          price: 4.50,
          store: 'newworld'
          // Missing brand, size, category
        }
      ];
      
      expect(() => {
        service.deduplicateProducts(productsWithMissingFields);
      }).not.toThrow();
      
      const result = service.deduplicateProducts(productsWithMissingFields);
      expect(result.stats.groupCount).toBeGreaterThan(0);
    });

    test('should handle duplicate product IDs gracefully', () => {
      const duplicateProducts: IProduct[] = [
        mockProducts[0],
        { ...mockProducts[0], store: 'newworld' } // Same ID, different store
      ];
      
      expect(() => {
        service.deduplicateProducts(duplicateProducts);
      }).not.toThrow();
    });

    test('should maintain confidence scores within valid range', () => {
      const result = service.deduplicateProducts(mockProducts);
      
      result.groups.forEach(group => {
        expect(group.groupConfidence).toBeGreaterThanOrEqual(0);
        expect(group.groupConfidence).toBeLessThanOrEqual(1);
        
        group.products.forEach(productMatch => {
          expect(productMatch.confidence).toBeGreaterThanOrEqual(0);
          expect(productMatch.confidence).toBeLessThanOrEqual(1);
        });
      });
    });
  });

  describe('Performance Requirements', () => {
    test('should process products within 2 second limit', () => {
      // Create larger dataset for performance testing
      const largeDataset: IProduct[] = [];
      
      for (let i = 0; i < 100; i++) {
        largeDataset.push({
          id: `product_${i}`,
          name: `Test Product ${i % 10}`, // Create some duplicates
          price: Math.random() * 10,
          store: ['woolworths', 'newworld', 'paknsave'][i % 3],
          brand: `Brand ${i % 5}`,
          category: `Category ${i % 3}`
        });
      }
      
      const result = service.deduplicateProducts(largeDataset);
      
      expect(result.stats.processingTimeMs).toBeLessThan(2000); // Under 2 seconds as per story
      expect(result.stats.originalCount).toBe(100);
    });
  });

  describe('Integration with Existing Architecture', () => {
    test('should maintain backward compatibility with groupProducts method', () => {
      // Test that the legacy method still works
      const unifiedProducts = service.groupProducts(mockProducts);
      
      expect(unifiedProducts).toBeInstanceOf(Array);
      expect(unifiedProducts.length).toBeGreaterThan(0);
      
      // Verify unified product structure
      unifiedProducts.forEach(product => {
        expect(product).toHaveProperty('unifiedId');
        expect(product).toHaveProperty('storePrices');
        expect(product).toHaveProperty('allStores');
        expect(product).toHaveProperty('lowestPrice');
        expect(product).toHaveProperty('bestStore');
      });
    });

    test('should export required functions as per story specification', () => {
      // Verify the service has all required methods
      expect(typeof service.deduplicateProducts).toBe('function');
      expect(typeof service.configureThresholds).toBe('function');
      expect(typeof service.getMatchingConfidence).toBe('function');
    });
  });
});

// Performance benchmark helper
describe('Performance Benchmarks', () => {
  test('should handle realistic Supabase result set sizes efficiently', () => {
    const service = new ProductDeduplicationService();
    
    // Simulate realistic Supabase query result (200-500 products)
    const realisticDataset: IProduct[] = [];
    const productNames = [
      'Milk 1L', 'Bread White 750g', 'Eggs Free Range 12pk', 'Chicken Breast 1kg',
      'Bananas 1kg', 'Rice 1kg', 'Pasta 500g', 'Olive Oil 500ml', 'Tomatoes 1kg'
    ];
    const brands = ['Brand A', 'Brand B', 'Brand C'];
    const stores = ['woolworths', 'newworld', 'paknsave'];
    
    for (let i = 0; i < 300; i++) {
      realisticDataset.push({
        id: `realistic_${i}`,
        name: productNames[i % productNames.length],
        price: 2 + Math.random() * 8,
        store: stores[i % stores.length],
        brand: brands[i % brands.length],
        category: 'groceries',
        size: '1kg'
      });
    }
    
    const startTime = performance.now();
    const result = service.deduplicateProducts(realisticDataset);
    const endTime = performance.now();
    
    expect(endTime - startTime).toBeLessThan(2000); // Under 2 seconds
    expect(result.stats.originalCount).toBe(300);
    expect(result.stats.groupCount).toBeLessThanOrEqual(productNames.length); // Should group effectively
    
    console.log(`Performance benchmark: ${result.stats.processingTimeMs}ms for ${realisticDataset.length} products`);
  });
});