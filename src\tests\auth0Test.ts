// Basic test to verify Auth0 service integration
import { Auth0Service } from '../services/auth0Service';
import { isAuth0Configured } from '../constants/config';

// Test Auth0 service functionality
export const testAuth0Integration = async () => {
  console.log('🧪 Testing Auth0 integration...');
  
  try {
    // Test 1: Check if Auth0 is properly configured
    const isConfigured = Auth0Service.isConfigured();
    console.log(`✅ Auth0 configuration check: ${isConfigured ? 'Configured' : 'Not configured (will use mock)'}`);
    
    // Test 2: Test mock login functionality
    console.log('🧪 Testing Auth0 mock login...');
    const mockResult = await Auth0Service.mockLogin();
    
    if (mockResult.success) {
      console.log('✅ Auth0 mock login successful');
      console.log('   User:', mockResult.user);
      console.log('   Has access token:', Boolean(mockResult.accessToken));
      
      // Test 3: Test token validation
      if (mockResult.accessToken) {
        const isValid = Auth0Service.isTokenValid(mockResult.accessToken);
        console.log(`✅ Token validation: ${isValid ? 'Valid' : 'Invalid'}`);
      }
    } else {
      console.log('❌ Auth0 mock login failed:', mockResult.error);
    }
    
    // Test 4: Test configuration status
    const configStatus = isAuth0Configured();
    console.log(`✅ Auth0 configuration status: ${configStatus}`);
    
    console.log('🎉 Auth0 integration test completed successfully!');
    return true;
    
  } catch (error) {
    console.error('❌ Auth0 integration test failed:', error);
    return false;
  }
};

// Export default test
export default testAuth0Integration;