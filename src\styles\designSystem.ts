// 🎨 FIGMA-STY<PERSON> DESIGN SYSTEM
// This file gives you Figma-like control over your entire app's design

export const designSystem = {
  // 🎨 COLOR PALETTE (like Figma's color styles)
  colors: {
    // Primary palette
    primary: {
      50: '#EEF2FF',
      100: '#E0E7FF', 
      500: '#667EEA',  // Main brand color
      600: '#5A67D8',
      900: '#312E81',
    },
    
    // Semantic colors
    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
    
    // Neutral palette
    gray: {
      50: '#F9FAFB',
      100: '#F3F4F6',
      200: '#E5E7EB',
      300: '#D1D5DB',
      500: '#6B7280',
      700: '#374151',
      900: '#111827',
    },
    
    // Background colors
    background: {
      primary: '#FFFFFF',
      secondary: '#F9FAFB',
      accent: '#667EEA',
    }
  },

  // 📏 SPACING (like Figma's spacing tokens)
  spacing: {
    xs: 4,
    sm: 8,
    md: 12,
    lg: 16,
    xl: 20,
    '2xl': 24,
    '3xl': 32,
    '4xl': 40,
    '5xl': 48,
  },

  // 🔤 TYPOGRAPHY (like Figma's text styles)
  typography: {
    fontFamily: {
      primary: 'System', // iOS: San Francisco, Android: Roboto
      bold: 'System-Bold',
    },
    fontSize: {
      xs: 12,
      sm: 14,
      md: 16,
      lg: 18,
      xl: 20,
      '2xl': 24,
      '3xl': 30,
      '4xl': 36,
    },
    fontWeight: {
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
      extrabold: '800',
    },
    lineHeight: {
      tight: 1.2,
      normal: 1.4,
      relaxed: 1.6,
    }
  },

  // 🌈 BORDER RADIUS (like Figma's corner radius)
  borderRadius: {
    none: 0,
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
    '2xl': 20,
    '3xl': 24,
    full: 9999,
  },

  // 💫 SHADOWS (like Figma's drop shadow effects)
  shadows: {
    sm: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 2,
    },
    md: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.1,
      shadowRadius: 6,
      elevation: 4,
    },
    lg: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.15,
      shadowRadius: 12,
      elevation: 8,
    },
    xl: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 16 },
      shadowOpacity: 0.2,
      shadowRadius: 24,
      elevation: 16,
    }
  },

  // 📐 COMPONENT SIZES (like Figma's component variants)
  components: {
    button: {
      small: { height: 32, paddingHorizontal: 12 },
      medium: { height: 40, paddingHorizontal: 16 },
      large: { height: 48, paddingHorizontal: 20 },
    },
    card: {
      small: { padding: 12, borderRadius: 8 },
      medium: { padding: 16, borderRadius: 12 },
      large: { padding: 20, borderRadius: 16 },
    },
    input: {
      height: 44,
      borderRadius: 8,
      paddingHorizontal: 12,
    }
  },

  // 🎬 ANIMATIONS (like Figma's smart animate)
  animations: {
    duration: {
      fast: 200,
      normal: 300,
      slow: 500,
    },
    easing: {
      easeIn: [0.4, 0, 1, 1],
      easeOut: [0, 0, 0.2, 1],
      easeInOut: [0.4, 0, 0.2, 1],
    }
  }
};

// 🎯 HELPER FUNCTIONS (like Figma's design tokens)
export const createStyle = {
  // Card creator (like Figma's component)
  card: (variant: 'small' | 'medium' | 'large' = 'medium') => ({
    backgroundColor: designSystem.colors.background.primary,
    ...designSystem.components.card[variant],
    ...designSystem.shadows.md,
  }),
  
  // Button creator
  button: (variant: 'primary' | 'secondary' = 'primary', size: 'small' | 'medium' | 'large' = 'medium') => ({
    backgroundColor: variant === 'primary' ? designSystem.colors.primary[500] : designSystem.colors.gray[100],
    ...designSystem.components.button[size],
    borderRadius: designSystem.borderRadius.lg,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
  }),
  
  // Text creator
  text: (size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' = 'md', weight: 'normal' | 'medium' | 'semibold' | 'bold' = 'normal') => ({
    fontSize: designSystem.typography.fontSize[size],
    fontWeight: designSystem.typography.fontWeight[weight],
    color: designSystem.colors.gray[900],
  })
};

export default designSystem;
