import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { IUnifiedProduct, StoreConfig } from '../types/shoppingList';
import { productDeduplicationService } from '../services/productDeduplicationService';

interface MultiStorePriceCardProps {
  product: IUnifiedProduct;
  onPress?: (product: IUnifiedProduct) => void;
  onAddToCart?: (product: IUnifiedProduct, store: string) => void;
  style?: ViewStyle;
  compact?: boolean;
}

export const MultiStorePriceCard: React.FC<MultiStorePriceCardProps> = ({
  product,
  onPress,
  onAddToCart,
  style,
  compact = false
}) => {
  const { colors } = useTheme();
  const storeConfigs = productDeduplicationService.getStoreConfigs();

  const renderStorePrice = (storeConfig: StoreConfig) => {
    const price = product.storePrices[storeConfig.id];
    const isAvailable = product.storeAvailability[storeConfig.id];
    const isLowest = price === product.lowestPrice;
    const storeDetail = product.storeDetails[storeConfig.id];

    if (!isAvailable || !price) {
      return (
        <View key={storeConfig.id} style={[styles.storePriceContainer, styles.unavailable]}>
          <Text style={[styles.storeIcon, { color: colors.textSecondary }]}>
            {storeConfig.icon}
          </Text>
          <Text style={[styles.unavailableText, { color: colors.textSecondary }]}>
            N/A
          </Text>
        </View>
      );
    }

    return (
      <TouchableOpacity
        key={storeConfig.id}
        style={[
          styles.storePriceContainer,
          isLowest && styles.lowestPriceContainer,
          { borderColor: isLowest ? storeConfig.color : colors.border }
        ]}
        onPress={() => onAddToCart?.(product, storeConfig.id)}
        activeOpacity={0.7}
      >
        <Text style={styles.storeIcon}>
          {storeConfig.icon}
        </Text>
        <View style={styles.priceInfo}>
          <Text style={[
            styles.priceText,
            { color: isLowest ? storeConfig.color : colors.text },
            isLowest && styles.lowestPriceText
          ]}>
            ${price.toFixed(2)}
          </Text>
          {storeDetail?.isOnSale && (
            <View style={[styles.saleIndicator, { backgroundColor: colors.error }]}>
              <Text style={styles.saleText}>SALE</Text>
            </View>
          )}
          {isLowest && product.allStores.length > 1 && (
            <View style={[styles.bestPriceIndicator, { backgroundColor: storeConfig.color }]}>
              <Text style={styles.bestPriceText}>BEST</Text>
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  const renderCompactStorePrice = (storeConfig: StoreConfig) => {
    const price = product.storePrices[storeConfig.id];
    const isAvailable = product.storeAvailability[storeConfig.id];
    const isLowest = price === product.lowestPrice;

    if (!isAvailable || !price) {
      return (
        <View key={storeConfig.id} style={styles.compactStorePrice}>
          <Text style={[styles.compactStoreIcon, { color: colors.textSecondary }]}>
            {storeConfig.icon}
          </Text>
          <Text style={[styles.compactPriceText, { color: colors.textSecondary }]}>
            N/A
          </Text>
        </View>
      );
    }

    return (
      <View key={storeConfig.id} style={[
        styles.compactStorePrice,
        isLowest && { backgroundColor: `${storeConfig.color}15` }
      ]}>
        <Text style={styles.compactStoreIcon}>
          {storeConfig.icon}
        </Text>
        <Text style={[
          styles.compactPriceText,
          { color: isLowest ? storeConfig.color : colors.text },
          isLowest && styles.compactLowestPrice
        ]}>
          ${price.toFixed(2)}
        </Text>
        {isLowest && (
          <View style={styles.compactBestIndicator}>
            <Ionicons name="checkmark-circle" size={12} color={storeConfig.color} />
          </View>
        )}
      </View>
    );
  };

  return (
    <TouchableOpacity
      style={[styles.container, { backgroundColor: colors.background }, style]}
      onPress={() => onPress?.(product)}
      activeOpacity={0.8}
    >
      {/* Product Header */}
      <View style={styles.header}>
        <View style={styles.productInfo}>
          <Text style={[styles.productName, { color: colors.text }]} numberOfLines={2}>
            {product.name}
          </Text>
          {product.brand && (
            <Text style={[styles.brandText, { color: colors.textSecondary }]}>
              {product.brand}
            </Text>
          )}
          {product.size && (
            <Text style={[styles.sizeText, { color: colors.textSecondary }]}>
              {product.size}
            </Text>
          )}
        </View>
        
        {/* Product Image Placeholder */}
        <View style={[styles.imagePlaceholder, { backgroundColor: colors.surface }]}>
          <Ionicons name="image-outline" size={24} color={colors.textSecondary} />
        </View>
      </View>

      {/* Price Comparison Section */}
      {compact ? (
        <View style={styles.compactPriceSection}>
          <View style={styles.compactStorePrices}>
            {storeConfigs.map(renderCompactStorePrice)}
          </View>
          {product.maxSavings > 0 && (
            <Text style={[styles.savingsText, { color: colors.success }]}>
              Save up to ${product.maxSavings.toFixed(2)}
            </Text>
          )}
        </View>
      ) : (
        <View style={styles.priceSection}>
          <View style={styles.storePrices}>
            {storeConfigs.map(renderStorePrice)}
          </View>
          
          {/* Savings Information */}
          {product.maxSavings > 0 && (
            <View style={styles.savingsContainer}>
              <Ionicons name="trending-down" size={16} color={colors.success} />
              <Text style={[styles.savingsText, { color: colors.success }]}>
                Save ${product.maxSavings.toFixed(2)} ({product.savingsPercentage.toFixed(0)}%)
              </Text>
            </View>
          )}
        </View>
      )}

      {/* Metadata */}
      {!compact && (
        <View style={styles.metadata}>
          <Text style={[styles.categoryText, { color: colors.textSecondary }]}>
            {product.category}
          </Text>
          <View style={styles.metadataRight}>
            <Text style={[styles.variantsText, { color: colors.textSecondary }]}>
              {product.allStores.length} store{product.allStores.length !== 1 ? 's' : ''}
            </Text>
            {product.confidence < 0.9 && (
              <View style={styles.confidenceIndicator}>
                <Ionicons name="information-circle-outline" size={12} color={colors.warning} />
              </View>
            )}
          </View>
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 12,
    borderRadius: 12,
    marginVertical: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  productInfo: {
    flex: 1,
    marginRight: 12,
  },
  productName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  brandText: {
    fontSize: 14,
    marginBottom: 2,
  },
  sizeText: {
    fontSize: 12,
  },
  imagePlaceholder: {
    width: 60,
    height: 60,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  priceSection: {
    marginBottom: 8,
  },
  storePrices: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  storePriceContainer: {
    flex: 1,
    alignItems: 'center',
    padding: 8,
    marginHorizontal: 2,
    borderWidth: 1,
    borderRadius: 8,
    minHeight: 60,
    justifyContent: 'center',
  },
  lowestPriceContainer: {
    borderWidth: 2,
    backgroundColor: 'rgba(0, 166, 81, 0.05)',
  },
  unavailable: {
    opacity: 0.5,
  },
  storeIcon: {
    fontSize: 18,
    marginBottom: 4,
  },
  priceInfo: {
    alignItems: 'center',
  },
  priceText: {
    fontSize: 14,
    fontWeight: '600',
  },
  lowestPriceText: {
    fontWeight: '700',
    fontSize: 15,
  },
  unavailableText: {
    fontSize: 12,
    fontWeight: '500',
  },
  saleIndicator: {
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 4,
    marginTop: 2,
  },
  saleText: {
    color: 'white',
    fontSize: 8,
    fontWeight: 'bold',
  },
  bestPriceIndicator: {
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 4,
    marginTop: 2,
  },
  bestPriceText: {
    color: 'white',
    fontSize: 8,
    fontWeight: 'bold',
  },
  savingsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 4,
  },
  savingsText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
  metadata: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  categoryText: {
    fontSize: 12,
    textTransform: 'capitalize',
  },
  metadataRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  variantsText: {
    fontSize: 12,
    marginRight: 4,
  },
  confidenceIndicator: {
    marginLeft: 4,
  },
  
  // Compact mode styles
  compactPriceSection: {
    marginTop: 8,
  },
  compactStorePrices: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  compactStorePrice: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 6,
    marginHorizontal: 1,
    borderRadius: 6,
  },
  compactStoreIcon: {
    fontSize: 14,
    marginRight: 4,
  },
  compactPriceText: {
    fontSize: 12,
    fontWeight: '500',
  },
  compactLowestPrice: {
    fontWeight: '700',
  },
  compactBestIndicator: {
    marginLeft: 2,
  },
});