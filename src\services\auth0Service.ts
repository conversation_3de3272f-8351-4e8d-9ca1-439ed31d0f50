import { API_CONFIG, isAuth0Configured } from '../constants/config';

// Auth0 response types
export interface Auth0LoginResult {
  success: boolean;
  user?: {
    id: string;
    email: string;
    name: string;
    picture?: string;
    emailVerified?: boolean;
  };
  accessToken?: string;
  refreshToken?: string;
  idToken?: string;
  error?: string;
}

export interface Auth0UserProfile {
  id: string;
  email: string;
  name: string;
  picture?: string;
  emailVerified?: boolean;
  sub?: string;
  nickname?: string;
  givenName?: string;
  familyName?: string;
}

export class Auth0Service {
  private static isInitialized = false;
  private static hasRealAuth0 = false;

  // Initialize the Auth0 service
  static async initialize(): Promise<void> {
    if (this.isInitialized) return;
    
    try {
      // Check if Auth0 is configured
      if (!isAuth0Configured()) {
        console.log('⚠️ Auth0 not configured, using mock implementation');
        this.hasRealAuth0 = false;
        this.isInitialized = true;
        return;
      }

      // Try to load react-native-auth0 dynamically
      try {
        const Auth0Module = await import('react-native-auth0');
        console.log('✅ Auth0 module loaded successfully');
        this.hasRealAuth0 = true;
      } catch (error) {
        console.warn('⚠️ Failed to load react-native-auth0, using mock implementation:', error);
        this.hasRealAuth0 = false;
      }
      
      this.isInitialized = true;
    } catch (error) {
      console.error('❌ Failed to initialize Auth0:', error);
      this.hasRealAuth0 = false;
      this.isInitialized = true;
    }
  }

  // Authorize with Auth0 using Universal Login
  static async authorize(): Promise<Auth0LoginResult> {
    try {
      await this.initialize();
      
      if (!this.hasRealAuth0) {
        console.log('🔐 Using Auth0 mock login (real Auth0 not available)');
        return await this.mockLogin();
      }

      console.log('🔐 Starting Auth0 authorization...');
      
      // Dynamic import to avoid bundling issues
      const Auth0Module = await import('react-native-auth0');
      const Auth0 = Auth0Module.default;
      
      const auth0Client = new Auth0({
        domain: API_CONFIG.AUTH0_DOMAIN,
        clientId: API_CONFIG.AUTH0_CLIENT_ID,
      });
      
      // Use Auth0's authorize method with web auth
      const credentials = await auth0Client.webAuth.authorize({
        scope: 'openid profile email offline_access',
        audience: `https://${API_CONFIG.AUTH0_DOMAIN}/api/v2/`,
      });

      console.log('✅ Auth0 authorization successful');

      // Get user profile
      const userProfile = await auth0Client.auth.userInfo({ token: credentials.accessToken });
      
      return {
        success: true,
        user: {
          id: userProfile.sub || userProfile.id || '',
          email: userProfile.email || '',
          name: userProfile.name || userProfile.nickname || '',
          picture: userProfile.picture,
          emailVerified: userProfile.emailVerified,
        },
        accessToken: credentials.accessToken,
        refreshToken: credentials.refreshToken,
        idToken: credentials.idToken,
      };
    } catch (error) {
      console.error('❌ Auth0 authorization failed:', error);
      
      // Fallback to mock login if real Auth0 fails
      console.log('🔐 Falling back to mock login');
      return await this.mockLogin();
    }
  }

  // Get user profile information
  static async getUserProfile(accessToken: string): Promise<Auth0UserProfile | null> {
    try {
      await this.initialize();
      
      if (!this.hasRealAuth0) {
        console.log('⚠️ Cannot get real user profile, Auth0 not available');
        return null;
      }

      const Auth0Module = await import('react-native-auth0');
      const Auth0 = Auth0Module.default;
      
      const auth0Client = new Auth0({
        domain: API_CONFIG.AUTH0_DOMAIN,
        clientId: API_CONFIG.AUTH0_CLIENT_ID,
      });
      
      const userInfo = await auth0Client.auth.userInfo({ token: accessToken });
      
      return {
        id: userInfo.sub || userInfo.id || '',
        email: userInfo.email || '',
        name: userInfo.name || userInfo.nickname || '',
        picture: userInfo.picture,
        emailVerified: userInfo.emailVerified,
        sub: userInfo.sub,
        nickname: userInfo.nickname,
        givenName: userInfo.givenName,
        familyName: userInfo.familyName,
      };
    } catch (error) {
      console.error('❌ Failed to get user profile:', error);
      return null;
    }
  }

  // Refresh access token
  static async refreshToken(refreshToken: string): Promise<{
    accessToken?: string;
    refreshToken?: string;
    idToken?: string;
    error?: string;
  }> {
    try {
      await this.initialize();
      
      if (!this.hasRealAuth0) {
        return {
          error: 'Auth0 not available - cannot refresh token',
        };
      }

      const Auth0Module = await import('react-native-auth0');
      const Auth0 = Auth0Module.default;
      
      const auth0Client = new Auth0({
        domain: API_CONFIG.AUTH0_DOMAIN,
        clientId: API_CONFIG.AUTH0_CLIENT_ID,
      });
      
      const credentials = await auth0Client.auth.refreshToken({ refreshToken });
      
      return {
        accessToken: credentials.accessToken,
        refreshToken: credentials.refreshToken,
        idToken: credentials.idToken,
      };
    } catch (error) {
      console.error('❌ Failed to refresh token:', error);
      return {
        error: error instanceof Error ? error.message : 'Failed to refresh token',
      };
    }
  }

  // Clear session (logout)
  static async clearSession(): Promise<void> {
    try {
      await this.initialize();
      
      if (!this.hasRealAuth0) {
        console.log('✅ Mock Auth0 session cleared');
        return;
      }

      const Auth0Module = await import('react-native-auth0');
      const Auth0 = Auth0Module.default;
      
      const auth0Client = new Auth0({
        domain: API_CONFIG.AUTH0_DOMAIN,
        clientId: API_CONFIG.AUTH0_CLIENT_ID,
      });
      
      await auth0Client.webAuth.clearSession();
      console.log('✅ Auth0 session cleared successfully');
    } catch (error) {
      console.error('❌ Failed to clear Auth0 session:', error);
      // Don't throw error here, as we want to continue with local logout
    }
  }

  // Check if Auth0 is configured
  static isConfigured(): boolean {
    return isAuth0Configured();
  }

  // Mock login for development/testing
  static async mockLogin(): Promise<Auth0LoginResult> {
    console.log('🔐 Using Auth0 mock login for development...');
    
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return {
      success: true,
      user: {
        id: 'auth0|mock_user_id',
        email: '<EMAIL>',
        name: 'Auth0 Demo User',
        picture: 'https://cdn.auth0.com/avatars/default.png',
        emailVerified: true,
      },
      accessToken: 'mock_auth0_access_token_' + Date.now(),
      refreshToken: 'mock_auth0_refresh_token_' + Date.now(),
      idToken: 'mock_auth0_id_token_' + Date.now(),
    };
  }

  // Check if access token is valid (basic check)
  static isTokenValid(accessToken: string): boolean {
    if (!accessToken) return false;
    
    // For a more robust implementation, you would decode the JWT and check expiration
    // For now, we'll do a basic check
    return accessToken.length > 0 && !accessToken.includes('undefined');
  }

  // Get token expiration time (if available)
  static getTokenExpiration(accessToken: string): number | null {
    try {
      // This would require JWT decoding
      // For now, return null - in a real app, you'd decode the JWT
      return null;
    } catch (error) {
      return null;
    }
  }

  // Check if we have real Auth0 available
  static hasRealAuth0Available(): boolean {
    return this.hasRealAuth0;
  }
}

// Export default instance
export default Auth0Service;