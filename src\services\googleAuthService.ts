import * as AuthSession from 'expo-auth-session';
import * as Crypto from 'expo-crypto';
import { Platform } from 'react-native';

// Google OAuth Configuration
const GOOGLE_CLIENT_ID_ANDROID = 'your-android-client-id.apps.googleusercontent.com';
const GOOGLE_CLIENT_ID_IOS = 'your-ios-client-id.apps.googleusercontent.com';
const GOOGLE_CLIENT_ID_WEB = 'your-web-client-id.apps.googleusercontent.com';

// Discovery document for Google OAuth
const discoveryDocument = {
  authorizationEndpoint: 'https://accounts.google.com/o/oauth2/v2/auth',
  tokenEndpoint: 'https://oauth2.googleapis.com/token',
  revocationEndpoint: 'https://oauth2.googleapis.com/revoke',
};

interface GoogleUser {
  id: string;
  email: string;
  name: string;
  picture?: string;
  verified_email: boolean;
}

interface GoogleAuthResult {
  success: boolean;
  user?: GoogleUser;
  accessToken?: string;
  error?: string;
}

export class GoogleAuthService {
  private static getClientId(): string {
    switch (Platform.OS) {
      case 'android':
        return GOOGLE_CLIENT_ID_ANDROID;
      case 'ios':
        return GOOGLE_CLIENT_ID_IOS;
      default:
        return GOOGLE_CLIENT_ID_WEB;
    }
  }

  private static getRedirectUri(): string {
    if (Platform.OS === 'web') {
      return 'http://localhost:8081';
    }
    return AuthSession.makeRedirectUri({
      scheme: 'airecipeplanner',
      path: 'auth'
    });
  }

  static async signInWithGoogle(): Promise<GoogleAuthResult> {
    try {
      console.log('🔐 Starting Google OAuth flow...');
      
      // Create a code verifier for PKCE
      const randomString = Math.random().toString(36).substring(2, 15) + 
                          Math.random().toString(36).substring(2, 15);
      
      const codeVerifier = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        randomString,
        { encoding: Crypto.CryptoEncoding.BASE64 }
      );
      
      const codeChallenge = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        codeVerifier,
        { encoding: Crypto.CryptoEncoding.BASE64 }
      );

      // Create the auth request
      const request = new AuthSession.AuthRequest({
        clientId: this.getClientId(),
        scopes: ['openid', 'profile', 'email'],
        responseType: AuthSession.ResponseType.Code,
        redirectUri: this.getRedirectUri(),
        codeChallenge,
        codeChallengeMethod: AuthSession.CodeChallengeMethod.S256,
        usePKCE: true,
        extraParams: {
          access_type: 'offline',
        },
      });

      console.log('📋 Auth request configuration:', {
        clientId: this.getClientId(),
        redirectUri: this.getRedirectUri(),
        scopes: request.scopes,
      });

      // Prompt for authentication
      const result = await request.promptAsync(discoveryDocument);
      
      console.log('📱 Auth prompt result:', {
        type: result.type,
        url: (result as any).url,
        error: (result as any).error
      });

      if (result.type === 'success') {
        // Exchange the authorization code for tokens
        const tokenResult = await AuthSession.exchangeCodeAsync(
          {
            clientId: this.getClientId(),
            code: result.params.code,
            redirectUri: this.getRedirectUri(),
            extraParams: {
              code_verifier: codeVerifier,
            },
          },
          discoveryDocument
        );

        console.log('🎫 Token exchange successful');

        // Get user info with the access token
        const userResponse = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
          headers: {
            Authorization: `Bearer ${tokenResult.accessToken}`,
          },
        });

        if (!userResponse.ok) {
          throw new Error('Failed to fetch user info');
        }

        const userData: GoogleUser = await userResponse.json();
        
        console.log('👤 User data received:', {
          id: userData.id,
          email: userData.email,
          name: userData.name,
          verified: userData.verified_email
        });

        return {
          success: true,
          user: userData,
          accessToken: tokenResult.accessToken,
        };
      } else {
        console.log('❌ OAuth cancelled or failed:', result.type);
        return {
          success: false,
          error: result.type === 'cancel' ? 'User cancelled' : 'Authentication failed',
        };
      }
    } catch (error) {
      console.error('❌ Google OAuth error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  static async signOut(): Promise<void> {
    try {
      console.log('🚪 Signing out from Google...');
      // Additional cleanup if needed
      // For now, we'll handle sign out in the AuthContext
    } catch (error) {
      console.error('❌ Google sign out error:', error);
    }
  }

  // Development/Testing method with mock data
  static async mockGoogleSignIn(): Promise<GoogleAuthResult> {
    console.log('🔧 Using mock Google sign in for development');
    
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    const mockUser: GoogleUser = {
      id: 'mock_google_' + Date.now(),
      email: '<EMAIL>',
      name: 'Test User',
      picture: 'https://via.placeholder.com/150x150.png?text=User',
      verified_email: true,
    };

    return {
      success: true,
      user: mockUser,
      accessToken: 'mock_access_token_' + Date.now(),
    };
  }
}

// Hook for using Google Auth in components
export const useGoogleAuth = () => {
  const signIn = async (): Promise<GoogleAuthResult> => {
    // Use mock for development, real OAuth for production
    if (__DEV__) {
      return GoogleAuthService.mockGoogleSignIn();
    }
    return GoogleAuthService.signInWithGoogle();
  };

  const signOut = async (): Promise<void> => {
    return GoogleAuthService.signOut();
  };

  return {
    signIn,
    signOut,
    isReady: true, // Always ready since we're using static discovery document
  };
};