import AsyncStorage from '@react-native-async-storage/async-storage';
import * as supabaseRecipeService from '../supabase/recipeService';

export interface Recipe {
  id: string;
  title: string;
  description?: string;
  ingredients: string[];
  instructions: string[];
  cookingTime: string;
  difficulty: string;
  mealType?: string;
  createdAt: string;
  servings: number;
  calories?: number;
  tags?: string[];
  
  // New image and categorization fields
  imageUrl?: string;
  imageUrlList?: string[]; // Multiple images for the recipe
  category?: string; // Primary category (e.g., "Italian", "Desserts", "Quick Meals")
  subcategory?: string; // Secondary category (e.g., "Pasta", "Cakes", "30-min meals")
  cuisine?: string; // Cuisine type (e.g., "Italian", "Asian", "Mexican")
  dishType?: string; // Type of dish (e.g., "Main Course", "Appetizer", "Side Dish")
  
  nutritionInfo?: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
  };
}

export interface ShoppingItem {
  id: string;
  name: string; // Simple name like "milk", "butter", "bread"
  quantity?: number; // Numeric quantity
  unit?: string; // "ltr" for liquids, "kg", "g", "each", etc.
  category: string;
  checked: boolean;
  recipeId?: string;
  selectedBrand?: string; // User's chosen brand
  listId?: string; // ID of the shopping list this item belongs to
  priceInfo?: {
    store: 'woolworths' | 'newworld' | 'paknsave';
    storeName: string;
    price: number;
    unit?: string;
    lastUpdated: string;
    productName: string; // Full product name with brand
  };
  allStorePrices?: Array<{
    store: string;
    storeName: string;
    product: {
      name: string;
      price: number;
      unit?: string;
      brand?: string;
    };
  }>;
}

// Shopping List Interface
export interface ShoppingList {
  id: string;
  name: string;
  color: string;
  createdAt: string;
  updatedAt: string;
  budget?: number;
  description?: string;
  itemCount?: number;
}

// Brand preferences storage
export interface BrandPreference {
  productName: string; // Simple product name like "milk"
  preferredBrand: string; // Brand name like "Anchor"
  lastUsed: string;
}

const BRAND_PREFERENCES_KEY = '@brand_preferences';

export interface UserPreferences {
  dietaryRestrictions: string[];
  kitchenEquipment: string[];
  defaultServings: number;
  weeklyBudget: string;
  perMealBudget: string;
  allergies?: string[];
  cuisinePreferences?: string[];
}

const STORAGE_KEYS = {
  RECIPES: '@recipes',
  SHOPPING_LIST: '@shopping_list',
  SHOPPING_LISTS: '@shopping_lists', // Multiple lists
  PREFERENCES: '@preferences',
  FIRST_LAUNCH: '@first_launch',
  USER_PREFERENCES: '@user_preferences',
  SAVED_RECIPES: '@saved_recipes', // For favorites/bookmarks
  ONBOARDING_COMPLETED: '@onboarding_completed',
};

// Recipe Storage Functions
export const saveRecipe = async (recipe: Recipe): Promise<void> => {
  try {
    const existingRecipes = await getRecipes();
    const updatedRecipes = [recipe, ...existingRecipes];
    await AsyncStorage.setItem(STORAGE_KEYS.RECIPES, JSON.stringify(updatedRecipes));
    
    // Note: Recipe storage now uses Supabase as primary source
    // Local AsyncStorage serves as a cache/offline backup
  } catch (error) {
    throw error;
  }
};

export const getRecipes = async (): Promise<Recipe[]> => {
  try {
    // First try to fetch from Supabase
    const { data: supabaseRecipes, error } = await supabaseRecipeService.getRecipes();
    
    if (!error && supabaseRecipes) {
      // Convert Supabase recipe format to local format
      const localRecipes: Recipe[] = supabaseRecipes.map(recipe => ({
        id: recipe.id,
        title: recipe.title,
        description: recipe.description,
        ingredients: recipe.ingredients,
        instructions: recipe.instructions,
        cookingTime: recipe.total_time,
        difficulty: recipe.difficulty,
        mealType: recipe.category,
        createdAt: recipe.created_at,
        servings: parseInt(recipe.servings) || 4,
        calories: parseInt(recipe.calories) || 0,
        tags: recipe.tags,
        imageUrl: recipe.image_url,
        category: recipe.category,
        cuisine: recipe.cuisine
      }));
      
      // Cache in AsyncStorage for offline access
      await AsyncStorage.setItem(STORAGE_KEYS.RECIPES, JSON.stringify(localRecipes));
      return localRecipes;
    }
    
    // Fallback to AsyncStorage if Supabase fails
    const recipesJson = await AsyncStorage.getItem(STORAGE_KEYS.RECIPES);
    return recipesJson ? JSON.parse(recipesJson) : [];
  } catch (error) {
    // Fallback to AsyncStorage
    try {
      const recipesJson = await AsyncStorage.getItem(STORAGE_KEYS.RECIPES);
      return recipesJson ? JSON.parse(recipesJson) : [];
    } catch {
      return [];
    }
  }
};

export const deleteRecipe = async (recipeId: string): Promise<void> => {
  try {
    const existingRecipes = await getRecipes();
    const updatedRecipes = existingRecipes.filter(recipe => recipe.id !== recipeId);
    await AsyncStorage.setItem(STORAGE_KEYS.RECIPES, JSON.stringify(updatedRecipes));
    
    // Note: Recipe removal now uses Supabase as primary source
    // Local AsyncStorage serves as a cache/offline backup
  } catch (error) {
    throw error;
  }
};

export const clearLocalRecipes = async (): Promise<void> => {
  try {
    await AsyncStorage.removeItem(STORAGE_KEYS.RECIPES);
    console.log('✅ Cleared all local recipes - using Algolia only');
  } catch (error) {
    console.log('⚠️ Could not clear local recipes:', error);
  }
};

// Shopping List Storage Functions
export const saveShoppingList = async (items: ShoppingItem[]): Promise<void> => {
  try {
    await AsyncStorage.setItem(STORAGE_KEYS.SHOPPING_LIST, JSON.stringify(items));
  } catch (error) {
    throw error;
  }
};

export const getShoppingList = async (): Promise<ShoppingItem[]> => {
  try {
    const itemsJson = await AsyncStorage.getItem(STORAGE_KEYS.SHOPPING_LIST);
    return itemsJson ? JSON.parse(itemsJson) : [];
  } catch (error) {
    return [];
  }
};

export const addToShoppingList = async (items: ShoppingItem[]): Promise<void> => {
  try {
    const existingItems = await getShoppingList();
    const updatedItems = [...existingItems, ...items];
    await saveShoppingList(updatedItems);
  } catch (error) {
    throw error;
  }
};

// Shopping List Management Functions
export const createShoppingList = async (list: Omit<ShoppingList, 'id' | 'createdAt' | 'updatedAt' | 'itemCount'>): Promise<ShoppingList> => {
  try {
    const newList: ShoppingList = {
      ...list,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      itemCount: 0
    };
    
    const existingLists = await getShoppingLists();
    const updatedLists = [...existingLists, newList];
    await AsyncStorage.setItem(STORAGE_KEYS.SHOPPING_LISTS, JSON.stringify(updatedLists));
    
    return newList;
  } catch (error) {
    throw error;
  }
};

export const getShoppingLists = async (): Promise<ShoppingList[]> => {
  try {
    const listsJson = await AsyncStorage.getItem(STORAGE_KEYS.SHOPPING_LISTS);
    if (!listsJson) {
      // Create default lists if none exist
      const defaultLists: ShoppingList[] = [
        {
          id: '1',
          name: 'HOME',
          color: '#BF5847',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          budget: 150,
          itemCount: 0
        },
        {
          id: '2',
          name: 'OFFICE',
          color: '#C74A3B',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          budget: 50,
          itemCount: 0
        },
        {
          id: '3',
          name: 'PARTY',
          color: '#B85450',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          budget: 75,
          itemCount: 0
        }
      ];
      await AsyncStorage.setItem(STORAGE_KEYS.SHOPPING_LISTS, JSON.stringify(defaultLists));
      return defaultLists;
    }
    return JSON.parse(listsJson);
  } catch (error) {
    return [];
  }
};

export const updateShoppingList = async (listId: string, updates: Partial<ShoppingList>): Promise<void> => {
  try {
    const lists = await getShoppingLists();
    const updatedLists = lists.map(list => 
      list.id === listId 
        ? { ...list, ...updates, updatedAt: new Date().toISOString() }
        : list
    );
    await AsyncStorage.setItem(STORAGE_KEYS.SHOPPING_LISTS, JSON.stringify(updatedLists));
  } catch (error) {
    throw error;
  }
};

export const deleteShoppingList = async (listId: string): Promise<void> => {
  try {
    const lists = await getShoppingLists();
    const updatedLists = lists.filter(list => list.id !== listId);
    await AsyncStorage.setItem(STORAGE_KEYS.SHOPPING_LISTS, JSON.stringify(updatedLists));
    
    // Also delete all items in this list
    const items = await getShoppingList();
    const updatedItems = items.filter(item => item.listId !== listId);
    await saveShoppingList(updatedItems);
  } catch (error) {
    throw error;
  }
};

export const getShoppingListItems = async (listId: string): Promise<ShoppingItem[]> => {
  try {
    const allItems = await getShoppingList();
    return allItems.filter(item => item.listId === listId);
  } catch (error) {
    return [];
  }
};

export const addToSpecificShoppingList = async (items: ShoppingItem[], listId: string): Promise<void> => {
  try {
    const itemsWithListId = items.map(item => ({ ...item, listId }));
    await addToShoppingList(itemsWithListId);
    
    // Update item count for the list
    const lists = await getShoppingLists();
    const allItems = await getShoppingList();
    const listItemCount = allItems.filter(item => item.listId === listId).length;
    
    await updateShoppingList(listId, { itemCount: listItemCount });
  } catch (error) {
    throw error;
  }
};

// User Preferences Storage Functions
export const saveUserPreferences = async (preferences: UserPreferences): Promise<void> => {
  try {
    await AsyncStorage.setItem(STORAGE_KEYS.USER_PREFERENCES, JSON.stringify(preferences));
  } catch (error) {
    throw error;
  }
};

export const getUserPreferences = async (): Promise<UserPreferences | null> => {
  try {
    const prefsJson = await AsyncStorage.getItem(STORAGE_KEYS.USER_PREFERENCES);
    return prefsJson ? JSON.parse(prefsJson) : null;
  } catch (error) {
    return null;
  }
};

export const updateUserPreferences = async (updates: Partial<UserPreferences>): Promise<void> => {
  try {
    const currentPrefs = await getUserPreferences();
    const updatedPrefs = { ...currentPrefs, ...updates };
    await saveUserPreferences(updatedPrefs as UserPreferences);
  } catch (error) {
    throw error;
  }
};

// Brand Preferences Functions
export const saveBrandPreference = async (productName: string, brand: string): Promise<void> => {
  try {
    const existingPrefs = await getBrandPreferences();
    const updatedPrefs = existingPrefs.filter(pref => pref.productName !== productName);
    
    updatedPrefs.push({
      productName: productName.toLowerCase(),
      preferredBrand: brand,
      lastUsed: new Date().toISOString()
    });
    
    await AsyncStorage.setItem(BRAND_PREFERENCES_KEY, JSON.stringify(updatedPrefs));
  } catch (error) {
    throw error;
  }
};

export const getBrandPreferences = async (): Promise<BrandPreference[]> => {
  try {
    const prefsJson = await AsyncStorage.getItem(BRAND_PREFERENCES_KEY);
    return prefsJson ? JSON.parse(prefsJson) : [];
  } catch (error) {
    return [];
  }
};

export const getPreferredBrand = async (productName: string): Promise<string | null> => {
  try {
    const prefs = await getBrandPreferences();
    const pref = prefs.find(p => p.productName === productName.toLowerCase());
    return pref ? pref.preferredBrand : null;
  } catch (error) {
    return null;
  }
};

// Get all brand preferences for better management
export const getAllBrandPreferences = async (): Promise<BrandPreference[]> => {
  try {
    const prefs = await getBrandPreferences();
    return prefs.sort((a, b) => new Date(b.lastUsed).getTime() - new Date(a.lastUsed).getTime());
  } catch (error) {
    return [];
  }
};

// Clear old brand preferences (older than 6 months)
export const clearOldBrandPreferences = async (): Promise<void> => {
  try {
    const prefs = await getBrandPreferences();
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
    
    const recentPrefs = prefs.filter(pref => 
      new Date(pref.lastUsed) > sixMonthsAgo
    );
    
    await AsyncStorage.setItem(BRAND_PREFERENCES_KEY, JSON.stringify(recentPrefs));
  } catch (error) {
  }
};

// Update brand preference usage time
export const updateBrandPreferenceUsage = async (productName: string): Promise<void> => {
  try {
    const prefs = await getBrandPreferences();
    const updatedPrefs = prefs.map(pref => 
      pref.productName === productName.toLowerCase()
        ? { ...pref, lastUsed: new Date().toISOString() }
        : pref
    );
    
    await AsyncStorage.setItem(BRAND_PREFERENCES_KEY, JSON.stringify(updatedPrefs));
  } catch (error) {
  }
};

// Helper function to determine unit type based on product name
export const getDefaultUnit = (productName: string): string => {
  const liquidItems = ['milk', 'juice', 'water', 'oil', 'vinegar', 'wine', 'beer', 'soda', 'cream'];
  const weightItems = ['meat', 'fish', 'cheese', 'flour', 'sugar', 'rice', 'pasta'];
  
  const lowerName = productName.toLowerCase();
  
  if (liquidItems.some(item => lowerName.includes(item))) {
    return 'ltr';
  } else if (weightItems.some(item => lowerName.includes(item))) {
    return 'kg';
  } else {
    return 'each';
  }
};

// Saved Recipes (Favorites/Bookmarks) Functions
export const saveRecipeToFavorites = async (recipeId: string): Promise<void> => {
  try {
    const savedRecipes = await getSavedRecipeIds();
    if (!savedRecipes.includes(recipeId)) {
      const updatedSavedRecipes = [...savedRecipes, recipeId];
      await AsyncStorage.setItem(STORAGE_KEYS.SAVED_RECIPES, JSON.stringify(updatedSavedRecipes));
    }
  } catch (error) {
    throw error;
  }
};

export const removeRecipeFromFavorites = async (recipeId: string): Promise<void> => {
  try {
    const savedRecipes = await getSavedRecipeIds();
    const updatedSavedRecipes = savedRecipes.filter(id => id !== recipeId);
    await AsyncStorage.setItem(STORAGE_KEYS.SAVED_RECIPES, JSON.stringify(updatedSavedRecipes));
  } catch (error) {
    throw error;
  }
};

export const getSavedRecipeIds = async (): Promise<string[]> => {
  try {
    const savedRecipesJson = await AsyncStorage.getItem(STORAGE_KEYS.SAVED_RECIPES);
    return savedRecipesJson ? JSON.parse(savedRecipesJson) : [];
  } catch (error) {
    return [];
  }
};

export const isRecipeSaved = async (recipeId: string): Promise<boolean> => {
  try {
    const savedRecipes = await getSavedRecipeIds();
    return savedRecipes.includes(recipeId);
  } catch (error) {
    return false;
  }
};

export const toggleRecipeSaved = async (recipeId: string): Promise<boolean> => {
  try {
    const isSaved = await isRecipeSaved(recipeId);
    if (isSaved) {
      await removeRecipeFromFavorites(recipeId);
      return false;
    } else {
      await saveRecipeToFavorites(recipeId);
      return true;
    }
  } catch (error) {
    throw error;
  }
};

// Onboarding Completion Functions
export const markOnboardingCompleted = async (): Promise<void> => {
  try {
    await AsyncStorage.setItem(STORAGE_KEYS.ONBOARDING_COMPLETED, 'true');
  } catch (error) {
    throw error;
  }
};

export const isOnboardingCompleted = async (): Promise<boolean> => {
  try {
    const completed = await AsyncStorage.getItem(STORAGE_KEYS.ONBOARDING_COMPLETED);
    return completed === 'true';
  } catch (error) {
    return false;
  }
};

// First Launch Check
export const isFirstLaunch = async (): Promise<boolean> => {
  try {
    console.log('🔍 Checking first launch status...');
    
    // Add timeout to prevent hanging
    const timeoutPromise = new Promise<boolean>((_, reject) => 
      setTimeout(() => reject(new Error('First launch check timeout')), 2000)
    );
    
    const checkPromise = (async () => {
      const hasLaunched = await AsyncStorage.getItem(STORAGE_KEYS.FIRST_LAUNCH);
      const onboardingCompleted = await isOnboardingCompleted();
      
      if (hasLaunched === null) {
        await AsyncStorage.setItem(STORAGE_KEYS.FIRST_LAUNCH, 'false');
        return true;
      }
      
      // If user has launched before but hasn't completed onboarding, show onboarding
      return !onboardingCompleted;
    })();
    
    const result = await Promise.race([checkPromise, timeoutPromise]);
    console.log('✅ First launch check completed:', result);
    return result;
  } catch (error) {
    console.warn('⚠️ First launch check failed, defaulting to false:', error);
    return false; // Safe default - skip onboarding
  }
}; 