import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';

interface ShoppingListItem {
  id: string;
  name: string;
  checked: boolean;
  quantity: number;
  unit?: string;
  selectedBrand?: string;
  selectedSize?: string;
  storePrices: Array<{
    store: string;
    price?: number;
    available: boolean;
    brand?: string;
    size?: string;
  }>;
  addedAt: Date;
}

interface SimpleShoppingListProps {
  items: ShoppingListItem[];
  onToggleChecked: (itemId: string) => void;
  onEditItem: (itemId: string) => void;
  showPrices?: boolean;
}

export const CategorizedShoppingList: React.FC<SimpleShoppingListProps> = ({
  items,
  onToggleChecked,
  onEditItem,
  showPrices = true,
}) => {
  const { colors: themeColors, isDark: isDarkMode } = useTheme();

  // Professional color scheme
  const colors = {
    light: {
      background: '#F8FAFC',
      surface: '#FFFFFF',
      primary: '#475569',
      text: '#0F172A',
      textSecondary: '#64748B',
      border: '#E2E8F0',
      shadow: 'rgba(15, 23, 42, 0.1)',
      success: '#10B981',
      warning: '#F59E0B',
    },
    dark: {
      background: '#0F172A',
      surface: '#1E293B',
      primary: '#06B6D4',
      text: '#F8FAFC',
      textSecondary: '#94A3B8',
      border: '#334155',
      shadow: 'rgba(0, 0, 0, 0.3)',
      success: '#059669',
      warning: '#D97706',
    }
  };

  const currentColors = isDarkMode ? colors.dark : colors.light;

  const sortedItems = useMemo(() => {
    return [...items].sort((a, b) => {
      // Sort by checked status first (unchecked items at top)
      if (a.checked !== b.checked) {
        return a.checked ? 1 : -1;
      }
      // Then sort by name
      return a.name.localeCompare(b.name);
    });
  }, [items]);

  const getLowestPrice = (item: ShoppingListItem) => {
    const availablePrices = item.storePrices
      .filter(sp => sp.available && sp.price && sp.price > 0)
      .map(sp => ({ price: sp.price!, store: sp.store }))
      .sort((a, b) => a.price - b.price);
    
    return availablePrices[0] || null;
  };


  const renderItem = ({ item }: { item: ShoppingListItem }) => {
    const lowestPrice = getLowestPrice(item);

    return (
      <View style={[
        styles.itemCard,
        { 
          backgroundColor: currentColors.surface,
          borderColor: currentColors.border,
          shadowColor: currentColors.shadow,
        }
      ]}>
        <View style={[styles.itemContent, item.checked && styles.itemChecked]}>
          {/* Checkbox */}
          <TouchableOpacity
            style={styles.checkboxContainer}
            onPress={() => onToggleChecked(item.id)}
          >
            <View style={[
              styles.checkbox,
              { borderColor: currentColors.primary },
              item.checked && { backgroundColor: currentColors.primary }
            ]}>
              {item.checked && (
                <Ionicons name="checkmark" size={12} color="white" />
              )}
            </View>
          </TouchableOpacity>

          {/* Item Details */}
          <View style={styles.itemDetails}>
            <Text style={[
              styles.itemName,
              { color: currentColors.text },
              item.checked && { opacity: 0.6, textDecorationLine: 'line-through' }
            ]}>
              {item.name}
            </Text>
            
            {item.selectedBrand && (
              <Text style={[styles.itemBrand, { color: currentColors.textSecondary }]}>
                {item.selectedBrand} • {item.selectedSize}
              </Text>
            )}

            {/* Price display */}
            {showPrices && !item.checked && lowestPrice && (
              <View style={styles.priceContainer}>
                <View style={[styles.priceChip, { backgroundColor: currentColors.success + '15' }]}>
                  <Text style={[styles.priceText, { color: currentColors.success }]}>
                    ${lowestPrice.price.toFixed(2)}
                  </Text>
                  <Text style={[styles.storeText, { color: currentColors.success }]}>
                    {lowestPrice.store}
                  </Text>
                </View>
              </View>
            )}
          </View>

          {/* Quantity and Actions */}
          <View style={styles.itemActions}>
            <View style={[styles.quantityContainer, { backgroundColor: currentColors.background }]}>
              <Text style={[styles.quantityText, { color: currentColors.text }]}>
                {item.quantity}
              </Text>
            </View>
            
            <TouchableOpacity
              style={styles.actionIconButton}
              onPress={() => onEditItem(item.id)}
            >
              <Ionicons name="ellipsis-horizontal" size={14} color={currentColors.textSecondary} />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  };

  if (sortedItems.length === 0) {
    return (
      <View style={[styles.emptyState, { backgroundColor: currentColors.surface }]}>
        <Ionicons name="basket-outline" size={48} color={currentColors.textSecondary} />
        <Text style={[styles.emptyTitle, { color: currentColors.text }]}>
          Your shopping list is empty
        </Text>
        <Text style={[styles.emptyText, { color: currentColors.textSecondary }]}>
          Add items to get started
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={sortedItems}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 120 }}
        ItemSeparatorComponent={() => <View style={{ height: 6 }} />}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  itemCard: {
    marginHorizontal: 20,
    marginBottom: 6,
    borderRadius: 8,
    borderWidth: 1,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 2,
    elevation: 1,
  },
  itemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
  },
  itemChecked: {
    opacity: 0.7,
  },
  checkboxContainer: {
    marginRight: 10,
  },
  checkbox: {
    width: 18,
    height: 18,
    borderRadius: 3,
    borderWidth: 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  itemDetails: {
    flex: 1,
  },
  itemName: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  itemBrand: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 4,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  priceChip: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  priceText: {
    fontSize: 11,
    fontWeight: '700',
  },
  storeText: {
    fontSize: 9,
    fontWeight: '600',
    textTransform: 'uppercase',
  },
  itemActions: {
    alignItems: 'center',
    gap: 6,
  },
  quantityContainer: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    minWidth: 20,
    alignItems: 'center',
  },
  quantityText: {
    fontSize: 12,
    fontWeight: '600',
  },
  actionIconButton: {
    padding: 2,
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
    marginHorizontal: 20,
    marginTop: 40,
    borderRadius: 16,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
});