import React, { memo, useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { OptimizedImage } from './OptimizedImage';
import { SkeletonLoader } from './SkeletonLoader';

const { width } = Dimensions.get('window');
const ITEM_WIDTH = (width - 48) / 2; // 2 columns with padding

interface Product {
  id: string;
  name: string;
  price: number;
  current_price?: number;
  original_price?: number;
  store: string;
  category?: string;
  brand?: string;
  unit?: string;
  size?: string;
  image_url?: string;
  is_available?: boolean;
  is_on_sale?: boolean;
}

interface ProductGroup {
  name: string;
  products: Product[];
  lowestPrice: number;
  highestPrice: number;
  stores: string[];
}

interface OptimizedProductCardProps {
  item: ProductGroup;
  viewMode: 'grid' | 'list';
  onPress: (item: ProductGroup) => void;
  onAddToList: (item: ProductGroup, selectedVariant?: any) => void;
  isLoading?: boolean;
}

const STORE_CONFIG = {
  woolworths: {
    name: 'Woolworths',
    logo: 'W',
    backgroundColor: '#00A651',
    textColor: '#FFFFFF',
  },
  newworld: {
    name: 'New World',
    logo: 'NW',
    backgroundColor: '#E31E24',
    textColor: '#FFFFFF',
  },
  paknsave: {
    name: "Pak'nSave",
    logo: 'PS',
    backgroundColor: '#FFD100',
    textColor: '#000000',
  },
};

const OptimizedProductCard: React.FC<OptimizedProductCardProps> = memo(({
  item,
  viewMode,
  onPress,
  onAddToList,
  isLoading = false,
}) => {
  const { colors } = useTheme();
  const [imageError, setImageError] = useState(false);

  const handlePress = useCallback(() => {
    onPress(item);
  }, [onPress, item]);

  const handleAddToList = useCallback(() => {
    onAddToList(item);
  }, [onAddToList, item]);

  const handleImageError = useCallback(() => {
    setImageError(true);
  }, []);

  if (isLoading) {
    return (
      <SkeletonLoader
        style={[
          styles.productCard,
          viewMode === 'grid' ? styles.gridCard : styles.listCard,
          { backgroundColor: colors.surface }
        ] as any}
      />
    );
  }

  const imageUrl = item.products.find(p => p.image_url)?.image_url;
  const uniqueSizes = new Set(item.products.map(p => `${p.size || ''}_${p.unit || ''}`));
  const hasMultipleSizes = uniqueSizes.size > 1;

  return (
    <TouchableOpacity
      style={[
        styles.productCard,
        viewMode === 'grid' ? styles.gridCard : styles.listCard,
        { backgroundColor: colors.surface }
      ]}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      <View style={styles.productImageContainer}>
        {imageUrl && !imageError ? (
          <OptimizedImage
            source={{ uri: imageUrl }}
            style={styles.productImage}
            onError={handleImageError}
            placeholder={
              <View style={[styles.productImagePlaceholder, { backgroundColor: colors.border }]}>
                <Ionicons name="image" size={32} color={colors.textSecondary} />
              </View>
            }
          />
        ) : (
          <View style={[styles.productImagePlaceholder, { backgroundColor: colors.border }]}>
            <Ionicons name="image" size={32} color={colors.textSecondary} />
          </View>
        )}
      </View>

      <View style={styles.productInfo}>
        <Text style={[styles.productName, { color: colors.text }]} numberOfLines={2}>
          {item.name}
        </Text>
        
        <View style={styles.priceInfo}>
          <Text style={[styles.priceRange, { color: colors.textSecondary }]}>
            ${item.lowestPrice.toFixed(2)}
            {item.lowestPrice !== item.highestPrice && (
              <Text> - ${item.highestPrice.toFixed(2)}</Text>
            )}
          </Text>
          
          {hasMultipleSizes && (
            <Text style={[styles.sizeVariantsText, { color: colors.textSecondary }]}>
              {uniqueSizes.size} sizes available
            </Text>
          )}
          
          {item.lowestPrice !== item.highestPrice && (
            <Text style={[styles.savings, { color: colors.primary }]}>
              Save ${(item.highestPrice - item.lowestPrice).toFixed(2)}
            </Text>
          )}
        </View>

        <View style={styles.storeIndicators}>
          {item.stores.slice(0, 3).map(store => {
            const config = STORE_CONFIG[store as keyof typeof STORE_CONFIG];
            return (
              <View
                key={store}
                style={[
                  styles.storeIndicator,
                  { backgroundColor: config?.backgroundColor || colors.border }
                ]}
              >
                <Text style={[
                  styles.storeIndicatorText,
                  { color: config?.textColor || colors.text }
                ]}>
                  {config?.logo || store.charAt(0).toUpperCase()}
                </Text>
              </View>
            );
          })}
          {item.stores.length > 3 && (
            <Text style={[styles.moreStoresText, { color: colors.textSecondary }]}>
              +{item.stores.length - 3}
            </Text>
          )}
        </View>
      </View>

      <TouchableOpacity
        style={[styles.addButton, { backgroundColor: colors.primary }]}
        onPress={handleAddToList}
        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
      >
        <Ionicons name="add" size={20} color="white" />
      </TouchableOpacity>
    </TouchableOpacity>
  );
});

OptimizedProductCard.displayName = 'OptimizedProductCard';

const styles = StyleSheet.create({
  productCard: {
    borderRadius: 12,
    padding: 12,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  gridCard: {
    width: ITEM_WIDTH,
    marginHorizontal: 4,
  },
  listCard: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
  },
  productImageContainer: {
    alignItems: 'center',
    marginBottom: 8,
  },
  productImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
  },
  productImagePlaceholder: {
    width: 60,
    height: 60,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  priceInfo: {
    marginBottom: 8,
  },
  priceRange: {
    fontSize: 16,
    fontWeight: '700',
  },
  savings: {
    fontSize: 12,
    fontWeight: '600',
    marginTop: 2,
  },
  sizeVariantsText: {
    fontSize: 11,
    fontStyle: 'italic',
    marginTop: 2,
  },
  storeIndicators: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  storeIndicator: {
    width: 20,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 4,
  },
  storeIndicatorText: {
    fontSize: 8,
    fontWeight: 'bold',
  },
  moreStoresText: {
    fontSize: 11,
    fontWeight: '500',
    marginLeft: 4,
  },
  addButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 8,
  },
});

export { OptimizedProductCard };
