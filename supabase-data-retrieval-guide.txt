SUPABASE DATA RETRIEVAL GUIDE
=================================
Comprehensive guide for retrieving and managing data from the Supabase database 
for the New Zealand Supermarket Price Scraping System

================================================================================
1. DATABASE STRUCTURE OVERVIEW
================================================================================

The Supabase PostgreSQL database contains price and product data for 3 New Zealand supermarkets:
- Woolworths (Node.js/TypeScript scraper)
- New World (C#/.NET scraper)  
- PakNSave (C#/.NET scraper)

Core Tables:
- stores: Store information and metadata
- brands: Product brand names
- categories: Product categories
- products: Product information and metadata
- prices: Historical pricing data with timestamps

Storage:
- product-images: Supabase Storage bucket for product images

================================================================================
2. TABLE SCHEMA DETAILS
================================================================================

STORES TABLE
------------
- id: bigint (auto-generated primary key)
- name: text (unique, not null) - Store name ("woolworths", "newworld", "paknsave")
- logo_url: text (optional) - Store logo URL

BRANDS TABLE  
------------
- id: bigint (auto-generated primary key)
- name: text (unique, not null) - Brand name

CATEGORIES TABLE
----------------
- id: bigint (auto-generated primary key) 
- name: text (unique, not null) - Category name

PRODUCTS TABLE
--------------
- id: text (primary key) - Unique product identifier per store
- name: text (not null) - Product name
- size: text (optional) - Product size/weight description
- brand_id: bigint (foreign key to brands.id)
- category_id: bigint (foreign key to categories.id)
- subcategory: text (optional) - Subcategory name
- unit_price: numeric(10,2) - Price per unit
- unit_name: text - Unit name (e.g. "per 100g", "each")
- original_unit_qty: numeric(10,2) - Original unit quantity for calculations
- source_site: text - Source website URL
- last_updated: timestamptz (not null) - When product data was last modified
- last_checked: timestamptz (not null) - When product was last scraped
- created_at: timestamptz (default now()) - Record creation timestamp

Indexes:
- products(category_id)
- products(brand_id)

PRICES TABLE
------------
- id: bigint (auto-generated primary key)
- product_id: text (foreign key to products.id, cascade delete)
- store_id: bigint (foreign key to stores.id, cascade delete)  
- price: numeric(10,2) (not null) - Product price at time of recording
- recorded_at: timestamptz (default now()) - Price recording timestamp

Indexes:
- prices(product_id, store_id) - Composite index for efficient lookups
- prices(recorded_at) - Time-based queries

Security:
- Row Level Security enabled
- Public read access policy for all price data

================================================================================
3. AUTHENTICATION AND CONNECTION
================================================================================

NODE.JS/TYPESCRIPT (Woolworths Scraper)
----------------------------------------
Environment Variables (.env file):
- SUPABASE_URL: https://your-project.supabase.co
- SUPABASE_ANON_KEY: Your anon/public API key
- SUPABASE_SERVICE_ROLE_KEY: Your service role key (preferred for writes)
- STORE_NAME: Optional store location name

Connection Code:
```typescript
import { createClient, SupabaseClient } from "@supabase/supabase-js";

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!, // or SUPABASE_ANON_KEY
  { auth: { persistSession: false } }
);
```

C#/.NET (New World & PakNSave Scrapers)  
---------------------------------------
Configuration (appsettings.json):
- POSTGRES_CONNECTION: PostgreSQL connection string
  Format: "Host=db.your-project.supabase.co;Port=5432;Database=postgres;Username=postgres;Password=your-password"

Connection Code:
```csharp
using Npgsql;
using Dapper;

private static async Task<NpgsqlConnection> GetConnection()
{
    var connection = new NpgsqlConnection(connectionString);
    await connection.OpenAsync();
    return connection;
}
```

================================================================================
4. DATA RETRIEVAL METHODS
================================================================================

FETCH ALL PRODUCTS FOR A STORE (SQL)
------------------------------------
```sql
SELECT p.*, b.name as brand_name, c.name as category_name
FROM products p
LEFT JOIN brands b ON p.brand_id = b.id  
LEFT JOIN categories c ON p.category_id = c.id
JOIN prices pr ON p.id = pr.product_id
JOIN stores s ON pr.store_id = s.id
WHERE s.name = 'woolworths'
ORDER BY p.last_updated DESC;
```

FETCH CURRENT PRICES FOR A STORE (SQL)
--------------------------------------
```sql
SELECT DISTINCT ON (pr.product_id) 
    p.name, p.size, pr.price, pr.recorded_at
FROM prices pr
JOIN products p ON pr.product_id = p.id
JOIN stores s ON pr.store_id = s.id  
WHERE s.name = 'woolworths'
ORDER BY pr.product_id, pr.recorded_at DESC;
```

FETCH PRICE HISTORY FOR A PRODUCT (SQL)
---------------------------------------
```sql
SELECT pr.price, pr.recorded_at, s.name as store_name
FROM prices pr
JOIN stores s ON pr.store_id = s.id
WHERE pr.product_id = 'your-product-id'
ORDER BY pr.recorded_at DESC
LIMIT 30;
```

TYPESCRIPT SUPABASE CLIENT EXAMPLES
-----------------------------------
```typescript
// Get all products for Woolworths
const { data: products, error } = await supabase
  .from('products')
  .select(`
    *,
    brands(name),
    categories(name),
    prices(price, recorded_at)
  `)
  .eq('prices.store_id', storeId)
  .order('last_updated', { ascending: false });

// Get latest prices for each product
const { data: latestPrices, error } = await supabase
  .from('prices')
  .select('*, products(name, size), stores(name)')
  .order('recorded_at', { ascending: false });

// Get price history for specific product
const { data: priceHistory, error } = await supabase
  .from('prices')  
  .select('price, recorded_at, stores(name)')
  .eq('product_id', productId)
  .order('recorded_at', { ascending: false })
  .limit(30);
```

C# DAPPER EXAMPLES
------------------
```csharp
// Get all products for a store
var products = await connection.QueryAsync<Product>(@"
    SELECT p.*, b.name as brand_name, c.name as category_name
    FROM products p
    LEFT JOIN brands b ON p.brand_id = b.id
    LEFT JOIN categories c ON p.category_id = c.id
    JOIN prices pr ON p.id = pr.product_id
    JOIN stores s ON pr.store_id = s.id
    WHERE s.name = @storeName", 
    new { storeName = "newworld" });

// Get latest price for a product
var latestPrice = await connection.QueryFirstOrDefaultAsync<decimal?>(@"
    SELECT pr.price 
    FROM prices pr
    JOIN stores s ON pr.store_id = s.id
    WHERE pr.product_id = @productId AND s.name = @storeName
    ORDER BY pr.recorded_at DESC
    LIMIT 1", 
    new { productId, storeName });
```

================================================================================
5. IMAGE RETRIEVAL
================================================================================

SUPABASE STORAGE STRUCTURE
--------------------------
Bucket: product-images
Path Format: products/{product-id}.jpg

RETRIEVE IMAGE URLs (TypeScript)
--------------------------------
```typescript
// Get public URL for product image
const { data } = supabase.storage
  .from('product-images')
  .getPublicUrl(`products/${productId}.jpg`);

const imageUrl = data.publicUrl;
```

DOWNLOAD IMAGE (TypeScript)
---------------------------
```typescript
// Download image blob
const { data, error } = await supabase.storage
  .from('product-images')
  .download(`products/${productId}.jpg`);

if (data) {
  const imageBlob = data;
  // Process image blob...
}
```

================================================================================
6. COMMON QUERY PATTERNS
================================================================================

PRICE COMPARISON ACROSS STORES
------------------------------
```sql
SELECT 
    p.name,
    p.size,
    s1.name as store_name,
    pr1.price,
    pr1.recorded_at
FROM products p
JOIN prices pr1 ON p.id = pr1.product_id
JOIN stores s1 ON pr1.store_id = s1.id
WHERE pr1.recorded_at = (
    SELECT MAX(pr2.recorded_at) 
    FROM prices pr2 
    WHERE pr2.product_id = p.id AND pr2.store_id = s1.id
)
ORDER BY p.name, s1.name;
```

PRODUCTS BY CATEGORY
--------------------
```sql
SELECT p.name, p.size, c.name as category, pr.price
FROM products p
JOIN categories c ON p.category_id = c.id
JOIN prices pr ON p.id = pr.product_id
JOIN stores s ON pr.store_id = s.id
WHERE c.name ILIKE '%dairy%' 
    AND s.name = 'woolworths'
    AND pr.recorded_at >= NOW() - INTERVAL '7 days'
ORDER BY pr.price ASC;
```

PRICE TRENDS
------------
```sql
SELECT 
    DATE_TRUNC('day', recorded_at) as date,
    AVG(price) as avg_price,
    MIN(price) as min_price,
    MAX(price) as max_price,
    COUNT(*) as price_count
FROM prices pr
JOIN products p ON pr.product_id = p.id
WHERE p.name ILIKE '%milk%'
    AND pr.recorded_at >= NOW() - INTERVAL '30 days'
GROUP BY DATE_TRUNC('day', recorded_at)
ORDER BY date DESC;
```

================================================================================
7. DATA UPSERTION PATTERNS
================================================================================

UPSERT PRODUCT (TypeScript)
---------------------------
```typescript
const { error } = await supabase
  .from('products')
  .upsert({
    id: product.id,
    name: product.name,
    size: product.size,
    unit_price: product.unitPrice,
    unit_name: product.unitName,
    original_unit_qty: product.originalUnitQuantity,
    source_site: product.sourceSite,
    last_checked: new Date().toISOString(),
    last_updated: new Date().toISOString()
  }, { 
    onConflict: 'id' 
  });
```

INSERT PRICE RECORD (TypeScript)
--------------------------------
```typescript
const { error } = await supabase
  .from('prices')
  .insert({
    product_id: product.id,
    store_id: storeId,
    price: product.currentPrice,
    recorded_at: new Date().toISOString()
  });
```

UPSERT PRODUCT (C#)
-------------------
```csharp
await connection.ExecuteAsync(@"
    INSERT INTO products(id,name,size,unit_price,unit_name,original_unit_qty,source_site,last_updated,last_checked)
    VALUES(@id,@name,@size,@uprice,@uname,@oqty,@site,@lu,@lc)
    ON CONFLICT(id) DO UPDATE SET 
        name=EXCLUDED.name, 
        size=EXCLUDED.size, 
        unit_price=EXCLUDED.unit_price, 
        unit_name=EXCLUDED.unit_name, 
        original_unit_qty=EXCLUDED.original_unit_qty, 
        source_site=EXCLUDED.source_site, 
        last_checked=EXCLUDED.last_checked",
    new { 
        id = product.id, 
        name = product.name, 
        size = product.size,
        uprice = product.unitPrice,
        uname = product.unitName,
        oqty = product.originalUnitQuantity,
        site = product.sourceSite,
        lu = product.lastUpdated,
        lc = product.lastChecked 
    });
```

================================================================================
8. BEST PRACTICES
================================================================================

EFFICIENT QUERYING
------------------
- Use indexes on commonly queried columns (product_id, store_id, recorded_at)
- Leverage composite indexes for multi-column lookups
- Use DISTINCT ON() for latest record per group queries
- Implement pagination for large result sets using LIMIT and OFFSET
- Use date range filters to limit historical data queries

ERROR HANDLING
--------------
- Always check for errors in Supabase client responses
- Implement retry logic for transient network failures
- Log detailed error messages for debugging
- Use transactions for multi-table operations
- Validate data before insertion to avoid constraint violations

PERFORMANCE OPTIMIZATION
------------------------
- Batch insert operations when possible
- Use prepared statements for repeated queries
- Implement connection pooling for C# applications
- Cache frequently accessed data (store IDs, category mappings)
- Use row-level security policies to limit data access

DATA SYNCHRONIZATION
--------------------
- Always insert price records even for unchanged prices (for historical tracking)
- Use upsert operations for product data to handle updates
- Implement last_checked timestamps to track scraping freshness
- Consider implementing soft deletes for discontinued products
- Use database triggers for automated data maintenance

SECURITY CONSIDERATIONS
-----------------------
- Use service role keys for write operations in scrapers
- Implement row-level security policies as needed
- Validate and sanitize all input data
- Store sensitive configuration in environment variables
- Regularly rotate API keys and database passwords

================================================================================
9. REAL-TIME FEATURES
================================================================================

SUPABASE REALTIME SUBSCRIPTIONS (TypeScript)
--------------------------------------------
```typescript
// Subscribe to price changes
const subscription = supabase
  .from('prices')
  .on('INSERT', (payload) => {
    console.log('New price recorded:', payload.new);
  })
  .subscribe();

// Subscribe to product updates  
const productSubscription = supabase
  .from('products')
  .on('UPDATE', (payload) => {
    console.log('Product updated:', payload.new);
  })
  .subscribe();
```

================================================================================
10. TROUBLESHOOTING
================================================================================

COMMON ISSUES
-------------
1. Connection timeouts: Check network connectivity and connection string
2. Permission errors: Verify API keys and RLS policies
3. Data type mismatches: Ensure proper type casting in queries
4. Foreign key violations: Verify referenced records exist
5. Storage access issues: Check bucket policies and file paths

DEBUGGING QUERIES
-----------------
- Enable query logging in Supabase dashboard
- Use EXPLAIN ANALYZE for query performance analysis
- Test queries directly in Supabase SQL editor
- Monitor database metrics and connection counts
- Use proper logging in application code

CONNECTION ISSUES
-----------------
- Verify environment variables are properly loaded
- Check firewall settings for database connections
- Test connection strings with minimal queries
- Monitor connection pool usage in production
- Implement proper connection disposal patterns

================================================================================

This guide covers the essential patterns for retrieving and managing data in the 
New Zealand Supermarket Price Scraping System. For additional implementation 
details, refer to the existing scraper code in:

- Woolworths/src/supabase.ts (TypeScript/Node.js)
- new-world/src/SupabaseDB.cs (C#/.NET)  
- paknsave/src/SupabaseDB.cs (C#/.NET)

For database schema updates, see: supabase/migrations/20240713_001_init.sql