import { productFetchService, Product, ProductSearchResult } from './productFetchService';
import { brandPreferenceService } from './brandPreferenceService';

export interface StructuredProduct {
  id: string;
  name: string;
  price: number;
  store: string;
  storeEmoji: string;
  category: string;
  brand: string;
  unit: string;
  image?: string;
  description?: string;
  isAvailable: boolean;
  promotions?: string[];
  priceComparison?: {
    isLowestPrice: boolean;
    savingsFromHighest: number;
    otherStores: Array<{
      store: string;
      price: number;
      savings: number;
    }>;
  };
}

export interface ShoppingListProduct {
  productName: string;
  selectedBrand?: string;
  recommendedProducts: StructuredProduct[];
  priceRange: {
    min: number;
    max: number;
    savings: number;
  };
  totalFound: number;
  bestValue: StructuredProduct;
  userPreference?: {
    preferredBrand: string;
    preferredStore: string;
    confidence: number;
  };
}

export interface CategoryProducts {
  category: string;
  products: StructuredProduct[];
  totalCount: number;
  priceRange: {
    min: number;
    max: number;
    average: number;
  };
  topBrands: Array<{
    brand: string;
    productCount: number;
    averagePrice: number;
  }>;
}

export interface StoreComparison {
  productName: string;
  stores: Array<{
    store: string;
    storeEmoji: string;
    product?: StructuredProduct;
    availability: 'available' | 'unavailable';
    priceRank?: number; // 1 = cheapest, 2 = second cheapest, etc.
  }>;
  recommendation: {
    bestValue: StructuredProduct | null;
    reasoning: string;
  };
}

const STORE_EMOJIS = {
  woolworths: '🛒',
  newworld: '🍎',
  paknsave: '💰'
};

class StructuredProductService {
  /**
   * Get structured products for the shopping list
   */
  async getShoppingListProducts(productNames: string[]): Promise<ShoppingListProduct[]> {
    const results: ShoppingListProduct[] = [];

    for (const productName of productNames) {
      try {
        console.log(`🛒 Processing shopping list item: ${productName}`);
        
        // Get products for this item
        const productResult = await productFetchService.searchProducts(productName, {
          maxResults: 20,
          sortBy: 'price',
          sortOrder: 'asc'
        });

        if (productResult.products.length === 0) {
          console.warn(`❌ No products found for: ${productName}`);
          continue;
        }

        // Get user preferences
        const userPreference = await brandPreferenceService.getBrandPreference(productName);

        // Structure the products
        const structuredProducts = this.structureProducts(productResult.products, productName);

        // Find best value (considering user preferences)
        const bestValue = this.findBestValue(structuredProducts, userPreference);

        // Calculate price range
        const prices = structuredProducts.map(p => p.price);
        const priceRange = {
          min: Math.min(...prices),
          max: Math.max(...prices),
          savings: Math.max(...prices) - Math.min(...prices)
        };

        results.push({
          productName,
          selectedBrand: userPreference?.selectedBrand,
          recommendedProducts: structuredProducts,
          priceRange,
          totalFound: structuredProducts.length,
          bestValue,
          userPreference: userPreference ? {
            preferredBrand: userPreference.selectedBrand,
            preferredStore: userPreference.store,
            confidence: userPreference.confidence
          } : undefined
        });

      } catch (error) {
        console.error(`❌ Error processing ${productName}:`, error);
      }
    }

    return results;
  }

  /**
   * Get products by category with structure
   */
  async getCategoryProducts(category: string, limit: number = 50): Promise<CategoryProducts> {
    const result = await productFetchService.fetchProductsByCategory(category, {
      maxResults: limit,
      sortBy: 'price',
      sortOrder: 'asc'
    });

    const structuredProducts = this.structureProducts(result.products, category);
    
    // Calculate price statistics
    const prices = structuredProducts.map(p => p.price);
    const priceRange = {
      min: Math.min(...prices),
      max: Math.max(...prices),
      average: prices.reduce((a, b) => a + b, 0) / prices.length
    };

    // Get top brands
    const brandStats = new Map<string, { count: number; totalPrice: number }>();
    structuredProducts.forEach(product => {
      const existing = brandStats.get(product.brand) || { count: 0, totalPrice: 0 };
      brandStats.set(product.brand, {
        count: existing.count + 1,
        totalPrice: existing.totalPrice + product.price
      });
    });

    const topBrands = Array.from(brandStats.entries())
      .map(([brand, stats]) => ({
        brand,
        productCount: stats.count,
        averagePrice: stats.totalPrice / stats.count
      }))
      .sort((a, b) => b.productCount - a.productCount)
      .slice(0, 10);

    return {
      category,
      products: structuredProducts,
      totalCount: structuredProducts.length,
      priceRange,
      topBrands
    };
  }

  /**
   * Compare a product across all stores
   */
  async compareProductAcrossStores(productName: string): Promise<StoreComparison> {
    const comparison = await productFetchService.compareProductPrices(productName);
    
    // Group by store
    const storeProducts = new Map<string, StructuredProduct>();
    comparison.products.forEach(product => {
      const structured = this.structureProduct(product, productName);
      
      // Keep the cheapest product for each store
      const existing = storeProducts.get(product.store);
      if (!existing || structured.price < existing.price) {
        storeProducts.set(product.store, structured);
      }
    });

    // Create store comparison
    const stores = Object.keys(STORE_EMOJIS).map(storeName => {
      const product = storeProducts.get(storeName);
      return {
        store: storeName,
        storeEmoji: STORE_EMOJIS[storeName as keyof typeof STORE_EMOJIS],
        product,
        availability: product ? 'available' as const : 'unavailable' as const
      };
    });

    // Add price rankings
    const availableStores = stores.filter(s => s.product);
    availableStores.sort((a, b) => a.product!.price - b.product!.price);
    availableStores.forEach((store, index) => {
      (store as any).priceRank = index + 1;
    });

    // Generate recommendation
    const bestValue = comparison.cheapest ? this.structureProduct(comparison.cheapest, productName) : availableStores[0]?.product || null;
    const reasoning = this.generateRecommendation(bestValue, comparison);

    return {
      productName,
      stores,
      recommendation: {
        bestValue: bestValue!,
        reasoning
      }
    };
  }

  /**
   * Get trending products (most searched/popular)
   */
  async getTrendingProducts(limit: number = 20): Promise<StructuredProduct[]> {
    // For now, get a mix of popular categories
    const popularCategories = ['Dairy & Eggs', 'Bakery', 'Meat & Seafood', 'Produce'];
    const allProducts: Product[] = [];

    for (const category of popularCategories) {
      const result = await productFetchService.fetchProductsByCategory(category, {
        maxResults: limit / popularCategories.length,
        sortBy: 'price',
        sortOrder: 'asc'
      });
      allProducts.push(...result.products);
    }

    return this.structureProducts(allProducts, 'trending');
  }

  /**
   * Search products with structure
   */
  async searchProducts(query: string, options: {
    category?: string;
    brand?: string;
    store?: string;
    maxResults?: number;
  } = {}): Promise<StructuredProduct[]> {
    const result = await productFetchService.searchProducts(query, {
      category: options.category,
      brand: options.brand,
      store: options.store as any,
      maxResults: options.maxResults || 50,
      sortBy: 'price',
      sortOrder: 'asc'
    });

    return this.structureProducts(result.products, query);
  }

  /**
   * Private method to structure a single product
   */
  private structureProduct(product: Product, context: string): StructuredProduct {
    return {
      id: product.id,
      name: product.name,
      price: product.price,
      store: product.storeName,
      storeEmoji: STORE_EMOJIS[product.store as keyof typeof STORE_EMOJIS] || '🏪',
      category: product.category,
      brand: product.brand,
      unit: product.unit,
      image: product.image,
      description: product.description,
      isAvailable: product.availability === 'in_stock',
      promotions: product.promotions
    };
  }

  /**
   * Private method to structure multiple products with price comparison
   */
  private structureProducts(products: Product[], context: string): StructuredProduct[] {
    const structured = products.map(product => this.structureProduct(product, context));
    
    // Add price comparison data
    const prices = structured.map(p => p.price);
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);

    structured.forEach(product => {
      const otherStores = structured
        .filter(p => p.store !== product.store)
        .map(p => ({
          store: p.store,
          price: p.price,
          savings: p.price - product.price
        }));

      product.priceComparison = {
        isLowestPrice: product.price === minPrice,
        savingsFromHighest: maxPrice - product.price,
        otherStores
      };
    });

    return structured;
  }

  /**
   * Private method to find best value considering user preferences
   */
  private findBestValue(products: StructuredProduct[], userPreference?: any): StructuredProduct {
    if (products.length === 0) {
      throw new Error('No products to evaluate');
    }

    // If user has a strong preference, prioritize it
    if (userPreference && userPreference.confidence > 0.7) {
      const preferredProduct = products.find(p => 
        p.brand === userPreference.selectedBrand && 
        p.store === userPreference.store
      );
      if (preferredProduct) {
        return preferredProduct;
      }
    }

    // Otherwise, find the best value (cheapest price)
    return products.reduce((best, current) => 
      current.price < best.price ? current : best
    );
  }

  /**
   * Private method to generate recommendation reasoning
   */
  private generateRecommendation(bestValue: StructuredProduct | null, comparison: any): string {
    if (!bestValue) {
      return 'No products available for comparison';
    }

    const savings = comparison.priceRange?.savings || 0;
    
    if (savings > 1) {
      return `${bestValue.storeEmoji} ${bestValue.store} offers the best value at $${bestValue.price.toFixed(2)}, saving you $${savings.toFixed(2)}`;
    } else if (savings > 0) {
      return `${bestValue.storeEmoji} ${bestValue.store} has the lowest price at $${bestValue.price.toFixed(2)}`;
    } else {
      return `${bestValue.storeEmoji} ${bestValue.store} - $${bestValue.price.toFixed(2)}`;
    }
  }

  /**
   * Get service configuration status
   */
  isConfigured(): boolean {
    return productFetchService.isConfigured();
  }
}

export const structuredProductService = new StructuredProductService();