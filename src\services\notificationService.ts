// TEMPORARILY DISABLED: expo-notifications causing navigation issues
// This service will be re-enabled once navigation is stable

export interface PriceAlert {
  id: string;
  productId: string;
  productName: string;
  targetPrice: number;
  currentPrice: number;
  store: string;
  userId: string;
  isActive: boolean;
  createdAt: Date;
  lastTriggered?: Date;
}

export class NotificationService {
  private static instance: NotificationService;
  
  static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }
  
  async initialize(): Promise<void> {
    console.log('Notification service temporarily disabled');
  }
  
  async sendPriceDropAlert(alert: PriceAlert): Promise<void> {
    console.log('Price drop alert temporarily disabled:', alert.productName);
  }
  
  static async scheduleNotification(alert: PriceAlert): Promise<string> {
    console.log('Notification service temporarily disabled');
    return 'temp-id';
  }
}