Matching Products Across Supermarkets

Step 1: Clean and Normalize Names. Before matching, preprocess all product names to reduce irrelevant

differences.   For   example,   convert   to   lowercase,   remove   punctuation/special   characters,   normalize   units

(e.g. “1L” vs “1 L”), and strip common “fluff” words (e.g. “New!”, “Sale”). In Python you might do: 

import re

name = re.sub(r'[^A-Za-z0-9 ]+', '', raw_name).lower().strip()

Or in SQL: 

UPDATE products

SET norm_name = lower(regexp_replace(name, '[^A-Za-z0-9 ]', '', 'g'));

This ensures fuzzy algorithms compare core terms. Use libraries like Python’s  unidecode  (for accents) or
Postgres  unaccent / citext  types as needed. 

Step 2: Choose String Similarity Metrics. Common approaches include: 

• 

Edit distance (Levenshtein): counts character insertions/deletions. Useful for minor typos. 

• 

Token-based Jaccard: treat names as sets of words and compute |intersection|/|union|

1

.

<PERSON>accard gives 0–1 similarity based on shared tokens. 

• 

Cosine similarity of word vectors: represent names as TF-IDF or embedding vectors and take the

dot-product norm . This accounts for term frequency and ignores length. 

2

For example, <PERSON><PERSON><PERSON> similarity is defined as the size of the intersection of two token sets divided by the size

of their union

1

, and cosine similarity is the dot product of two term-vectors divided by their magnitudes

2

. You can also use n-gram similarity (especially Postgres’ trigram) to capture partial overlap. 

Step 3: Fuzzy Matching with Python. In Python, libraries like RapidFuzz or textdistance implement these

metrics.   RapidFuzz   is   a   fast,   C++-backed   library   (MIT-licensed)   that   can   compute   token-based   or   edit-

distance scores

3

. Example using RapidFuzz’s  process  API: 

from rapidfuzz import process, fuzz

# Example list of names from New World

neww_names = [row["norm_name"] for row in new_world_products]

source_name = paknsave_product["norm_name"]

# Find best match in New World for the Pak'nSave name

1

match = process.extractOne(source_name, neww_names,

scorer=fuzz.token_sort_ratio)

matched_name, score, index = match

# e.g. ("brand x milk fullcream 1l", 92, 5)

The   token_sort_ratio   or   token_set_ratio   handles   out-of-order   words.   You   can   set   a   score

threshold   (e.g.   ≥80)   to   accept   a   match.   For   large   datasets,   consider   rapidfuzz.process.cdist   or

batch   methods   to   compute   pairwise   scores   efficiently.   Other   libraries   include  textdistance  or

recordlinkage/dedupe for advanced record linkage. 

Step   4:   SQL   Fuzzy   Matching   (Postgres/Supabase).  Supabase’s   Postgres   supports   extensions   for   fuzzy

matching

4

5

. First enable them: 

CREATE EXTENSION IF NOT EXISTS fuzzystrmatch;

CREATE EXTENSION IF NOT EXISTS pg_trgm;

• 

Levenshtein: The  fuzzystrmatch  extension provides the  levenshtein(s1, s2)  function. You

can join tables on a maximum distance, e.g.: 

SELECT a.id AS pakn_id, b.id AS neww_id,

levenshtein(a.norm_name, b.norm_name) AS edit_dist

FROM products a

JOIN products b ON a.store = 'PaknSave' AND b.store = 'New World'

WHERE levenshtein(a.norm_name, b.norm_name) < 5;

• 

Trigram similarity: With  pg_trgm , use  similarity()  or the  %  operator (requires setting a
threshold, e.g.  SET pg_trgm.similarity_threshold = 0.4 ). For example: 

SELECT a.id AS pakn_id, b.id AS wool_id,

similarity(a.norm_name, b.norm_name) AS sim

FROM products a

JOIN products b

ON a.store = 'PaknSave' AND b.store = 'Woolworths'

AND a.norm_name % b.norm_name

-- matches if similarity > threshold

ORDER BY sim DESC

LIMIT 10;

This returns the top Woolworths matches for each Pak’nSave product by trigram similarity. You can adjust
the threshold or filter by  similarity() > 0.5  to control fuzziness. Using  pg_trgm  is efficient with a

GIN   index   on   the   name   column.   Together,   Postgres’s   fuzzystrmatch   and   pg_trgm   cover   many   common

similarity needs

4

5

. 

Step 5: Use External IDs if Available. Whenever possible, match on universal codes. If your scraped data

includes barcodes/GTIN/EAN or manufacturer SKUs, join on those fields directly. For example: 

2

SELECT p1.name, p2.name

FROM products p1

JOIN products p2 ON p1.ean = p2.ean

WHERE p1.store = 'PaknSave' AND p2.store = 'New World';

Exact ID matches avoid fuzzy logic entirely. In practice, many scraped catalogues lack these IDs – as one

retailer analysis notes, “GTIN codes…are not often presented” in SKU-level data, so matching must rely on

labels

6

.   If   IDs   are   missing,   consider   augmenting   data   via   an   external   product   database   (e.g.

OpenFoodFacts or GS1 data) or image recognition APIs that can extract barcodes. 

Step 6: Advanced NLP/ML Approaches.  For hard cases, AI techniques can help. You can embed product

names (or full descriptions) using a model like Sentence-BERT or spaCy, then compute cosine similarity on

the embeddings. E.g.: 

from sentence_transformers import SentenceTransformer, util

model = SentenceTransformer('all-MiniLM-L6-v2')

emb1 = model.encode("Brand X Full Cream Milk 1L")

emb2 = model.encode("Brand X Milk Full Cream 1 Litre")

score = util.cos_sim(emb1, emb2).item()

# ~0.9 for very similar

A high cosine score suggests a match. Alternatively, you could use a language model (e.g. OpenAI GPT-4)

with a prompt like “Are these product names referring to the same item?” for ambiguous cases. On the ML

side, one can also train a classifier/regressor on labeled pairs: compare metadata features (name similarity,

brand match, price proximity) and predict match probability

7

. In practice, pairwise blocking (e.g. only

comparing within the same brand or category) and human review of borderline cases is often needed.

Databricks notes that fully automated matching is “impossible” without errors, but ML can automatically

accept high-confidence matches and flag uncertain ones for review

7

. 

Step   7:   Integrate   with   Supabase   Workflow.  With   names   matched,   assemble   a   comparison   table   in
Supabase. For example, use the Python   supabase-py   client to fetch products, run matching logic, and

upsert results: 

from supabase import create_client

supabase = create_client(SUPABASE_URL, SUPABASE_KEY)

products =

supabase.table('products').select('id,store,norm_name,price').execute().data

# (Assume products is now a list of dicts; convert to pandas or loop.)

# Compute matches and insert into a price_comparison table

for pakn in products:

# find matching items in other stores, e.g. via RapidFuzz or direct lookup

# then upsert into price_comparison with pakn_id, matched_neww_id, 

matched_wool_id, and prices.

pass

3

Alternatively, you can do everything in SQL by joining the  products  table to itself (as shown above) and
inserting into a  product_matches  view or table. For instance: 

CREATE TABLE product_matches AS

SELECT a.id AS pakn_id, b.id AS neww_id, c.id AS wool_id,

a.price AS pakn_price, b.price AS neww_price, c.price AS wool_price

FROM products a

JOIN products b ON (a.norm_name % b.norm_name) AND b.store = 'New World'

JOIN products c ON (a.norm_name % c.norm_name) AND c.store = 'Woolworths'

WHERE a.store='PaknSave'

AND similarity(a.norm_name, b.norm_name) > 0.5

AND similarity(a.norm_name, c.norm_name) > 0.5;

Finally, present the joined results in your front-end or BI tool. The end goal is a table with each Pak’nSave

product matched to the corresponding New World and Woolworths items (if any), along with their prices for

direct   comparison.   By   combining   robust   name   normalization,   appropriate   similarity   metrics   (token/

character-based or vector-based), and leveraging any available IDs, you can automate most matches. Only a

few difficult cases should require manual resolution or higher-level AI tools. 

Summary of Recommended Tools: Use Python libraries like RapidFuzz for fuzzy string matching

3

, and

consider sentence-transformers or OpenAI for semantic matching. In SQL, enable PostgreSQL’s pg_trgm

(trigram   similarity)   and  fuzzystrmatch  (Levenshtein)   extensions

4

5

  and   write   similarity-based   JOIN

queries. Normalize and tokenize names first, and always check for exact IDs (barcodes/SKUs) to shortcut

matching. Following these steps yields a reliable price-comparison table linking “the same” products across

Pak’nSave, New World, and Woolworths. 

Sources:  Authoritative   documentation   and   examples   for   these   techniques   have   been   used,   including

Postgres   extension   references

4

5

  and   known   text-similarity   definitions

1

2

,   as   well   as   industry

discussion of product matching challenges

6

7

. 

1

Jaccard index - Wikipedia

https://en.wikipedia.org/wiki/Jaccard_index

2

Cosine similarity - Wikipedia

https://en.wikipedia.org/wiki/Cosine_similarity

3

RapidFuzz · PyPI

https://pypi.org/project/RapidFuzz/

4

5

Postgres Extensions Overview | Supabase Docs

https://supabase.com/docs/guides/database/extensions

6

7

Product Fuzzy Matching with Zingg | Databricks Blog

https://www.databricks.com/blog/using-images-and-metadata-product-fuzzy-matching-zingg

4

