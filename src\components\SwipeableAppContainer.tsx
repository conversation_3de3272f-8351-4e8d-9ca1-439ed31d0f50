import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  Animated,
  TouchableOpacity,
  StatusBar,
  Platform,
} from 'react-native';
import { 
  GestureHandlerRootView, 
  PanGestureHandler, 
  PanGestureHandlerGestureEvent 
} from 'react-native-gesture-handler';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '../context/ThemeContext';
import { theme, getThemeColors } from '../styles';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface SwipeableAppContainerProps {
  shoppingListComponent: React.ReactNode;
  cookbookComponent: React.ReactNode;
  friendsComponent: React.ReactNode;
  onOpenDrawer: () => void;
}

export const SwipeableAppContainer: React.FC<SwipeableAppContainerProps> = ({
  shoppingListComponent,
  cookbookComponent,
  friendsComponent,
  onOpenDrawer,
}) => {
  const { colors: themeColors } = useTheme();
  const [currentScreen, setCurrentScreen] = useState<'shopping' | 'cookbook' | 'friends'>('shopping');
  const translateX = useRef(new Animated.Value(0)).current;
  const gestureRef = useRef<PanGestureHandler>(null);

  const handleGesture = Animated.event(
    [{ nativeEvent: { translationX: translateX } }],
    { useNativeDriver: true }
  );

  const handleGestureEnd = (event: any) => {
    const { translationX, velocityX } = event.nativeEvent;
    
    let newScreen = currentScreen;
    let targetTranslateX = 0;
    
    if (currentScreen === 'shopping') {
      // From shopping: swipe left to go to cookbook
      if (translationX < -SCREEN_WIDTH * 0.3 || velocityX < -500) {
        newScreen = 'cookbook';
        targetTranslateX = -SCREEN_WIDTH;
      }
    } else if (currentScreen === 'cookbook') {
      // From cookbook: swipe left to go to friends, swipe right to go to shopping
      if (translationX < -SCREEN_WIDTH * 0.3 || velocityX < -500) {
        newScreen = 'friends';
        targetTranslateX = -SCREEN_WIDTH * 2;
      } else if (translationX > SCREEN_WIDTH * 0.3 || velocityX > 500) {
        newScreen = 'shopping';
        targetTranslateX = 0;
      } else {
        targetTranslateX = -SCREEN_WIDTH;
      }
    } else { // friends
      // From friends: swipe right to go to cookbook
      if (translationX > SCREEN_WIDTH * 0.3 || velocityX > 500) {
        newScreen = 'cookbook';
        targetTranslateX = -SCREEN_WIDTH;
      } else {
        targetTranslateX = -SCREEN_WIDTH * 2;
      }
    }

    Animated.spring(translateX, {
      toValue: targetTranslateX,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start();

    if (newScreen !== currentScreen) {
      setCurrentScreen(newScreen);
    }
  };

  const switchToScreen = (screen: 'shopping' | 'cookbook' | 'friends') => {
    setCurrentScreen(screen);
    let targetTranslateX = 0;
    
    if (screen === 'shopping') {
      targetTranslateX = 0;
    } else if (screen === 'cookbook') {
      targetTranslateX = -SCREEN_WIDTH;
    } else { // friends
      targetTranslateX = -SCREEN_WIDTH * 2;
    }
    
    Animated.spring(translateX, {
      toValue: targetTranslateX,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start();
  };

  return (
    <GestureHandlerRootView style={[styles.container, { backgroundColor: themeColors.background }]}>
      <StatusBar barStyle="dark-content" backgroundColor={themeColors.background} />
      <SafeAreaView style={styles.safeArea}>
        {/* Beautiful Header with Gradient */}
        <LinearGradient
          colors={[themeColors.background, themeColors.backgroundSecondary]}
          style={[styles.header, { borderBottomColor: themeColors.border }]}
        >
          {/* Drawer Menu */}
          <TouchableOpacity onPress={onOpenDrawer} style={[styles.drawerButton, { backgroundColor: themeColors.surface }]}>
            <Ionicons name="menu" size={24} color={themeColors.primary} />
          </TouchableOpacity>
          
          {/* Navigation Icons */}
          <View style={[styles.navigationContainer, { backgroundColor: themeColors.backgroundTertiary }]}>
            <TouchableOpacity
              onPress={() => switchToScreen('shopping')}
              style={[
                styles.navButton,
                currentScreen === 'shopping' && { backgroundColor: themeColors.primary + '15' }
              ]}
            >
              <Ionicons 
                name="list" 
                size={18} 
                color={currentScreen === 'shopping' ? themeColors.primary : themeColors.textSecondary} 
              />
              <Text style={[
                styles.navButtonText,
                { color: currentScreen === 'shopping' ? themeColors.primary : themeColors.textSecondary }
              ]}>
                Shopping
              </Text>
            </TouchableOpacity>
            
            <View style={[styles.divider, { backgroundColor: themeColors.border }]} />
            
            <TouchableOpacity
              onPress={() => switchToScreen('cookbook')}
              style={[
                styles.navButton,
                currentScreen === 'cookbook' && { backgroundColor: themeColors.primary + '15' }
              ]}
            >
              <Ionicons 
                name="book" 
                size={18} 
                color={currentScreen === 'cookbook' ? themeColors.primary : themeColors.textSecondary} 
              />
              <Text style={[
                styles.navButtonText,
                { color: currentScreen === 'cookbook' ? themeColors.primary : themeColors.textSecondary }
              ]}>
                Cookbook
              </Text>
            </TouchableOpacity>
            
            <View style={[styles.divider, { backgroundColor: themeColors.border }]} />
            
            <TouchableOpacity
              onPress={() => switchToScreen('friends')}
              style={[
                styles.navButton,
                currentScreen === 'friends' && { backgroundColor: themeColors.primary + '15' }
              ]}
            >
              <Ionicons 
                name="people" 
                size={18} 
                color={currentScreen === 'friends' ? themeColors.primary : themeColors.textSecondary} 
              />
              <Text style={[
                styles.navButtonText,
                { color: currentScreen === 'friends' ? themeColors.primary : themeColors.textSecondary }
              ]}>
                Friends
              </Text>
            </TouchableOpacity>
          </View>
        </LinearGradient>

        {/* Screen Indicator */}
        <View style={[styles.screenIndicator, { backgroundColor: themeColors.backgroundSecondary }]}>
          <Text style={[styles.screenTitle, { color: themeColors.text }]}>
            {currentScreen === 'shopping' ? '🛒 Shopping List' : 
             currentScreen === 'cookbook' ? '📚 Recipe Cookbook' : '👥 Friends & Family'}
          </Text>
          <View style={styles.screenDots}>
            {['shopping', 'cookbook', 'friends'].map((screen, index) => (
              <View
                key={screen}
                style={[
                  styles.dot,
                  {
                    backgroundColor: currentScreen === screen 
                      ? themeColors.primary 
                      : themeColors.textTertiary,
                    width: currentScreen === screen ? 20 : 6,
                  }
                ]}
              />
            ))}
          </View>
        </View>

        {/* Swipeable Content */}
        <PanGestureHandler
          ref={gestureRef}
          onGestureEvent={handleGesture}
          onEnded={handleGestureEnd}
          activeOffsetX={[-10, 10]}
        >
          <Animated.View style={styles.contentContainer}>
            <Animated.View
              style={[
                styles.screensContainer,
                {
                  transform: [{ translateX }],
                },
              ]}
            >
              {/* Shopping List Screen */}
              <View style={[styles.screen, { backgroundColor: themeColors.background }]}>
                {shoppingListComponent}
              </View>
              
              {/* Cookbook Screen */}
              <View style={[styles.screen, { backgroundColor: themeColors.background }]}>
                {cookbookComponent}
              </View>
              
              {/* Friends & Family Screen */}
              <View style={[styles.screen, { backgroundColor: themeColors.background }]}>
                {friendsComponent}
              </View>
            </Animated.View>
          </Animated.View>
        </PanGestureHandler>
      </SafeAreaView>
    </GestureHandlerRootView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.base,
    borderBottomWidth: 0.5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  drawerButton: {
    padding: theme.spacing.sm,
    borderRadius: theme.radius.lg,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  navigationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: theme.radius.xl,
    padding: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  navButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.radius.lg,
    gap: 4,
  },
  navButtonText: {
    fontSize: theme.typography.fontSize.xs,
    fontWeight: theme.typography.fontWeight.medium,
  },
  divider: {
    width: 1,
    height: 24,
    marginHorizontal: 6,
  },
  screenIndicator: {
    alignItems: 'center',
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.base,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  screenTitle: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.semibold,
  },
  screenDots: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  dot: {
    height: 6,
    borderRadius: 3,
  },
  contentContainer: {
    flex: 1,
  },
  screensContainer: {
    flexDirection: 'row',
    width: SCREEN_WIDTH * 3,
    height: '100%',
  },
  screen: {
    width: SCREEN_WIDTH,
    height: '100%',
  },
});