import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  ScrollView,
  StyleSheet,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '../context/ThemeContext';
import { theme } from '../styles';
import { StoreOptimization } from '../types/shoppingList';

interface StoreOptimizationBannerProps {
  optimization: StoreOptimization;
  onStrategyChange?: (strategy: 'cheapest_total' | 'single_store_convenience' | 'balanced') => void;
  currentStrategy?: 'cheapest_total' | 'single_store_convenience' | 'balanced';
  compact?: boolean;
}

export const StoreOptimizationBanner: React.FC<StoreOptimizationBannerProps> = ({
  optimization,
  onStrategyChange,
  currentStrategy = 'balanced',
  compact = false,
}) => {
  const { colors } = useTheme();
  const [showDetails, setShowDetails] = useState(false);

  const getStoreColor = (store: string): string => {
    const storeColors = {
      woolworths: '#00A651',
      newworld: '#E31E24',
      paknsave: '#FFD100',
    };
    return storeColors[store as keyof typeof storeColors] || theme.colors.primary;
  };

  const getStoreEmoji = (store: string): string => {
    const storeEmojis = {
      woolworths: '🍎',
      newworld: '🛒',
      paknsave: '💰',
    };
    return storeEmojis[store as keyof typeof storeEmojis] || '🏪';
  };

  const getStrategyIcon = (strategy: string): string => {
    switch (strategy) {
      case 'cheapest_total':
        return '💰';
      case 'single_store_convenience':
        return '🏪';
      case 'balanced':
        return '⚖️';
      default:
        return '💡';
    }
  };

  const getConvenienceText = (score: number): string => {
    if (score >= 90) return 'Very Convenient';
    if (score >= 70) return 'Convenient';
    if (score >= 50) return 'Moderate';
    return 'Less Convenient';
  };

  const renderCompactBanner = () => (
    <TouchableOpacity
      style={[styles.compactBanner, { backgroundColor: colors.surface, borderColor: colors.border }]}
      onPress={() => setShowDetails(true)}
    >
      <View style={styles.compactContent}>
        <Text style={styles.bannerIcon}>💡</Text>
        <View style={styles.compactInfo}>
          <Text style={[styles.compactTitle, { color: colors.text }]}>
            Smart Shopping
          </Text>
          <Text style={[styles.compactSubtitle, { color: colors.textSecondary }]}>
            Save ${optimization.totalSavings.toFixed(2)} • {optimization.recommendedStores.length} store{optimization.recommendedStores.length > 1 ? 's' : ''}
          </Text>
        </View>
        <Text style={[styles.viewDetailsText, { color: theme.colors.primary }]}>
          View ▶
        </Text>
      </View>
    </TouchableOpacity>
  );

  const renderFullBanner = () => (
    <View style={[styles.banner, { backgroundColor: colors.surface, borderColor: colors.border }]}>
      <View style={styles.bannerHeader}>
        <View style={styles.headerLeft}>
          <Text style={styles.bannerIcon}>
            {getStrategyIcon(optimization.strategy)}
          </Text>
          <View style={styles.headerText}>
            <Text style={[styles.bannerTitle, { color: colors.text }]}>
              Smart Shopping Suggestion
            </Text>
            <Text style={[styles.bannerDescription, { color: colors.textSecondary }]}>
              {optimization.description}
            </Text>
          </View>
        </View>
        
        <TouchableOpacity
          style={[styles.detailsButton, { borderColor: colors.border }]}
          onPress={() => setShowDetails(true)}
        >
          <Text style={[styles.detailsButtonText, { color: colors.textSecondary }]}>
            Details
          </Text>
        </TouchableOpacity>
      </View>

      {/* Quick stats */}
      <View style={styles.statsRow}>
        <View style={styles.statItem}>
          <Text style={[styles.statValue, { color: '#4CAF50' }]}>
            ${optimization.totalSavings.toFixed(2)}
          </Text>
          <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
            Total Savings
          </Text>
        </View>
        
        <View style={styles.statItem}>
          <Text style={[styles.statValue, { color: colors.text }]}>
            {optimization.recommendedStores.length}
          </Text>
          <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
            Store{optimization.recommendedStores.length > 1 ? 's' : ''}
          </Text>
        </View>
        
        <View style={styles.statItem}>
          <Text style={[styles.statValue, { color: colors.text }]}>
            {getConvenienceText(optimization.convenienceScore)}
          </Text>
          <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
            Convenience
          </Text>
        </View>
      </View>

      {/* Store breakdown */}
      <View style={styles.storesRow}>
        {optimization.recommendedStores.slice(0, 3).map((store, index) => (
          <View key={store.store} style={[styles.storeChip, { backgroundColor: colors.backgroundSecondary }]}>
            <Text style={styles.storeChipEmoji}>
              {getStoreEmoji(store.store)}
            </Text>
            <Text style={[styles.storeChipText, { color: getStoreColor(store.store) }]}>
              {store.storeName}
            </Text>
            <Text style={[styles.storeChipCount, { color: colors.textSecondary }]}>
              {store.itemCount} item{store.itemCount > 1 ? 's' : ''}
            </Text>
          </View>
        ))}
        
        {optimization.recommendedStores.length > 3 && (
          <View style={[styles.moreStoresChip, { backgroundColor: colors.backgroundSecondary }]}>
            <Text style={[styles.moreStoresText, { color: colors.textSecondary }]}>
              +{optimization.recommendedStores.length - 3} more
            </Text>
          </View>
        )}
      </View>
    </View>
  );

  const renderDetailsModal = () => (
    <Modal
      visible={showDetails}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={() => setShowDetails(false)}
    >
      <SafeAreaView style={[styles.modalContainer, { backgroundColor: colors.backgroundSecondary }]}>
        {/* Modal Header */}
        <View style={[styles.modalHeader, { backgroundColor: colors.surface, borderBottomColor: colors.border }]}>
          <TouchableOpacity onPress={() => setShowDetails(false)}>
            <Text style={[styles.modalCloseButton, { color: colors.textSecondary }]}>
              Close
            </Text>
          </TouchableOpacity>
          
          <Text style={[styles.modalTitle, { color: colors.text }]}>
            Shopping Optimization
          </Text>
          
          <View style={{ width: 50 }} />
        </View>

        <ScrollView style={styles.modalContent} showsVerticalScrollIndicator={false}>
          {/* Strategy Selection */}
          {onStrategyChange && (
            <View style={[styles.strategySection, { backgroundColor: colors.surface }]}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                Optimization Strategy
              </Text>
              
              <View style={styles.strategyOptions}>
                {[
                  { key: 'cheapest_total', label: 'Cheapest Total', icon: '💰', desc: 'Save the most money' },
                  { key: 'single_store_convenience', label: 'Single Store', icon: '🏪', desc: 'Shop at one store only' },
                  { key: 'balanced', label: 'Balanced', icon: '⚖️', desc: 'Balance savings and convenience' },
                ].map((strategy) => (
                  <TouchableOpacity
                    key={strategy.key}
                    style={[
                      styles.strategyOption,
                      {
                        backgroundColor: currentStrategy === strategy.key ? theme.colors.primary + '20' : 'transparent',
                        borderColor: currentStrategy === strategy.key ? theme.colors.primary : colors.border,
                      }
                    ]}
                    onPress={() => onStrategyChange(strategy.key as any)}
                  >
                    <Text style={styles.strategyIcon}>{strategy.icon}</Text>
                    <View style={styles.strategyText}>
                      <Text style={[styles.strategyLabel, { color: colors.text }]}>
                        {strategy.label}
                      </Text>
                      <Text style={[styles.strategyDesc, { color: colors.textSecondary }]}>
                        {strategy.desc}
                      </Text>
                    </View>
                    {currentStrategy === strategy.key && (
                      <Text style={[styles.selectedIndicator, { color: theme.colors.primary }]}>
                        ✓
                      </Text>
                    )}
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          )}

          {/* Current Optimization */}
          <View style={[styles.optimizationSection, { backgroundColor: colors.surface }]}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Current Recommendation
            </Text>
            
            <View style={styles.currentStrategy}>
              <Text style={styles.currentStrategyIcon}>
                {getStrategyIcon(optimization.strategy)}
              </Text>
              <View style={styles.currentStrategyText}>
                <Text style={[styles.currentStrategyTitle, { color: colors.text }]}>
                  {optimization.strategy === 'budget' ? 'Budget Approach' :
                   optimization.strategy === 'single' ? 'Single Store' :
                   'Mixed Approach'}
                </Text>
                <Text style={[styles.currentStrategyDesc, { color: colors.textSecondary }]}>
                  {optimization.description}
                </Text>
              </View>
            </View>

            {/* Savings breakdown */}
            <View style={styles.savingsBreakdown}>
              <View style={styles.savingsRow}>
                <Text style={[styles.savingsLabel, { color: colors.textSecondary }]}>
                  Total Potential Savings:
                </Text>
                <Text style={[styles.savingsAmount, { color: '#4CAF50' }]}>
                  ${optimization.totalSavings.toFixed(2)}
                </Text>
              </View>
              
              <View style={styles.savingsRow}>
                <Text style={[styles.savingsLabel, { color: colors.textSecondary }]}>
                  Convenience Score:
                </Text>
                <Text style={[styles.savingsAmount, { color: colors.text }]}>
                  {optimization.convenienceScore}/100
                </Text>
              </View>
            </View>
          </View>

          {/* Store Details */}
          <View style={[styles.storesSection, { backgroundColor: colors.surface }]}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Recommended Stores
            </Text>
            
            {optimization.recommendedStores.map((store, index) => (
              <View key={store.store} style={[styles.storeDetail, { borderBottomColor: colors.border }]}>
                <View style={styles.storeDetailHeader}>
                  <View style={styles.storeDetailLeft}>
                    <Text style={styles.storeDetailEmoji}>
                      {getStoreEmoji(store.store)}
                    </Text>
                    <View style={styles.storeDetailInfo}>
                      <Text style={[styles.storeDetailName, { color: getStoreColor(store.store) }]}>
                        {store.storeName}
                      </Text>
                      <Text style={[styles.storeDetailStats, { color: colors.textSecondary }]}>
                        {store.itemCount} items • ${store.totalCost.toFixed(2)}
                      </Text>
                    </View>
                  </View>
                </View>
                
                {/* Items list */}
                <View style={styles.storeItems}>
                  {store.items.slice(0, 3).map((item, itemIndex) => (
                    <Text key={itemIndex} style={[styles.storeItemText, { color: colors.textSecondary }]}>
                      • {item}
                    </Text>
                  ))}
                  {store.items.length > 3 && (
                    <Text style={[styles.moreItemsText, { color: colors.textTertiary }]}>
                      ... and {store.items.length - 3} more items
                    </Text>
                  )}
                </View>
              </View>
            ))}
          </View>
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );

  return (
    <>
      {compact ? renderCompactBanner() : renderFullBanner()}
      {renderDetailsModal()}
    </>
  );
};

const styles = StyleSheet.create({
  compactBanner: {
    borderRadius: theme.radius.lg,
    borderWidth: 1,
    marginHorizontal: theme.spacing.base,
    marginVertical: theme.spacing.sm,
  },
  compactContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: theme.spacing.sm,
  },
  compactInfo: {
    flex: 1,
    marginLeft: theme.spacing.sm,
  },
  compactTitle: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.semibold,
  },
  compactSubtitle: {
    fontSize: theme.typography.fontSize.sm,
    marginTop: 2,
  },
  viewDetailsText: {
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.semibold,
  },
  banner: {
    borderRadius: theme.radius.lg,
    borderWidth: 1,
    marginHorizontal: theme.spacing.base,
    marginVertical: theme.spacing.sm,
    padding: theme.spacing.base,
  },
  bannerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: theme.spacing.sm,
  },
  headerLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  bannerIcon: {
    fontSize: 24,
    marginRight: theme.spacing.sm,
  },
  headerText: {
    flex: 1,
  },
  bannerTitle: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.semibold,
    marginBottom: 4,
  },
  bannerDescription: {
    fontSize: theme.typography.fontSize.sm,
    lineHeight: 18,
  },
  detailsButton: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.radius.base,
    borderWidth: 1,
  },
  detailsButtonText: {
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.medium,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: theme.spacing.sm,
    paddingVertical: theme.spacing.sm,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.bold,
  },
  statLabel: {
    fontSize: theme.typography.fontSize.xs,
    marginTop: 2,
  },
  storesRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing.xs,
  },
  storeChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.radius.base,
    gap: theme.spacing.xs,
  },
  storeChipEmoji: {
    fontSize: 14,
  },
  storeChipText: {
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.semibold,
  },
  storeChipCount: {
    fontSize: theme.typography.fontSize.xs,
  },
  moreStoresChip: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.radius.base,
  },
  moreStoresText: {
    fontSize: theme.typography.fontSize.sm,
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.base,
    paddingVertical: theme.spacing.base,
    borderBottomWidth: 1,
  },
  modalCloseButton: {
    fontSize: theme.typography.fontSize.base,
    width: 50,
  },
  modalTitle: {
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.semibold,
    textAlign: 'center',
  },
  modalContent: {
    flex: 1,
    padding: theme.spacing.base,
  },
  strategySection: {
    borderRadius: theme.radius.lg,
    padding: theme.spacing.base,
    marginBottom: theme.spacing.base,
  },
  sectionTitle: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.semibold,
    marginBottom: theme.spacing.sm,
  },
  strategyOptions: {
    gap: theme.spacing.sm,
  },
  strategyOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: theme.spacing.sm,
    borderRadius: theme.radius.base,
    borderWidth: 1,
  },
  strategyIcon: {
    fontSize: 20,
    marginRight: theme.spacing.sm,
  },
  strategyText: {
    flex: 1,
  },
  strategyLabel: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.medium,
  },
  strategyDesc: {
    fontSize: theme.typography.fontSize.sm,
    marginTop: 2,
  },
  selectedIndicator: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  optimizationSection: {
    borderRadius: theme.radius.lg,
    padding: theme.spacing.base,
    marginBottom: theme.spacing.base,
  },
  currentStrategy: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: theme.spacing.sm,
  },
  currentStrategyIcon: {
    fontSize: 24,
    marginRight: theme.spacing.sm,
  },
  currentStrategyText: {
    flex: 1,
  },
  currentStrategyTitle: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.semibold,
    marginBottom: 4,
  },
  currentStrategyDesc: {
    fontSize: theme.typography.fontSize.sm,
    lineHeight: 18,
  },
  savingsBreakdown: {
    gap: theme.spacing.xs,
  },
  savingsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  savingsLabel: {
    fontSize: theme.typography.fontSize.sm,
  },
  savingsAmount: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.semibold,
  },
  storesSection: {
    borderRadius: theme.radius.lg,
    padding: theme.spacing.base,
  },
  storeDetail: {
    paddingVertical: theme.spacing.sm,
    borderBottomWidth: 1,
  },
  storeDetailHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: theme.spacing.xs,
  },
  storeDetailLeft: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flex: 1,
  },
  storeDetailEmoji: {
    fontSize: 20,
    marginRight: theme.spacing.sm,
  },
  storeDetailInfo: {
    flex: 1,
  },
  storeDetailName: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.semibold,
  },
  storeDetailStats: {
    fontSize: theme.typography.fontSize.sm,
    marginTop: 2,
  },
  storeItems: {
    marginLeft: 32,
    gap: 2,
  },
  storeItemText: {
    fontSize: theme.typography.fontSize.sm,
  },
  moreItemsText: {
    fontSize: theme.typography.fontSize.sm,
    fontStyle: 'italic',
  },
});