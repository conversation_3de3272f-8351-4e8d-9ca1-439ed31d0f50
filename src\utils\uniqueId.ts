/**
 * Robust unique ID generation utility
 * Prevents duplicate IDs even when created rapidly
 */

let counter = 0;
const startTime = Date.now();

/**
 * Generate a unique ID that's guaranteed to be unique even when called rapidly
 */
export function generateUniqueId(prefix: string = 'id'): string {
  counter++;
  const timestamp = Date.now() - startTime; // Use relative timestamp
  const random = Math.random().toString(36).substr(2, 9);
  const sequence = counter.toString(36);
  
  return `${prefix}_${timestamp}_${sequence}_${random}`;
}

/**
 * Generate a unique item ID
 */
export function generateItemId(): string {
  return generateUniqueId('item');
}

/**
 * Generate a unique list ID
 */
export function generateListId(): string {
  return generateUniqueId('list');
}

/**
 * Generate a unique component key for React
 */
export function generateComponentKey(baseKey: string): string {
  return `${baseKey}_${generateUniqueId('key')}`;
}

/**
 * Create a unique key for arrays with potential duplicates
 */
export function createArrayKey(baseKey: string, index: number): string {
  return `${baseKey}_idx_${index}_${Date.now()}_${Math.random().toString(36).substr(2, 4)}`;
}