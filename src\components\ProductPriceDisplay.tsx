import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { ProductPricing, SupermarketPrice, PriceService } from '../services/PriceService';

interface ProductPriceDisplayProps {
  pricing: ProductPricing | null;
  compact?: boolean;
  showAllPrices?: boolean;
}

export const ProductPriceDisplay: React.FC<ProductPriceDisplayProps> = ({
  pricing,
  compact = false,
  showAllPrices = false,
}) => {
  if (!pricing) {
    return (
      <View style={styles.noPricing}>
        <Text style={styles.noPricingText}>Price unavailable</Text>
      </View>
    );
  }

  const bestPrice = pricing.bestPrice;
  const availablePrices = pricing.prices.filter(p => p.inStock);

  if (compact && bestPrice) {
    // Compact view - show only best price with small indicator
    return (
      <View style={styles.compactContainer}>
        <View style={styles.bestPriceContainer}>
          <Text style={styles.bestPriceLabel}>Best</Text>
          <Text style={styles.bestPriceAmount}>{PriceService.formatPrice(bestPrice.price)}</Text>
          <Text style={styles.supermarketEmoji}>{bestPrice.supermarketLogo}</Text>
        </View>
        {availablePrices.length > 1 && (
          <Text style={styles.moreOptionsText}>+{availablePrices.length - 1} more</Text>
        )}
      </View>
    );
  }

  if (showAllPrices) {
    // Full view - show all available prices
    return (
      <View style={styles.fullContainer}>
        <Text style={styles.priceHeader}>Prices Comparison</Text>
        <View style={styles.pricesGrid}>
          {availablePrices.map((price, index) => {
            const isBest = bestPrice && price.price === bestPrice.price && price.supermarket === bestPrice.supermarket;
            return (
              <View key={`${price.supermarket}-${index}`} style={[styles.priceCard, isBest && styles.bestPriceCard]}>
                <View style={styles.priceCardHeader}>
                  <Text style={styles.supermarketLogo}>{price.supermarketLogo}</Text>
                  {isBest && <View style={styles.bestBadge}><Text style={styles.bestBadgeText}>BEST</Text></View>}
                </View>
                <Text style={[styles.priceAmount, isBest && styles.bestPriceText]}>
                  {PriceService.formatPrice(price.price)}
                </Text>
                <Text style={styles.priceUnit}>per {price.unit}</Text>
              </View>
            );
          })}
        </View>
        {pricing.averagePrice && (
          <Text style={styles.averagePrice}>
            Avg: {PriceService.formatPrice(pricing.averagePrice)}
          </Text>
        )}
      </View>
    );
  }

  // Default view - modern horizontal price strip
  return (
    <View style={styles.defaultContainer}>
      <View style={styles.priceStrip}>
        {availablePrices.slice(0, 3).map((price, index) => {
          const isBest = bestPrice && price.price === bestPrice.price && price.supermarket === bestPrice.supermarket;
          return (
            <View key={`${price.supermarket}-${index}`} style={[styles.priceChip, isBest && styles.bestChip]}>
              <Text style={styles.chipLogo}>{price.supermarketLogo}</Text>
              <Text style={[styles.chipPrice, isBest && styles.bestChipPrice]}>
                {PriceService.formatPrice(price.price)}
              </Text>
            </View>
          );
        })}
        {availablePrices.length > 3 && (
          <View style={styles.moreChip}>
            <Text style={styles.moreChipText}>+{availablePrices.length - 3}</Text>
          </View>
        )}
      </View>
      {bestPrice && pricing.averagePrice && bestPrice.price < pricing.averagePrice && (
        <View style={styles.savingsIndicator}>
          <Text style={styles.savingsText}>
            Save ${(pricing.averagePrice - bestPrice.price).toFixed(2)}
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  // No pricing styles
  noPricing: {
    paddingVertical: 4,
    paddingHorizontal: 8,
    backgroundColor: '#f8f9fa',
    borderRadius: 6,
  },
  noPricingText: {
    fontSize: 12,
    color: '#6c757d',
    fontStyle: 'italic',
  },
  
  // Compact view styles
  compactContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  bestPriceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#e8f5e8',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 12,
    gap: 4,
  },
  bestPriceLabel: {
    fontSize: 10,
    color: '#28a745',
    fontWeight: '600',
    textTransform: 'uppercase',
  },
  bestPriceAmount: {
    fontSize: 14,
    color: '#28a745',
    fontWeight: '700',
  },
  supermarketEmoji: {
    fontSize: 12,
  },
  moreOptionsText: {
    fontSize: 11,
    color: '#6c757d',
    fontStyle: 'italic',
  },
  
  // Full view styles
  fullContainer: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 12,
    marginTop: 8,
  },
  priceHeader: {
    fontSize: 14,
    fontWeight: '600',
    color: '#495057',
    marginBottom: 8,
  },
  pricesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  priceCard: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 8,
    minWidth: 80,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  bestPriceCard: {
    borderColor: '#28a745',
    backgroundColor: '#f8fff8',
  },
  priceCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    gap: 4,
  },
  supermarketLogo: {
    fontSize: 16,
  },
  bestBadge: {
    backgroundColor: '#28a745',
    paddingHorizontal: 4,
    paddingVertical: 1,
    borderRadius: 4,
  },
  bestBadgeText: {
    fontSize: 8,
    color: 'white',
    fontWeight: '700',
  },
  priceAmount: {
    fontSize: 14,
    fontWeight: '600',
    color: '#495057',
  },
  bestPriceText: {
    color: '#28a745',
  },
  priceUnit: {
    fontSize: 10,
    color: '#6c757d',
  },
  averagePrice: {
    fontSize: 12,
    color: '#6c757d',
    textAlign: 'center',
    marginTop: 8,
    fontStyle: 'italic',
  },
  
  // Default view styles
  defaultContainer: {
    marginTop: 8,
  },
  priceStrip: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    flexWrap: 'wrap',
  },
  priceChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 16,
    gap: 4,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  bestChip: {
    backgroundColor: '#e8f5e8',
    borderColor: '#28a745',
  },
  chipLogo: {
    fontSize: 12,
  },
  chipPrice: {
    fontSize: 12,
    fontWeight: '600',
    color: '#495057',
  },
  bestChipPrice: {
    color: '#28a745',
  },
  moreChip: {
    backgroundColor: '#e9ecef',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 16,
  },
  moreChipText: {
    fontSize: 11,
    color: '#6c757d',
    fontWeight: '500',
  },
  savingsIndicator: {
    marginTop: 4,
    alignSelf: 'flex-start',
  },
  savingsText: {
    fontSize: 11,
    color: '#28a745',
    fontWeight: '600',
    backgroundColor: '#e8f5e8',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
});