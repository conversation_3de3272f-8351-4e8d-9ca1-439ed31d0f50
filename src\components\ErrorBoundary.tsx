import React, { Component, ReactNode } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: any) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: any;
}

/**
 * Main ErrorBoundary component for wrapping the entire app or large sections
 * 
 * @example
 * ```tsx
 * // Wrap your main app navigation
 * <ErrorBoundary onError={logError}>
 *   <AuthProvider>
 *     <AppNavigator />
 *   </AuthProvider>
 * </ErrorBoundary>
 * ```
 * 
 * @example
 * ```tsx
 * // Custom fallback UI
 * <ErrorBoundary 
 *   onError={(error, info) => console.log('Error:', error)}
 *   fallback={<CustomErrorView />}
 * >
 *   <YourComponent />
 * </ErrorBoundary>
 * ```
 */
export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('🚨 ErrorBoundary caught an error:', error);
    console.error('📍 Error Info:', errorInfo);
    
    // Call optional error handler
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
    
    // Store error info for debugging
    this.setState({ errorInfo });
  }

  handleRetry = () => {
    console.log('🔄 User triggered error boundary retry');
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  handleRestart = () => {
    console.log('🔄 User triggered app restart');
    // In a real app, you might want to clear storage or navigate to a safe screen
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <ScrollView contentContainerStyle={styles.container}>
          <View style={styles.content}>
            <View style={styles.iconContainer}>
              <Ionicons name="alert-circle" size={64} color="#FF6B6B" />
            </View>
            
            <Text style={styles.title}>Oops! Something went wrong</Text>
            
            <Text style={styles.message}>
              We encountered an unexpected error while preparing your recipes. 
              Don't worry - your saved recipes and preferences are safe!
            </Text>

            <View style={styles.buttonContainer}>
              <TouchableOpacity 
                style={[styles.button, styles.primaryButton]} 
                onPress={this.handleRetry}
              >
                <Ionicons name="refresh" size={20} color="#fff" style={styles.buttonIcon} />
                <Text style={styles.primaryButtonText}>Try Again</Text>
              </TouchableOpacity>

              <TouchableOpacity 
                style={[styles.button, styles.secondaryButton]} 
                onPress={this.handleRestart}
              >
                <Ionicons name="home" size={20} color="#007AFF" style={styles.buttonIcon} />
                <Text style={styles.secondaryButtonText}>Go to Home</Text>
            </TouchableOpacity>
            </View>

            <View style={styles.helpContainer}>
              <Text style={styles.helpTitle}>💡 What you can try:</Text>
              <Text style={styles.helpItem}>• Close and reopen the app</Text>
              <Text style={styles.helpItem}>• Check your internet connection</Text>
              <Text style={styles.helpItem}>• Update the app if available</Text>
              <Text style={styles.helpItem}>• Contact support if the issue persists</Text>
            </View>

            {__DEV__ && this.state.error && (
              <View style={styles.debugContainer}>
                <Text style={styles.debugTitle}>🔧 Developer Info:</Text>
                <ScrollView style={styles.debugScroll} showsVerticalScrollIndicator={false}>
                  <Text style={styles.debugLabel}>Error Message:</Text>
                <Text style={styles.debugText}>{this.state.error.message}</Text>
                  
                  {this.state.error.stack && (
                    <>
                      <Text style={styles.debugLabel}>Stack Trace:</Text>
                      <Text style={styles.debugText}>{this.state.error.stack}</Text>
                    </>
                  )}
                  
                  {this.state.errorInfo && (
                    <>
                      <Text style={styles.debugLabel}>Component Stack:</Text>
                      <Text style={styles.debugText}>{this.state.errorInfo.componentStack}</Text>
                    </>
                  )}
                </ScrollView>
              </View>
            )}
          </View>
        </ScrollView>
      );
    }

    return this.props.children;
  }
}

// Specialized ErrorBoundary for individual screens
interface ScreenErrorBoundaryProps {
  children: ReactNode;
  screenName?: string;
  onRetry?: () => void;
  onGoBack?: () => void;
}

interface ScreenErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

/**
 * ScreenErrorBoundary - Lightweight error boundary for individual screens
 * 
 * Perfect for wrapping screens that might have specific error-prone operations
 * like API calls, complex calculations, or AI interactions.
 * 
 * @example
 * ```tsx
 * // Basic usage with screen name
 * export const MyScreen = () => (
 *   <ScreenErrorBoundary screenName="Recipe Generator">
 *     <MyScreenContent />
 *   </ScreenErrorBoundary>
 * );
 * ```
 * 
 * @example
 * ```tsx
 * // With custom retry and navigation handlers
 * export const MyScreen = () => {
 *   const navigation = useNavigation();
 *   
 *   return (
 *     <ScreenErrorBoundary 
 *       screenName="AI Recipe Generator"
 *       onRetry={() => {
 *         // Custom retry logic
 *         refreshData();
 *       }}
 *       onGoBack={() => {
 *         navigation.goBack();
 *       }}
 *     >
 *       <MyScreenContent />
 *     </ScreenErrorBoundary>
 *   );
 * };
 * ```
 * 
 * @example
 * ```tsx
 * // For screens with complex state that needs reset
 * export const MyScreen = () => {
 *   const [data, setData] = useState(null);
 *   const [loading, setLoading] = useState(false);
 *   
 *   const handleRetry = () => {
 *     setData(null);
 *     setLoading(false);
 *     // Re-fetch data or reset state
 *   };
 *   
 *   return (
 *     <ScreenErrorBoundary 
 *       screenName="Data Heavy Screen"
 *       onRetry={handleRetry}
 *     >
 *       <MyScreenContent data={data} loading={loading} />
 *     </ScreenErrorBoundary>
 *   );
 * };
 * ```
 */
export class ScreenErrorBoundary extends Component<ScreenErrorBoundaryProps, ScreenErrorBoundaryState> {
  constructor(props: ScreenErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ScreenErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error(`🚨 Screen Error (${this.props.screenName || 'Unknown'}):`, error);
    console.error('📍 Screen Error Info:', errorInfo);
  }

  handleRetry = () => {
    console.log(`🔄 Retrying screen: ${this.props.screenName || 'Unknown'}`);
    this.setState({ hasError: false, error: undefined });
    if (this.props.onRetry) {
      this.props.onRetry();
    }
  };

  handleGoBack = () => {
    console.log(`⬅️ Going back from screen: ${this.props.screenName || 'Unknown'}`);
    this.setState({ hasError: false, error: undefined });
    if (this.props.onGoBack) {
      this.props.onGoBack();
    }
  };

  render() {
    if (this.state.hasError) {
      return (
        <View style={screenStyles.container}>
          <View style={screenStyles.content}>
            <Ionicons name="warning" size={48} color="#FF6B6B" />
            
            <Text style={screenStyles.title}>
              {this.props.screenName ? `${this.props.screenName} Error` : 'Screen Error'}
            </Text>
            
            <Text style={screenStyles.message}>
              Something went wrong on this screen. Your data is safe and you can try again.
            </Text>

            <View style={screenStyles.buttonContainer}>
              <TouchableOpacity 
                style={[screenStyles.button, screenStyles.primaryButton]} 
                onPress={this.handleRetry}
              >
                <Ionicons name="refresh" size={18} color="#fff" style={screenStyles.buttonIcon} />
                <Text style={screenStyles.primaryButtonText}>Try Again</Text>
              </TouchableOpacity>

              {this.props.onGoBack && (
                <TouchableOpacity 
                  style={[screenStyles.button, screenStyles.secondaryButton]} 
                  onPress={this.handleGoBack}
                >
                  <Ionicons name="arrow-back" size={18} color="#007AFF" style={screenStyles.buttonIcon} />
                  <Text style={screenStyles.secondaryButtonText}>Go Back</Text>
                </TouchableOpacity>
              )}
            </View>

            {__DEV__ && this.state.error && (
              <View style={screenStyles.debugContainer}>
                <Text style={screenStyles.debugTitle}>Debug: {this.state.error.message}</Text>
              </View>
            )}
          </View>
        </View>
      );
    }

    return this.props.children;
  }
}

/**
 * Usage Guidelines:
 * 
 * 1. **Main App ErrorBoundary**: Wrap your entire app navigator
 *    - Catches catastrophic errors that would crash the app
 *    - Provides comprehensive error reporting and recovery
 *    - Include error logging/reporting service integration
 * 
 * 2. **Screen ErrorBoundary**: Wrap individual screens or components
 *    - Catches errors specific to that screen
 *    - Allows users to retry or go back
 *    - Less disruptive than app-wide errors
 * 
 * 3. **Best Practices**:
 *    - Use ScreenErrorBoundary for screens with:
 *      * API calls
 *      * Complex state management
 *      * Third-party integrations
 *      * Heavy computations
 *      * AI/ML operations
 *    
 *    - Always provide meaningful screen names
 *    - Implement custom retry logic when possible
 *    - Add navigation handlers for better UX
 *    - Test error scenarios in development
 * 
 * 4. **Error Reporting Integration**:
 *    ```tsx
 *    const logError = (error: Error, errorInfo: any) => {
 *      // Development logging
 *      console.error('Error details:', { error, errorInfo });
 *      
 *      // Production error reporting
 *      if (!__DEV__) {
 *        // Sentry
 *        Sentry.captureException(error, { extra: errorInfo });
 *        
 *        // Firebase Crashlytics
 *        crashlytics().recordError(error);
 *        
 *        // Custom analytics
 *        analytics().logEvent('app_error', {
 *          error_message: error.message,
 *          screen: errorInfo.componentStack
 *        });
 *      }
 *    };
 *    ```
 */

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#f8f9fa',
    minHeight: '100%',
  },
  content: {
    alignItems: 'center',
    maxWidth: 350,
    width: '100%',
  },
  iconContainer: {
    marginBottom: 24,
    padding: 16,
    backgroundColor: '#fff',
    borderRadius: 50,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 16,
    textAlign: 'center',
  },
  message: {
    fontSize: 16,
    color: '#6c757d',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  buttonContainer: {
    width: '100%',
    marginBottom: 32,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 12,
    marginBottom: 12,
  },
  primaryButton: {
    backgroundColor: '#007AFF',
    shadowColor: '#007AFF',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  secondaryButton: {
    backgroundColor: '#fff',
    borderWidth: 2,
    borderColor: '#007AFF',
  },
  buttonIcon: {
    marginRight: 8,
  },
  primaryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButtonText: {
    color: '#007AFF',
    fontSize: 16,
    fontWeight: '600',
  },
  helpContainer: {
    backgroundColor: '#e8f4f8',
    padding: 20,
    borderRadius: 12,
    width: '100%',
    marginBottom: 24,
  },
  helpTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2c3e50',
    marginBottom: 12,
  },
  helpItem: {
    fontSize: 14,
    color: '#495057',
    marginBottom: 6,
    lineHeight: 20,
  },
  debugContainer: {
    backgroundColor: '#2c3e50',
    padding: 16,
    borderRadius: 12,
    width: '100%',
    maxHeight: 300,
  },
  debugTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
    marginBottom: 12,
  },
  debugScroll: {
    maxHeight: 200,
  },
  debugLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#ffc107',
    marginTop: 8,
    marginBottom: 4,
  },
  debugText: {
    fontSize: 12,
    color: '#e9ecef',
    fontFamily: 'monospace',
    lineHeight: 16,
  },
});

// Styles for ScreenErrorBoundary
const screenStyles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#f8f9fa',
  },
  content: {
    alignItems: 'center',
    maxWidth: 280,
    width: '100%',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 16,
    marginBottom: 12,
    textAlign: 'center',
  },
  message: {
    fontSize: 14,
    color: '#6c757d',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  buttonContainer: {
    width: '100%',
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginBottom: 8,
  },
  primaryButton: {
    backgroundColor: '#007AFF',
  },
  secondaryButton: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  buttonIcon: {
    marginRight: 6,
  },
  primaryButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  secondaryButtonText: {
    color: '#007AFF',
    fontSize: 14,
    fontWeight: '600',
  },
  debugContainer: {
    backgroundColor: '#f0f0f0',
    padding: 12,
    borderRadius: 8,
    width: '100%',
    marginTop: 16,
  },
  debugTitle: {
    fontSize: 12,
    color: '#666',
    fontFamily: 'monospace',
  },
}); 