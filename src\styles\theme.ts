/**
 * AI Recipe Planner - Modern Theme System
 * 
 * High-end app design with sophisticated color palette,
 * modern gradients, and premium visual effects.
 */

import { Platform } from 'react-native';

// Enhanced minimalistic color palette with warmth
const COLORS = {
  // Primary brand colors - Warm & sophisticated
  primary: '#74B9FF',        // Gentle sky blue - friendly and trustworthy
  primaryDark: '#0984E3',    // Deeper blue for pressed states
  primaryLight: '#A29BFE',   // Soft lavender accent
  primaryGradient: ['#74B9FF', '#0984E3'] as const, // Beautiful blue gradient
  
  // Secondary colors - Warm neutrals
  secondary: '#FDCB6E',      // Soft golden yellow - warm and inviting
  secondaryDark: '#E17055',  // Muted coral for depth
  secondaryLight: '#FFF5E6', // Very light warm cream
  secondaryGradient: ['#FDCB6E', '#E17055'] as const, // Warm golden gradient
  
  // Accent colors - Cool sophistication
  accent: '#A29BFE',         // Soft lavender - premium feel
  accentDark: '#6C5CE7',     // Deeper purple
  accentLight: '#DDD6FE',    // Very light purple tint
  accentGradient: ['#A29BFE', '#6C5CE7'] as const, // Elegant purple gradient
  
  // Semantic colors - Muted and professional
  success: '#00B894',        // Muted teal green - calming success
  warning: '#FDCB6E',        // Warm golden yellow - friendly warning
  error: '#E17055',          // Muted coral red - gentle error
  info: '#74B9FF',          // Soft sky blue - informative
  
  // Special effect colors - Enhanced minimalism
  premium: '#A29BFE',        // Soft lavender for premium features
  premiumGradient: ['#A29BFE', '#DDD6FE'] as const, // Subtle premium gradient
  glassmorphism: 'rgba(255, 255, 255, 0.8)', // Enhanced glass effect
  
  // Neutral colors - Enhanced minimalistic light theme
  light: {
    background: '#FEFEFE',           // Slightly warm white background
    backgroundSecondary: '#FDFBF7',  // Warm cream for cards
    backgroundTertiary: '#F8F9FA',   // Cool light gray for sections
    surface: '#FFFFFF',              // Pure white surfaces
    surfaceSecondary: '#FDFBF7',     // Warm cream for secondary surfaces
    surfaceElevated: '#FFFFFF',      // White with enhanced shadows
    
    text: '#2D3436',                 // Rich charcoal with warmth
    textSecondary: '#636E72',        // Soft gray-blue
    textTertiary: '#74B9FF',         // Gentle blue for links
    textQuaternary: '#B2BEC3',       // Light gray for muted text
    textMuted: '#DDD6FE',            // Very light purple tint
    
    border: '#E2E8F0',               // Light borders
    borderSecondary: '#CBD5E1',      // Medium borders
    borderAccent: '#94A3B8',         // Accent borders
    separator: '#F1F5F9',            // Separators
    
    overlay: 'rgba(15, 23, 42, 0.5)', // Dark overlay
    overlayLight: 'rgba(248, 250, 252, 0.8)', // Light overlay
    shadow: 'rgba(15, 23, 42, 0.1)',  // Subtle shadows
    shadowDark: 'rgba(15, 23, 42, 0.2)', // Darker shadows
    
    // Glass morphism effects
    glass: 'rgba(255, 255, 255, 0.25)',
    glassStrong: 'rgba(255, 255, 255, 0.35)',
    glassBorder: 'rgba(255, 255, 255, 0.18)',
  },
  
  // Neutral colors - Dark theme (Premium dark design)
  dark: {
    background: '#0F172A',           // Rich dark blue
    backgroundSecondary: '#1E293B',  // Lighter dark blue
    backgroundTertiary: '#334155',   // Medium dark blue
    surface: '#1E293B',              // Dark surfaces
    surfaceSecondary: '#334155',     // Secondary dark surfaces
    surfaceElevated: '#475569',      // Elevated dark surfaces
    
    text: '#F8FAFC',                 // Pure white text
    textSecondary: '#E2E8F0',        // Light gray text
    textTertiary: '#CBD5E1',         // Medium gray text
    textQuaternary: '#94A3B8',       // Dark gray text
    textMuted: '#64748B',            // Muted text
    
    border: '#334155',               // Dark borders
    borderSecondary: '#475569',      // Medium dark borders
    borderAccent: '#64748B',         // Accent borders
    separator: '#1E293B',            // Dark separators
    
    overlay: 'rgba(15, 23, 42, 0.8)', // Dark overlay
    overlayLight: 'rgba(30, 41, 59, 0.8)', // Light dark overlay
    shadow: 'rgba(0, 0, 0, 0.3)',     // Dark shadows
    shadowDark: 'rgba(0, 0, 0, 0.5)',  // Very dark shadows
    
    // Glass morphism effects for dark theme
    glass: 'rgba(30, 41, 59, 0.25)',
    glassStrong: 'rgba(30, 41, 59, 0.35)',
    glassBorder: 'rgba(148, 163, 184, 0.18)',
  },
};

// Modern typography system with improved hierarchy
const TYPOGRAPHY = {
  // Font families - Modern and readable
  fontFamily: {
    regular: Platform.OS === 'ios' ? 'SF Pro Display' : 'Roboto',
    medium: Platform.OS === 'ios' ? 'SF Pro Display' : 'Roboto-Medium',
    semibold: Platform.OS === 'ios' ? 'SF Pro Display' : 'Roboto-Bold',
    bold: Platform.OS === 'ios' ? 'SF Pro Display' : 'Roboto-Black',
    monospace: Platform.OS === 'ios' ? 'SF Mono' : 'Roboto Mono',
  },
  
  // Font sizes - Refined scale
  fontSize: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 28,
    '4xl': 32,
    '5xl': 40,
    '6xl': 48,
    '7xl': 56,
  },
  
  // Line heights - Optimized for readability
  lineHeight: {
    xs: 16,
    sm: 20,
    base: 24,
    lg: 26,
    xl: 28,
    '2xl': 32,
    '3xl': 36,
    '4xl': 40,
    '5xl': 48,
    '6xl': 56,
    '7xl': 64,
  },
  
  // Font weights - Modern hierarchy
  fontWeight: {
    light: '300' as const,
    normal: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: '700' as const,
    black: '800' as const,
  },
  
  // Letter spacing for premium feel
  letterSpacing: {
    tight: -0.5,
    normal: 0,
    wide: 0.5,
    wider: 1,
  },
};

// Sophisticated spacing system (8px grid for modern feel)
const SPACING = {
  xs: 4,
  sm: 8,
  md: 12,
  base: 16,
  lg: 20,
  xl: 24,
  '2xl': 32,
  '3xl': 40,
  '4xl': 48,
  '5xl': 64,
  '6xl': 80,
  '7xl': 96,
  '8xl': 128,
  '9xl': 160,
  '10xl': 192,
};

// Modern border radius system
const RADIUS = {
  none: 0,
  xs: 4,
  sm: 6,
  base: 8,
  lg: 12,
  xl: 16,
  '2xl': 20,
  '3xl': 24,
  '4xl': 28,
  '5xl': 32,
  full: 9999,
};

// Enhanced shadow system with multiple levels
const SHADOWS = {
  none: {
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  xs: {
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 1,
    elevation: 1,
  },
  sm: {
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  base: {
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 4,
  },
  lg: {
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  xl: {
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 12,
  },
  '2xl': {
    shadowOffset: { width: 0, height: 16 },
    shadowOpacity: 0.25,
    shadowRadius: 24,
    elevation: 16,
  },
  '3xl': {
    shadowOffset: { width: 0, height: 24 },
    shadowOpacity: 0.3,
    shadowRadius: 32,
    elevation: 24,
  },
  // Premium shadows
  glow: {
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  premium: {
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 20,
    elevation: 16,
  },
};

// Modern component design tokens
const COMPONENTS = {
  // Modern button styles
  button: {
    height: {
      sm: 36,
      base: 48,
      lg: 56,
      xl: 64,
    },
    padding: {
      sm: { paddingHorizontal: SPACING.base, paddingVertical: SPACING.sm },
      base: { paddingHorizontal: SPACING.xl, paddingVertical: SPACING.base },
      lg: { paddingHorizontal: SPACING['2xl'], paddingVertical: SPACING.lg },
      xl: { paddingHorizontal: SPACING['3xl'], paddingVertical: SPACING.xl },
    },
    borderRadius: RADIUS.xl,
  },
  
  // Modern input styles
  input: {
    height: 52,
    padding: SPACING.base,
    borderRadius: RADIUS.xl,
    borderWidth: 1.5,
  },
  
  // Modern card styles
  card: {
    borderRadius: RADIUS['2xl'],
    padding: SPACING.xl,
    ...SHADOWS.lg,
  },
  
  // Premium card styles
  cardPremium: {
    borderRadius: RADIUS['3xl'],
    padding: SPACING['2xl'],
    ...SHADOWS.xl,
  },
  
  // Modern tab bar
  tabBar: {
    height: 80,
    paddingTop: SPACING.sm,
    paddingBottom: SPACING.lg,
    borderTopWidth: 0,
    borderRadius: RADIUS['2xl'],
  },
  
  // Modern header
  header: {
    height: Platform.OS === 'ios' ? 44 : 56,
    paddingHorizontal: SPACING.xl,
  },
  
  // Glass morphism component
  glass: {
    borderRadius: RADIUS.xl,
    borderWidth: 1,
    backdropFilter: 'blur(20px)',
  },
};

// Enhanced recipe-specific colors
const RECIPE_COLORS = {
  // Difficulty levels with gradients
  difficulty: {
    easy: '#10B981',
    easyGradient: ['#10B981', '#34D399'] as const,
    medium: '#F59E0B',
    mediumGradient: ['#F59E0B', '#FBBF24'] as const,
    hard: '#EF4444',
    hardGradient: ['#EF4444', '#F87171'] as const,
  },
  
  // Meal types with modern colors
  mealType: {
    breakfast: '#F59E0B',
    breakfastGradient: ['#F59E0B', '#FBBF24'] as const,
    lunch: '#10B981',
    lunchGradient: ['#10B981', '#34D399'] as const,
    dinner: '#3B82F6',
    dinnerGradient: ['#3B82F6', '#60A5FA'] as const,
    snack: '#F97316',
    snackGradient: ['#F97316', '#FB923C'] as const,
    dessert: '#8B5CF6',
    dessertGradient: ['#8B5CF6', '#A78BFA'] as const,
  },
  
  // Dietary restrictions
  dietary: {
    vegetarian: '#10B981',
    vegan: '#059669',
    glutenFree: '#F59E0B',
    dairyFree: '#06B6D4',
    keto: '#EC4899',
    paleo: '#8B5CF6',
  },
  
  // Nutrition with modern palette
  nutrition: {
    calories: '#F97316',
    protein: '#3B82F6',
    carbs: '#10B981',
    fat: '#F59E0B',
    fiber: '#8B5CF6',
  },
};

// Enhanced minimalistic gradients with beautiful colors
const GRADIENTS = {
  primary: ['#74B9FF', '#0984E3'] as const,     // Sky blue to deeper blue
  secondary: ['#FDCB6E', '#E17055'] as const,   // Golden yellow to coral
  accent: ['#A29BFE', '#6C5CE7'] as const,      // Lavender to deeper purple
  premium: ['#A29BFE', '#DDD6FE'] as const,     // Subtle premium gradient
  sunset: ['#FDCB6E', '#A29BFE'] as const,      // Golden to lavender
  ocean: ['#74B9FF', '#00B894'] as const,       // Sky blue to teal
  forest: ['#134E5E', '#71B280'] as const,
  warm: ['#FA709A', '#FEE140'] as const,
  cool: ['#A8EDEA', '#FED6E3'] as const,
  dark: ['#2C3E50', '#4A6741'] as const,
};

// Animation curves for smooth interactions
const ANIMATIONS = {
  easing: {
    ease: [0.25, 0.1, 0.25, 1],
    easeIn: [0.42, 0, 1, 1],
    easeOut: [0, 0, 0.58, 1],
    easeInOut: [0.42, 0, 0.58, 1],
    spring: [0.68, -0.55, 0.265, 1.55],
  },
  duration: {
    fast: 150,
    normal: 300,
    slow: 500,
    slower: 800,
  },
};

// Create modern theme object
export const theme = {
  colors: COLORS,
  typography: TYPOGRAPHY,
  spacing: SPACING,
  radius: RADIUS,
  shadows: SHADOWS,
  components: COMPONENTS,
  recipeColors: RECIPE_COLORS,
  gradients: GRADIENTS,
  animations: ANIMATIONS,
} as const;

// Theme type
export type Theme = typeof theme;

// Theme mode type
export type ThemeMode = 'light' | 'dark';

// Current theme mode
let currentThemeMode: ThemeMode = 'light';

// Get current theme colors based on mode
export const getThemeColors = (mode: ThemeMode = currentThemeMode) => {
  return {
    ...COLORS,
    ...COLORS[mode],
    gradients: GRADIENTS,
  };
};

// Set theme mode
export const setThemeMode = (mode: ThemeMode) => {
  currentThemeMode = mode;
};

// Get current theme mode
export const getThemeMode = (): ThemeMode => currentThemeMode;

// Utility function to create themed styles
export const createThemedStyles = <T extends Record<string, any>>(
  stylesFn: (theme: Theme, colors: ReturnType<typeof getThemeColors>) => T
) => {
  return (mode: ThemeMode = currentThemeMode) => {
    const colors = getThemeColors(mode);
    return stylesFn(theme, colors);
  };
};

// Export individual theme tokens
export const colors = COLORS;
export const typography = TYPOGRAPHY;
export const spacing = SPACING;
export const radius = RADIUS;
export const shadows = SHADOWS;
export const components = COMPONENTS;
export const recipeColors = RECIPE_COLORS;
export const gradients = GRADIENTS;
export const animations = ANIMATIONS;