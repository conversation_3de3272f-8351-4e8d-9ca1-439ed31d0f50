import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '../context/ThemeContext';
import { theme } from '../styles';
import { priceComparisonService, PriceComparisonResult, StoreSearchResult } from '../services/priceComparisonService';

interface PriceComparisonModalProps {
  visible: boolean;
  onClose: () => void;
  searchQuery: string;
  onSelectProduct: (result: StoreSearchResult) => void;
}

const { width: SCREEN_WIDTH } = Dimensions.get('window');

export const PriceComparisonModal: React.FC<PriceComparisonModalProps> = ({
  visible,
  onClose,
  searchQuery,
  onSelectProduct,
}) => {
  const { colors } = useTheme();
  const [loading, setLoading] = useState(false);
  const [comparison, setComparison] = useState<PriceComparisonResult | null>(null);
  const [sortBy, setSortBy] = useState<'price' | 'relevance'>('price');

  useEffect(() => {
    if (visible && searchQuery) {
      searchPrices();
    }
  }, [visible, searchQuery, sortBy]);

  const searchPrices = async () => {
    if (!searchQuery.trim()) return;

    setLoading(true);
    try {
      const result = await priceComparisonService.searchProductAcrossStores(searchQuery, {
        maxResults: 5,
        sortBy,
        includeOutOfStock: false,
      });
      setComparison(result);
    } catch (error) {
      console.error('Price search failed:', error);
      Alert.alert('Search Error', 'Failed to search for prices. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const formatPrice = (price: number): string => {
    return price > 0 ? `$${price.toFixed(2)}` : 'Price unavailable';
  };

  const getStoreColor = (store: string): string => {
    const storeColors = {
      woolworths: '#00A651',
      newworld: '#E31E24',
      paknsave: '#FFD100',
    };
    return storeColors[store as keyof typeof storeColors] || theme.colors.primary;
  };

  const getStoreEmoji = (store: string): string => {
    const storeEmojis = {
      woolworths: '🍎',
      newworld: '🛒',
      paknsave: '💰',
    };
    return storeEmojis[store as keyof typeof storeEmojis] || '🏪';
  };

  const renderPriceCard = (result: StoreSearchResult, index: number) => {
    const isLowest = comparison?.cheapestOption?.product.objectID === result.product.objectID;
    const savings = comparison?.priceRange && comparison.priceRange.max > result.product.price 
      ? comparison.priceRange.max - result.product.price 
      : 0;

    return (
      <TouchableOpacity
        key={`${result.store}-${index}`}
        style={[
          {
            backgroundColor: colors.surface,
            borderRadius: theme.radius.lg,
            padding: theme.spacing.base,
            marginBottom: theme.spacing.sm,
            borderWidth: isLowest ? 2 : 1,
            borderColor: isLowest ? '#00C851' : colors.border,
          },
          isLowest && {
            backgroundColor: colors.surface,
            shadowColor: '#00C851',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 8,
            elevation: 4,
          }
        ]}
        onPress={() => onSelectProduct(result)}
      >
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <View style={{ flex: 1 }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: theme.spacing.xs }}>
              <Text style={{ fontSize: 18, marginRight: theme.spacing.xs }}>
                {getStoreEmoji(result.store)}
              </Text>
              <Text style={{
                fontSize: theme.typography.fontSize.sm,
                fontWeight: theme.typography.fontWeight.semibold,
                color: getStoreColor(result.store),
              }}>
                {result.storeName}
              </Text>
              {isLowest && (
                <View style={{
                  backgroundColor: '#00C851',
                  paddingHorizontal: theme.spacing.xs,
                  paddingVertical: 2,
                  borderRadius: theme.radius.sm,
                  marginLeft: theme.spacing.xs,
                }}>
                  <Text style={{
                    color: 'white',
                    fontSize: theme.typography.fontSize.xs,
                    fontWeight: theme.typography.fontWeight.bold,
                  }}>
                    LOWEST
                  </Text>
                </View>
              )}
            </View>
            
            <Text style={{
              fontSize: theme.typography.fontSize.base,
              fontWeight: theme.typography.fontWeight.medium,
              color: colors.text,
              marginBottom: theme.spacing.xs,
            }}>
              {result.product.name}
            </Text>
            
            {result.product.brand && (
              <Text style={{
                fontSize: theme.typography.fontSize.sm,
                color: colors.textSecondary,
                marginBottom: theme.spacing.xs,
              }}>
                {result.product.brand}
              </Text>
            )}
            
            {result.product.description && (
              <Text style={{
                fontSize: theme.typography.fontSize.sm,
                color: colors.textTertiary,
                lineHeight: 18,
              }} numberOfLines={2}>
                {result.product.description}
              </Text>
            )}
          </View>
          
          <View style={{ alignItems: 'flex-end', marginLeft: theme.spacing.sm }}>
            <Text style={{
              fontSize: theme.typography.fontSize.xl,
              fontWeight: theme.typography.fontWeight.bold,
              color: isLowest ? '#00C851' : colors.text,
            }}>
              {formatPrice(result.product.price)}
            </Text>
            
            {result.product.unit && (
              <Text style={{
                fontSize: theme.typography.fontSize.xs,
                color: colors.textSecondary,
                marginTop: 2,
              }}>
                {result.product.unit}
              </Text>
            )}
            
            {savings > 0 && (
              <Text style={{
                fontSize: theme.typography.fontSize.xs,
                color: '#00C851',
                marginTop: 2,
                fontWeight: theme.typography.fontWeight.semibold,
              }}>
                Save ${savings.toFixed(2)}
              </Text>
            )}
          </View>
        </View>
        
        {result.product.promotions && result.product.promotions.length > 0 && (
          <View style={{
            backgroundColor: '#FFE0B2',
            borderRadius: theme.radius.sm,
            padding: theme.spacing.xs,
            marginTop: theme.spacing.xs,
          }}>
            <Text style={{
              fontSize: theme.typography.fontSize.xs,
              color: '#F57C00',
              fontWeight: theme.typography.fontWeight.semibold,
            }}>
              🏷️ {result.product.promotions[0]}
            </Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView style={{ flex: 1, backgroundColor: colors.backgroundSecondary }}>
        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          paddingHorizontal: theme.spacing.base,
          paddingVertical: theme.spacing.base,
          backgroundColor: colors.surface,
          borderBottomWidth: 1,
          borderBottomColor: colors.border,
        }}>
          <TouchableOpacity onPress={onClose}>
            <Text style={{
              fontSize: theme.typography.fontSize.base,
              color: colors.textSecondary,
            }}>
              Cancel
            </Text>
          </TouchableOpacity>
          
          <Text style={{
            fontSize: theme.typography.fontSize.lg,
            fontWeight: theme.typography.fontWeight.semibold,
            color: colors.text,
          }}>
            Price Comparison
          </Text>
          
          <TouchableOpacity onPress={() => {}}>
            <Text style={{
              fontSize: theme.typography.fontSize.base,
              color: 'transparent',
            }}>
              Cancel
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
          <View style={{ padding: theme.spacing.base }}>
            <Text style={{
              fontSize: theme.typography.fontSize.base,
              color: colors.textSecondary,
              marginBottom: theme.spacing.sm,
            }}>
              Searching for "{searchQuery}"
            </Text>

            <View style={{
              flexDirection: 'row',
              marginBottom: theme.spacing.base,
              gap: theme.spacing.sm,
            }}>
              <TouchableOpacity
                style={[
                  {
                    paddingHorizontal: theme.spacing.base,
                    paddingVertical: theme.spacing.xs,
                    borderRadius: theme.radius.full,
                    borderWidth: 1,
                    borderColor: colors.border,
                    backgroundColor: sortBy === 'price' ? theme.colors.primary : colors.surface,
                  }
                ]}
                onPress={() => setSortBy('price')}
              >
                <Text style={{
                  fontSize: theme.typography.fontSize.sm,
                  color: sortBy === 'price' ? 'white' : colors.textSecondary,
                  fontWeight: sortBy === 'price' ? theme.typography.fontWeight.semibold : 'normal',
                }}>
                  By Price
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  {
                    paddingHorizontal: theme.spacing.base,
                    paddingVertical: theme.spacing.xs,
                    borderRadius: theme.radius.full,
                    borderWidth: 1,
                    borderColor: colors.border,
                    backgroundColor: sortBy === 'relevance' ? theme.colors.primary : colors.surface,
                  }
                ]}
                onPress={() => setSortBy('relevance')}
              >
                <Text style={{
                  fontSize: theme.typography.fontSize.sm,
                  color: sortBy === 'relevance' ? 'white' : colors.textSecondary,
                  fontWeight: sortBy === 'relevance' ? theme.typography.fontWeight.semibold : 'normal',
                }}>
                  By Relevance
                </Text>
              </TouchableOpacity>
            </View>

            {loading ? (
              <View style={{
                flex: 1,
                alignItems: 'center',
                justifyContent: 'center',
                paddingVertical: theme.spacing['2xl'],
              }}>
                <ActivityIndicator size="large" color={theme.colors.primary} />
                <Text style={{
                  fontSize: theme.typography.fontSize.base,
                  color: colors.textSecondary,
                  marginTop: theme.spacing.base,
                }}>
                  Searching prices...
                </Text>
              </View>
            ) : comparison ? (
              <>
                {comparison.priceRange && (
                  <View style={{
                    backgroundColor: colors.surface,
                    borderRadius: theme.radius.lg,
                    padding: theme.spacing.base,
                    marginBottom: theme.spacing.base,
                  }}>
                    <Text style={{
                      fontSize: theme.typography.fontSize.sm,
                      color: colors.textSecondary,
                      marginBottom: theme.spacing.xs,
                    }}>
                      Price Range
                    </Text>
                    <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                      <Text style={{
                        fontSize: theme.typography.fontSize.base,
                        color: colors.text,
                      }}>
                        {formatPrice(comparison.priceRange.min)} - {formatPrice(comparison.priceRange.max)}
                      </Text>
                      <Text style={{
                        fontSize: theme.typography.fontSize.base,
                        color: '#00C851',
                        fontWeight: theme.typography.fontWeight.semibold,
                      }}>
                        Save up to ${comparison.priceRange.savings.toFixed(2)}
                      </Text>
                    </View>
                  </View>
                )}

                {comparison.results.length > 0 ? (
                  comparison.results.map((result, index) => renderPriceCard(result, index))
                ) : (
                  <View style={{
                    alignItems: 'center',
                    paddingVertical: theme.spacing['2xl'],
                  }}>
                    <Text style={{ fontSize: 48, marginBottom: theme.spacing.base }}>🔍</Text>
                    <Text style={{
                      fontSize: theme.typography.fontSize.lg,
                      fontWeight: theme.typography.fontWeight.semibold,
                      color: colors.text,
                      marginBottom: theme.spacing.xs,
                    }}>
                      No products found
                    </Text>
                    <Text style={{
                      fontSize: theme.typography.fontSize.base,
                      color: colors.textSecondary,
                      textAlign: 'center',
                    }}>
                      Try adjusting your search term or check back later
                    </Text>
                  </View>
                )}
              </>
            ) : null}
          </View>
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );
};