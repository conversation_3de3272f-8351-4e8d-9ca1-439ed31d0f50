/**
 * ANALYZE SIZE NORMALIZATION ISSUES
 * 
 * Investigates size normalization problems in the deduplication system
 * that may cause incorrect price comparisons or grouping issues.
 */

import { createClient } from '@supabase/supabase-js';
import { normalizeSize, sizesMatch } from '../utils/stringMatching';
import { ProductDeduplicationService } from '../services/productDeduplicationService';
import { IProduct } from '../types/shoppingList';

const supabaseUrl = 'https://emjwniuqwhvohjassnrg.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVtanduaXVxd2h2b2hqYXNzbnJnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjQwMTYwNSwiZXhwIjoyMDY3OTc3NjA1fQ.oTowRoNAjlUmk10O9pSMK2BvL0XlyBb5orVRrlEryHs';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function analyzeSizeNormalizationIssues() {
  console.log('🔍 ANALYZING SIZE NORMALIZATION ISSUES...\n');

  try {
    // Test 1: Analyze current size normalization logic
    console.log('1. Testing Size Normalization Logic...');
    
    const testSizes = [
      '1L', '1 L', '1 Litre', '1 litre', '1000ml', '1000 ml',
      '2L', '2 Litre', '2000ml', '2000 ml',
      '500g', '500 g', '0.5kg', '0.5 kg',
      '700g', '700 gram', '0.7kg',
      '1kg', '1 kg', '1000g', '1000 gram',
      '12 pack', '12pack', '12ea', '12 each',
      '6 x 330ml', '6x330ml', '6 pack'
    ];

    console.log('   Size normalization results:');
    testSizes.forEach(size => {
      const normalized = normalizeSize(size);
      console.log(`   "${size}" → ${normalized.value}${normalized.unit} (valid: ${normalized.valid})`);
    });

    // Test 2: Check problematic size comparisons
    console.log('\n2. Testing Problematic Size Comparisons...');
    
    const problemPairs = [
      ['1L', '2L'],           // Different sizes - should NOT match
      ['1L', '1000ml'],       // Same size - should match
      ['2L', '2000ml'],       // Same size - should match
      ['500g', '0.5kg'],      // Same size - should match
      ['1kg', '1000g'],       // Same size - should match
      ['1L', '500ml'],        // Different sizes - should NOT match
      ['12 pack', '6 pack'],  // Different counts - should NOT match
    ];

    console.log('   Size comparison results:');
    problemPairs.forEach(([size1, size2]) => {
      const match = sizesMatch(size1, size2, true);
      const norm1 = normalizeSize(size1);
      const norm2 = normalizeSize(size2);
      const shouldMatch = norm1.valid && norm2.valid && norm1.unit === norm2.unit && norm1.value === norm2.value;
      
      console.log(`   "${size1}" vs "${size2}": ${match ? '✅ MATCH' : '❌ NO MATCH'} (should: ${shouldMatch ? 'MATCH' : 'NO MATCH'})`);
      if (match !== shouldMatch) {
        console.log(`      ⚠️  INCORRECT RESULT! ${norm1.value}${norm1.unit} vs ${norm2.value}${norm2.unit}`);
      }
    });

    // Test 3: Fetch real products to analyze size issues
    console.log('\n3. Analyzing Real Product Data...');
    
    const { data: products, error } = await supabase
      .from('products')
      .select('*')
      .not('price', 'is', null)
      .limit(500);

    if (error) {
      console.error('❌ Error fetching products:', error);
      return;
    }

    if (!products || products.length === 0) {
      console.error('❌ No products found');
      return;
    }

    // Group products by similar names to find size variations
    const productGroups = new Map<string, IProduct[]>();
    
    products.forEach(product => {
      // Create a simplified name key (remove size info)
      const nameKey = product.name
        .toLowerCase()
        .replace(/\b\d+\.?\d*\s*(l|litre|liter|ml|millilitre|milliliter|g|gram|kg|kilogram|pack|ea|each|x)\b/gi, '')
        .replace(/[^\w\s]/g, ' ')
        .replace(/\s+/g, ' ')
        .trim();
      
      if (!productGroups.has(nameKey)) {
        productGroups.set(nameKey, []);
      }
      productGroups.get(nameKey)!.push(product);
    });

    // Find groups with multiple sizes
    const multiSizeGroups = Array.from(productGroups.entries())
      .filter(([_, products]) => products.length > 1)
      .map(([name, products]) => {
        const sizes = products.map(p => p.size || p.unit || 'unknown').filter(s => s !== 'unknown');
        const uniqueSizes = [...new Set(sizes)];
        return { name, products, uniqueSizes };
      })
      .filter(group => group.uniqueSizes.length > 1)
      .slice(0, 10); // Top 10 examples

    console.log(`   Found ${multiSizeGroups.length} product groups with multiple sizes:`);
    
    multiSizeGroups.forEach((group, index) => {
      console.log(`\n   ${index + 1}. "${group.name}" (${group.products.length} products)`);
      console.log(`      Sizes: [${group.uniqueSizes.join(', ')}]`);
      
      // Show price range for each size
      group.uniqueSizes.forEach(size => {
        const productsWithSize = group.products.filter(p => (p.size || p.unit) === size);
        const prices = productsWithSize.map(p => p.price);
        const minPrice = Math.min(...prices);
        const maxPrice = Math.max(...prices);
        
        console.log(`        ${size}: $${minPrice.toFixed(2)}${minPrice !== maxPrice ? ` - $${maxPrice.toFixed(2)}` : ''} (${productsWithSize.length} stores)`);
      });
    });

    // Test 4: Check current deduplication behavior with size variations
    console.log('\n4. Testing Current Deduplication with Size Variations...');
    
    if (multiSizeGroups.length > 0) {
      const testGroup = multiSizeGroups[0];
      console.log(`   Testing with: "${testGroup.name}"`);
      
      const deduplicationService = new ProductDeduplicationService();
      const result = deduplicationService.deduplicateProducts(testGroup.products);
      
      console.log(`   Original products: ${testGroup.products.length}`);
      console.log(`   After deduplication: ${result.stats.unifiedCount}`);
      console.log(`   Groups created: ${result.stats.groupCount}`);
      
      // Analyze each group
      result.groups.forEach((group, index) => {
        const sizes = group.products.map(p => p.product.size || p.product.unit || 'unknown');
        const uniqueSizes = [...new Set(sizes)];
        const prices = group.products.map(p => p.product.price);
        
        console.log(`     Group ${index + 1}: ${uniqueSizes.join(', ')} | Prices: $${Math.min(...prices).toFixed(2)} - $${Math.max(...prices).toFixed(2)}`);
        
        if (uniqueSizes.length > 1) {
          console.log(`       ⚠️  PROBLEM: Different sizes grouped together!`);
        }
      });
    }

    // Test 5: Check SQL normalization patterns
    console.log('\n5. Testing SQL Normalization Patterns...');
    
    const sqlTestNames = [
      'Anchor Blue Milk 1L Premium Fresh',
      'Anchor Blue Milk 2 Litre Premium Fresh', 
      'Anchor Blue Milk 1000ml Premium Fresh',
      'Pams White Bread 700g Sliced Fresh',
      'Pams White Bread 700 gram Sliced Fresh'
    ];

    console.log('   SQL normalization simulation:');
    sqlTestNames.forEach(name => {
      // Simulate the SQL regex patterns
      let normalized = name.toLowerCase();
      
      // Remove fluff words
      normalized = normalized.replace(/\b(new|sale|special|offer|limited|fresh|premium|select|choice|value|pack)\b/gi, ' ');
      
      // Normalize units
      normalized = normalized.replace(/\b(\d+)\s*(l|litre|liter)s?\b/gi, '$1l');
      normalized = normalized.replace(/\b(\d+)\s*(ml|millilitre|milliliter)s?\b/gi, '$1ml');
      
      // Remove non-alphanumeric
      normalized = normalized.replace(/[^a-zA-Z0-9\s]/g, '');
      
      // Normalize whitespace
      normalized = normalized.replace(/\s+/g, ' ').trim();
      
      console.log(`   "${name}"`);
      console.log(`   → "${normalized}"`);
    });

    console.log('\n✅ Size normalization analysis completed!');
    
    return {
      testProductsCount: products.length,
      multiSizeGroupsFound: multiSizeGroups.length,
      sampleGroup: multiSizeGroups[0] || null
    };

  } catch (error) {
    console.error('❌ Size normalization analysis failed:', error);
    throw error;
  }
}

// Run the analysis
if (require.main === module) {
  analyzeSizeNormalizationIssues()
    .then((results) => {
      console.log('\n📊 SIZE NORMALIZATION ANALYSIS SUMMARY:');
      console.log('='.repeat(60));
      console.log(`Products Analyzed: ${results?.testProductsCount || 0}`);
      console.log(`Multi-Size Groups Found: ${results?.multiSizeGroupsFound || 0}`);
      
      if (results?.sampleGroup) {
        console.log(`Sample Group: "${results.sampleGroup.name}"`);
        console.log(`  Sizes: [${results.sampleGroup.uniqueSizes.join(', ')}]`);
        console.log(`  Products: ${results.sampleGroup.products.length}`);
      }
      
      console.log('\n🎯 IDENTIFIED ISSUES:');
      console.log('   1. Size normalization may group different sizes incorrectly');
      console.log('   2. Price comparisons between different sizes are misleading');
      console.log('   3. SQL normalization removes size info completely');
      console.log('   4. Unit conversion logic needs validation');
      
      console.log('\n📋 RECOMMENDED FIXES:');
      console.log('   1. Enhance size matching to be more strict');
      console.log('   2. Separate products by size in consolidated cards');
      console.log('   3. Fix SQL normalization to preserve size information');
      console.log('   4. Add size-aware price comparison logic');
      
      console.log('='.repeat(60));
    })
    .catch((error) => {
      console.error('❌ Analysis failed:', error);
    });
}

export { analyzeSizeNormalizationIssues };
