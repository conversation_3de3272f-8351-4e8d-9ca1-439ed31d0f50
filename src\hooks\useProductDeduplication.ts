
import { useState, useEffect, useMemo } from 'react';
import { IProduct } from '../types/shoppingList';
import { productDeduplicationService, IUnifiedProduct } from '../services/productDeduplicationService';
import { useOptimizedProducts } from './useOptimizedProducts'; // Assuming this hook provides the raw product data

interface IUseProductDeduplication {
  unifiedProducts: IUnifiedProduct[];
  loading: boolean;
  error: Error | null;
}

export const useProductDeduplication = (): IUseProductDeduplication => {
  const { products, loadingState } = useOptimizedProducts({
    pageSize: 100, // Increased from 20 to 100 to load more products
    searchQuery: '',
    selectedCategory: 'All',
    selectedStores: ['woolworths', 'newworld', 'paknsave'],
    sortBy: 'relevance',
    enableCache: true,
  }); // Fetch raw products
  const [unifiedProducts, setUnifiedProducts] = useState<IUnifiedProduct[]>([]);

  useEffect(() => {
    console.log(`🔄 Product deduplication: Processing ${products.length} raw products`);
    
    if (products.length > 0) {
      try {
        const deduplicated = productDeduplicationService.groupProducts(products);
        console.log(`✅ Product deduplication: Created ${deduplicated.length} unified products`);
        setUnifiedProducts(deduplicated);
      } catch (error) {
        console.error('❌ Product deduplication failed:', error);
        // Fallback: use original products as unified products with proper IProduct fields
        const fallbackProducts = products.map(product => ({
          // IProduct required fields
          id: product.id,
          name: product.name,
          price: product.price || product.current_price || 0,
          store: product.store,
          
          // IProduct optional fields
          originalPrice: product.original_price,
          brand: product.brand,
          size: product.size,
          category: product.category,
          imageUrl: product.image_url,
          isOnSale: product.is_on_sale,
          unit: product.unit,
          last_updated: product.last_updated,
          is_available: product.is_available,
          
          // IUnifiedProduct additional fields
          lowestPrice: product.price || product.current_price || 0,
          highestPrice: product.price || product.current_price || 0,
          storePrices: { [product.store]: product.price || product.current_price || 0 },
          allStores: [product.store],
        }));
        setUnifiedProducts(fallbackProducts as IUnifiedProduct[]);
      }
    } else if (!loadingState.initial) {
      console.log('⚠️ No products found to deduplicate');
      setUnifiedProducts([]);
    }
  }, [products, loadingState.initial]);

  return {
    unifiedProducts,
    loading: loadingState.initial || loadingState.loadingMore,
    error: loadingState.error ? new Error(loadingState.error) : null
  };
};
