
import { useState, useEffect, useMemo } from 'react';
import { IProduct, IUnifiedProduct } from '../types/shoppingList';
import { productDeduplicationService } from '../services/productDeduplicationService';
import { useOptimizedProducts } from './useOptimizedProducts';

interface IUseProductDeduplication {
  unifiedProducts: IUnifiedProduct[];
  loading: boolean;
  error: Error | null;
  totalProducts: number;
  storeCount: number;
  averageSavings: number;
  refresh: () => Promise<void>;
}

export const useProductDeduplication = (options: {
  pageSize?: number;
  searchQuery?: string;
  selectedCategory?: string;
  selectedStores?: string[];
  sortBy?: string;
  enableCache?: boolean;
} = {}): IUseProductDeduplication => {
  const {
    pageSize = 200,
    searchQuery = '',
    selectedCategory = 'All',
    selectedStores = ['woolworths', 'newworld', 'paknsave'],
    sortBy = 'relevance',
    enableCache = true,
  } = options;
  
  const { products, loadingState, refresh: refreshProducts } = useOptimizedProducts({
    pageSize,
    searchQuery,
    selectedCategory,
    selectedStores,
    sortBy,
    enableCache,
  });
  
  const [unifiedProducts, setUnifiedProducts] = useState<IUnifiedProduct[]>([]);
  const [processingError, setProcessingError] = useState<Error | null>(null);

  // Process products into unified format with enhanced error handling
  const processProducts = useMemo(() => {
    return (rawProducts: IProduct[]) => {
      if (!rawProducts || rawProducts.length === 0) {
        console.log('⚠️ No products provided for deduplication');
        return [];
      }
      
      console.log(`🔄 Product deduplication: Processing ${rawProducts.length} raw products`);
      
      try {
        // Enhanced filtering with better validation
        const validProducts = rawProducts.filter(product => {
          if (!product || typeof product !== 'object') return false;
          if (!product.id || !product.name) return false;
          if (!product.price || product.price <= 0 || product.price >= 1000) return false;
          if (!product.store || !product.category) return false;
          return true;
        });
        
        console.log(`📊 Valid products for deduplication: ${validProducts.length}/${rawProducts.length}`);
        
        if (validProducts.length === 0) {
          console.warn('⚠️ No valid products after filtering');
          return [];
        }
        
        // Use enhanced deduplication with improved configuration
        const deduplicationResult = productDeduplicationService.deduplicateProducts(validProducts, {
          requireSizeMatch: false,
          enableFuzzySizeMatching: true,
          nameSimilarityThreshold: 0.8, // Slightly relaxed for better matching
          minimumConfidence: 0.65
        });

        const deduplicated = deduplicationResult.unifiedProducts;
        const avgSavings = deduplicated.length > 0 
          ? deduplicated.reduce((sum, p) => sum + (p.maxSavings || 0), 0) / deduplicated.length 
          : 0;
        
        console.log(`✅ Product deduplication: Created ${deduplicated.length} unified products`);
        console.log(`📈 Average savings per product: $${avgSavings.toFixed(2)}`);
        console.log(`📊 Deduplication efficiency: ${((1 - deduplicated.length / validProducts.length) * 100).toFixed(1)}% reduction`);
        
        setProcessingError(null);
        return deduplicated;
      } catch (error) {
        console.error('❌ Product deduplication failed:', {
          error: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined,
          rawProductsCount: rawProducts.length
        });
        setProcessingError(error instanceof Error ? error : new Error('Deduplication failed'));
        
        // Return empty array on error
        return [];
      }
    };
  }, []);

  useEffect(() => {
    if (products.length > 0) {
      const processed = processProducts(products);
      setUnifiedProducts(processed);
    } else if (!loadingState.initial) {
      console.log('⚠️ No products found to deduplicate');
      setUnifiedProducts([]);
    }
  }, [products, processProducts, loadingState.initial]);

  // Calculate statistics
  const stats = useMemo(() => {
    const totalProducts = products.length;
    const storeCount = new Set(products.map(p => p.store)).size;
    const averageSavings = unifiedProducts.length > 0 
      ? unifiedProducts.reduce((sum, p) => sum + p.maxSavings, 0) / unifiedProducts.length
      : 0;
      
    return { totalProducts, storeCount, averageSavings };
  }, [products, unifiedProducts]);

  const refresh = async () => {
    if (refreshProducts) {
      refreshProducts();
    }
  };

  return {
    unifiedProducts,
    loading: loadingState.initial || loadingState.loadingMore,
    error: processingError || (loadingState.error ? new Error(loadingState.error) : null),
    totalProducts: stats.totalProducts,
    storeCount: stats.storeCount,
    averageSavings: stats.averageSavings,
    refresh
  };
};
