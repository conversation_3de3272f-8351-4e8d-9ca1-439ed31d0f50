
import { useState, useEffect, useMemo } from 'react';
import { IProduct } from '../types/shoppingList';
import { productDeduplicationService, IUnifiedProduct } from '../services/productDeduplicationService';
import { useOptimizedProducts } from './useOptimizedProducts'; // Assuming this hook provides the raw product data

interface IUseProductDeduplication {
  unifiedProducts: IUnifiedProduct[];
  loading: boolean;
  error: Error | null;
}

export const useProductDeduplication = (): IUseProductDeduplication => {
  const { products, loadingState } = useOptimizedProducts({
    pageSize: 20,
    searchQuery: '',
    selectedCategory: 'All',
    selectedStores: ['woolworths', 'newworld', 'paknsave'],
    sortBy: 'relevance',
    enableCache: true,
  }); // Fetch raw products
  const [unifiedProducts, setUnifiedProducts] = useState<IUnifiedProduct[]>([]);

  useEffect(() => {
    if (products.length > 0) {
      const deduplicated = productDeduplicationService.groupProducts(products);
      setUnifiedProducts(deduplicated);
    }
  }, [products]);

  return {
    unifiedProducts,
    loading: loadingState.initial || loadingState.loadingMore,
    error: loadingState.error ? new Error(loadingState.error) : null
  };
};
