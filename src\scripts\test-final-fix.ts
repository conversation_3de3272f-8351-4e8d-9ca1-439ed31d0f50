/**
 * FINAL TEST - Verify Complete Product Display Fix
 * 
 * This script tests the pagination-based solution to show all products
 * from all three New Zealand supermarket chains.
 */

import { productFetchService } from '../services/productFetchService';

async function testFinalFix() {
  console.log('🧪 Testing Final Product Display Fix...\n');

  try {
    // Test 1: Fetch a large number of products (more than 1000)
    console.log('1. Testing productFetchService with 5000 products...');
    
    const startTime = Date.now();
    const result = await productFetchService.fetchProducts({
      maxResults: 5000,
      sortBy: 'name',
      includeOutOfStock: true
    });
    const endTime = Date.now();

    console.log(`   ✅ Fetched ${result.products.length} products in ${endTime - startTime}ms`);
    console.log(`   📊 Total available: ${result.total}`);
    console.log(`   🔄 Has more: ${result.hasMore}`);

    // Breakdown by store
    const storeBreakdown = result.products.reduce((acc: any, product) => {
      acc[product.store] = (acc[product.store] || 0) + 1;
      return acc;
    }, {});

    console.log('   📊 Store breakdown:');
    Object.entries(storeBreakdown).forEach(([store, count]) => {
      console.log(`      ${store}: ${count} products`);
    });

    // Test 2: Sample products from each store
    console.log('\n2. Sample products from each store:');
    const stores = ['woolworths', 'newworld', 'paknsave'];
    
    for (const store of stores) {
      const storeProducts = result.products.filter(p => p.store === store).slice(0, 3);
      console.log(`   ${store.toUpperCase()}:`);
      storeProducts.forEach((product, index) => {
        console.log(`      ${index + 1}. ${product.name} - $${product.price}`);
      });
    }

    // Test 3: Check for price data
    console.log('\n3. Price data analysis:');
    const withPrices = result.products.filter(p => p.price && p.price > 0);
    const withoutPrices = result.products.filter(p => !p.price || p.price <= 0);
    
    console.log(`   Products with valid prices: ${withPrices.length}`);
    console.log(`   Products without prices: ${withoutPrices.length}`);
    console.log(`   Price coverage: ${((withPrices.length / result.products.length) * 100).toFixed(1)}%`);

    // Test 4: Test smaller fetch to ensure normal operation still works
    console.log('\n4. Testing normal operation (100 products)...');
    
    const smallResult = await productFetchService.fetchProducts({
      maxResults: 100,
      sortBy: 'price'
    });

    console.log(`   ✅ Small fetch returned ${smallResult.products.length} products`);

    console.log('\n✅ Final fix test completed!');
    
    return {
      largeDatasetProducts: result.products.length,
      totalAvailable: result.total,
      storeBreakdown,
      priceCoverage: (withPrices.length / result.products.length) * 100,
      smallDatasetProducts: smallResult.products.length,
      performanceMs: endTime - startTime
    };

  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  }
}

// Run the test
if (require.main === module) {
  testFinalFix()
    .then((result) => {
      console.log('\n📊 FINAL TEST RESULTS:');
      console.log('='.repeat(60));
      console.log(`Large Dataset (5000 requested): ${result?.largeDatasetProducts || 0} products`);
      console.log(`Small Dataset (100 requested): ${result?.smallDatasetProducts || 0} products`);
      console.log(`Performance: ${result?.performanceMs || 0}ms`);
      console.log(`Price Coverage: ${result?.priceCoverage?.toFixed(1) || 0}%`);
      
      console.log('\nStore Distribution:');
      if (result?.storeBreakdown) {
        Object.entries(result.storeBreakdown).forEach(([store, count]) => {
          console.log(`  ${store}: ${count} products`);
        });
      }
      
      if ((result?.largeDatasetProducts || 0) > 3000) {
        console.log('\n🎉 SUCCESS: Product display issue is COMPLETELY FIXED!');
        console.log('   ✅ App now shows thousands of products from all stores');
        console.log('   ✅ Pagination handles Supabase 1000 row limit');
        console.log('   ✅ Performance is acceptable');
        console.log('   ✅ Ready for product deduplication implementation');
      } else {
        console.log('\n⚠️  PARTIAL SUCCESS: Still not showing full product range');
        console.log('   Additional investigation may be needed');
      }
      console.log('='.repeat(60));
    })
    .catch((error) => {
      console.error('❌ Final test failed:', error);
    });
}

export { testFinalFix };
