export interface SupermarketPrice {
  supermarket: string;
  supermarketLogo: string;
  price: number;
  unit: string;
  inStock: boolean;
  lastUpdated: string;
}

export interface ProductPricing {
  productName: string;
  category: string;
  prices: SupermarketPrice[];
  bestPrice?: SupermarketPrice;
  averagePrice?: number;
}

// Mock supermarket database - in a real app, this would come from APIs
const SUPERMARKET_DATA = {
  woolworths: {
    name: 'Woolworths',
    logo: '🟢',
    color: '#1BAA4B'
  },
  coles: {
    name: 'Cole<PERSON>',
    logo: '🔴',
    color: '#E60012'
  },
  aldi: {
    name: 'ALDI',
    logo: '🟠',
    color: '#FF6600'
  },
  newworld: {
    name: 'New World',
    logo: '🟡',
    color: '#FFD700'
  }
};

// Mock product pricing database
const MOCK_PRODUCT_PRICES: Record<string, ProductPricing> = {
  'milk': {
    productName: 'Milk',
    category: 'Dairy & Eggs',
    prices: [
      {
        supermarket: 'woolworths',
        supermarketLogo: '🟢',
        price: 3.20,
        unit: '2L',
        inStock: true,
        lastUpdated: new Date().toISOString()
      },
      {
        supermarket: 'coles',
        supermarketLogo: '🔴',
        price: 3.15,
        unit: '2L',
        inStock: true,
        lastUpdated: new Date().toISOString()
      },
      {
        supermarket: 'aldi',
        supermarketLogo: '🟠',
        price: 2.89,
        unit: '2L',
        inStock: true,
        lastUpdated: new Date().toISOString()
      }
    ]
  },
  'bread': {
    productName: 'Bread',
    category: 'Bakery',
    prices: [
      {
        supermarket: 'woolworths',
        supermarketLogo: '🟢',
        price: 2.50,
        unit: 'loaf',
        inStock: true,
        lastUpdated: new Date().toISOString()
      },
      {
        supermarket: 'coles',
        supermarketLogo: '🔴',
        price: 2.80,
        unit: 'loaf',
        inStock: false,
        lastUpdated: new Date().toISOString()
      },
      {
        supermarket: 'aldi',
        supermarketLogo: '🟠',
        price: 1.99,
        unit: 'loaf',
        inStock: true,
        lastUpdated: new Date().toISOString()
      }
    ]
  },
  'bananas': {
    productName: 'Bananas',
    category: 'Produce',
    prices: [
      {
        supermarket: 'woolworths',
        supermarketLogo: '🟢',
        price: 3.90,
        unit: 'kg',
        inStock: true,
        lastUpdated: new Date().toISOString()
      },
      {
        supermarket: 'coles',
        supermarketLogo: '🔴',
        price: 4.20,
        unit: 'kg',
        inStock: true,
        lastUpdated: new Date().toISOString()
      },
      {
        supermarket: 'newworld',
        supermarketLogo: '🟡',
        price: 3.70,
        unit: 'kg',
        inStock: true,
        lastUpdated: new Date().toISOString()
      }
    ]
  },
  'chicken breast': {
    productName: 'Chicken Breast',
    category: 'Meat & Seafood',
    prices: [
      {
        supermarket: 'woolworths',
        supermarketLogo: '🟢',
        price: 12.99,
        unit: 'kg',
        inStock: true,
        lastUpdated: new Date().toISOString()
      },
      {
        supermarket: 'coles',
        supermarketLogo: '🔴',
        price: 13.50,
        unit: 'kg',
        inStock: true,
        lastUpdated: new Date().toISOString()
      },
      {
        supermarket: 'aldi',
        supermarketLogo: '🟠',
        price: 11.99,
        unit: 'kg',
        inStock: true,
        lastUpdated: new Date().toISOString()
      }
    ]
  },
  'rice': {
    productName: 'Rice',
    category: 'Pantry',
    prices: [
      {
        supermarket: 'woolworths',
        supermarketLogo: '🟢',
        price: 4.50,
        unit: '1kg',
        inStock: true,
        lastUpdated: new Date().toISOString()
      },
      {
        supermarket: 'coles',
        supermarketLogo: '🔴',
        price: 4.80,
        unit: '1kg',
        inStock: true,
        lastUpdated: new Date().toISOString()
      },
      {
        supermarket: 'aldi',
        supermarketLogo: '🟠',
        price: 3.99,
        unit: '1kg',
        inStock: true,
        lastUpdated: new Date().toISOString()
      }
    ]
  }
};

export class PriceService {
  static async getProductPricing(productName: string): Promise<ProductPricing | null> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Fuzzy match product names
    const normalizedName = productName.toLowerCase().trim();
    const exactMatch = MOCK_PRODUCT_PRICES[normalizedName];
    
    if (exactMatch) {
      const pricing = { ...exactMatch };
      
      // Calculate best price and average
      const availablePrices = pricing.prices.filter(p => p.inStock);
      if (availablePrices.length > 0) {
        pricing.bestPrice = availablePrices.reduce((min, current) => 
          current.price < min.price ? current : min
        );
        pricing.averagePrice = availablePrices.reduce((sum, p) => sum + p.price, 0) / availablePrices.length;
      }
      
      return pricing;
    }
    
    // Try partial matching
    const partialMatch = Object.entries(MOCK_PRODUCT_PRICES).find(([key]) => 
      normalizedName.includes(key) || key.includes(normalizedName)
    );
    
    if (partialMatch) {
      const pricing = { ...partialMatch[1] };
      const availablePrices = pricing.prices.filter(p => p.inStock);
      if (availablePrices.length > 0) {
        pricing.bestPrice = availablePrices.reduce((min, current) => 
          current.price < min.price ? current : min
        );
        pricing.averagePrice = availablePrices.reduce((sum, p) => sum + p.price, 0) / availablePrices.length;
      }
      return pricing;
    }
    
    return null;
  }
  
  static async getAllSupermarkets() {
    return SUPERMARKET_DATA;
  }
  
  static formatPrice(price: number): string {
    return `$${price.toFixed(2)}`;
  }
  
  static getSavingsAmount(currentPrice: number, bestPrice: number): number {
    return currentPrice - bestPrice;
  }
  
  static getSavingsPercentage(currentPrice: number, bestPrice: number): number {
    return ((currentPrice - bestPrice) / currentPrice) * 100;
  }
}