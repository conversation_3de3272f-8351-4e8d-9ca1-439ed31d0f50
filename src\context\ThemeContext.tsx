/**
 * Theme Context - Dynamic Theme Management
 * 
 * Provides centralized theme management with:
 * - Light/Dark mode support
 * - System theme detection
 * - Manual theme switching
 * - Context-based theme access throughout the app
 */

import React, { createContext, useState, useMemo, useContext, useEffect } from 'react';
import { useColorScheme } from 'react-native';
import { getThemeColors, ThemeMode } from '../styles';

type ThemeColors = ReturnType<typeof getThemeColors>;

interface ThemeContextType {
  mode: ThemeMode;
  colors: ThemeColors;
  toggleTheme: () => void;
  setTheme: (theme: ThemeMode) => void;
  isDark: boolean;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const systemTheme = useColorScheme() || 'light';
  const [themeMode, setThemeMode] = useState<ThemeMode>(systemTheme);

  const toggleTheme = () => {
    setThemeMode(prevMode => (prevMode === 'light' ? 'dark' : 'light'));
  };

  const setTheme = (theme: ThemeMode) => {
    setThemeMode(theme);
  };

  // Update theme if system theme changes - prevent infinite loops
  useEffect(() => {
    if (systemTheme && systemTheme !== themeMode) {
      setThemeMode(systemTheme);
    }
  }, [systemTheme, themeMode]);

  const colors = useMemo(() => getThemeColors(themeMode), [themeMode]);
  const isDark = themeMode === 'dark';

  const value = useMemo(() => ({
    mode: themeMode,
    colors,
    toggleTheme,
    setTheme,
    isDark,
  }), [themeMode, colors]);

  return <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>;
};

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// Convenience hook for just getting colors
export const useThemeColors = () => {
  const { colors } = useTheme();
  return colors;
};

// HOC for wrapping components with theme (simplified version)
export const withTheme = <P extends object>(
  Component: React.ComponentType<P & { theme: ThemeContextType }>
) => {
  return (props: P) => {
    const theme = useTheme();
    return <Component {...props} theme={theme} />;
  };
};

export default ThemeProvider; 