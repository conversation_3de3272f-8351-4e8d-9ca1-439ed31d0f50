import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { structuredProductService, StructuredProduct, ShoppingListProduct } from '../services/structuredProductService';
import { productFetchService } from '../services/productFetchService';

export const ProductTestComponent: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any>(null);
  const [testType, setTestType] = useState<string>('');

  const runTest = async (type: string) => {
    setLoading(true);
    setTestType(type);
    setResults(null);

    try {
      let result;
      
      switch (type) {
        case 'search':
          result = await productFetchService.searchProducts('milk', { maxResults: 10 });
          break;
        case 'category':
          result = await structuredProductService.getCategoryProducts('Dairy & Eggs', 10);
          break;
        case 'shopping-list':
          result = await structuredProductService.getShoppingListProducts(['milk', 'bread', 'eggs']);
          break;
        case 'compare':
          result = await structuredProductService.compareProductAcrossStores('milk');
          break;
        case 'trending':
          result = await structuredProductService.getTrendingProducts(10);
          break;
        default:
          throw new Error('Unknown test type');
      }

      setResults(result);
      console.log(`✅ ${type} test completed:`, result);
      
    } catch (error) {
      console.error(`❌ ${type} test failed:`, error);
      Alert.alert('Test Failed', `Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const renderResults = () => {
    if (!results) return null;

    switch (testType) {
      case 'search':
        return (
          <View style={styles.resultsContainer}>
            <Text style={styles.resultsTitle}>Search Results ({results.totalCount})</Text>
            {results.products.slice(0, 5).map((product: any, index: number) => (
              <View key={index} style={styles.productCard}>
                <Text style={styles.productName}>{product.name}</Text>
                <Text style={styles.productDetails}>
                  {product.storeName} • ${product.price.toFixed(2)} • {product.unit}
                </Text>
                <Text style={styles.productBrand}>{product.brand} • {product.category}</Text>
              </View>
            ))}
          </View>
        );

      case 'category':
        return (
          <View style={styles.resultsContainer}>
            <Text style={styles.resultsTitle}>Category: {results.category}</Text>
            <Text style={styles.statsText}>
              Total: {results.totalCount} products
            </Text>
            <Text style={styles.statsText}>
              Price Range: ${results.priceRange.min.toFixed(2)} - ${results.priceRange.max.toFixed(2)}
            </Text>
            <Text style={styles.statsText}>
              Top Brands: {results.topBrands.slice(0, 3).map((b: any) => b.brand).join(', ')}
            </Text>
            {results.products.slice(0, 3).map((product: StructuredProduct, index: number) => (
              <View key={index} style={styles.productCard}>
                <Text style={styles.productName}>{product.storeEmoji} {product.name}</Text>
                <Text style={styles.productDetails}>
                  {product.store} • ${product.price.toFixed(2)} • {product.unit}
                </Text>
              </View>
            ))}
          </View>
        );

      case 'shopping-list':
        return (
          <View style={styles.resultsContainer}>
            <Text style={styles.resultsTitle}>Shopping List Results</Text>
            {results.map((item: ShoppingListProduct, index: number) => (
              <View key={index} style={styles.shoppingItem}>
                <Text style={styles.shoppingItemName}>{item.productName}</Text>
                <Text style={styles.statsText}>
                  Found: {item.totalFound} products • 
                  Range: ${item.priceRange.min.toFixed(2)} - ${item.priceRange.max.toFixed(2)}
                </Text>
                <Text style={styles.bestValue}>
                  Best Value: {item.bestValue.storeEmoji} {item.bestValue.store} - ${item.bestValue.price.toFixed(2)}
                </Text>
                {item.userPreference && (
                  <Text style={styles.preference}>
                    Preference: {item.userPreference.preferredBrand} at {item.userPreference.preferredStore}
                  </Text>
                )}
              </View>
            ))}
          </View>
        );

      case 'compare':
        return (
          <View style={styles.resultsContainer}>
            <Text style={styles.resultsTitle}>Store Comparison: {results.productName}</Text>
            {results.stores.map((store: any, index: number) => (
              <View key={index} style={styles.storeCard}>
                <Text style={styles.storeName}>
                  {store.storeEmoji} {store.store}
                  {store.priceRank && ` (Rank: ${store.priceRank})`}
                </Text>
                {store.product ? (
                  <Text style={styles.productDetails}>
                    {store.product.name} - ${store.product.price.toFixed(2)}
                  </Text>
                ) : (
                  <Text style={styles.unavailable}>Not available</Text>
                )}
              </View>
            ))}
            <Text style={styles.recommendation}>
              💡 {results.recommendation.reasoning}
            </Text>
          </View>
        );

      case 'trending':
        return (
          <View style={styles.resultsContainer}>
            <Text style={styles.resultsTitle}>Trending Products</Text>
            {results.slice(0, 5).map((product: StructuredProduct, index: number) => (
              <View key={index} style={styles.productCard}>
                <Text style={styles.productName}>{product.storeEmoji} {product.name}</Text>
                <Text style={styles.productDetails}>
                  {product.store} • ${product.price.toFixed(2)} • {product.category}
                </Text>
              </View>
            ))}
          </View>
        );

      default:
        return <Text>Unknown result type</Text>;
    }
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Product Service Test</Text>
      
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={styles.button}
          onPress={() => runTest('search')}
          disabled={loading}
        >
          <Text style={styles.buttonText}>Test Search</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.button}
          onPress={() => runTest('category')}
          disabled={loading}
        >
          <Text style={styles.buttonText}>Test Category</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.button}
          onPress={() => runTest('shopping-list')}
          disabled={loading}
        >
          <Text style={styles.buttonText}>Test Shopping List</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.button}
          onPress={() => runTest('compare')}
          disabled={loading}
        >
          <Text style={styles.buttonText}>Test Compare</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.button}
          onPress={() => runTest('trending')}
          disabled={loading}
        >
          <Text style={styles.buttonText}>Test Trending</Text>
        </TouchableOpacity>
      </View>

      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Running {testType} test...</Text>
        </View>
      )}

      {renderResults()}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  buttonContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  button: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    marginBottom: 8,
    minWidth: '48%',
  },
  buttonText: {
    color: 'white',
    textAlign: 'center',
    fontSize: 14,
    fontWeight: '600',
  },
  loadingContainer: {
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
  },
  resultsContainer: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
  },
  resultsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  statsText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  productCard: {
    backgroundColor: '#f9f9f9',
    padding: 12,
    borderRadius: 6,
    marginBottom: 8,
  },
  productName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  productDetails: {
    fontSize: 14,
    color: '#666',
  },
  productBrand: {
    fontSize: 12,
    color: '#888',
  },
  shoppingItem: {
    backgroundColor: '#f0f8ff',
    padding: 12,
    borderRadius: 6,
    marginBottom: 12,
  },
  shoppingItemName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  bestValue: {
    fontSize: 14,
    color: '#007AFF',
    fontWeight: '600',
  },
  preference: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic',
  },
  storeCard: {
    backgroundColor: '#f9f9f9',
    padding: 10,
    borderRadius: 6,
    marginBottom: 6,
  },
  storeName: {
    fontSize: 14,
    fontWeight: '600',
  },
  unavailable: {
    fontSize: 12,
    color: '#999',
    fontStyle: 'italic',
  },
  recommendation: {
    fontSize: 14,
    color: '#007AFF',
    fontWeight: '600',
    marginTop: 10,
    padding: 10,
    backgroundColor: '#f0f8ff',
    borderRadius: 6,
  },
});