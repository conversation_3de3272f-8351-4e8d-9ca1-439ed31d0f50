import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { supabase } from '../supabase/client';
import { productFetchService } from '../services/productFetchService';

/**
 * Test component to diagnose product loading issues
 * This helps identify where the data flow breaks
 */
export const ProductTestComponent: React.FC = () => {
  const [results, setResults] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  const addResult = (message: string) => {
    console.log(message);
    setResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const runTests = async () => {
    setLoading(true);
    setResults([]);
    
    addResult('🔧 Starting Product Loading Diagnostics...');
    
    // Test 1: Environment Variables
    addResult('1️⃣ Testing Environment Variables:');
    const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;
    
    addResult(`   SUPABASE_URL: ${supabaseUrl ? '✅ Set' : '❌ Missing'}`);
    addResult(`   SUPABASE_ANON_KEY: ${supabaseKey ? '✅ Set' : '❌ Missing'}`);
    
    if (!supabaseUrl || !supabaseKey) {
      addResult('❌ Critical: Missing Supabase environment variables!');
      addResult('💡 Check your .env file and restart Expo');
      setLoading(false);
      return;
    }
    
    // Test 2: Direct Supabase Connection
    addResult('2️⃣ Testing Direct Supabase Connection:');
    try {
      const { count, error } = await supabase
        .from('products')
        .select('*', { count: 'exact', head: true });
      
      if (error) {
        addResult(`   ❌ Supabase Error: ${error.message}`);
      } else {
        addResult(`   ✅ Connected! Found ${count} total products`);
      }
    } catch (error) {
      addResult(`   ❌ Connection Error: ${error}`);
    }
    
    // Test 3: Simple Product Query
    addResult('3️⃣ Testing Simple Product Query:');
    try {
      const { data, error } = await supabase
        .from('products')
        .select('id, name, price, current_price, store')
        .limit(5);
      
      if (error) {
        addResult(`   ❌ Query Error: ${error.message}`);
      } else {
        addResult(`   ✅ Query Success! Retrieved ${data.length} products`);
        data.forEach((product, index) => {
          const price = product.price || product.current_price || 'N/A';
          addResult(`      ${index + 1}. ${product.name} - $${price} (${product.store})`);
        });
      }
    } catch (error) {
      addResult(`   ❌ Query Exception: ${error}`);
    }
    
    // Test 4: Product Fetch Service
    addResult('4️⃣ Testing ProductFetchService:');
    try {
      const result = await productFetchService.fetchProducts({
        maxResults: 10
      });
      
      addResult(`   ✅ Service Success! Retrieved ${result.products.length} products`);
      addResult(`   📊 Total count: ${result.totalCount}`);
      addResult(`   🏪 Stores: ${Object.keys(result.byStore).join(', ')}`);
      addResult(`   📂 Categories: ${result.categories.length} found`);
      
      if (result.products.length > 0) {
        const sample = result.products[0];
        addResult(`   📋 Sample: ${sample.name} - $${sample.price} (${sample.storeName})`);
      }
    } catch (error) {
      addResult(`   ❌ Service Error: ${error}`);
    }
    
    // Test 5: Joined Query (used by useOptimizedProducts)
    addResult('5️⃣ Testing Joined Query (useOptimizedProducts style):');
    try {
      const { data, error } = await supabase
        .from('products')
        .select(`
          *,
          prices!inner (
            price,
            store_id,
            recorded_at,
            stores!inner (
              name,
              display_name
            )
          )
        `)
        .limit(5);
      
      if (error) {
        addResult(`   ❌ Joined Query Error: ${error.message}`);
      } else {
        addResult(`   ✅ Joined Query Success! Retrieved ${data.length} products with price data`);
      }
    } catch (error) {
      addResult(`   ❌ Joined Query Exception: ${error}`);
    }
    
    addResult('');
    addResult('🏁 Diagnostics Complete!');
    addResult('💡 Check the logs above to identify the issue');
    
    setLoading(false);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>🔧 Product Loading Test</Text>
      
      <TouchableOpacity 
        style={[styles.button, loading && styles.buttonDisabled]} 
        onPress={runTests}
        disabled={loading}
      >
        <Text style={styles.buttonText}>
          {loading ? '🔄 Running Tests...' : '🚀 Run Diagnostics'}
        </Text>
      </TouchableOpacity>
      
      <ScrollView style={styles.results} showsVerticalScrollIndicator={true}>
        {results.map((result, index) => (
          <Text 
            key={index} 
            style={[
              styles.resultText,
              result.includes('❌') && styles.errorText,
              result.includes('✅') && styles.successText,
              result.includes('💡') && styles.infoText,
            ]}
          >
            {result}
          </Text>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
  },
  buttonText: {
    color: 'white',
    textAlign: 'center',
    fontSize: 16,
    fontWeight: '600',
  },
  results: {
    flex: 1,
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 15,
  },
  resultText: {
    fontSize: 12,
    fontFamily: 'monospace',
    marginBottom: 4,
    color: '#333',
  },
  errorText: {
    color: '#FF3B30',
    fontWeight: '600',
  },
  successText: {
    color: '#34C759',
    fontWeight: '600',
  },
  infoText: {
    color: '#007AFF',
    fontWeight: '600',
  },
});