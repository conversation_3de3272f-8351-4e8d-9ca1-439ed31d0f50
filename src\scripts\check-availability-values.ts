/**
 * CHECK AVAILABILITY VALUES
 * 
 * This script checks what availability values exist in the database
 * to understand why only 1000 products are showing.
 */

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://emjwniuqwhvohjassnrg.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVtanduaXVxd2h2b2hqYXNzbnJnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjQwMTYwNSwiZXhwIjoyMDY3OTc3NjA1fQ.oTowRoNAjlUmk10O9pSMK2BvL0XlyBb5orVRrlEryHs';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function checkAvailabilityValues() {
  console.log('🔍 Checking availability values in database...\n');

  // Get all unique availability values
  const { data: availabilityData, error } = await supabase
    .rpc('get_availability_breakdown');

  if (error) {
    console.log('RPC failed, using manual query...');
    
    // Manual approach - get sample of products with different availability values
    const { data: sampleData, error: sampleError } = await supabase
      .from('products')
      .select('availability, store')
      .range(0, 5000);

    if (sampleError) {
      console.error('Sample query failed:', sampleError);
      return;
    }

    // Count availability values manually
    const availabilityBreakdown = sampleData?.reduce((acc: any, product) => {
      const availability = product.availability || 'null';
      acc[availability] = (acc[availability] || 0) + 1;
      return acc;
    }, {}) || {};

    console.log('📊 Availability breakdown (from 1000 sample):');
    Object.entries(availabilityBreakdown).forEach(([availability, count]) => {
      console.log(`   ${availability}: ${count} products`);
    });

    // Check specific availability values
    const testValues = ['available', 'in_stock', 'out_of_stock', 'discontinued', null, ''];
    
    console.log('\n🧪 Testing specific availability values:');
    for (const value of testValues) {
      let query = supabase.from('products').select('id', { count: 'exact', head: true });
      
      if (value === null) {
        query = query.is('availability', null);
      } else if (value === '') {
        query = query.eq('availability', '');
      } else {
        query = query.eq('availability', value);
      }

      const { count, error: testError } = await query;
      
      if (testError) {
        console.log(`   ${value || 'null'}: ERROR - ${testError.message}`);
      } else {
        console.log(`   ${value || 'null'}: ${count} products`);
      }
    }
  }

  // Check what happens if we remove availability filter entirely
  console.log('\n🚀 Testing query without availability filter:');
  const { data: allProducts, error: allError } = await supabase
    .from('products')
    .select('id, name, store, availability, price')
    .not('price', 'is', null)  // Only keep price filter
    .order('name', { ascending: true })
    .range(0, 2000);  // Get first 2000

  if (allError) {
    console.error('   ERROR:', allError);
  } else {
    console.log(`   ✅ Returned ${allProducts?.length || 0} products with prices`);
    
    // Breakdown by store
    const storeBreakdown = allProducts?.reduce((acc: any, product) => {
      acc[product.store] = (acc[product.store] || 0) + 1;
      return acc;
    }, {}) || {};

    console.log('   📊 Store breakdown:');
    Object.entries(storeBreakdown).forEach(([store, count]) => {
      console.log(`      ${store}: ${count} products`);
    });
  }

  console.log('\n✅ Availability check complete!');
}

// Run the check
if (require.main === module) {
  checkAvailabilityValues()
    .catch((error) => {
      console.error('❌ Check failed:', error);
    });
}

export { checkAvailabilityValues };
