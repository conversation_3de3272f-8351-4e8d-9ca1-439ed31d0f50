// Configuration constants
// Environment variables validation and setup

interface EnvironmentVariable {
  key: string;
  envKey: string;
  required: boolean;
  description: string;
  setupInstructions?: string;
}

const ENV_VARIABLES: EnvironmentVariable[] = [
  {
    key: 'GEMINI_API_KEY',
    envKey: 'EXPO_PUBLIC_GEMINI_API_KEY',
    required: false, // Optional - app works offline without it
    description: 'Gemini AI API key for recipe generation',
    setupInstructions: '1. Visit https://aistudio.google.com/app/apikey\n2. Create an API key\n3. Add EXPO_PUBLIC_GEMINI_API_KEY=your_key to your .env file'
  },
  {
    key: 'SUPABASE_URL',
    envKey: 'EXPO_PUBLIC_SUPABASE_URL',
    required: true,
    description: 'Supabase project URL',
    setupInstructions: '1. Create project at https://supabase.com\n2. Get your project URL\n3. Add EXPO_PUBLIC_SUPABASE_URL=your_url to your .env file'
  },
  {
    key: 'SUPABASE_ANON_KEY',
    envKey: 'EXPO_PUBLIC_SUPABASE_ANON_KEY',
    required: true,
    description: 'Supabase anonymous key',
    setupInstructions: '1. In your Supabase project settings\n2. Copy the anon/public key\n3. Add EXPO_PUBLIC_SUPABASE_ANON_KEY=your_key to your .env file'
  },
  {
    key: 'AUTH0_DOMAIN',
    envKey: 'EXPO_PUBLIC_AUTH0_DOMAIN',
    required: true,
    description: 'Auth0 domain for authentication',
    setupInstructions: '1. Create Auth0 account at https://auth0.com\n2. Create a new application\n3. Copy your domain\n4. Add EXPO_PUBLIC_AUTH0_DOMAIN=your-domain.auth0.com to your .env file'
  },
  {
    key: 'AUTH0_CLIENT_ID',
    envKey: 'EXPO_PUBLIC_AUTH0_CLIENT_ID',
    required: true,
    description: 'Auth0 client ID for authentication',
    setupInstructions: '1. In your Auth0 application settings\n2. Copy the Client ID\n3. Add EXPO_PUBLIC_AUTH0_CLIENT_ID=your_client_id to your .env file'
  },
];

class ConfigurationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ConfigurationError';
  }
}

// Validate environment variables and provide clear error messages
const validateEnvironmentVariables = (): Record<string, string> => {
  const config: Record<string, string> = {};
  const missingRequired: EnvironmentVariable[] = [];

  for (const variable of ENV_VARIABLES) {
    const value = process.env[variable.envKey];
    
    if (!value || value.trim() === '') {
      if (variable.required) {
        missingRequired.push(variable);
      }
      config[variable.key] = '';
    } else {
      config[variable.key] = value.trim();
    }
  }

  // Handle missing required variables (throw error)
  if (missingRequired.length > 0) {
    let errorMessage = '❌ Missing required environment variables:\n\n';
    
    missingRequired.forEach(variable => {
      errorMessage += `• ${variable.envKey} - ${variable.description}\n`;
      if (variable.setupInstructions) {
        errorMessage += `  Setup: ${variable.setupInstructions}\n`;
      }
      errorMessage += '\n';
    });
    
    errorMessage += 'Please add these variables to your .env file and restart the app.';
    throw new ConfigurationError(errorMessage);
  }

  return config;
};

// Validate and load configuration
let validatedConfig: Record<string, string>;
try {
  validatedConfig = validateEnvironmentVariables();
} catch (error) {
  if (error instanceof ConfigurationError) {
    console.error(error.message);
    // Use fallback config for development
    validatedConfig = {
      GEMINI_API_KEY: '',
      SUPABASE_URL: '',
      SUPABASE_ANON_KEY: '',
      AUTH0_DOMAIN: '',
      AUTH0_CLIENT_ID: ''
    };
  } else {
    throw error;
  }
}

export const API_CONFIG = {
  // Gemini API for AI recipe generation
  GEMINI_API_KEY: validatedConfig.GEMINI_API_KEY,
  GEMINI_API_URL: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent',
  
  // Supabase for data storage
  SUPABASE_URL: validatedConfig.SUPABASE_URL,
  SUPABASE_ANON_KEY: validatedConfig.SUPABASE_ANON_KEY,
  
  // Auth0 for authentication
  AUTH0_DOMAIN: validatedConfig.AUTH0_DOMAIN,
  AUTH0_CLIENT_ID: validatedConfig.AUTH0_CLIENT_ID,
};

export const APP_CONFIG = {
  MAX_RECIPES_STORAGE: 50,
  MAX_SHOPPING_ITEMS: 100,
  RECIPE_GENERATION_TIMEOUT: 10000, // 10 seconds
};

export const CATEGORIES = {
  INGREDIENTS: [
    'Produce',
    'Meat & Seafood',
    'Dairy & Eggs',
    'Pantry',
    'Frozen',
    'Bakery',
    'Beverages',
    'Snacks',
    'Condiments',
    'Spices & Herbs',
  ],
  MEAL_TYPES: ['Breakfast', 'Lunch', 'Dinner', 'Snack', 'Dessert'],
  CUISINES: [
    'Italian',
    'Mexican',
    'Chinese',
    'Japanese',
    'Indian',
    'Thai',
    'Mediterranean',
    'American',
    'French',
    'Korean',
  ],
  DIETARY: [
    'Vegetarian',
    'Vegan',
    'Gluten-Free',
    'Dairy-Free',
    'Keto',
    'Paleo',
    'Low-Carb',
    'Sugar-Free',
  ],
};

// Helper functions for runtime configuration checks
export const isGeminiConfigured = (): boolean => {
  return Boolean(API_CONFIG.GEMINI_API_KEY && API_CONFIG.GEMINI_API_KEY.trim() !== '');
};

export const isSupabaseConfigured = (): boolean => {
  return Boolean(
    API_CONFIG.SUPABASE_URL && 
    API_CONFIG.SUPABASE_ANON_KEY && 
    API_CONFIG.SUPABASE_URL.trim() !== '' && 
    API_CONFIG.SUPABASE_ANON_KEY.trim() !== ''
  );
};

export const isAuth0Configured = (): boolean => {
  return Boolean(
    API_CONFIG.AUTH0_DOMAIN && 
    API_CONFIG.AUTH0_CLIENT_ID && 
    API_CONFIG.AUTH0_DOMAIN.trim() !== '' && 
    API_CONFIG.AUTH0_CLIENT_ID.trim() !== ''
  );
};


export const getConfigurationStatus = (): {
  gemini: boolean;
  supabase: boolean;
  auth0: boolean;
  allConfigured: boolean;
  hasAnyConfiguration: boolean;
} => {
  const gemini = isGeminiConfigured();
  const supabase = isSupabaseConfigured();
  const auth0 = isAuth0Configured();
  
  return {
    gemini,
    supabase,
    auth0,
    allConfigured: gemini && supabase && auth0,
    hasAnyConfiguration: gemini || supabase || auth0
  };
};

// Export the configuration error class for use in other parts of the app
export { ConfigurationError };