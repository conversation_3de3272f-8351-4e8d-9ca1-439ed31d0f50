/**
 * Test script for Product Deduplication
 * Run with: npx ts-node src/scripts/testDeduplication.ts
 */

import { supabase } from '../supabase/client';
import { productDeduplicationService } from '../services/productDeduplicationService';

async function testProductDeduplication() {
  console.log('🧪 Testing Product Deduplication Service');
  console.log('=====================================\n');

  try {
    // Fetch sample products from Supabase
    console.log('📥 Fetching products from Supabase...');
    
    const { data: products, error } = await supabase
      .from('products')
      .select(`
        id, name, display_name, price, current_price, original_price, store, category, 
        brand, brand_name, unit, size, package_size, pack_size, weight,
        image_url, thumbnail_url, is_available, availability, barcode, sku,
        promotion_type, promotion_description, is_on_sale, last_updated
      `)
      .not('name', 'is', null)
      .not('price', 'is', null)
      .gte('price', 0)
      .limit(100); // Test with first 100 products

    if (error) {
      throw error;
    }

    if (!products || products.length === 0) {
      console.log('❌ No products found in database');
      return;
    }

    console.log(`✅ Fetched ${products.length} products from database\n`);

    // Process products to match our interface
    const processedProducts = products.map(product => ({
      ...product,
      id: product.id || `${product.store}-${product.name}`,
      current_price: product.current_price || product.price,
      is_available: product.is_available !== false && product.availability !== 'Out of Stock',
      is_on_sale: product.is_on_sale || (product.original_price && product.price < product.original_price),
      name: product.display_name || product.name,
      promotion_text: product.promotion_description || '',
    }));

    // Apply deduplication
    console.log('🔄 Applying product deduplication...');
    const groups = productDeduplicationService.groupProducts(processedProducts);
    
    console.log(`✅ Created ${groups.length} product groups from ${processedProducts.length} individual products\n`);

    // Calculate deduplication effectiveness
    const deduplicationRate = ((processedProducts.length - groups.length) / processedProducts.length) * 100;
    console.log(`📊 Deduplication Rate: ${deduplicationRate.toFixed(1)}%\n`);

    // Show detailed results
    console.log('📋 Product Group Analysis:');
    console.log('========================\n');

    // Sort groups by number of stores (most unified first)
    const sortedGroups = groups.sort((a, b) => b.stores.length - a.stores.length);
    
    // Show top 10 most unified products
    console.log('🏆 Top 10 Most Unified Products:');
    sortedGroups.slice(0, 10).forEach((group, index) => {
      const priceInfo = (productDeduplicationService.constructor as any).getPriceInfo(group);
      
      console.log(`\n${index + 1}. ${group.name}`);
      console.log(`   🏪 Stores: ${group.stores.join(', ')} (${group.stores.length} stores)`);
      console.log(`   💰 Price Range: ${priceInfo.priceRange}`);
      console.log(`   🎯 Best Deal: ${group.bestPrice.store} at $${(group.bestPrice.current_price || group.bestPrice.price).toFixed(2)}`);
      console.log(`   📦 Size: ${group.size || 'N/A'}`);
      console.log(`   🏷️ Brand: ${group.brand || 'N/A'}`);
      console.log(`   📸 Image: ${group.bestImage ? '✅' : '❌'}`);
      console.log(`   🔥 On Sale: ${group.isOnSale ? '✅' : '❌'}`);
      console.log(`   📊 Variants: ${group.totalVariants} (${group.availableCount} available)`);
      
      if (priceInfo.savingsAmount > 0) {
        console.log(`   💵 Max Savings: $${priceInfo.savingsAmount.toFixed(2)} (${priceInfo.savingsPercentage.toFixed(1)}%)`);
      }
    });

    // Show store coverage statistics
    console.log('\n\n📊 Store Coverage Statistics:');
    console.log('============================');
    
    const storeStats = {
      'All 3 stores': groups.filter(g => g.stores.length === 3).length,
      '2 stores': groups.filter(g => g.stores.length === 2).length,
      '1 store only': groups.filter(g => g.stores.length === 1).length,
    };

    Object.entries(storeStats).forEach(([coverage, count]) => {
      const percentage = (count / groups.length * 100).toFixed(1);
      console.log(`${coverage}: ${count} products (${percentage}%)`);
    });

    // Show savings opportunities
    console.log('\n\n💰 Savings Opportunities:');
    console.log('=========================');
    
    const groupsWithSavings = groups.filter(g => (g.highestPrice - g.lowestPrice) > 0.50);
    console.log(`Products with significant savings (>$0.50): ${groupsWithSavings.length}`);
    
    if (groupsWithSavings.length > 0) {
      const totalMaxSavings = groupsWithSavings.reduce((sum, g) => sum + (g.highestPrice - g.lowestPrice), 0);
      console.log(`Total potential savings: $${totalMaxSavings.toFixed(2)}`);
      console.log(`Average savings per product: $${(totalMaxSavings / groupsWithSavings.length).toFixed(2)}`);
    }

    // Show categorization
    console.log('\n\n📂 Category Breakdown:');
    console.log('======================');
    
    const categories = new Map<string, number>();
    groups.forEach(group => {
      const category = group.category || 'Unknown';
      categories.set(category, (categories.get(category) || 0) + 1);
    });

    Array.from(categories.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .forEach(([category, count]) => {
        const percentage = (count / groups.length * 100).toFixed(1);
        console.log(`${category}: ${count} products (${percentage}%)`);
      });

    console.log('\n✅ Deduplication test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
if (require.main === module) {
  testProductDeduplication().then(() => {
    console.log('\n🏁 Test script finished');
    process.exit(0);
  }).catch((error) => {
    console.error('💥 Test script failed:', error);
    process.exit(1);
  });
}

export { testProductDeduplication };