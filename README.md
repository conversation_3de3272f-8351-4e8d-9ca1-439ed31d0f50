# AI Recipe Planner

An AI-powered recipe planning application that helps you create recipes based on available ingredients.

## Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Expo CLI
- Supabase account
- Algolia account
- Google Gemini API key

## Setup

### 1. Clone the repository

```bash
git clone [your-repo-url]
cd ai-recipe-planner
```

### 2. Install dependencies

```bash
npm install
# or
yarn install
```

### 3. Environment Configuration

Copy the example environment file:

```bash
cp .env.example .env
```

Then edit the `.env` file and add your API keys:

#### Gemini AI Configuration
- `EXPO_PUBLIC_GEMINI_API_KEY`: Your Google Gemini API key for recipe generation

#### Algolia Configuration
- `EXPO_PUBLIC_ALGOLIA_APP_ID`: Your Algolia Application ID
- `EXPO_PUBLIC_ALGOLIA_API_KEY`: Your Algolia Search API key (public)
- `EXPO_PUBLIC_ALGOLIA_INDEX_NAME`: The name of your Algolia index
- `ALGOLIA_ADMIN_KEY`: Your Algolia Admin API key (keep secure!)

#### Supabase Configuration
- `SUPABASE_URL`: Your Supabase project URL
- `SUPABASE_ANON_KEY`: Your Supabase anonymous key

### 4. Supabase Setup

1. Create a new project in [Supabase](https://supabase.com)
2. Once your project is created, go to Settings > API
3. Copy your project URL and paste it as `SUPABASE_URL` in your `.env` file
4. Copy your anon/public key and paste it as `SUPABASE_ANON_KEY` in your `.env` file

### 5. Running the application

```bash
# Start the Expo development server
npx expo start

# Run on iOS
npx expo run:ios

# Run on Android
npx expo run:android

# Run on web
npx expo start --web
```

## CI/CD Configuration

For GitHub Actions or other CI/CD pipelines, you need to configure the following secrets:

### GitHub Actions
1. Go to your repository on GitHub
2. Navigate to Settings > Secrets and variables > Actions
3. Add the following repository secrets:
   - `EXPO_PUBLIC_GEMINI_API_KEY`
   - `EXPO_PUBLIC_ALGOLIA_APP_ID`
   - `EXPO_PUBLIC_ALGOLIA_API_KEY`
   - `EXPO_PUBLIC_ALGOLIA_INDEX_NAME`
   - `ALGOLIA_ADMIN_KEY`
   - `SUPABASE_URL`
   - `SUPABASE_ANON_KEY`

### Other CI/CD Platforms
Configure the same environment variables as secrets in your CI/CD platform of choice (GitLab CI, CircleCI, Jenkins, etc.).

## Project Structure

```
ai-recipe-planner/
├── .env                    # Environment variables (not committed)
├── .env.example           # Example environment variables
├── scrapers/              # Web scrapers for ingredient data
│   └── woolworths-scrape/
│       └── .env          # Scraper-specific environment variables
├── src/                   # Source code
├── assets/               # Images and other assets
└── README.md            # This file
```

## Security Notes

- Never commit your `.env` file to version control
- Keep your `ALGOLIA_ADMIN_KEY` secure and only use it in server-side scripts
- Your `SUPABASE_ANON_KEY` is safe to use in client-side code as it's designed for public access with Row Level Security (RLS)

## Troubleshooting

### Supabase Connection Issues
- Verify your `SUPABASE_URL` and `SUPABASE_ANON_KEY` are correct
- Check that your Supabase project is active and not paused
- Ensure you have proper network connectivity

### Environment Variables Not Loading
- Make sure you've copied `.env.example` to `.env`
- Restart your development server after changing environment variables
- For Expo, ensure variables are prefixed with `EXPO_PUBLIC_` for client-side access

## Contributing

Please read our contributing guidelines before submitting pull requests.

## License

[Your License Here]
