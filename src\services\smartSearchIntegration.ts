/**
 * Smart Search Integration
 * 
 * Example integration showing how to use the smart search and product image services
 * together for a complete shopping experience.
 */

import { smartSearchService, SmartSearchResult } from './smartSearchService';
import { productImageService, ProductImage } from './productImageService';

export interface EnhancedSearchResult extends SmartSearchResult {
  brandImage?: ProductImage;
  alternativeBrands?: Array<{
    brand: string;
    image?: ProductImage;
    price: number;
    store: string;
  }>;
}

export class SmartSearchIntegration {
  /**
   * Enhanced search with images and brand alternatives
   */
  async searchWithImages(query: string, options: {
    maxResults?: number;
    includeAlternatives?: boolean;
    preferredStore?: 'woolworths' | 'newworld' | 'paknsave';
  } = {}): Promise<EnhancedSearchResult[]> {
    const { maxResults = 10, includeAlternatives = true, preferredStore } = options;

    try {
      console.log(`🔍 Enhanced search for: "${query}"`);

      // Get smart search results
      const searchResults = await smartSearchService.searchProducts(query, {
        maxResults,
        includeImages: false, // We'll get better images separately
        preferredStore
      });

      if (searchResults.length === 0) {
        return [];
      }

      // Enhance results with images and alternatives
      const enhancedResults: EnhancedSearchResult[] = [];

      for (const result of searchResults) {
        const enhanced: EnhancedSearchResult = { ...result };

        // Get brand image
        try {
          const brandImage = await productImageService.getBrandImage({
            productName: result.name,
            brand: result.brand,
            preferredStore: result.store as any,
            size: 'thumbnail',
            fallbackToGeneric: true
          });

          if (brandImage) {
            enhanced.brandImage = brandImage;
          }
        } catch (error) {
          console.warn(`Failed to get brand image for ${result.brand}:`, error);
        }

        // Get alternative brands if requested
        if (includeAlternatives) {
          try {
            const alternativeImages = await productImageService.getBrandImages(result.name, 5);
            enhanced.alternativeBrands = alternativeImages
              .filter(img => img.brand !== result.brand)
              .map(img => ({
                brand: img.brand,
                image: img,
                price: 0, // Would need to fetch actual price
                store: img.store
              }));
          } catch (error) {
            console.warn(`Failed to get alternative brands for ${result.name}:`, error);
          }
        }

        enhancedResults.push(enhanced);
      }

      console.log(`✅ Enhanced search completed: ${enhancedResults.length} results`);
      return enhancedResults;

    } catch (error) {
      console.error('Enhanced search failed:', error);
      return [];
    }
  }

  /**
   * Get smart suggestions with preview images
   */
  async getSmartSuggestionsWithImages(partialQuery: string): Promise<Array<{
    query: string;
    type: string;
    previewImage?: ProductImage;
  }>> {
    try {
      const suggestions = await smartSearchService.getSuggestions(partialQuery, 8);
      
      const enhancedSuggestions = await Promise.all(
        suggestions.map(async (suggestion) => {
          let previewImage: ProductImage | undefined;
          
          try {
            const images = await productImageService.getBrandImages(suggestion.query, 1);
            if (images.length > 0) {
              previewImage = images[0];
            }
          } catch (error) {
            // Ignore image loading errors for suggestions
          }

          return {
            query: suggestion.query,
            type: suggestion.type,
            previewImage
          };
        })
      );

      return enhancedSuggestions;
    } catch (error) {
      console.error('Smart suggestions with images failed:', error);
      return [];
    }
  }

  /**
   * Clear all caches
   */
  clearCaches(): void {
    smartSearchService.clearCache();
    productImageService.clearCache();
  }
}

export const smartSearchIntegration = new SmartSearchIntegration(); 