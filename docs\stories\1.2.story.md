# Story 1.2: Consolidated Product Card with Multi-Store Price Display - Brownfield Addition

**Epic:** Product Deduplication & Consolidated Price Display  
**Story ID:** 1.2  
**Status:** Draft  
**Estimated Effort:** 12 hours  
**Priority:** High  
**Dependencies:** Story 1.1 (Product Deduplication Algorithm)

## User Story

As a **grocery shopper using the AI Recipe Planner**,
I want **to see all store options and prices for a product in a single, consolidated card**,
So that **I can easily compare prices across stores and select my preferred option without switching between multiple cards**.

## Story Context

**Existing System Integration:**
- **Integrates with:** Existing product card components, shopping list functionality, price display logic
- **Technology:** React Native components, existing design system, TypeScript
- **Follows pattern:** Existing component architecture in `src/components/`
- **Touch points:** Product display pipeline, shopping list integration, price comparison modal functionality

## Acceptance Criteria

### Functional Requirements

1. **Consolidated Product Card Design**
   - Create `ConsolidatedProductCard` component that displays a single product with multiple store options
   - Show product image, name, brand, and size prominently at the top
   - Display all available stores with their prices in an organized, scannable format
   - Highlight the cheapest option with visual indicators (green price, "Best Deal" badge)

2. **Multi-Store Price Display**
   - Show store logos (🍎 Woolworths, 🛒 New World, 💰 Pak'nSave) with corresponding prices
   - Display unit pricing (per ltr, per kg, per each) consistently
   - Show price differences from cheapest option (e.g., "+$0.50 more")
   - Handle cases where product is not available at certain stores

3. **Interactive Store Selection**
   - Allow users to tap on any store option to select it for their shopping list
   - Provide clear visual feedback for selected store option
   - Maintain existing "Add to Shopping List" functionality with selected store context
   - Support quick store switching without losing product context

### Integration Requirements

4. **Existing shopping list APIs remain unchanged** - component works with current shopping list service interfaces

5. **Current price comparison service integration maintained** - leverages existing price data structures and formatting

6. **Design follows existing component patterns** - uses established design system, styling patterns, and component structure

### Technical Requirements

7. **Component Implementation**
   - Create `src/components/ConsolidatedProductCard.tsx`
   - Accept deduplication service output as props
   - Implement responsive design for different screen sizes
   - Include proper TypeScript interfaces for all props and state

8. **User Experience Features**
   - Smooth animations for store selection and price highlighting
   - Loading states for price updates
   - Error states for missing price data
   - Accessibility support (screen readers, proper contrast)

9. **Shopping List Integration**
   - Seamless integration with existing shopping list functionality
   - Preserve user's store preferences and brand choices
   - Support quantity selection and modification
   - Maintain existing shopping list item structure

## Definition of Done

- ✅ `ConsolidatedProductCard` component implemented and styled
- ✅ All store options display correctly with proper price formatting
- ✅ Store selection works seamlessly with shopping list integration
- ✅ Component handles edge cases (missing prices, unavailable stores)
- ✅ Visual design matches existing app aesthetic and usability standards
- ✅ Component tests cover all interaction scenarios
- ✅ Accessibility requirements met (WCAG 2.1 AA compliance)
- ✅ Performance optimized for smooth scrolling in product lists

## Technical Implementation Notes

### Key Files to Create
- `src/components/ConsolidatedProductCard.tsx`
- `src/components/StoreOptionSelector.tsx` (sub-component)
- `src/types/consolidatedProduct.ts` (TypeScript interfaces)
- `__tests__/components/ConsolidatedProductCard.test.tsx`

### Integration Points
- Deduplication service output (from Story 1.1)
- Existing shopping list service
- Current price formatting utilities
- Design system components and styling

### Design Considerations
- Expandable/collapsible store options for space efficiency
- Clear visual hierarchy: product info → price comparison → action buttons
- Consistent with existing product card styling and interactions

## Dependencies
- **Story 1.1:** Product Deduplication Algorithm (must be completed first)

## Risks & Mitigation
- **Risk:** Complex UI may impact performance in product lists
- **Mitigation:** Optimize rendering, implement proper memoization, performance testing
- **Risk:** Store selection UX may be confusing
- **Mitigation:** User testing, clear visual feedback, follow existing patterns

---
**Created:** 2025-01-22  
**Last Updated:** 2025-01-22  
**Assigned To:** [TBD]
