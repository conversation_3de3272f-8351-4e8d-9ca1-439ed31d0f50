/**
 * Consolidated Product Card Component
 * Story 1.2: Consolidated Product Card with Multi-Store Price Display
 * 
 * Displays a single product with multiple store options and prices in an organized,
 * scannable format. Integrates with the deduplication service from Story 1.1.
 */

import React, { useState, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { OptimizedImage } from './OptimizedImage';
import { ShoppingListSelector } from './ShoppingListSelector';
import { IUnifiedProduct, StoreConfig } from '../types/shoppingList';
import { productDeduplicationService } from '../services/productDeduplicationService';

const { width } = Dimensions.get('window');
const CARD_MARGIN = 16;
const CARD_WIDTH = width - (CARD_MARGIN * 2);

interface ConsolidatedProductCardProps {
  product: IUnifiedProduct;
  viewMode?: 'grid' | 'list';
  onPress?: (product: IUnifiedProduct) => void;
  onAddToList?: (product: IUnifiedProduct, store: string) => void;
  style?: any;
}

export const ConsolidatedProductCard: React.FC<ConsolidatedProductCardProps> = ({
  product,
  viewMode = 'list',
  onPress,
  onAddToList,
  style,
}) => {
  const { colors } = useTheme();
  const [selectedStore, setSelectedStore] = useState<string | null>(null);
  const [showListSelector, setShowListSelector] = useState(false);
  const [expanded, setExpanded] = useState(false);

  // Get store configurations
  const storeConfigs = useMemo(() => 
    productDeduplicationService.getStoreConfigs(), []
  );

  // Get store configuration by ID
  const getStoreConfig = useCallback((storeId: string): StoreConfig | undefined => 
    productDeduplicationService.getStoreConfig(storeId), []
  );

  // Calculate price statistics
  const priceStats = useMemo(() => {
    const prices = Object.values(product.storePrices).filter(price => price > 0);
    if (prices.length === 0) return { lowest: 0, highest: 0, savings: 0 };
    
    const lowest = Math.min(...prices);
    const highest = Math.max(...prices);
    const savings = highest - lowest;
    
    return { lowest, highest, savings };
  }, [product.storePrices]);

  // Get the store with the lowest price
  const bestPriceStore = useMemo(() => {
    let bestStore = null;
    let bestPrice = Infinity;
    
    Object.entries(product.storePrices).forEach(([store, price]) => {
      if (price > 0 && price < bestPrice) {
        bestPrice = price;
        bestStore = store;
      }
    });
    
    return bestStore;
  }, [product.storePrices]);

  // Handle card press
  const handleCardPress = useCallback(() => {
    if (onPress) {
      onPress(product);
    } else {
      setExpanded(!expanded);
    }
  }, [onPress, product, expanded]);

  // Handle store selection for adding to list
  const handleStoreSelect = useCallback((storeId: string) => {
    setSelectedStore(storeId);
    setShowListSelector(true);
  }, []);

  // Handle successful addition to shopping list
  const handleAddSuccess = useCallback((listName: string) => {
    setShowListSelector(false);
    setSelectedStore(null);
    Alert.alert(
      '✅ Added to Shopping List',
      `"${product.name}" has been added to "${listName}"`
    );
  }, [product.name]);

  // Render store price option
  const renderStorePrice = useCallback((storeId: string, price: number) => {
    const storeConfig = getStoreConfig(storeId);
    if (!storeConfig || price <= 0) return null;

    const isBestPrice = storeId === bestPriceStore;
    const priceDifference = price - priceStats.lowest;

    return (
      <TouchableOpacity
        key={storeId}
        style={[
          styles.storeOption,
          {
            backgroundColor: isBestPrice ? `${storeConfig.color}15` : colors.surface,
            borderColor: isBestPrice ? storeConfig.color : colors.border,
          }
        ]}
        onPress={() => handleStoreSelect(storeId)}
        activeOpacity={0.7}
      >
        <View style={styles.storeInfo}>
          <View style={[styles.storeIcon, { backgroundColor: storeConfig.color }]}>
            <Text style={[styles.storeIconText, { color: '#FFFFFF' }]}>
              {storeConfig.icon}
            </Text>
          </View>
          <View style={styles.storeDetails}>
            <Text style={[styles.storeName, { color: colors.text }]}>
              {storeConfig.displayName}
            </Text>
            {isBestPrice && (
              <View style={[styles.bestDealBadge, { backgroundColor: storeConfig.color }]}>
                <Text style={styles.bestDealText}>Best Deal</Text>
              </View>
            )}
          </View>
        </View>
        
        <View style={styles.priceSection}>
          <Text style={[
            styles.price,
            {
              color: isBestPrice ? storeConfig.color : colors.text,
              fontWeight: isBestPrice ? '700' : '600'
            }
          ]}>
            ${price.toFixed(2)}
          </Text>
          {priceDifference > 0 && (
            <Text style={[styles.priceDifference, { color: colors.textSecondary }]}>
              +${priceDifference.toFixed(2)}
            </Text>
          )}
        </View>

        <Ionicons 
          name="add-circle" 
          size={24} 
          color={isBestPrice ? storeConfig.color : colors.primary} 
        />
      </TouchableOpacity>
    );
  }, [bestPriceStore, priceStats.lowest, colors, getStoreConfig, handleStoreSelect]);

  // Render compact store indicators (for collapsed view)
  const renderCompactStoreIndicators = useCallback(() => {
    const availableStores = Object.entries(product.storePrices)
      .filter(([_, price]) => price > 0)
      .map(([storeId]) => storeId);

    return (
      <View style={styles.compactStoreIndicators}>
        {availableStores.map(storeId => {
          const storeConfig = getStoreConfig(storeId);
          if (!storeConfig) return null;

          const isBestPrice = storeId === bestPriceStore;
          
          return (
            <View
              key={storeId}
              style={[
                styles.compactStoreIndicator,
                {
                  backgroundColor: isBestPrice ? storeConfig.color : `${storeConfig.color}30`,
                  borderColor: storeConfig.color,
                }
              ]}
            >
              <Text style={[
                styles.compactStoreText,
                { color: isBestPrice ? '#FFFFFF' : storeConfig.color }
              ]}>
                {storeConfig.icon}
              </Text>
            </View>
          );
        })}
      </View>
    );
  }, [product.storePrices, bestPriceStore, getStoreConfig]);

  // Get the best product image
  const getProductImage = useCallback(() => {
    // Use the best image selected by deduplication service
    return product.imageUrl || product.allStores[0]?.imageUrl;
  }, [product]);

  return (
    <>
      <TouchableOpacity
        style={[styles.card, { backgroundColor: colors.surface }, style]}
        onPress={handleCardPress}
        activeOpacity={0.8}
      >
        {/* Product Header */}
        <View style={styles.productHeader}>
          <View style={styles.productImageContainer}>
            {getProductImage() ? (
              <OptimizedImage
                source={{ uri: getProductImage() }}
                style={styles.productImage}
                placeholder={
                  <View style={[styles.imagePlaceholder, { backgroundColor: colors.border }]}>
                    <Ionicons name="image-outline" size={24} color={colors.textSecondary} />
                  </View>
                }
              />
            ) : (
              <View style={[styles.imagePlaceholder, { backgroundColor: colors.border }]}>
                <Ionicons name="image-outline" size={24} color={colors.textSecondary} />
              </View>
            )}
          </View>

          <View style={styles.productInfo}>
            <Text style={[styles.productName, { color: colors.text }]} numberOfLines={2}>
              {product.name}
            </Text>
            
            {product.brand && (
              <Text style={[styles.productBrand, { color: colors.textSecondary }]}>
                {product.brand}
              </Text>
            )}

            <View style={styles.priceRange}>
              <Text style={[styles.priceRangeText, { color: colors.primary }]}>
                ${priceStats.lowest.toFixed(2)}
                {priceStats.savings > 0 && (
                  <Text style={[styles.priceRangeHigh, { color: colors.textSecondary }]}>
                    {' - '}${priceStats.highest.toFixed(2)}
                  </Text>
                )}
              </Text>
              {priceStats.savings > 0.50 && (
                <Text style={[styles.savingsText, { color: colors.success }]}>
                  Save ${priceStats.savings.toFixed(2)}
                </Text>
              )}
            </View>

            {!expanded && renderCompactStoreIndicators()}
          </View>

          <TouchableOpacity
            style={styles.expandButton}
            onPress={() => setExpanded(!expanded)}
          >
            <Ionicons 
              name={expanded ? "chevron-up" : "chevron-down"} 
              size={20} 
              color={colors.textSecondary} 
            />
          </TouchableOpacity>
        </View>

        {/* Expanded Store Options */}
        {expanded && (
          <View style={styles.storeOptionsContainer}>
            <Text style={[styles.storeOptionsTitle, { color: colors.text }]}>
              Available at {Object.values(product.storePrices).filter(p => p > 0).length} stores:
            </Text>
            <View style={styles.storeOptions}>
              {Object.entries(product.storePrices).map(([storeId, price]) => 
                renderStorePrice(storeId, price)
              )}
            </View>
          </View>
        )}
      </TouchableOpacity>

      {/* Shopping List Selector Modal */}
      {showListSelector && selectedStore && (
        <ShoppingListSelector
          visible={showListSelector}
          onClose={() => {
            setShowListSelector(false);
            setSelectedStore(null);
          }}
          product={{
            ...product.allStores.find(p => p.store === selectedStore) || product.allStores[0],
            store: selectedStore,
            price: product.storePrices[selectedStore] || 0,
          }}
          onSuccess={handleAddSuccess}
        />
      )}
    </>
  );
};
