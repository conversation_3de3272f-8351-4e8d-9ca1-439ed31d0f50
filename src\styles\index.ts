/**
 * Styles Index - Centralized theme exports
 * 
 * This file provides a single entry point for all theme-related
 * imports throughout the app.
 */

// Core theme system with enhanced minimalistic colors
export * from './theme';
export { theme } from './theme';

// Common styles
export * from './commonStyles';
export { commonStyles as defaultCommonStyles } from './commonStyles';

// Recipe-specific styles  
export * from './recipeStyles';
export { recipeStyles as defaultRecipeStyles } from './recipeStyles';

// Convenience exports for easy destructuring
export {
  colors,
  typography,
  spacing,
  radius,
  shadows,
  components,
  recipeColors,
} from './theme';

// Theme utilities
export type { Theme, ThemeMode } from './theme';
export {
  getThemeColors,
  setThemeMode,
  getThemeMode,
  createThemedStyles,
} from './theme'; 