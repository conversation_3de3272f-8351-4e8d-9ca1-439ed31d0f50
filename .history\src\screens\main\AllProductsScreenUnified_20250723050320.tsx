import React, { useState, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  SafeAreaView,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';
import { IUnifiedProduct } from '../../types/shoppingList';
import { useProductDeduplication } from '../../hooks/useProductDeduplication';
import { MultiStorePriceCard } from '../../components/MultiStorePriceCard';
import { productDeduplicationService } from '../../services/productDeduplicationService';
import { PricingFixesTestCard } from '../../components/PricingFixesTestCard';

export const AllProductsScreenUnified: React.FC = () => {
  const { colors } = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [selectedStores, setSelectedStores] = useState(['woolworths', 'newworld', 'paknsave']);
  const [sortBy, setSortBy] = useState<'name' | 'price' | 'savings'>('name');
  const [viewMode, setViewMode] = useState<'detailed' | 'compact'>('detailed');
  const [refreshing, setRefreshing] = useState(false);
  const [showPricingTest, setShowPricingTest] = useState(false);

  // Use the enhanced deduplication hook
  const {
    unifiedProducts,
    loading,
    error,
    totalProducts,
    storeCount,
    averageSavings,
    refresh
  } = useProductDeduplication({
    pageSize: 200,
    searchQuery,
    selectedCategory,
    selectedStores,
    enableCache: true,
  });

  // Filter and sort products locally
  const filteredProducts = useMemo(() => {
    let filtered = unifiedProducts;

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(product => 
        product.name.toLowerCase().includes(query) ||
        (product.brand && product.brand.toLowerCase().includes(query)) ||
        (product.category && product.category.toLowerCase().includes(query))
      );
    }

    // Apply category filter
    if (selectedCategory !== 'All') {
      filtered = filtered.filter(product => product.category === selectedCategory);
    }

    // Apply store filter
    if (selectedStores.length < 3) {
      filtered = filtered.filter(product => 
        product.allStores.some(store => selectedStores.includes(store))
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'price':
          return a.lowestPrice - b.lowestPrice;
        case 'savings':
          return b.maxSavings - a.maxSavings;
        case 'name':
        default:
          return a.name.localeCompare(b.name);
      }
    });

    return filtered;
  }, [unifiedProducts, searchQuery, selectedCategory, selectedStores, sortBy]);

  // Get unique categories from unified products
  const categories = useMemo(() => {
    const categorySet = new Set(unifiedProducts.map(p => p.category).filter(Boolean));
    return ['All', ...Array.from(categorySet).sort()];
  }, [unifiedProducts]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await refresh();
    setRefreshing(false);
  }, [refresh]);

  const handleProductPress = useCallback((product: IUnifiedProduct) => {
    // TODO: Navigate to product detail screen or show price comparison modal
    console.log('Product pressed:', product.name);
  }, []);

  const handleAddToCart = useCallback((product: IUnifiedProduct, store: string) => {
    Alert.alert(
      'Add to Cart',
      `Add ${product.name} from ${productDeduplicationService.getStoreConfig(store)?.displayName} for $${product.storePrices[store]?.toFixed(2)}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Add', 
          onPress: () => {
            // TODO: Implement actual add to cart functionality
            console.log(`Adding ${product.name} from ${store} to cart`);
          }
        }
      ]
    );
  }, []);

  const renderStoreFilter = () => {
    const storeConfigs = productDeduplicationService.getStoreConfigs();
    
    return (
      <View style={styles.storeFilters}>
        {storeConfigs.map(store => (
          <TouchableOpacity
            key={store.id}
            style={[
              styles.storeFilter,
              {
                backgroundColor: selectedStores.includes(store.id) 
                  ? `${store.color}20` 
                  : colors.surface,
                borderColor: selectedStores.includes(store.id) 
                  ? store.color 
                  : colors.border,
              }
            ]}
            onPress={() => {
              if (selectedStores.includes(store.id)) {
                // Don't allow removing all stores
                if (selectedStores.length > 1) {
                  setSelectedStores(prev => prev.filter(s => s !== store.id));
                }
              } else {
                setSelectedStores(prev => [...prev, store.id]);
              }
            }}
          >
            <Text style={styles.storeIcon}>{store.icon}</Text>
            <Text style={[
              styles.storeFilterText,
              { 
                color: selectedStores.includes(store.id) 
                  ? store.color 
                  : colors.text 
              }
            ]}>
              {store.displayName}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const renderCategoryFilter = () => (
    <View style={styles.categoryFilters}>
      <FlatList
        horizontal
        showsHorizontalScrollIndicator={false}
        data={categories}
        keyExtractor={(item) => item || 'unknown'}
        renderItem={({ item: category }) => (
          <TouchableOpacity
            style={[
              styles.categoryFilter,
              {
                backgroundColor: selectedCategory === category ? colors.primary : colors.surface,
                borderColor: colors.border,
              }
            ]}
            onPress={() => setSelectedCategory(category || 'All')}
          >
            <Text style={[
              styles.categoryFilterText,
              { 
                color: selectedCategory === category ? colors.background : colors.text 
              }
            ]}>
              {category || 'Unknown'}
            </Text>
          </TouchableOpacity>
        )}
      />
    </View>
  );

  const renderSortOptions = () => (
    <View style={styles.sortContainer}>
      {[
        { key: 'name', label: 'Name', icon: 'text' },
        { key: 'price', label: 'Price', icon: 'pricetag' },
        { key: 'savings', label: 'Savings', icon: 'trending-down' }
      ].map(option => (
        <TouchableOpacity
          key={option.key}
          style={[
            styles.sortOption,
            {
              backgroundColor: sortBy === option.key ? colors.primary : colors.surface,
              borderColor: colors.border,
            }
          ]}
          onPress={() => setSortBy(option.key as any)}
        >
          <Ionicons
            name={option.icon as any}
            size={16}
            color={sortBy === option.key ? colors.background : colors.text}
          />
          <Text style={[
            styles.sortOptionText,
            { 
              color: sortBy === option.key ? colors.background : colors.text 
            }
          ]}>
            {option.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderHeader = () => (
    <View style={[styles.header, { backgroundColor: colors.background }]}>
      {/* Title and View Mode Toggle */}
      <View style={styles.headerTop}>
        <View>
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            Product Comparison
          </Text>
          <Text style={[styles.headerSubtitle, { color: colors.textSecondary }]}>
            {filteredProducts.length} products • {storeCount} stores • 
            Avg savings: ${averageSavings.toFixed(2)}
          </Text>
        </View>
        <View style={styles.headerButtons}>
          <TouchableOpacity
            style={[styles.testButton, { backgroundColor: colors.primary }]}
            onPress={() => setShowPricingTest(true)}
          >
            <Text style={styles.testButtonText}>🔧 Test Fixes</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.viewModeButton, { backgroundColor: colors.surface }]}
            onPress={() => setViewMode(prev => prev === 'detailed' ? 'compact' : 'detailed')}
          >
            <Ionicons
              name={viewMode === 'detailed' ? 'list' : 'grid'}
              size={20}
              color={colors.text}
            />
          </TouchableOpacity>
        </View>
      </View>

      {/* Store Filters */}
      {renderStoreFilter()}

      {/* Category Filters */}
      {renderCategoryFilter()}

      {/* Sort Options */}
      {renderSortOptions()}
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, styles.centered, { backgroundColor: colors.background }]}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
          Loading and deduplicating products...
        </Text>
        <Text style={[styles.loadingSubtext, { color: colors.textSecondary }]}>
          Analyzing {totalProducts} products from {storeCount} stores
        </Text>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={[styles.container, styles.centered, { backgroundColor: colors.background }]}>
        <Ionicons name="alert-circle" size={64} color={colors.error} />
        <Text style={[styles.errorText, { color: colors.error }]}>
          Failed to load products
        </Text>
        <Text style={[styles.errorSubtext, { color: colors.textSecondary }]}>
          {error.message}
        </Text>
        <TouchableOpacity
          style={[styles.retryButton, { backgroundColor: colors.primary }]}
          onPress={handleRefresh}
        >
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <FlatList
        data={filteredProducts}
        renderItem={({ item }) => (
          <MultiStorePriceCard
            product={item}
            onPress={handleProductPress}
            onAddToCart={handleAddToCart}
            compact={viewMode === 'compact'}
          />
        )}
        keyExtractor={(item) => item.unifiedId}
        ListHeaderComponent={renderHeader}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="storefront-outline" size={64} color={colors.textSecondary} />
            <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
              No products found
            </Text>
            <Text style={[styles.emptySubtext, { color: colors.textSecondary }]}>
              Try adjusting your filters or search query
            </Text>
          </View>
        }
      />

      {/* Pricing Fixes Test Modal */}
      {showPricingTest && (
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: colors.background }]}>
            <PricingFixesTestCard onClose={() => setShowPricingTest(false)} />
          </View>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    padding: 16,
  },
  header: {
    marginBottom: 16,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  testButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    marginRight: 8,
  },
  testButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerSubtitle: {
    fontSize: 14,
    marginTop: 4,
  },
  viewModeButton: {
    padding: 8,
    borderRadius: 8,
  },
  storeFilters: {
    flexDirection: 'row',
    marginBottom: 12,
    gap: 8,
  },
  storeFilter: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  storeIcon: {
    fontSize: 16,
    marginRight: 6,
  },
  storeFilterText: {
    fontSize: 14,
    fontWeight: '500',
  },
  categoryFilters: {
    marginBottom: 12,
  },
  categoryFilter: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 20,
    borderWidth: 1,
  },
  categoryFilterText: {
    fontSize: 14,
    fontWeight: '500',
  },
  sortContainer: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 8,
  },
  sortOption: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  sortOptionText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    fontWeight: '500',
  },
  loadingSubtext: {
    marginTop: 8,
    fontSize: 14,
  },
  errorText: {
    marginTop: 16,
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  errorSubtext: {
    marginTop: 8,
    fontSize: 14,
    textAlign: 'center',
  },
  retryButton: {
    marginTop: 16,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
    marginTop: 8,
  },
});