/**
 * DEBUG SCRIPT - Find Supabase Query Limitations
 * 
 * This script investigates why we're getting exactly 1000 products
 * when the database contains 10,309 products.
 */

import { createClient } from '@supabase/supabase-js';

// Use hardcoded values for testing
const supabaseUrl = 'https://emjwniuqwhvohjassnrg.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVtanduaXVxd2h2b2hqYXNzbnJnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjQwMTYwNSwiZXhwIjoyMDY3OTc3NjA1fQ.oTowRoNAjlUmk10O9pSMK2BvL0XlyBb5orVRrlEryHs';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function debugSupabaseLimits() {
  console.log('🔍 Debugging Supabase Query Limitations...\n');

  // Test 1: Simple count query
  console.log('1. Testing simple count query...');
  const { count: totalCount, error: countError } = await supabase
    .from('products')
    .select('*', { count: 'exact', head: true });

  console.log(`   Total count: ${totalCount} (${countError ? 'ERROR: ' + countError.message : 'SUCCESS'})`);

  // Test 2: Query without any filters or limits
  console.log('\n2. Testing query without filters or limits...');
  const { data: allData, error: allError } = await supabase
    .from('products')
    .select('id, name, store');

  console.log(`   Returned: ${allData?.length || 0} products (${allError ? 'ERROR: ' + allError.message : 'SUCCESS'})`);

  // Test 3: Query with explicit large range
  console.log('\n3. Testing query with explicit range(0, 5000)...');
  const { data: rangeData, error: rangeError } = await supabase
    .from('products')
    .select('id, name, store')
    .range(0, 5000);

  console.log(`   Returned: ${rangeData?.length || 0} products (${rangeError ? 'ERROR: ' + rangeError.message : 'SUCCESS'})`);

  // Test 4: Query with different ordering
  console.log('\n4. Testing query with different ordering...');
  const { data: orderedData, error: orderedError } = await supabase
    .from('products')
    .select('id, name, store')
    .order('created_at', { ascending: false })
    .range(0, 2000);

  console.log(`   Returned: ${orderedData?.length || 0} products (${orderedError ? 'ERROR: ' + orderedError.message : 'SUCCESS'})`);

  // Test 5: Check if it's a view limitation
  console.log('\n5. Testing available_products view...');
  const { data: viewData, error: viewError } = await supabase
    .from('available_products')
    .select('*')
    .limit(100);

  console.log(`   View returned: ${viewData?.length || 0} products (${viewError ? 'ERROR: ' + viewError.message : 'SUCCESS'})`);

  // Test 6: Check specific stores
  console.log('\n6. Testing individual store queries...');
  const stores = ['woolworths', 'newworld', 'paknsave'];
  
  for (const store of stores) {
    const { data: storeData, error: storeError } = await supabase
      .from('products')
      .select('id, name')
      .eq('store', store);

    console.log(`   ${store}: ${storeData?.length || 0} products (${storeError ? 'ERROR: ' + storeError.message : 'SUCCESS'})`);
  }

  // Test 7: Check if there's a specific filter causing the 1000 limit
  console.log('\n7. Testing availability filter impact...');
  
  const { data: availableOnly, error: availError } = await supabase
    .from('products')
    .select('id, name, availability')
    .eq('availability', 'available');

  console.log(`   Products with availability='available': ${availableOnly?.length || 0} (${availError ? 'ERROR: ' + availError.message : 'SUCCESS'})`);

  const { data: nullAvailability, error: nullError } = await supabase
    .from('products')
    .select('id, name, availability')
    .is('availability', null);

  console.log(`   Products with availability=null: ${nullAvailability?.length || 0} (${nullError ? 'ERROR: ' + nullError.message : 'SUCCESS'})`);

  // Test 8: Check RLS policies
  console.log('\n8. Checking for Row Level Security policies...');
  const { data: rlsData, error: rlsError } = await supabase
    .rpc('pg_policies')
    .select('*');

  if (rlsError) {
    console.log('   RLS check failed (expected - no access to pg_policies)');
  } else {
    console.log(`   Found ${rlsData?.length || 0} RLS policies`);
  }

  console.log('\n✅ Debug complete!');
  
  return {
    totalCount,
    queryWithoutFilters: allData?.length || 0,
    queryWithRange: rangeData?.length || 0,
    queryWithOrdering: orderedData?.length || 0,
    availableProducts: availableOnly?.length || 0,
    nullAvailability: nullAvailability?.length || 0
  };
}

// Run the debug
if (require.main === module) {
  debugSupabaseLimits()
    .then((result) => {
      console.log('\n📊 DEBUG RESULTS SUMMARY:');
      console.log('='.repeat(50));
      console.log(`Database Total Count: ${result?.totalCount || 0}`);
      console.log(`Query Without Filters: ${result?.queryWithoutFilters || 0}`);
      console.log(`Query With Range(0,5000): ${result?.queryWithRange || 0}`);
      console.log(`Query With Ordering: ${result?.queryWithOrdering || 0}`);
      console.log(`Available Products Only: ${result?.availableProducts || 0}`);
      console.log(`Null Availability: ${result?.nullAvailability || 0}`);
      
      if ((result?.queryWithoutFilters || 0) === 1000) {
        console.log('\n🔍 FINDING: Default 1000 row limit is being applied');
        console.log('   This suggests a Supabase configuration or RLS policy limitation');
      }
      
      if ((result?.availableProducts || 0) === 1000) {
        console.log('\n🔍 FINDING: Exactly 1000 products have availability="available"');
        console.log('   The availability filter is the root cause of the limitation');
      }
      
      console.log('='.repeat(50));
    })
    .catch((error) => {
      console.error('❌ Debug failed:', error);
    });
}

export { debugSupabaseLimits };
