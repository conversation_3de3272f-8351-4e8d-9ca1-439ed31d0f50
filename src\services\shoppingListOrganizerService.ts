interface ShoppingListItem {
  id: string;
  name: string;
  category: string;
  checked: boolean;
  quantity: number;
  unit?: string;
  selectedBrand?: string;
  selectedSize?: string;
  storePrices: Array<{
    store: string;
    price?: number;
    available: boolean;
    brand?: string;
    size?: string;
  }>;
  addedAt: Date;
}

interface CategorySection {
  category: string;
  icon: string;
  items: ShoppingListItem[];
  totalItems: number;
  checkedItems: number;
  estimatedCost: number;
  isCollapsed?: boolean;
}

interface OrganizedShoppingList {
  sections: CategorySection[];
  totalItems: number;
  totalChecked: number;
  totalCost: number;
  lastUpdated: Date;
}

export class ShoppingListOrganizerService {
  private categoryOrder = [
    'Produce',
    'Dairy', 
    'Meat',
    'Bakery',
    'Pantry',
    'Beverages',
    'Frozen',
    'Household',
    'Other'
  ];

  private categoryIcons = {
    'Produce': '🥬',
    'Dairy': '🥛',
    'Meat': '🥩',
    'Bakery': '🍞',
    'Pantry': '🥫',
    'Beverages': '☕',
    'Frozen': '🧊',
    'Household': '🧽',
    'Other': '📦'
  };

  organizeShoppingList(items: ShoppingListItem[]): OrganizedShoppingList {
    // Group items by category
    const categoryGroups = this.groupItemsByCategory(items);
    
    // Create organized sections
    const sections: CategorySection[] = [];
    let totalCost = 0;
    let totalChecked = 0;

    // Process categories in order - only include categories that have items
    for (const category of this.categoryOrder) {
      const categoryItems = categoryGroups.get(category) || [];
      
      if (categoryItems.length > 0) { // Only create sections for categories with items
        const checkedCount = categoryItems.filter(item => item.checked).length;
        const categoryCost = this.calculateCategoryCost(categoryItems);
        
        sections.push({
          category,
          icon: this.categoryIcons[category as keyof typeof this.categoryIcons] || '📦',
          items: this.sortItemsInCategory(categoryItems),
          totalItems: categoryItems.length,
          checkedItems: checkedCount,
          estimatedCost: categoryCost,
          isCollapsed: false,
        });

        totalCost += categoryCost;
        totalChecked += checkedCount;
      }
    }

    // Also check for any items in categories not in our standard order
    for (const [category, categoryItems] of categoryGroups.entries()) {
      if (!this.categoryOrder.includes(category) && categoryItems.length > 0) {
        const checkedCount = categoryItems.filter(item => item.checked).length;
        const categoryCost = this.calculateCategoryCost(categoryItems);
        
        sections.push({
          category,
          icon: this.categoryIcons[category as keyof typeof this.categoryIcons] || '📦',
          items: this.sortItemsInCategory(categoryItems),
          totalItems: categoryItems.length,
          checkedItems: checkedCount,
          estimatedCost: categoryCost,
          isCollapsed: false,
        });

        totalCost += categoryCost;
        totalChecked += checkedCount;
      }
    }

    return {
      sections,
      totalItems: items.length,
      totalChecked,
      totalCost,
      lastUpdated: new Date(),
    };
  }

  private groupItemsByCategory(items: ShoppingListItem[]): Map<string, ShoppingListItem[]> {
    const groups = new Map<string, ShoppingListItem[]>();

    // Initialize all categories
    for (const category of this.categoryOrder) {
      groups.set(category, []);
    }

    // Group items
    for (const item of items) {
      const category = item.category || 'Other';
      const normalizedCategory = this.normalizeCategory(category);
      
      if (!groups.has(normalizedCategory)) {
        groups.set(normalizedCategory, []);
      }
      
      groups.get(normalizedCategory)!.push(item);
    }

    return groups;
  }

  private normalizeCategory(category: string): string {
    const normalized = category.toLowerCase();
    
    // Map variations to standard categories
    const categoryMappings = {
      'fruit': 'Produce',
      'vegetable': 'Produce',
      'vegetables': 'Produce',
      'fruits': 'Produce',
      'fresh': 'Produce',
      'milk': 'Dairy',
      'cheese': 'Dairy',
      'yogurt': 'Dairy',
      'yoghurt': 'Dairy',
      'butter': 'Dairy',
      'chicken': 'Meat',
      'beef': 'Meat',
      'pork': 'Meat',
      'fish': 'Meat',
      'seafood': 'Meat',
      'bread': 'Bakery',
      'pastry': 'Bakery',
      'baked': 'Bakery',
      'bakery': 'Bakery',
      'canned': 'Pantry',
      'dry': 'Pantry',
      'spices': 'Pantry',
      'condiments': 'Pantry',
      'coffee': 'Beverages',
      'tea': 'Beverages',
      'juice': 'Beverages',
      'soda': 'Beverages',
      'water': 'Beverages',
      'drinks': 'Beverages',
      'cleaning': 'Household',
      'toiletries': 'Household',
      'personal care': 'Household',
    };

    // Check for exact matches first
    for (const [key, value] of Object.entries(categoryMappings)) {
      if (normalized.includes(key)) {
        return value;
      }
    }

    // Check if it's already a standard category
    const standardCategory = this.categoryOrder.find(cat => 
      cat.toLowerCase() === normalized
    );

    return standardCategory || 'Other';
  }

  private sortItemsInCategory(items: ShoppingListItem[]): ShoppingListItem[] {
    return items.sort((a, b) => {
      // Unchecked items first
      if (a.checked !== b.checked) {
        return a.checked ? 1 : -1;
      }
      
      // Then by name alphabetically
      return a.name.localeCompare(b.name);
    });
  }

  private calculateCategoryCost(items: ShoppingListItem[]): number {
    return items
      .filter(item => !item.checked) // Only unchecked items
      .reduce((total, item) => {
        const availablePrices = item.storePrices
          .filter(sp => sp.available && sp.price && sp.price > 0)
          .map(sp => sp.price!);
        
        if (availablePrices.length === 0) return total;
        
        const lowestPrice = Math.min(...availablePrices);
        return total + (lowestPrice * item.quantity);
      }, 0);
  }

  // Get shopping order suggestions based on typical store layout
  getShoppingOrder(): string[] {
    return [
      'Produce',      // Usually at entrance
      'Bakery',       // Often near produce
      'Dairy',        // Usually around perimeter
      'Meat',         // Usually around perimeter
      'Frozen',       // Usually in middle aisles
      'Pantry',       // Middle aisles
      'Beverages',    // Middle aisles
      'Household',    // Usually last section
      'Other'         // Any remaining items
    ];
  }

  // Toggle category collapsed state
  toggleCategoryCollapsed(sections: CategorySection[], categoryName: string): CategorySection[] {
    return sections.map(section => ({
      ...section,
      isCollapsed: section.category === categoryName ? !section.isCollapsed : section.isCollapsed
    }));
  }

  // Get category statistics
  getCategoryStats(sections: CategorySection[]) {
    const stats = sections.map(section => ({
      category: section.category,
      icon: section.icon,
      totalItems: section.totalItems,
      checkedItems: section.checkedItems,
      completionPercentage: section.totalItems > 0 
        ? Math.round((section.checkedItems / section.totalItems) * 100)
        : 0,
      estimatedCost: section.estimatedCost,
    }));

    return {
      categories: stats,
      overallCompletion: sections.length > 0
        ? Math.round(
            (sections.reduce((sum, s) => sum + s.checkedItems, 0) / 
             sections.reduce((sum, s) => sum + s.totalItems, 0)) * 100
          )
        : 0,
      totalEstimatedCost: sections.reduce((sum, s) => sum + s.estimatedCost, 0),
    };
  }

  // Suggest next items to shop for based on store layout
  suggestNextItems(sections: CategorySection[]): ShoppingListItem[] {
    const nextItems: ShoppingListItem[] = [];
    const shoppingOrder = this.getShoppingOrder();

    for (const categoryName of shoppingOrder) {
      const section = sections.find(s => s.category === categoryName);
      if (section) {
        const uncheckedItems = section.items.filter(item => !item.checked);
        if (uncheckedItems.length > 0) {
          nextItems.push(...uncheckedItems.slice(0, 3)); // Next 3 items from this category
          if (nextItems.length >= 5) break; // Limit to 5 suggestions
        }
      }
    }

    return nextItems;
  }
}

export const shoppingListOrganizerService = new ShoppingListOrganizerService();