import React, { useState } from 'react';
import {
  View,
  Text,
  Modal,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { useShopping, ShoppingList } from '../context/ShoppingContext';

interface Product {
  id: string;
  name: string;
  price: number;
  current_price?: number;
  original_price?: number;
  store: string;
  category?: string;
  brand?: string;
  brand_name?: string;
  unit?: string;
  size?: string;
  image_url?: string;
  is_available?: boolean;
  is_on_sale?: boolean;
  promotion_text?: string;
}

interface ListSelectionModalProps {
  visible: boolean;
  onClose: () => void;
  product: Product | null;
  onProductAdded?: (listName: string) => void;
}

interface ListItemProps {
  list: ShoppingList;
  onSelect: (list: ShoppingList) => void;
  isSelected: boolean;
}

const ListItem: React.FC<ListItemProps> = ({ list, onSelect, isSelected }) => {
  const { colors } = useTheme();
  
  return (
    <TouchableOpacity
      style={[
        styles.listItem,
        { 
          backgroundColor: isSelected ? `${list.color}20` : colors.surface,
          borderColor: isSelected ? list.color : colors.border,
        }
      ]}
      onPress={() => onSelect(list)}
    >
      <View style={styles.listItemContent}>
        <View style={[styles.listIcon, { backgroundColor: list.color }]}>
          <Ionicons name={list.icon as any} size={20} color="white" />
        </View>
        
        <View style={styles.listInfo}>
          <Text style={[styles.listName, { color: colors.text }]}>{list.name}</Text>
          {list.description && (
            <Text style={[styles.listDescription, { color: colors.textSecondary }]} numberOfLines={1}>
              {list.description}
            </Text>
          )}
          <View style={styles.listStats}>
            <Text style={[styles.listStatsText, { color: colors.textSecondary }]}>
              {list.totalItems || 0} items • ${(list.totalPrice || 0).toFixed(2)}
            </Text>
          </View>
        </View>
        
        <View style={styles.selectionIndicator}>
          {isSelected ? (
            <Ionicons name="checkmark-circle" size={24} color={list.color} />
          ) : (
            <View style={[styles.unselectedCircle, { borderColor: colors.border }]} />
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

export const ListSelectionModal: React.FC<ListSelectionModalProps> = ({
  visible,
  onClose,
  product,
  onProductAdded,
}) => {
  const { colors } = useTheme();
  const { shoppingLists, addProductToList, createList } = useShopping();
  const [selectedList, setSelectedList] = useState<ShoppingList | null>(null);
  const [loading, setLoading] = useState(false);
  const [showCreateForm, setShowCreateForm] = useState(false);

  const handleAddToList = async () => {
    if (!selectedList || !product) return;

    setLoading(true);
    try {
      await addProductToList(selectedList.id, product);
      onProductAdded?.(selectedList.name);
      onClose();
      setSelectedList(null);
      Alert.alert(
        'Added to List',
        `"${product.name}" has been added to "${selectedList.name}"`
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to add product to list. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateQuickList = async () => {
    if (!product) return;

    setLoading(true);
    try {
      const newList = await createList(
        `${product.name} List`,
        `Shopping list for ${product.name}`,
        '#3B82F6',
        'cart'
      );
      
      await addProductToList(newList.id, product);
      onProductAdded?.(newList.name);
      onClose();
      setSelectedList(null);
      Alert.alert(
        'List Created',
        `Created "${newList.name}" and added "${product.name}" to it`
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to create list. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderListItem = ({ item }: { item: ShoppingList }) => (
    <ListItem
      list={item}
      onSelect={setSelectedList}
      isSelected={selectedList?.id === item.id}
    />
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="list" size={48} color={colors.border} />
      <Text style={[styles.emptyTitle, { color: colors.text }]}>
        No Shopping Lists
      </Text>
      <Text style={[styles.emptySubtext, { color: colors.textSecondary }]}>
        Create your first shopping list to save products
      </Text>
      <TouchableOpacity
        style={[styles.createButton, { backgroundColor: colors.primary }]}
        onPress={handleCreateQuickList}
        disabled={loading}
      >
        <Ionicons name="add" size={20} color="white" />
        <Text style={styles.createButtonText}>Create List</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={[styles.header, { borderBottomColor: colors.border }]}>
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            Add to Shopping List
          </Text>
          <TouchableOpacity onPress={onClose}>
            <Ionicons name="close" size={24} color={colors.text} />
          </TouchableOpacity>
        </View>

        {product && (
          <View style={[styles.productInfo, { backgroundColor: colors.surface }]}>
            <Text style={[styles.productName, { color: colors.text }]} numberOfLines={2}>
              {product.name}
            </Text>
            <View style={styles.productDetails}>
              <Text style={[styles.productPrice, { color: colors.primary }]}>
                ${(product.current_price || product.price).toFixed(2)}
              </Text>
              <Text style={[styles.productStore, { color: colors.textSecondary }]}>
                {product.store}
              </Text>
            </View>
          </View>
        )}

        <View style={styles.content}>
          {shoppingLists.length === 0 ? (
            renderEmptyState()
          ) : (
            <>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                Select a List
              </Text>
              <FlatList
                data={shoppingLists}
                renderItem={renderListItem}
                keyExtractor={(item) => item.id}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.listContainer}
              />
            </>
          )}
        </View>

        {shoppingLists.length > 0 && (
          <View style={[styles.footer, { borderTopColor: colors.border }]}>
            <TouchableOpacity
              style={[styles.quickCreateButton, { backgroundColor: colors.surface }]}
              onPress={handleCreateQuickList}
              disabled={loading}
            >
              <Ionicons name="add-circle-outline" size={20} color={colors.primary} />
              <Text style={[styles.quickCreateText, { color: colors.primary }]}>
                Create New List
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.addButton,
                { 
                  backgroundColor: selectedList ? colors.primary : colors.border,
                  opacity: selectedList ? 1 : 0.6,
                }
              ]}
              onPress={handleAddToList}
              disabled={!selectedList || loading}
            >
              {loading ? (
                <ActivityIndicator color="white" />
              ) : (
                <>
                  <Ionicons name="add" size={20} color="white" />
                  <Text style={styles.addButtonText}>
                    Add to {selectedList?.name || 'List'}
                  </Text>
                </>
              )}
            </TouchableOpacity>
          </View>
        )}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  productInfo: {
    padding: 16,
    margin: 16,
    borderRadius: 12,
  },
  productName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  productDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  productPrice: {
    fontSize: 18,
    fontWeight: '700',
  },
  productStore: {
    fontSize: 14,
    textTransform: 'capitalize',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
  },
  listContainer: {
    paddingBottom: 16,
  },
  listItem: {
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 2,
  },
  listItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  listIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  listInfo: {
    flex: 1,
  },
  listName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  listDescription: {
    fontSize: 14,
    marginBottom: 4,
  },
  listStats: {
    flexDirection: 'row',
  },
  listStatsText: {
    fontSize: 12,
  },
  selectionIndicator: {
    marginLeft: 12,
  },
  unselectedCircle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 24,
  },
  createButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  createButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  footer: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
    gap: 12,
  },
  quickCreateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
    flex: 1,
  },
  quickCreateText: {
    fontSize: 14,
    fontWeight: '600',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
    flex: 2,
    justifyContent: 'center',
  },
  addButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});