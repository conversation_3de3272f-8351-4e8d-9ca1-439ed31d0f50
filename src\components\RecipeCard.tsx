import React, { useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Platform,
  ViewStyle,
  Image,
} from 'react-native';
import { Recipe } from '../utils/storage';
import { 
  theme, 
  getThemeColors, 
  recipeStyles, 
  commonStyles, 
  ThemeMode 
} from '../styles';
import { Ionicons } from '@expo/vector-icons';

export type RecipeCardVariant = 'compact' | 'full' | 'enhanced';
export type RecipeCardLayout = 'vertical' | 'horizontal';

interface RecipeCardProps {
  recipe: Recipe;
  onPress: () => void;
  
  // Appearance Control
  variant?: RecipeCardVariant;
  layout?: RecipeCardLayout;
  themeMode?: ThemeMode;
  
  // Feature Toggles
  showTags?: boolean;
  showCalories?: boolean;
  showDifficulty?: boolean;
  showServings?: boolean;
  showCookingTime?: boolean;
  showDescription?: boolean;
  showNutritionInfo?: boolean;
  
  // Layout Options
  maxTags?: number;
  titleLines?: number;
  descriptionLines?: number;
  
  // Styling
  style?: ViewStyle;
  width?: number;
  imageHeight?: number;
  
  // Behavior
  enableAnimation?: boolean;
  activeOpacity?: number;
  
  // Actions
  onDelete?: (recipeId: string, recipeTitle: string) => void;
  deleteIcon?: string;
  deleteButtonStyle?: ViewStyle;
  onAddToShoppingList?: (ingredients: string[], recipeTitle: string) => void;
  showAddToShoppingList?: boolean;
  onSave?: (recipeId: string, recipe: Recipe) => void;
  showSaveButton?: boolean;
  isSaved?: boolean;
}

// Helper functions (extracted from original component, now theme-aware)
const getFoodIcon = (mealType?: string, tags?: string[]) => {
  const type = (mealType || '').toLowerCase();
  const allTags = tags?.join(' ').toLowerCase() || '';
  
  if (type.includes('breakfast') || allTags.includes('breakfast')) return '🥞';
  if (type.includes('dessert') || allTags.includes('dessert')) return '🍰';
  if (allTags.includes('healthy') || allTags.includes('vegan')) return '🥗';
  if (allTags.includes('vegetarian')) return '🥕';
  if (type.includes('lunch')) return '🥪';
  if (type.includes('dinner')) return '🍽️';
  if (allTags.includes('quick')) return '⚡';
  return '🍳';
};

const getDifficultyBadge = (difficulty?: string) => {
  if (!difficulty) return { 
    color: theme.recipeColors.difficulty.medium, 
    text: 'Medium' 
  };
  
  switch (difficulty.toLowerCase()) {
    case 'easy':
      return { 
        color: theme.recipeColors.difficulty.easy, 
        text: 'Easy' 
      };
    case 'medium':
      return { 
        color: theme.recipeColors.difficulty.medium, 
        text: 'Medium' 
      };
    case 'hard':
      return { 
        color: theme.recipeColors.difficulty.hard, 
        text: 'Hard' 
      };
    default:
      return { 
        color: theme.recipeColors.difficulty.medium, 
        text: difficulty 
      };
  }
};

const getTimeIcon = (cookingTime?: string) => {
  if (!cookingTime) return '⏰';
  
  const time = cookingTime.toLowerCase();
  if (time.includes('15') || time.includes('quick')) return '⚡';
  if (time.includes('30')) return '⏰';
  if (time.includes('45') || time.includes('1 hour')) return '⏳';
  return '🕐';
};

const getBackgroundColor = (mealType?: string, tags?: string[], themeMode: ThemeMode = 'light') => {
  const colors = getThemeColors(themeMode);
  const type = (mealType || '').toLowerCase();
  const allTags = tags?.join(' ').toLowerCase() || '';
  
  if (type.includes('breakfast') || allTags.includes('breakfast')) {
    return colors.backgroundSecondary;
  } else if (type.includes('dessert') || allTags.includes('dessert')) {
    return colors.backgroundTertiary;
  } else if (allTags.includes('healthy') || allTags.includes('vegan') || allTags.includes('vegetarian')) {
    return colors.backgroundSecondary;
  } else if (type.includes('dinner') || allTags.includes('dinner')) {
    return colors.backgroundSecondary;
  } else {
    return colors.backgroundSecondary;
  }
};

export const RecipeCard: React.FC<RecipeCardProps> = ({
  recipe,
  onPress,
  
  // Appearance
  variant = 'full',
  layout = 'vertical',
  themeMode = 'light',
  
  // Features (defaults based on variant)
  showTags = variant !== 'compact',
  showCalories = variant === 'enhanced',
  showDifficulty = true,
  showServings = false,
  showCookingTime = true,
  showDescription = variant === 'enhanced',
  showNutritionInfo = variant === 'enhanced',
  
  // Layout options
  maxTags = variant === 'compact' ? 1 : 3,
  titleLines = variant === 'compact' ? 1 : 2,
  descriptionLines = 2,
  
  // Styling
  style,
  width,
  imageHeight,
  
  // Behavior
  enableAnimation = true,
  activeOpacity = 0.8,
  
  // Actions
  onDelete,
  deleteIcon,
  deleteButtonStyle,
  onAddToShoppingList,
  showAddToShoppingList = false,
  onSave,
  showSaveButton = true,
  isSaved = false,
}) => {
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const opacityAnim = useRef(new Animated.Value(1)).current;
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(true);

  // Get themed styles
  const colors = getThemeColors(themeMode);
  const themedRecipeStyles = StyleSheet.create({
    ...recipeStyles,
    // Override with current theme colors
    recipeCard: {
      ...recipeStyles.recipeCard,
      backgroundColor: colors.surface,
      shadowColor: colors.shadow,
    },
    recipeCardCompact: {
      ...recipeStyles.recipeCardCompact,
      backgroundColor: colors.surface,
      shadowColor: colors.shadow,
    },
  });

  // Animation handlers
  const handlePressIn = () => {
    if (!enableAnimation) return;
    
    Animated.parallel([
      Animated.spring(scaleAnim, {
        toValue: 0.95,
        useNativeDriver: true,
        tension: 300,
        friction: 8,
      }),
      Animated.timing(opacityAnim, {
        toValue: activeOpacity,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handlePressOut = () => {
    if (!enableAnimation) return;
    
    Animated.parallel([
      Animated.spring(scaleAnim, {
        toValue: 1,
        useNativeDriver: true,
        tension: 300,
        friction: 8,
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start();
  };

  // Computed values
  const backgroundColor = getBackgroundColor(recipe.mealType, recipe.tags, themeMode);
  const foodIcon = getFoodIcon(recipe.mealType, recipe.tags);
  const difficultyBadge = getDifficultyBadge(recipe.difficulty);
  const timeIcon = getTimeIcon(recipe.cookingTime);
  
  // Dynamic styling based on variant and layout
  const getCardStyle = (): ViewStyle[] => {
    const baseStyle: ViewStyle[] = [themedRecipeStyles.recipeCard];
    
    if (layout === 'horizontal') {
      baseStyle.push(styles.horizontalCard);
    }
    
    switch (variant) {
      case 'compact':
        baseStyle.push(themedRecipeStyles.recipeCardCompact);
        break;
      case 'enhanced':
        baseStyle.push(styles.enhancedCard);
        break;
      default:
        // Use full card style
        break;
    }
    
    if (width) {
      baseStyle.push({ width });
    }
    
    return baseStyle;
  };
  
  const getImageHeight = () => {
    if (imageHeight) return imageHeight;
    
    switch (variant) {
      case 'compact':
        return 100;
      case 'enhanced':
        return 180;
      default:
        return 140;
    }
  };

  // Info row items
  const infoItems = [];
  
  if (showCookingTime) {
    infoItems.push({
      icon: timeIcon,
      text: recipe.cookingTime,
      key: 'time'
    });
  }
  
  if (showCalories && (recipe.calories || recipe.nutritionInfo?.calories)) {
    const calories = recipe.calories || recipe.nutritionInfo?.calories;
    infoItems.push({
      icon: '🔥',
      text: `${calories}`,
      key: 'calories'
    });
  }

  return (
    <Animated.View
      style={[
        getCardStyle(),
        enableAnimation && {
          transform: [{ scale: scaleAnim }],
          opacity: opacityAnim,
        },
        style,
      ]}
    >
      <TouchableOpacity
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={enableAnimation ? 1 : 0.7}
        style={styles.touchableContent}
      >
        {/* Bookmark Icon (top left) */}
        {showSaveButton && (
          <TouchableOpacity
            style={{ position: 'absolute', top: 10, left: 10, zIndex: 2 }}
            onPress={(e) => {
              e.stopPropagation();
              if (onSave) onSave(recipe.id, recipe);
            }}
          >
            <Ionicons
              name={isSaved ? 'bookmark' : 'bookmark-outline'}
              size={28}
              color={isSaved ? '#f9c74f' : '#fff'}
              style={{ textShadowColor: '#000', textShadowRadius: 4 }}
            />
          </TouchableOpacity>
        )}
        {layout === 'horizontal' ? (
          // Horizontal Layout
          <View style={styles.horizontalContent}>
            <View style={[themedRecipeStyles.recipeCardImageCompact, { backgroundColor }]}>
              <Text style={styles.foodIcon}>{foodIcon}</Text>
            </View>
            
            <View style={styles.horizontalInfo}>
              <Text style={[themedRecipeStyles.recipeTitleCompact, { color: colors.text }]} numberOfLines={titleLines}>
                {recipe.title}
              </Text>
              
              {showDescription && recipe.description && (
                <Text style={[themedRecipeStyles.recipeDescriptionCompact, { color: colors.textSecondary }]} numberOfLines={descriptionLines}>
                  {recipe.description}
                </Text>
              )}
              
              {infoItems.length > 0 && (
                <View style={themedRecipeStyles.recipeMetadataCompact}>
                  {infoItems.map((item, index) => (
                    <React.Fragment key={item.key}>
                      {index > 0 && <View style={styles.infoDivider} />}
                      <View style={themedRecipeStyles.recipeMetaItemCompact}>
                        <Text style={[themedRecipeStyles.recipeMetaIcon, { color: colors.textTertiary }]}>{item.icon}</Text>
                        <Text style={[themedRecipeStyles.recipeMetaTextCompact, { color: colors.textSecondary }]}>{item.text}</Text>
                      </View>
                    </React.Fragment>
                  ))}
                </View>
              )}
            </View>
          </View>
        ) : (
          // Vertical Layout (default)
          <>
            {/* Image Section */}
            <View style={[themedRecipeStyles.recipeCardImage, { height: getImageHeight(), backgroundColor }]}>
              {recipe.imageUrl && !imageError ? (
                <Image
                  source={{ uri: recipe.imageUrl }}
                  style={[styles.recipeImage, { height: getImageHeight() }]}
                  resizeMode="cover"
                  onError={(error) => {
                    console.log(`❌ Image failed to load for recipe "${recipe.title}":`, recipe.imageUrl);
                    console.log('Error details:', error.nativeEvent?.error || 'Unknown error');
                    setImageError(true);
                    setImageLoading(false);
                  }}
                  onLoadStart={() => {
                    console.log(`🔄 Loading image for recipe "${recipe.title}":`, recipe.imageUrl);
                    setImageLoading(true);
                    setImageError(false);
                  }}
                  onLoad={() => {
                    console.log(`✅ Image loaded successfully for recipe "${recipe.title}"`);
                    setImageLoading(false);
                    setImageError(false);
                  }}
                  onLoadEnd={() => {
                    setImageLoading(false);
                  }}
                />
              ) : (
                <View style={styles.iconContainer}>
                  <Text style={[styles.foodIcon, variant === 'enhanced' && styles.largeFoodIcon]}>
                    {foodIcon}
                  </Text>
                  {imageError && recipe.imageUrl && (
                    <Text style={[styles.errorText, { color: colors.textSecondary }]}>
                      Image unavailable
                    </Text>
                  )}
                </View>
              )}
              
              {/* Difficulty Badge */}
              {showDifficulty && (
                <View style={[themedRecipeStyles.difficultyBadge, { backgroundColor: difficultyBadge.color }]}>
                  <Text style={themedRecipeStyles.difficultyText}>{difficultyBadge.text}</Text>
                </View>
              )}
              
              {/* Action Buttons */}
              <View style={styles.actionButtonsContainer}>
                {/* Top Right: Save and Delete Buttons */}
                <View style={styles.rightButtonsContainer}>
                  {showSaveButton && onSave && (
                    <TouchableOpacity 
                      style={[styles.actionButton, styles.saveButton]}
                      onPress={(event) => {
                        event.stopPropagation();
                        onSave(recipe.id, recipe);
                      }}
                      activeOpacity={0.7}
                    >
                      <Text style={styles.actionIcon}>{isSaved ? '❤️' : '🤍'}</Text>
                    </TouchableOpacity>
                  )}
                  
                  {onDelete && (
                    <TouchableOpacity 
                      style={[styles.actionButton, styles.deleteButton, deleteButtonStyle]}
                      onPress={(event) => {
                        event.stopPropagation();
                        onDelete(recipe.id, recipe.title);
                      }}
                      activeOpacity={0.7}
                    >
                      <Text style={styles.actionIcon}>{deleteIcon || '🗑️'}</Text>
                    </TouchableOpacity>
                  )}
                </View>
              </View>
            </View>

            {/* Content Section */}
            <View style={[styles.contentContainer, { padding: theme.spacing.base }]}>
              <Text style={[themedRecipeStyles.recipeTitle, { color: colors.text }]} numberOfLines={titleLines}>
                {recipe.title}
              </Text>
              
              {showDescription && recipe.description && (
                <Text style={[themedRecipeStyles.recipeDescription, { color: colors.textSecondary }]} numberOfLines={descriptionLines}>
                  {recipe.description}
                </Text>
              )}
              
              {/* Info Row */}
              {infoItems.length > 0 && (
                <View style={themedRecipeStyles.recipeMetadata}>
                  {infoItems.map((item, index) => (
                    <React.Fragment key={item.key}>
                      {index > 0 && <View style={styles.infoDivider} />}
                      <View style={themedRecipeStyles.recipeMetaItem}>
                        <Text style={[themedRecipeStyles.recipeMetaIcon, { color: colors.textTertiary }]}>{item.icon}</Text>
                        <Text style={[themedRecipeStyles.recipeMetaText, { color: colors.textSecondary }]}>{item.text}</Text>
                      </View>
                    </React.Fragment>
                  ))}
                </View>
              )}

              {/* Nutrition Info (Enhanced variant) */}
              {showNutritionInfo && recipe.nutritionInfo && (
                <View style={themedRecipeStyles.nutritionContainer}>
                  <Text style={[styles.nutritionTitle, { color: colors.text }]}>Nutrition per serving:</Text>
                  <View style={styles.nutritionRow}>
                    <Text style={[styles.nutritionItem, { color: colors.textSecondary }]}>Protein: {recipe.nutritionInfo.protein}g</Text>
                    <Text style={[styles.nutritionItem, { color: colors.textSecondary }]}>Carbs: {recipe.nutritionInfo.carbs}g</Text>
                    <Text style={[styles.nutritionItem, { color: colors.textSecondary }]}>Fat: {recipe.nutritionInfo.fat}g</Text>
                  </View>
                </View>
              )}

              {/* Tags */}
              {showTags && recipe.tags && recipe.tags.length > 0 && (
                <View style={themedRecipeStyles.tagContainer}>
                  {recipe.tags.slice(0, maxTags).map((tag, index) => {
                    // Check if it's a calorie tag
                    const isCalorieTag = tag.includes('cal');
                    
                    return (
                      <View 
                        key={index} 
                        style={isCalorieTag ? themedRecipeStyles.calorieTag : themedRecipeStyles.tag}
                      >
                        <Text 
                          style={isCalorieTag ? themedRecipeStyles.calorieTagText : [themedRecipeStyles.tagText, { color: colors.textSecondary }]}
                        >
                          {tag}
                        </Text>
                      </View>
                    );
                  })}
                  {recipe.tags.length > maxTags && (
                    <View style={[themedRecipeStyles.tag, { backgroundColor: colors.backgroundTertiary }]}>
                      <Text style={[themedRecipeStyles.tagText, { color: colors.textTertiary }]}>+{recipe.tags.length - maxTags}</Text>
                    </View>
                  )}
                </View>
              )}
            </View>
          </>
        )}
      </TouchableOpacity>
    </Animated.View>
  );
};

// Remaining styles that aren't covered by the theme system yet
const styles = StyleSheet.create({
  // Content layouts
  touchableContent: {
    flex: 1,
  },
  horizontalContent: {
    flexDirection: 'row',
    flex: 1,
  },
  horizontalInfo: {
    flex: 1,
    padding: theme.spacing.base,
    justifyContent: 'center',
  },
  
  // Image section
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  recipeImage: {
    width: '100%',
    borderTopLeftRadius: theme.radius.xl,
    borderTopRightRadius: theme.radius.xl,
  },
  foodIcon: {
    fontSize: 48,
    textShadowColor: 'rgba(0, 0, 0, 0.1)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  largeFoodIcon: {
    fontSize: 64,
  },
  errorText: {
    fontSize: theme.typography.fontSize.xs,
    textAlign: 'center',
    marginTop: 4,
    opacity: 0.7,
  },
  
  // Content section
  contentContainer: {
    flex: 1,
  },
  
  // Info row
  infoDivider: {
    width: 1,
    height: 12,
    backgroundColor: theme.colors.light.separator,
    marginHorizontal: theme.spacing.sm,
  },
  
  // Enhanced card specific
  enhancedCard: {
    borderRadius: theme.radius['2xl'],
    ...theme.shadows.lg,
  },
  
  // Horizontal card specific
  horizontalCard: {
    flexDirection: 'row',
    height: 120,
  },
  
  // Action buttons container
  actionButtonsContainer: {
    position: 'absolute',
    top: theme.spacing.sm,
    left: theme.spacing.sm,
    right: theme.spacing.sm,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  
  // Right buttons container
  rightButtonsContainer: {
    flexDirection: 'row',
    gap: theme.spacing.xs,
  },
  
  // Base action button style
  actionButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: theme.radius.full,
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
    ...theme.shadows.sm,
  },
  
  // Shopping list button
  shoppingListButton: {
    backgroundColor: 'rgba(0, 200, 81, 0.9)',
  },
  
  // Save button (top right)
  saveButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
  },
  
  // Delete button (specific overrides)
  deleteButton: {
    backgroundColor: 'rgba(255, 59, 48, 0.9)',
  },
  
  // Action icon
  actionIcon: {
    fontSize: 16,
    color: 'white',
  },
  
  // Nutrition styles (for enhanced variant)
  nutritionTitle: {
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.medium,
    marginBottom: theme.spacing.xs,
  },
  nutritionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  nutritionItem: {
    fontSize: theme.typography.fontSize.xs,
    fontWeight: theme.typography.fontWeight.normal,
  },
}); 