-- SQL Functions for Enhanced Product Matching
-- Based on recommendations from Matching_Products_Across_Supermarkets.txt
-- These functions should be executed in Supabase SQL Editor

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS fuzzystrmatch;
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- Function to enable fuzzystrmatch extension
CREATE OR REPLACE FUNCTION enable_fuzzystrmatch()
RETURNS void AS $$
BEGIN
  CREATE EXTENSION IF NOT EXISTS fuzzystrmatch;
END;
$$ LANGUAGE plpgsql;

-- Function to enable pg_trgm extension  
CREATE OR REPLACE FUNCTION enable_pg_trgm()
RETURNS void AS $$
BEGIN
  CREATE EXTENSION IF NOT EXISTS pg_trgm;
END;
$$ LANGUAGE plpgsql;

-- Add normalized name column if it doesn't exist
ALTER TABLE products ADD COLUMN IF NOT EXISTS norm_name TEXT;

-- Function to create normalized names
CREATE OR REPLACE FUNCTION create_normalized_names()
RETURNS void AS $$
BEGIN
  -- Update normalized names using advanced normalization
  UPDATE products 
  SET norm_name = lower(
    regexp_replace(
      regexp_replace(
        regexp_replace(
          regexp_replace(name, '\b(new|sale|special|offer|limited|fresh|premium|select|choice|value|pack)\b', ' ', 'gi'),
          '\b(\d+)\s*(l|litre|liter)s?\b', '\1l', 'gi'
        ),
        '\b(\d+)\s*(ml|millilitre|milliliter)s?\b', '\1ml', 'gi'
      ),
      '[^a-zA-Z0-9\s]', '', 'g'
    )
  )
  WHERE norm_name IS NULL OR norm_name = '';
  
  -- Create index for better performance
  CREATE INDEX IF NOT EXISTS idx_products_norm_name_gin ON products USING gin(norm_name gin_trgm_ops);
END;
$$ LANGUAGE plpgsql;

-- Function to find trigram matches between stores
CREATE OR REPLACE FUNCTION find_trigram_matches(
  source_store TEXT,
  target_store TEXT,
  similarity_threshold FLOAT DEFAULT 0.5
)
RETURNS TABLE(
  source_id TEXT,
  target_id TEXT,
  source_name TEXT,
  target_name TEXT,
  source_price FLOAT,
  target_price FLOAT,
  similarity_score FLOAT
) AS $$
BEGIN
  -- Set similarity threshold
  PERFORM set_limit(similarity_threshold);
  
  RETURN QUERY
  SELECT 
    a.id::TEXT as source_id,
    b.id::TEXT as target_id,
    a.name as source_name,
    b.name as target_name,
    a.price as source_price,
    b.price as target_price,
    similarity(a.norm_name, b.norm_name) as similarity_score
  FROM products a
  JOIN products b ON a.norm_name % b.norm_name
  WHERE a.store = source_store 
    AND b.store = target_store
    AND a.id != b.id
    AND similarity(a.norm_name, b.norm_name) >= similarity_threshold
  ORDER BY similarity_score DESC;
END;
$$ LANGUAGE plpgsql;

-- Function to find Levenshtein matches between stores
CREATE OR REPLACE FUNCTION find_levenshtein_matches(
  source_store TEXT,
  target_store TEXT,
  max_distance INTEGER DEFAULT 5
)
RETURNS TABLE(
  source_id TEXT,
  target_id TEXT,
  source_name TEXT,
  target_name TEXT,
  source_price FLOAT,
  target_price FLOAT,
  edit_distance INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    a.id::TEXT as source_id,
    b.id::TEXT as target_id,
    a.name as source_name,
    b.name as target_name,
    a.price as source_price,
    b.price as target_price,
    levenshtein(a.norm_name, b.norm_name) as edit_distance
  FROM products a
  JOIN products b ON levenshtein(a.norm_name, b.norm_name) <= max_distance
  WHERE a.store = source_store 
    AND b.store = target_store
    AND a.id != b.id
  ORDER BY edit_distance ASC;
END;
$$ LANGUAGE plpgsql;

-- Function to find comprehensive matches using multiple techniques
CREATE OR REPLACE FUNCTION find_comprehensive_matches(
  similarity_threshold FLOAT DEFAULT 0.5
)
RETURNS TABLE(
  pakn_id TEXT,
  neww_id TEXT,
  wool_id TEXT,
  pakn_name TEXT,
  neww_name TEXT,
  wool_name TEXT,
  pakn_price FLOAT,
  neww_price FLOAT,
  wool_price FLOAT,
  avg_similarity FLOAT
) AS $$
BEGIN
  -- Set similarity threshold for trigram matching
  PERFORM set_limit(similarity_threshold);
  
  RETURN QUERY
  SELECT 
    a.id::TEXT as pakn_id,
    b.id::TEXT as neww_id,
    c.id::TEXT as wool_id,
    a.name as pakn_name,
    b.name as neww_name,
    c.name as wool_name,
    a.price as pakn_price,
    b.price as neww_price,
    c.price as wool_price,
    (similarity(a.norm_name, b.norm_name) + similarity(a.norm_name, c.norm_name)) / 2 as avg_similarity
  FROM products a
  JOIN products b ON a.norm_name % b.norm_name AND b.store = 'newworld'
  JOIN products c ON a.norm_name % c.norm_name AND c.store = 'woolworths'
  WHERE a.store = 'paknsave'
    AND similarity(a.norm_name, b.norm_name) >= similarity_threshold
    AND similarity(a.norm_name, c.norm_name) >= similarity_threshold
  ORDER BY avg_similarity DESC;
END;
$$ LANGUAGE plpgsql;

-- Function to create product matches table
CREATE OR REPLACE FUNCTION create_product_matches_table()
RETURNS void AS $$
BEGIN
  -- Drop existing table if it exists
  DROP TABLE IF EXISTS product_matches;
  
  -- Create comprehensive product matches table
  CREATE TABLE product_matches AS
  SELECT 
    a.id as pakn_id,
    b.id as neww_id,
    c.id as wool_id,
    a.name as pakn_name,
    b.name as neww_name,
    c.name as wool_name,
    a.price as pakn_price,
    b.price as neww_price,
    c.price as wool_price,
    similarity(a.norm_name, b.norm_name) as neww_similarity,
    similarity(a.norm_name, c.norm_name) as wool_similarity,
    LEAST(a.price, COALESCE(b.price, a.price), COALESCE(c.price, a.price)) as best_price,
    GREATEST(a.price, COALESCE(b.price, a.price), COALESCE(c.price, a.price)) - 
    LEAST(a.price, COALESCE(b.price, a.price), COALESCE(c.price, a.price)) as max_savings,
    now() as created_at
  FROM products a
  LEFT JOIN products b ON a.norm_name % b.norm_name 
    AND b.store = 'newworld' 
    AND similarity(a.norm_name, b.norm_name) >= 0.5
  LEFT JOIN products c ON a.norm_name % c.norm_name 
    AND c.store = 'woolworths' 
    AND similarity(a.norm_name, c.norm_name) >= 0.5
  WHERE a.store = 'paknsave'
    AND (b.id IS NOT NULL OR c.id IS NOT NULL); -- Only include products with matches
  
  -- Create indexes for better performance
  CREATE INDEX idx_product_matches_pakn_id ON product_matches(pakn_id);
  CREATE INDEX idx_product_matches_neww_id ON product_matches(neww_id);
  CREATE INDEX idx_product_matches_wool_id ON product_matches(wool_id);
  CREATE INDEX idx_product_matches_best_price ON product_matches(best_price);
  CREATE INDEX idx_product_matches_max_savings ON product_matches(max_savings DESC);
END;
$$ LANGUAGE plpgsql;

-- Function to refresh product matches (for periodic updates)
CREATE OR REPLACE FUNCTION refresh_product_matches()
RETURNS void AS $$
BEGIN
  -- Refresh normalized names first
  PERFORM create_normalized_names();
  
  -- Recreate matches table
  PERFORM create_product_matches_table();
END;
$$ LANGUAGE plpgsql;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION enable_fuzzystrmatch() TO authenticated;
GRANT EXECUTE ON FUNCTION enable_pg_trgm() TO authenticated;
GRANT EXECUTE ON FUNCTION create_normalized_names() TO authenticated;
GRANT EXECUTE ON FUNCTION find_trigram_matches(TEXT, TEXT, FLOAT) TO authenticated;
GRANT EXECUTE ON FUNCTION find_levenshtein_matches(TEXT, TEXT, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION find_comprehensive_matches(FLOAT) TO authenticated;
GRANT EXECUTE ON FUNCTION create_product_matches_table() TO authenticated;
GRANT EXECUTE ON FUNCTION refresh_product_matches() TO authenticated;

-- Grant table permissions
GRANT SELECT ON product_matches TO authenticated;
