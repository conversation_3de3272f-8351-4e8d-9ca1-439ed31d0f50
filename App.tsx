import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { StatusBar } from 'expo-status-bar';
import { View, Text, ActivityIndicator, Platform } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { AuthProvider } from './src/context/AuthContext';
import { ThemeProvider, useTheme } from './src/context/ThemeContext';
import { ShoppingProvider } from './src/context/ShoppingContext';
import { useAppBootstrap } from './src/hooks';
import { getStatusBarStyle } from './src/utils/platformUtils';
import { ErrorBoundary } from './src/components/ErrorBoundary';
import { 
  theme, 
  commonStyles 
} from './src/styles';

// Import screens
import { LoginScreen } from './src/screens/auth/LoginScreen';
import { RegisterScreen } from './src/screens/auth/RegisterScreen';
import { ForgotPasswordScreen } from './src/screens/auth/ForgotPasswordScreen';
import { OnboardingScreen } from './src/screens/onboarding/OnboardingScreen';
import { AllProductsScreen } from './src/screens/main/AllProductsScreen';
import AllProductsScreenMinimal from './src/screens/main/AllProductsScreenMinimal';
import { AllProductsScreenFixed } from './src/screens/main/AllProductsScreenFixed';
import { ShoppingListScreen } from './src/screens/main/ShoppingListScreen';
import { RecipesScreen } from './src/screens/main/RecipesScreen';
import { RecipesScreenSimple } from './src/screens/main/RecipesScreenSimple';
import { NeumorphismFriendsScreen } from './src/screens/main/NeumorphismFriendsScreen';

// Import new components
import { SwipeableAppContainer } from './src/components/SwipeableAppContainer';
import { ModernSwipeableContainer } from './src/components/ModernSwipeableContainer';
import { SimpleTestContainer } from './src/components/SimpleTestContainer';
import { SlideOutDrawer } from './src/components/SlideOutDrawer';

// Types
import { RootStackParamList, AuthStackParamList } from './src/types/navigation';

const RootStack = createStackNavigator<RootStackParamList>();
const AuthStack = createStackNavigator<AuthStackParamList>();

// Theme is now handled by ThemeProvider context

// Error logging function for tracking errors
const logError = (error: Error, errorInfo: any) => {
  console.error('🚨 App Error Logged:', {
    message: error.message,
    stack: error.stack,
    componentStack: errorInfo.componentStack,
    timestamp: new Date().toISOString(),
  });
  
  // In a production app, you might want to send this to a service like:
  // - Sentry
  // - Bugsnag
  // - Firebase Crashlytics
  // - Custom logging endpoint
  
  // Example (uncomment and configure as needed):
  // if (!__DEV__) {
  //   crashlytics().recordError(error);
  //   analytics().logEvent('app_error', {
  //     error_message: error.message,
  //     error_screen: 'unknown'
  //   });
  // }
};

// Auth Navigator
function AuthNavigator() {
  const { colors } = useTheme();
  
  return (
    <AuthStack.Navigator 
      screenOptions={{ 
        headerShown: false,
        cardStyle: { backgroundColor: colors.background }
      }}
    >
      <AuthStack.Screen name="Login" component={LoginScreen} />
      <AuthStack.Screen name="Register" component={RegisterScreen} />
      <AuthStack.Screen name="ForgotPassword" component={ForgotPasswordScreen} />
    </AuthStack.Navigator>
  );
}

// Main Navigator with Swipeable Interface
function MainNavigator() {
  const [showDrawer, setShowDrawer] = React.useState(false);
  
  const handleOpenDrawer = () => {
    setShowDrawer(true);
  };
  
  const handleCloseDrawer = () => {
    setShowDrawer(false);
  };
  
  const handleNavigateToProfile = () => {
    // Handle profile navigation
    console.log('Navigate to profile');
  };
  
  return (
    <>
      <ModernSwipeableContainer
        shoppingListComponent={<ShoppingListScreen />}
        allProductsComponent={<AllProductsScreenFixed />}
        cookbookComponent={<RecipesScreenSimple />}
        onOpenDrawer={handleOpenDrawer}
      />
      <SlideOutDrawer
        visible={showDrawer}
        onClose={handleCloseDrawer}
        onNavigateToProfile={handleNavigateToProfile}
      />
    </>
  );
}

// App Navigator Component
function AppNavigator() {
  const { colors } = useTheme();
  const { isAppReady, shouldShowOnboarding, isAuthenticated, error } = useAppBootstrap();

  // Show loading screen while app is initializing
  if (!isAppReady) {
    return (
      <View style={[
        commonStyles.loadingContainer,
        { backgroundColor: colors.background }
      ]}>
        <Text style={{ fontSize: 48, marginBottom: theme.spacing.lg }}>🍳</Text>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={[
          commonStyles.loadingText,
          { 
            color: colors.text,
            marginTop: theme.spacing.lg,
            fontSize: theme.typography.fontSize.base,
            fontWeight: theme.typography.fontWeight.medium,
          }
        ]}>
          Loading AI Recipe Planner...
        </Text>
        <Text style={[
          commonStyles.loadingSubtext,
          { 
            color: colors.textSecondary,
            marginTop: theme.spacing.xs,
          }
        ]}>
          {error ? 'Retrying...' : 'Preparing your culinary journey'}
        </Text>
        {error && (
          <Text style={[
            commonStyles.loadingSubtext,
            { 
              color: theme.colors.error,
              marginTop: theme.spacing.sm,
              textAlign: 'center',
            }
          ]}>
            {error}
          </Text>
        )}
      </View>
    );
  }

  return (
    <NavigationContainer>
      <RootStack.Navigator screenOptions={{ 
        headerShown: false,
        cardStyle: { backgroundColor: colors.background }
      }}>
        {!isAuthenticated ? (
          <RootStack.Screen name="Auth" component={AuthNavigator} />
        ) : shouldShowOnboarding ? (
          <RootStack.Screen name="Onboarding" component={OnboardingScreen} />
        ) : (
          <RootStack.Screen name="Main" component={MainNavigator} />
        )}
      </RootStack.Navigator>
    </NavigationContainer>
  );
}

// Root App Component
export default function App() {
  return (
    <SafeAreaProvider>
      <ThemeProvider>
        <ShoppingProvider>
          <AuthProvider>
            <ThemedApp />
          </AuthProvider>
        </ShoppingProvider>
      </ThemeProvider>
    </SafeAreaProvider>
  );
}

// Themed App Component (needs access to theme context)
function ThemedApp() {
  const { colors } = useTheme();
  
  return (
    <>
      <StatusBar 
        style={getStatusBarStyle() as any}
        backgroundColor={Platform.OS === 'android' ? colors.background : undefined}
      />
      <ErrorBoundary onError={logError}>
        <AppNavigator />
      </ErrorBoundary>
    </>
  );
}
