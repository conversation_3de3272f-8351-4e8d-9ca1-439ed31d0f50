/**
 * PRODUCT DATABASE DIAGNOSTIC SCRIPT
 * 
 * This script investigates why only 187 products are showing in the app
 * when it should display products from all three NZ supermarket chains.
 */

import { createClient } from '@supabase/supabase-js';

// Use hardcoded values for diagnostic (from architecture.md)
const supabaseUrl = 'https://emjwniuqwhvohjassnrg.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVtanduaXVxd2h2b2hqYXNzbnJnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjQwMTYwNSwiZXhwIjoyMDY3OTc3NjA1fQ.oTowRoNAjlUmk10O9pSMK2BvL0XlyBb5orVRrlEryHs';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

interface ProductCount {
  store: string;
  count: number;
  sample_products: any[];
}

interface DiagnosticResult {
  total_products: number;
  by_store: ProductCount[];
  availability_breakdown: any[];
  price_breakdown: any[];
  recent_products: any[];
  query_limits_found: string[];
}

async function diagnoseProductDatabase(): Promise<DiagnosticResult> {
  console.log('🔍 Starting Product Database Diagnostic...\n');

  // 1. Get total product count
  console.log('1. Checking total product count...');
  const { count: totalCount, error: countError } = await supabase
    .from('products')
    .select('*', { count: 'exact', head: true });

  if (countError) {
    console.error('Error getting total count:', countError);
    throw countError;
  }

  console.log(`   Total products in database: ${totalCount}`);

  // 2. Get count by store
  console.log('\n2. Checking products by store...');
  const storeBreakdown: ProductCount[] = [];
  
  for (const store of ['woolworths', 'newworld', 'paknsave']) {
    const { data: storeProducts, error: storeError } = await supabase
      .from('products')
      .select('id, name, price, brand, category, availability')
      .eq('store', store)
      .limit(5); // Get sample products

    if (storeError) {
      console.error(`Error getting ${store} products:`, storeError);
      continue;
    }

    const { count: storeCount } = await supabase
      .from('products')
      .select('*', { count: 'exact', head: true })
      .eq('store', store);

    storeBreakdown.push({
      store,
      count: storeCount || 0,
      sample_products: storeProducts || []
    });

    console.log(`   ${store}: ${storeCount || 0} products`);
    if (storeProducts && storeProducts.length > 0) {
      console.log(`      Sample: ${storeProducts[0].name} - $${storeProducts[0].price}`);
    }
  }

  // 3. Check availability breakdown
  console.log('\n3. Checking availability breakdown...');
  const { data: availabilityData, error: availError } = await supabase
    .from('products')
    .select('availability')
    .not('availability', 'is', null);

  const availabilityBreakdown = availabilityData?.reduce((acc: any, item) => {
    acc[item.availability] = (acc[item.availability] || 0) + 1;
    return acc;
  }, {}) || {};

  console.log('   Availability breakdown:', availabilityBreakdown);

  // 4. Check price breakdown (products with/without prices)
  console.log('\n4. Checking price data...');
  const { count: withPrices } = await supabase
    .from('products')
    .select('*', { count: 'exact', head: true })
    .not('price', 'is', null);

  const { count: withoutPrices } = await supabase
    .from('products')
    .select('*', { count: 'exact', head: true })
    .is('price', null);

  const priceBreakdown = [
    { type: 'with_prices', count: withPrices || 0 },
    { type: 'without_prices', count: withoutPrices || 0 }
  ];

  console.log(`   Products with prices: ${withPrices || 0}`);
  console.log(`   Products without prices: ${withoutPrices || 0}`);

  // 5. Get recent products to check data freshness
  console.log('\n5. Checking recent products...');
  const { data: recentProducts, error: recentError } = await supabase
    .from('products')
    .select('id, name, store, price, created_at, updated_at, last_updated')
    .order('created_at', { ascending: false })
    .limit(10);

  if (recentError) {
    console.error('Error getting recent products:', recentError);
  } else {
    console.log('   Most recent products:');
    recentProducts?.forEach((product, index) => {
      console.log(`      ${index + 1}. ${product.name} (${product.store}) - Created: ${product.created_at}`);
    });
  }

  // 6. Test the actual queries used by the app
  console.log('\n6. Testing app queries...');
  const queryLimitsFound: string[] = [];

  // Test productFetchService query (from AllProductsScreenFixed)
  const { data: fetchServiceResults, error: fetchError } = await supabase
    .from('products')
    .select('*')
    .order('name', { ascending: true })
    .limit(200);

  if (fetchError) {
    console.error('   productFetchService query error:', fetchError);
  } else {
    console.log(`   productFetchService query returned: ${fetchServiceResults?.length || 0} products`);
    if ((fetchServiceResults?.length || 0) < 200) {
      queryLimitsFound.push(`productFetchService: Only ${fetchServiceResults?.length} products returned (limit: 200)`);
    }
  }

  // Test useOptimizedProducts query
  const { data: optimizedResults, error: optimizedError } = await supabase
    .from('products')
    .select('*')
    .ilike('name', '%milk%')
    .not('price', 'is', null)
    .range(0, 19)
    .order('name', { ascending: true });

  if (optimizedError) {
    console.error('   useOptimizedProducts query error:', optimizedError);
  } else {
    console.log(`   useOptimizedProducts query (milk search) returned: ${optimizedResults?.length || 0} products`);
  }

  // Test smartSearchService query
  const { data: smartResults, error: smartError } = await supabase
    .from('products')
    .select('id, name, brand, category, store, price, image_url, description')
    .not('price', 'is', null)
    .order('price', { ascending: true })
    .limit(20);

  if (smartError) {
    console.error('   smartSearchService query error:', smartError);
  } else {
    console.log(`   smartSearchService query returned: ${smartResults?.length || 0} products`);
  }

  console.log('\n✅ Diagnostic complete!\n');

  return {
    total_products: totalCount || 0,
    by_store: storeBreakdown,
    availability_breakdown: Object.entries(availabilityBreakdown).map(([key, value]) => ({ [key]: value })),
    price_breakdown: priceBreakdown,
    recent_products: recentProducts || [],
    query_limits_found: queryLimitsFound
  };
}

// Run the diagnostic
if (require.main === module) {
  diagnoseProductDatabase()
    .then((result) => {
      console.log('📊 DIAGNOSTIC SUMMARY:');
      console.log('='.repeat(50));
      console.log(`Total Products: ${result.total_products}`);
      console.log('\nBy Store:');
      result.by_store.forEach(store => {
        console.log(`  ${store.store}: ${store.count} products`);
      });
      console.log('\nPrice Data:');
      result.price_breakdown.forEach(item => {
        console.log(`  ${item.type}: ${item.count}`);
      });
      if (result.query_limits_found.length > 0) {
        console.log('\n⚠️  Query Limitations Found:');
        result.query_limits_found.forEach(limit => {
          console.log(`  - ${limit}`);
        });
      }
      console.log('='.repeat(50));
    })
    .catch((error) => {
      console.error('❌ Diagnostic failed:', error);
    });
}

export { diagnoseProductDatabase };
