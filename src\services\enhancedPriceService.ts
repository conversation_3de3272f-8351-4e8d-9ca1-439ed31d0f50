/**
 * Enhanced Price Service
 * 
 * Combines Algolia search with scraped data for the most
 * accurate and up-to-date price information
 */

import { StoreSearchResult, PriceComparisonResult, priceComparisonService } from './priceComparisonService';
import { scrapedDataService, PriceChange } from './scrapedDataService';

export interface EnhancedPriceData {
  product: string;
  bestPrice: StoreSearchResult | null;
  allPrices: StoreSearchResult[];
  savings: number;
  priceChange?: PriceChange;
  dataSource: 'scraped' | 'algolia' | 'hybrid';
  lastUpdated: string;
  confidence: 'high' | 'medium' | 'low';
}

export interface PriceUpdateNotification {
  product: string;
  oldPrice: number;
  newPrice: number;
  store: string;
  changePercent: number;
  isSignificant: boolean;
}

class EnhancedPriceService {
  private updateListeners: Set<(notification: PriceUpdateNotification) => void> = new Set();

  constructor() {
    // Subscribe to scraped data updates
    scrapedDataService.onDataUpdate(() => {
      this.handleDataUpdate();
    });
  }

  private async handleDataUpdate() {
    try {
      const recentChanges = await scrapedDataService.getRecentPriceChanges();
      
      // Notify listeners about significant price changes
      recentChanges.forEach(change => {
        if (change.isSignificant) {
          const notification: PriceUpdateNotification = {
            product: change.product.name,
            oldPrice: change.previousPrice,
            newPrice: change.newPrice,
            store: change.product.storeName,
            changePercent: change.changePercent,
            isSignificant: change.isSignificant
          };
          
          this.notifyPriceUpdate(notification);
        }
      });
    } catch (error) {
      console.error('Error handling data update:', error);
    }
  }

  /**
   * Get comprehensive price data for a product using all available sources
   */
  async getEnhancedPriceData(productName: string, preferredBrand?: string): Promise<EnhancedPriceData> {
    try {
      // Try scraped data first (faster, more current)
      const scrapedComparison = await scrapedDataService.getPriceComparison(productName);
      const recentChanges = await scrapedDataService.getRecentPriceChanges();
      
      // Find relevant price change
      const priceChange = recentChanges.find(change => 
        change.product.name.toLowerCase().includes(productName.toLowerCase())
      );

      if (scrapedComparison.results.length > 0) {
        const allPrices = scrapedComparison.results;
        const bestPrice = scrapedComparison.bestPrice || null;
        const savings = scrapedComparison.savings || 0;

        return {
          product: productName,
          bestPrice,
          allPrices,
          savings,
          priceChange,
          dataSource: 'scraped',
          lastUpdated: new Date().toISOString(),
          confidence: 'high'
        };
      }

      // Fallback to Algolia if scraped data unavailable
      if (priceComparisonService.isConfigured()) {
        console.log('📡 Falling back to Algolia for:', productName);
        
        const algoliaResult = await priceComparisonService.searchProductAcrossStores(productName, {
          maxResults: 5,
          sortBy: 'price',
          brand: preferredBrand
        });

        return {
          product: productName,
          bestPrice: algoliaResult.cheapestOption || null,
          allPrices: algoliaResult.results,
          savings: algoliaResult.priceRange?.savings || 0,
          priceChange,
          dataSource: 'algolia',
          lastUpdated: new Date().toISOString(),
          confidence: 'medium'
        };
      }

      // No data available
      return {
        product: productName,
        bestPrice: null,
        allPrices: [],
        savings: 0,
        priceChange,
        dataSource: 'scraped',
        lastUpdated: new Date().toISOString(),
        confidence: 'low'
      };

    } catch (error) {
      console.error('Error getting enhanced price data:', error);
      
      return {
        product: productName,
        bestPrice: null,
        allPrices: [],
        savings: 0,
        dataSource: 'scraped',
        lastUpdated: new Date().toISOString(),
        confidence: 'low'
      };
    }
  }

  /**
   * Search for products with smart matching
   */
  async smartProductSearch(query: string, options: {
    maxResults?: number;
    preferredBrand?: string;
    category?: string;
  } = {}): Promise<StoreSearchResult[]> {
    const maxResults = options.maxResults || 5;

    try {
      // First try scraped data
      const scrapedResults = await scrapedDataService.searchProducts(query, maxResults);
      
      if (scrapedResults.length > 0) {
        return scrapedResults.slice(0, maxResults);
      }

      // Fallback to Algolia
      if (priceComparisonService.isConfigured()) {
        const algoliaResult = await priceComparisonService.searchProductAcrossStores(query, {
          maxResults,
          sortBy: 'price',
          brand: options.preferredBrand,
          category: options.category
        });
        
        return algoliaResult.results;
      }

      return [];
    } catch (error) {
      console.error('Error in smart product search:', error);
      return [];
    }
  }

  /**
   * Get product suggestions for autocomplete
   */
  getProductSuggestions(query: string, limit: number = 8): string[] {
    return scrapedDataService.getProductSuggestions(query, limit);
  }

  /**
   * Get data freshness information
   */
  getDataFreshness() {
    return scrapedDataService.getDataFreshness();
  }

  /**
   * Force refresh all price data
   */
  async refreshPriceData(): Promise<void> {
    await scrapedDataService.forceRefresh();
  }

  /**
   * Subscribe to price update notifications
   */
  onPriceUpdate(callback: (notification: PriceUpdateNotification) => void): () => void {
    this.updateListeners.add(callback);
    return () => this.updateListeners.delete(callback);
  }

  private notifyPriceUpdate(notification: PriceUpdateNotification) {
    this.updateListeners.forEach(callback => {
      try {
        callback(notification);
      } catch (error) {
        console.error('Error in price update listener:', error);
      }
    });
  }

  /**
   * Get recent price changes across all products
   */
  async getRecentPriceChanges(): Promise<PriceChange[]> {
    return scrapedDataService.getRecentPriceChanges();
  }

  /**
   * Check if service is ready to provide price data
   */
  isServiceReady(): boolean {
    return true; // Always ready as we have fallbacks
  }

  /**
   * Get service status
   */
  getServiceStatus() {
    const scrapedFreshness = scrapedDataService.getDataFreshness();
    const algoliaConfigured = priceComparisonService.isConfigured();
    
    return {
      scrapedDataAvailable: !scrapedFreshness.isStale,
      algoliaConfigured,
      lastScrapedUpdate: scrapedFreshness.lastUpdate,
      preferredSource: !scrapedFreshness.isStale ? 'scraped' : algoliaConfigured ? 'algolia' : 'none'
    };
  }
}

export const enhancedPriceService = new EnhancedPriceService(); 