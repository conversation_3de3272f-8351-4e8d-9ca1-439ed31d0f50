/**
 * Price Comparison Best Practices Example
 * 
 * This example demonstrates best practices for implementing price comparison logic
 * across multiple supermarkets using your app's data models and services.
 * 
 * Key Features Demonstrated:
 * - Multi-store price comparison
 * - Special pricing and discounts handling
 * - Price optimization strategies
 * - Error handling and edge cases
 * - Performance optimization techniques
 * - Type-safe implementations
 */

import { priceComparisonService } from '../services/priceComparisonService';
import type { Database } from '../supabase/types';

// ===== CORE DATA MODELS =====

/**
 * Enhanced Product interface matching your Supabase schema
 */
export interface Product {
  id: string;
  name: string;
  displayName?: string;
  description?: string;
  brand?: string;
  brandName?: string;
  
  // Price information
  price: number;
  currentPrice?: number;
  originalPrice?: number;
  salePrice?: number;
  unitPrice?: number;
  specialPrice?: number;
  isSpecial?: boolean;
  discountPercentage?: number;
  
  // Product details
  category?: string;
  subcategory?: string;
  size?: string;
  weight?: string;
  unit?: string;
  packSize?: string;
  
  // Store information
  store: 'woolworths' | 'newworld' | 'paknsave';
  storeName?: string;
  availability?: 'in_stock' | 'low_stock' | 'out_of_stock';
  isAvailable?: boolean;
  
  // Media and metadata
  imageUrl?: string;
  thumbnailUrl?: string;
  nutritionalInfo?: Record<string, any>;
  allergens?: string[];
  tags?: string[];
  
  // Promotions and pricing
  promotionType?: string;
  promotionDescription?: string;
  promotions?: any;
  
  // Quality indicators
  rating?: number;
  reviewCount?: number;
  isCommonItem?: boolean;
  
  // Timestamps
  lastUpdated?: string;
  createdAt?: string;
}

/**
 * Store-specific price information
 */
export interface StorePrice {
  store: 'woolworths' | 'newworld' | 'paknsave';
  storeName: string;
  storeColor: string;
  
  // Pricing
  price?: number;
  originalPrice?: number;
  specialPrice?: number;
  unitPrice?: number;
  isSpecial: boolean;
  discountPercentage?: number;
  
  // Availability
  available: boolean;
  stockLevel?: number;
  availability?: string;
  
  // Product specifics
  brand?: string;
  size?: string;
  unit?: string;
  imageUrl?: string;
  
  // Promotions
  promotionText?: string;
  promotionType?: string;
  validUntil?: string;
  
  // Metadata
  lastChecked?: string;
  relevanceScore?: number;
}

/**
 * Price comparison result for a single product
 */
export interface PriceComparisonResult {
  productName: string;
  searchQuery: string;
  storePrices: StorePrice[];
  
  // Analysis
  cheapestPrice?: number;
  cheapestStore?: string;
  mostExpensivePrice?: number;
  averagePrice: number;
  priceRange: number;
  potentialSavings: number;
  
  // Recommendations
  bestValueStore?: string;
  recommendedAction: 'buy_now' | 'wait_for_sale' | 'consider_alternatives';
  confidenceScore: number;
  
  // Metadata
  comparedAt: string;
  totalStoresChecked: number;
  availableStoresCount: number;
}

/**
 * Shopping list optimization result
 */
export interface ShoppingListOptimization {
  items: Array<{
    productName: string;
    comparison: PriceComparisonResult;
    selectedStore?: string;
    selectedPrice?: number;
    savings?: number;
  }>;
  
  // Cost breakdown
  totalCost: number;
  totalSavings: number;
  averageSavingsPerItem: number;
  
  // Store distribution
  storeBreakdown: Array<{
    store: string;
    storeName: string;
    itemCount: number;
    subtotal: number;
    percentage: number;
  }>;
  
  // Strategy analysis
  strategy: 'single_store' | 'multi_store' | 'hybrid';
  recommendedStores: string[];
  shoppingSessions: Array<{
    store: string;
    items: string[];
    subtotal: number;
    travelCost?: number;
  }>;
  
  // Quality metrics
  completionRate: number; // Percentage of items found
  unavailableItems: string[];
  alternativeSuggestions: Array<{
    originalItem: string;
    alternatives: string[];
  }>;
}

// ===== BEST PRACTICES IMPLEMENTATION =====

export class PriceComparisonBestPractices {
  
  /**
   * BEST PRACTICE 1: Comprehensive Product Search
   * 
   * Search for a product across all stores with intelligent matching
   * and fallback strategies for better results.
   */
  static async searchProductAcrossStores(
    query: string,
    options: {
      maxResults?: number;
      includeAlternatives?: boolean;
      strictMatching?: boolean;
      categoryFilter?: string;
      brandPreference?: string;
      priceRange?: { min: number; max: number };
    } = {}
  ): Promise<PriceComparisonResult> {
    
    try {
      console.log(`🔍 Starting comprehensive product search for: "${query}"`);
      
      // Step 1: Primary search with exact matching
      let searchResults = await priceComparisonService.searchProductAcrossStores(query, {
        maxResults: options.maxResults || 10,
        sortBy: 'price',
        includeOutOfStock: false,
        category: options.categoryFilter,
        brand: options.brandPreference
      });
      
      // Step 2: If no results, try fuzzy matching
      if (searchResults.results.length === 0 && !options.strictMatching) {
        console.log(`🔄 No exact matches found, trying fuzzy search...`);
        
        // Try with partial keywords
        const keywords = query.toLowerCase().split(' ').filter(word => word.length > 2);
        for (const keyword of keywords) {
          const fuzzyResults = await priceComparisonService.searchProductAcrossStores(keyword, {
            maxResults: 5,
            sortBy: 'relevance'
          });
          
          if (fuzzyResults.results.length > 0) {
            searchResults = fuzzyResults;
            break;
          }
        }
      }
      
      // Step 3: Convert to StorePrice format
      const storePrices: StorePrice[] = searchResults.results.map(result => ({
        store: result.store,
        storeName: result.storeName,
        storeColor: this.getStoreColor(result.store),
        price: result.product.price,
        originalPrice: result.product.price,
        specialPrice: result.product.specialPrice,
        isSpecial: result.product.isSpecial || false,
        available: true,
        brand: result.product.brand,
        size: result.product.unit,
        imageUrl: result.product.imageUrl,
        lastChecked: new Date().toISOString(),
        relevanceScore: result.relevanceScore || 1.0
      }));
      
      // Step 4: Add unavailable stores as empty entries
      const allStores: Array<'woolworths' | 'newworld' | 'paknsave'> = ['woolworths', 'newworld', 'paknsave'];
      const availableStores = storePrices.map(sp => sp.store);
      
      for (const store of allStores) {
        if (!availableStores.includes(store)) {
          storePrices.push({
            store,
            storeName: this.getStoreName(store),
            storeColor: this.getStoreColor(store),
            available: false,
            isSpecial: false,
            lastChecked: new Date().toISOString()
          });
        }
      }
      
      // Step 5: Calculate analysis metrics
      const availablePrices = storePrices
        .filter(sp => sp.available && sp.price)
        .map(sp => sp.specialPrice || sp.price!);
      
      const cheapestPrice = availablePrices.length > 0 ? Math.min(...availablePrices) : undefined;
      const mostExpensivePrice = availablePrices.length > 0 ? Math.max(...availablePrices) : undefined;
      const averagePrice = availablePrices.length > 0 
        ? availablePrices.reduce((sum, price) => sum + price, 0) / availablePrices.length 
        : 0;
      const priceRange = mostExpensivePrice && cheapestPrice ? mostExpensivePrice - cheapestPrice : 0;
      
      // Step 6: Determine recommendations
      const cheapestStore = storePrices.find(sp => 
        sp.available && (sp.specialPrice || sp.price) === cheapestPrice
      )?.store;
      
      const recommendedAction = this.determineRecommendedAction(storePrices, averagePrice);
      const confidenceScore = this.calculateConfidenceScore(storePrices, searchResults.results.length);
      
      return {
        productName: query,
        searchQuery: query,
        storePrices,
        cheapestPrice,
        cheapestStore,
        mostExpensivePrice,
        averagePrice,
        priceRange,
        potentialSavings: priceRange,
        bestValueStore: cheapestStore,
        recommendedAction,
        confidenceScore,
        comparedAt: new Date().toISOString(),
        totalStoresChecked: allStores.length,
        availableStoresCount: storePrices.filter(sp => sp.available).length
      };
      
    } catch (error) {
      console.error(`❌ Error in product search for "${query}":`, error);
      throw new Error(`Failed to search for product: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
  
  /**
   * BEST PRACTICE 2: Intelligent Shopping List Optimization
   * 
   * Optimize an entire shopping list considering multiple factors:
   * - Total cost minimization
   * - Travel convenience
   * - Store preferences
   * - Special offers and promotions
   */
  static async optimizeShoppingList(
    items: string[],
    options: {
      strategy?: 'cheapest_total' | 'single_store' | 'convenience' | 'balanced';
      maxStores?: number;
      preferredStores?: string[];
      includeSpecials?: boolean;
      considerTravelCost?: boolean;
      travelCostPerStore?: number;
    } = {}
  ): Promise<ShoppingListOptimization> {
    
    try {
      console.log(`🛒 Optimizing shopping list with ${items.length} items using ${options.strategy || 'balanced'} strategy`);
      
      // Step 1: Get price comparisons for all items
      const itemComparisons = await Promise.all(
        items.map(async (item) => {
          const comparison = await this.searchProductAcrossStores(item, {
            maxResults: 3,
            includeAlternatives: true
          });
          return { productName: item, comparison };
        })
      );
      
      // Step 2: Filter out unavailable items
      const availableComparisons = itemComparisons.filter(
        ({ comparison }) => comparison.storePrices.some(sp => sp.available)
      );
      
      const unavailableItems = itemComparisons
        .filter(({ comparison }) => !comparison.storePrices.some(sp => sp.available))
        .map(({ productName }) => productName);
      
      // Step 3: Apply optimization strategy
      let optimizedItems: Array<{
        productName: string;
        comparison: PriceComparisonResult;
        selectedStore?: string;
        selectedPrice?: number;
        savings?: number;
      }> = [];
      
      switch (options.strategy) {
        case 'cheapest_total':
          optimizedItems = this.optimizeForCheapestTotal(availableComparisons);
          break;
        case 'single_store':
          optimizedItems = this.optimizeForSingleStore(availableComparisons, options.preferredStores);
          break;
        case 'convenience':
          optimizedItems = this.optimizeForConvenience(availableComparisons, options.maxStores || 2);
          break;
        default:
          optimizedItems = this.optimizeBalanced(availableComparisons, options);
      }
      
      // Step 4: Calculate totals and breakdown
      const totalCost = optimizedItems.reduce((sum, item) => sum + (item.selectedPrice || 0), 0);
      const totalSavings = optimizedItems.reduce((sum, item) => sum + (item.savings || 0), 0);
      const averageSavingsPerItem = optimizedItems.length > 0 ? totalSavings / optimizedItems.length : 0;
      
      // Step 5: Create store breakdown
      const storeBreakdown = this.createStoreBreakdown(optimizedItems, totalCost);
      
      // Step 6: Determine shopping sessions
      const shoppingSessions = this.createShoppingSessions(optimizedItems, options.travelCostPerStore);
      
      // Step 7: Calculate completion rate
      const completionRate = availableComparisons.length / items.length;
      
      // Step 8: Generate alternative suggestions
      const alternativeSuggestions = this.generateAlternativeSuggestions(unavailableItems);
      
      return {
        items: optimizedItems,
        totalCost,
        totalSavings,
        averageSavingsPerItem,
        storeBreakdown,
        strategy: this.determineActualStrategy(storeBreakdown),
        recommendedStores: storeBreakdown.slice(0, 3).map(sb => sb.store),
        shoppingSessions,
        completionRate,
        unavailableItems,
        alternativeSuggestions
      };
      
    } catch (error) {
      console.error(`❌ Error optimizing shopping list:`, error);
      throw new Error(`Failed to optimize shopping list: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
  
  /**
   * BEST PRACTICE 3: Real-time Price Monitoring
   * 
   * Monitor prices for specific products and alert when prices drop
   * or when better deals become available.
   */
  static async createPriceAlert(
    productName: string,
    targetPrice: number,
    options: {
      stores?: string[];
      includeSpecials?: boolean;
      alertThreshold?: number; // Percentage below target price
    } = {}
  ) {
    try {
      console.log(`🔔 Creating price alert for "${productName}" with target price $${targetPrice}`);
      
      const comparison = await this.searchProductAcrossStores(productName);
      const threshold = options.alertThreshold || 0.1; // 10% below target
      const alertPrice = targetPrice * (1 - threshold);
      
      // Check current prices against alert criteria
      const alerts = comparison.storePrices
        .filter(sp => sp.available && sp.price)
        .map(sp => {
          const currentPrice = sp.specialPrice || sp.price!;
          const isAlert = currentPrice <= alertPrice;
          const savingsAmount = targetPrice - currentPrice;
          const savingsPercentage = (savingsAmount / targetPrice) * 100;
          
          return {
            store: sp.store,
            storeName: sp.storeName,
            currentPrice,
            targetPrice,
            isAlert,
            savingsAmount: isAlert ? savingsAmount : 0,
            savingsPercentage: isAlert ? savingsPercentage : 0,
            alertType: sp.isSpecial ? 'special_offer' : 'price_drop',
            message: isAlert 
              ? `${sp.storeName}: ${productName} is now $${currentPrice.toFixed(2)} (${savingsPercentage.toFixed(1)}% below target)`
              : `${sp.storeName}: ${productName} is $${currentPrice.toFixed(2)} (above target of $${targetPrice.toFixed(2)})`
          };
        });
      
      const activeAlerts = alerts.filter(alert => alert.isAlert);
      
      return {
        productName,
        targetPrice,
        alertPrice,
        currentBestPrice: comparison.cheapestPrice,
        activeAlerts,
        allPrices: alerts,
        hasAlerts: activeAlerts.length > 0,
        bestAlert: activeAlerts.length > 0 
          ? activeAlerts.reduce((best, current) => 
              current.savingsAmount > best.savingsAmount ? current : best
            )
          : null,
        checkedAt: new Date().toISOString()
      };
      
    } catch (error) {
      console.error(`❌ Error creating price alert for "${productName}":`, error);
      throw error;
    }
  }
  
  /**
   * BEST PRACTICE 4: Store Performance Analytics
   * 
   * Analyze which stores consistently offer the best prices
   * for specific categories or overall shopping patterns.
   */
  static async analyzeStorePerformance(
    products: string[],
    timeframe?: { start: string; end: string }
  ) {
    try {
      console.log(`📊 Analyzing store performance for ${products.length} products`);
      
      // Get comparisons for all products
      const comparisons = await Promise.all(
        products.map(product => this.searchProductAcrossStores(product))
      );
      
      // Calculate store statistics
      const storeStats: Record<string, {
        store: string;
        storeName: string;
        productsAvailable: number;
        averagePrice: number;
        cheapestCount: number;
        totalSavings: number;
        reliability: number;
        specialOffersCount: number;
        averageRating: number;
      }> = {};
      
      const stores = ['woolworths', 'newworld', 'paknsave'];
      
      // Initialize store stats
      stores.forEach(store => {
        storeStats[store] = {
          store,
          storeName: this.getStoreName(store),
          productsAvailable: 0,
          averagePrice: 0,
          cheapestCount: 0,
          totalSavings: 0,
          reliability: 0,
          specialOffersCount: 0,
          averageRating: 0
        };
      });
      
      // Calculate metrics
      comparisons.forEach(comparison => {
        const availablePrices = comparison.storePrices.filter(sp => sp.available && sp.price);
        const cheapestPrice = Math.min(...availablePrices.map(sp => sp.specialPrice || sp.price!));
        
        comparison.storePrices.forEach(storePrice => {
          const stats = storeStats[storePrice.store];
          
          if (storePrice.available && storePrice.price) {
            stats.productsAvailable++;
            stats.averagePrice = (stats.averagePrice + (storePrice.specialPrice || storePrice.price)) / 2;
            
            // Check if this store has the cheapest price
            if ((storePrice.specialPrice || storePrice.price) === cheapestPrice) {
              stats.cheapestCount++;
            }
            
            // Calculate savings compared to average
            const avgPrice = comparison.averagePrice;
            const currentPrice = storePrice.specialPrice || storePrice.price;
            if (currentPrice < avgPrice) {
              stats.totalSavings += (avgPrice - currentPrice);
            }
            
            // Count special offers
            if (storePrice.isSpecial) {
              stats.specialOffersCount++;
            }
          }
        });
      });
      
      // Calculate final metrics
      Object.values(storeStats).forEach(stats => {
        stats.reliability = products.length > 0 ? stats.productsAvailable / products.length : 0;
        stats.averageRating = this.calculateStoreRating(stats);
      });
      
      // Sort by performance
      const rankedStores = Object.values(storeStats).sort((a, b) => b.averageRating - a.averageRating);
      
      return {
        analysisDate: new Date().toISOString(),
        productCount: products.length,
        timeframe,
        storePerformance: rankedStores,
        summary: {
          bestOverallStore: rankedStores[0],
          mostReliableStore: rankedStores.reduce((best, current) => 
            current.reliability > best.reliability ? current : best
          ),
          cheapestStore: rankedStores.reduce((best, current) => 
            current.averagePrice < best.averagePrice ? current : best
          ),
          bestForSpecials: rankedStores.reduce((best, current) => 
            current.specialOffersCount > best.specialOffersCount ? current : best
          )
        },
        recommendations: this.generateStoreRecommendations(rankedStores)
      };
      
    } catch (error) {
      console.error('❌ Error analyzing store performance:', error);
      throw error;
    }
  }
  
  // ===== PRIVATE HELPER METHODS =====
  
  private static getStoreColor(store: string): string {
    const colors = {
      woolworths: '#00A651',
      newworld: '#E31E24',
      paknsave: '#FFD100'
    };
    return colors[store as keyof typeof colors] || '#666666';
  }
  
  private static getStoreName(store: string): string {
    const names = {
      woolworths: 'Woolworths',
      newworld: 'New World',
      paknsave: "Pak'nSave"
    };
    return names[store as keyof typeof names] || store;
  }
  
  private static determineRecommendedAction(
    storePrices: StorePrice[], 
    averagePrice: number
  ): 'buy_now' | 'wait_for_sale' | 'consider_alternatives' {
    const cheapestPrice = Math.min(
      ...storePrices.filter(sp => sp.available && sp.price).map(sp => sp.specialPrice || sp.price!)
    );
    
    const hasSpecials = storePrices.some(sp => sp.isSpecial);
    const priceVariation = storePrices.filter(sp => sp.available && sp.price).length;
    
    if (hasSpecials && cheapestPrice < averagePrice * 0.8) {
      return 'buy_now';
    } else if (priceVariation < 2) {
      return 'consider_alternatives';
    } else {
      return 'wait_for_sale';
    }
  }
  
  private static calculateConfidenceScore(storePrices: StorePrice[], resultCount: number): number {
    const availableStores = storePrices.filter(sp => sp.available).length;
    const maxStores = 3;
    const storeScore = availableStores / maxStores;
    const dataScore = Math.min(resultCount / 5, 1); // Normalize to max 5 results
    
    return (storeScore + dataScore) / 2;
  }
  
  private static optimizeForCheapestTotal(
    comparisons: Array<{ productName: string; comparison: PriceComparisonResult }>
  ) {
    return comparisons.map(({ productName, comparison }) => {
      const cheapestStore = comparison.storePrices
        .filter(sp => sp.available && sp.price)
        .reduce((cheapest, current) => {
          const cheapestPrice = cheapest.specialPrice || cheapest.price!;
          const currentPrice = current.specialPrice || current.price!;
          return currentPrice < cheapestPrice ? current : cheapest;
        });
      
      const selectedPrice = cheapestStore.specialPrice || cheapestStore.price!;
      const savings = comparison.averagePrice - selectedPrice;
      
      return {
        productName,
        comparison,
        selectedStore: cheapestStore.store,
        selectedPrice,
        savings: savings > 0 ? savings : 0
      };
    });
  }
  
  private static optimizeForSingleStore(
    comparisons: Array<{ productName: string; comparison: PriceComparisonResult }>,
    preferredStores?: string[]
  ) {
    // Calculate total cost per store
    const storeTotals: Record<string, { total: number; items: number; available: number }> = {};
    
    comparisons.forEach(({ comparison }) => {
      comparison.storePrices.forEach(sp => {
        if (!storeTotals[sp.store]) {
          storeTotals[sp.store] = { total: 0, items: 0, available: 0 };
        }
        
        if (sp.available && sp.price) {
          storeTotals[sp.store].total += sp.specialPrice || sp.price;
          storeTotals[sp.store].available++;
        }
        storeTotals[sp.store].items++;
      });
    });
    
    // Find best store considering availability and cost
    const bestStore = Object.entries(storeTotals)
      .filter(([store, data]) => data.available > 0)
      .sort((a, b) => {
        // Prioritize availability, then cost
        const availabilityA = a[1].available / a[1].items;
        const availabilityB = b[1].available / b[1].items;
        
        if (Math.abs(availabilityA - availabilityB) > 0.1) {
          return availabilityB - availabilityA;
        }
        
        return a[1].total - b[1].total;
      })[0]?.[0];
    
    return comparisons.map(({ productName, comparison }) => {
      const storePrice = comparison.storePrices.find(sp => sp.store === bestStore);
      const selectedPrice = storePrice?.available ? (storePrice.specialPrice || storePrice.price) : undefined;
      
      return {
        productName,
        comparison,
        selectedStore: bestStore,
        selectedPrice,
        savings: 0 // Single store optimization doesn't focus on savings
      };
    });
  }
  
  private static optimizeForConvenience(
    comparisons: Array<{ productName: string; comparison: PriceComparisonResult }>,
    maxStores: number
  ) {
    // Group items by their cheapest stores
    const storeGroups: Record<string, Array<{ productName: string; comparison: PriceComparisonResult; price: number }>> = {};
    
    comparisons.forEach(({ productName, comparison }) => {
      const cheapestStore = comparison.storePrices
        .filter(sp => sp.available && sp.price)
        .reduce((cheapest, current) => {
          const cheapestPrice = cheapest.specialPrice || cheapest.price!;
          const currentPrice = current.specialPrice || current.price!;
          return currentPrice < cheapestPrice ? current : cheapest;
        });
      
      const store = cheapestStore.store;
      if (!storeGroups[store]) {
        storeGroups[store] = [];
      }
      
      storeGroups[store].push({
        productName,
        comparison,
        price: cheapestStore.specialPrice || cheapestStore.price!
      });
    });
    
    // Select top stores by item count and total savings
    const topStores = Object.entries(storeGroups)
      .sort((a, b) => b[1].length - a[1].length)
      .slice(0, maxStores)
      .map(([store]) => store);
    
    return comparisons.map(({ productName, comparison }) => {
      // Find best available option within selected stores
      const availableOptions = comparison.storePrices
        .filter(sp => sp.available && sp.price && topStores.includes(sp.store));
      
      if (availableOptions.length === 0) {
        // Fall back to cheapest option if not available in selected stores
        const cheapestStore = comparison.storePrices
          .filter(sp => sp.available && sp.price)
          .reduce((cheapest, current) => {
            const cheapestPrice = cheapest.specialPrice || cheapest.price!;
            const currentPrice = current.specialPrice || current.price!;
            return currentPrice < cheapestPrice ? current : cheapest;
          });
        
        return {
          productName,
          comparison,
          selectedStore: cheapestStore.store,
          selectedPrice: cheapestStore.specialPrice || cheapestStore.price!,
          savings: 0
        };
      }
      
      const bestOption = availableOptions.reduce((best, current) => {
        const bestPrice = best.specialPrice || best.price!;
        const currentPrice = current.specialPrice || current.price!;
        return currentPrice < bestPrice ? current : best;
      });
      
      return {
        productName,
        comparison,
        selectedStore: bestOption.store,
        selectedPrice: bestOption.specialPrice || bestOption.price!,
        savings: comparison.averagePrice - (bestOption.specialPrice || bestOption.price!)
      };
    });
  }
  
  private static optimizeBalanced(
    comparisons: Array<{ productName: string; comparison: PriceComparisonResult }>,
    options: any
  ) {
    // Balance between cost savings and convenience
    const storeFrequency: Record<string, number> = {};
    
    // Count how often each store has the cheapest option
    comparisons.forEach(({ comparison }) => {
      if (comparison.cheapestStore) {
        storeFrequency[comparison.cheapestStore] = (storeFrequency[comparison.cheapestStore] || 0) + 1;
      }
    });
    
    // If one store dominates (>60% of cheapest items), use single store strategy
    const dominantStore = Object.entries(storeFrequency)
      .reduce((a, b) => (storeFrequency[a[0]] || 0) > (storeFrequency[b[0]] || 0) ? a : b);
    
    if ((dominantStore[1] || 0) >= comparisons.length * 0.6) {
      return this.optimizeForSingleStore(comparisons, [dominantStore[0]]);
    } else {
      return this.optimizeForCheapestTotal(comparisons);
    }
  }
  
  private static createStoreBreakdown(
    items: Array<{ selectedStore?: string; selectedPrice?: number }>,
    totalCost: number
  ) {
    const breakdown: Record<string, { itemCount: number; subtotal: number }> = {};
    
    items.forEach(item => {
      if (item.selectedStore && item.selectedPrice) {
        if (!breakdown[item.selectedStore]) {
          breakdown[item.selectedStore] = { itemCount: 0, subtotal: 0 };
        }
        breakdown[item.selectedStore].itemCount++;
        breakdown[item.selectedStore].subtotal += item.selectedPrice;
      }
    });
    
    return Object.entries(breakdown).map(([store, data]) => ({
      store,
      storeName: this.getStoreName(store),
      itemCount: data.itemCount,
      subtotal: data.subtotal,
      percentage: totalCost > 0 ? (data.subtotal / totalCost) * 100 : 0
    })).sort((a, b) => b.subtotal - a.subtotal);
  }
  
  private static createShoppingSessions(
    items: Array<{ productName: string; selectedStore?: string; selectedPrice?: number }>,
    travelCostPerStore?: number
  ) {
    const sessions: Record<string, { items: string[]; subtotal: number }> = {};
    
    items.forEach(item => {
      if (item.selectedStore && item.selectedPrice) {
        if (!sessions[item.selectedStore]) {
          sessions[item.selectedStore] = { items: [], subtotal: 0 };
        }
        sessions[item.selectedStore].items.push(item.productName);
        sessions[item.selectedStore].subtotal += item.selectedPrice;
      }
    });
    
    return Object.entries(sessions).map(([store, data]) => ({
      store,
      items: data.items,
      subtotal: data.subtotal,
      travelCost: travelCostPerStore
    }));
  }
  
  private static determineActualStrategy(
    storeBreakdown: Array<{ store: string; itemCount: number; percentage: number }>
  ): 'single_store' | 'multi_store' | 'hybrid' {
    if (storeBreakdown.length === 1) {
      return 'single_store';
    } else if (storeBreakdown[0]?.percentage >= 70) {
      return 'hybrid';
    } else {
      return 'multi_store';
    }
  }
  
  private static generateAlternativeSuggestions(unavailableItems: string[]) {
    // This would typically use AI or a product mapping service
    // For now, return generic suggestions
    return unavailableItems.map(item => ({
      originalItem: item,
      alternatives: [
        `${item} (generic brand)`,
        `${item} (different size)`,
        `Similar to ${item}`
      ]
    }));
  }
  
  private static calculateStoreRating(stats: any): number {
    // Weighted scoring system
    const reliabilityWeight = 0.3;
    const priceWeight = 0.4;
    const specialsWeight = 0.3;
    
    const reliabilityScore = stats.reliability;
    const priceScore = stats.cheapestCount / Math.max(stats.productsAvailable, 1);
    const specialsScore = stats.specialOffersCount / Math.max(stats.productsAvailable, 1);
    
    return (reliabilityScore * reliabilityWeight) + 
           (priceScore * priceWeight) + 
           (specialsScore * specialsWeight);
  }
  
  private static generateStoreRecommendations(rankedStores: any[]) {
    return [
      `${rankedStores[0]?.storeName} is your best overall choice with consistent prices and availability`,
      `Consider ${rankedStores[1]?.storeName} for special offers and promotions`,
      `${rankedStores[2]?.storeName} may have competitive prices on specific categories`
    ];
  }
}

// ===== USAGE EXAMPLES =====

/**
 * Example 1: Basic Product Price Comparison
 */
export async function exampleBasicPriceComparison() {
  try {
    console.log('\n=== Example 1: Basic Price Comparison ===');
    
    const comparison = await PriceComparisonBestPractices.searchProductAcrossStores('milk 2L', {
      maxResults: 5,
      includeAlternatives: true
    });
    
    console.log(`Product: ${comparison.productName}`);
    console.log(`Cheapest: $${comparison.cheapestPrice?.toFixed(2)} at ${comparison.cheapestStore}`);
    console.log(`Average: $${comparison.averagePrice.toFixed(2)}`);
    console.log(`Potential savings: $${comparison.potentialSavings.toFixed(2)}`);
    console.log(`Recommendation: ${comparison.recommendedAction}`);
    
    comparison.storePrices.forEach(sp => {
      if (sp.available) {
        console.log(`  ${sp.storeName}: $${(sp.specialPrice || sp.price)?.toFixed(2)} ${sp.isSpecial ? '(Special)' : ''}`);
      } else {
        console.log(`  ${sp.storeName}: Not available`);
      }
    });
    
    return comparison;
  } catch (error) {
    console.error('Example 1 failed:', error);
  }
}

/**
 * Example 2: Shopping List Optimization
 */
export async function exampleShoppingListOptimization() {
  try {
    console.log('\n=== Example 2: Shopping List Optimization ===');
    
    const shoppingList = [
      'bread',
      'milk 2L',
      'eggs dozen',
      'bananas 1kg',
      'chicken breast',
      'pasta 500g',
      'tomatoes 1kg'
    ];
    
    const optimization = await PriceComparisonBestPractices.optimizeShoppingList(shoppingList, {
      strategy: 'balanced',
      maxStores: 2,
      includeSpecials: true
    });
    
    console.log(`Total cost: $${optimization.totalCost.toFixed(2)}`);
    console.log(`Total savings: $${optimization.totalSavings.toFixed(2)}`);
    console.log(`Strategy: ${optimization.strategy}`);
    console.log(`Completion rate: ${(optimization.completionRate * 100).toFixed(1)}%`);
    
    console.log('\nStore breakdown:');
    optimization.storeBreakdown.forEach(store => {
      console.log(`  ${store.storeName}: ${store.itemCount} items, $${store.subtotal.toFixed(2)} (${store.percentage.toFixed(1)}%)`);
    });
    
    if (optimization.unavailableItems.length > 0) {
      console.log('\nUnavailable items:', optimization.unavailableItems.join(', '));
    }
    
    return optimization;
  } catch (error) {
    console.error('Example 2 failed:', error);
  }
}

/**
 * Example 3: Price Alert System
 */
export async function examplePriceAlert() {
  try {
    console.log('\n=== Example 3: Price Alert ===');
    
    const alert = await PriceComparisonBestPractices.createPriceAlert('olive oil 500ml', 8.00, {
      alertThreshold: 0.15, // Alert when 15% below target
      includeSpecials: true
    });
    
    console.log(`Alert for: ${alert.productName}`);
    console.log(`Target price: $${alert.targetPrice.toFixed(2)}`);
    console.log(`Alert threshold: $${alert.alertPrice.toFixed(2)}`);
    console.log(`Current best: $${alert.currentBestPrice?.toFixed(2)}`);
    console.log(`Active alerts: ${alert.activeAlerts.length}`);
    
    if (alert.hasAlerts) {
      console.log('\n🔔 Price Alerts:');
      alert.activeAlerts.forEach(alertItem => {
        console.log(`  ${alertItem.message}`);
      });
    } else {
      console.log('\n😴 No alerts at this time');
    }
    
    return alert;
  } catch (error) {
    console.error('Example 3 failed:', error);
  }
}

/**
 * Example 4: Store Performance Analysis
 */
export async function exampleStorePerformanceAnalysis() {
  try {
    console.log('\n=== Example 4: Store Performance Analysis ===');
    
    const products = [
      'bread', 'milk', 'eggs', 'bananas', 'chicken breast',
      'pasta', 'rice', 'potatoes', 'onions', 'tomatoes'
    ];
    
    const analysis = await PriceComparisonBestPractices.analyzeStorePerformance(products);
    
    console.log(`Analysis of ${analysis.productCount} products`);
    console.log(`Date: ${analysis.analysisDate}`);
    
    console.log('\nStore Rankings:');
    analysis.storePerformance.forEach((store, index) => {
      console.log(`${index + 1}. ${store.storeName}`);
      console.log(`   Reliability: ${(store.reliability * 100).toFixed(1)}%`);
      console.log(`   Cheapest count: ${store.cheapestCount}/${analysis.productCount}`);
      console.log(`   Special offers: ${store.specialOffersCount}`);
      console.log(`   Rating: ${store.averageRating.toFixed(2)}/1.0`);
    });
    
    console.log('\nRecommendations:');
    analysis.recommendations.forEach(rec => console.log(`• ${rec}`));
    
    return analysis;
  } catch (error) {
    console.error('Example 4 failed:', error);
  }
}

// ===== ADVANCED PATTERNS =====

/**
 * Pattern: Batch Processing for Large Shopping Lists
 */
export async function batchProcessLargeShoppingList(items: string[], batchSize = 10) {
  const batches = [];
  for (let i = 0; i < items.length; i += batchSize) {
    batches.push(items.slice(i, i + batchSize));
  }
  
  const results = [];
  for (const batch of batches) {
    const batchResult = await PriceComparisonBestPractices.optimizeShoppingList(batch);
    results.push(batchResult);
    
    // Add delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  return results;
}

/**
 * Pattern: Caching for Frequently Searched Products
 */
export class PriceComparisonCache {
  private static cache = new Map<string, { data: any; expires: number }>();
  private static CACHE_DURATION = 15 * 60 * 1000; // 15 minutes
  
  static async getCachedComparison(productName: string) {
    const cacheKey = productName.toLowerCase();
    const cached = this.cache.get(cacheKey);
    
    if (cached && cached.expires > Date.now()) {
      console.log(`📋 Cache hit for "${productName}"`);
      return cached.data;
    }
    
    console.log(`🔄 Cache miss for "${productName}", fetching fresh data`);
    const fresh = await PriceComparisonBestPractices.searchProductAcrossStores(productName);
    
    this.cache.set(cacheKey, {
      data: fresh,
      expires: Date.now() + this.CACHE_DURATION
    });
    
    return fresh;
  }
  
  static clearCache() {
    this.cache.clear();
  }
  
  static getCacheStats() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }
}

/**
 * Pattern: Error Recovery and Fallback Strategies
 */
export async function robustPriceComparison(productName: string) {
  const strategies = [
    // Strategy 1: Exact match
    () => PriceComparisonBestPractices.searchProductAcrossStores(productName, { strictMatching: true }),
    
    // Strategy 2: Fuzzy match
    () => PriceComparisonBestPractices.searchProductAcrossStores(productName, { strictMatching: false }),
    
    // Strategy 3: Keywords only
    () => {
      const keywords = productName.split(' ').filter(word => word.length > 2);
      return PriceComparisonBestPractices.searchProductAcrossStores(keywords[0]);
    },
    
    // Strategy 4: Category search
    () => PriceComparisonBestPractices.searchProductAcrossStores('', { categoryFilter: 'food' })
  ];
  
  for (let i = 0; i < strategies.length; i++) {
    try {
      console.log(`🔍 Trying search strategy ${i + 1} for "${productName}"`);
      const result = await strategies[i]();
      
      if (result.storePrices.some(sp => sp.available)) {
        console.log(`✅ Strategy ${i + 1} succeeded`);
        return result;
      }
    } catch (error) {
      console.log(`❌ Strategy ${i + 1} failed:`, error);
      if (i === strategies.length - 1) {
        throw error;
      }
    }
  }
  
  throw new Error(`All search strategies failed for "${productName}"`);
}

// Export all examples for easy testing
export const examples = {
  basicComparison: exampleBasicPriceComparison,
  shoppingListOptimization: exampleShoppingListOptimization,
  priceAlert: examplePriceAlert,
  storeAnalysis: exampleStorePerformanceAnalysis
};

console.log('📚 Price Comparison Best Practices loaded! Use examples object to run demonstrations.');