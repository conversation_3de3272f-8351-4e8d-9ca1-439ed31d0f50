// Simple Node.js test script to verify product deduplication functionality
const { productDeduplicationService } = require('./src/services/productDeduplicationService');

// Mock products data with similar products across different stores
const mockProducts = [
  {
    id: '1',
    name: 'Anchor Milk 1L',
    price: 3.50,
    store: 'woolworths',
    brand: 'Anchor',
    size: '1L',
    category: 'dairy',
    unit: '1 litre',
    is_available: true
  },
  {
    id: '2',
    name: 'Anchor Milk 1L',
    price: 3.20,
    store: 'newworld',
    brand: 'Anchor',
    size: '1L',
    category: 'dairy',
    unit: '1 litre',
    is_available: true
  },
  {
    id: '3',
    name: 'Anchor Milk 1L',
    price: 2.99,
    store: 'paknsave',
    brand: 'Anchor',
    size: '1L',
    category: 'dairy',
    unit: '1 litre',
    is_available: true
  },
  {
    id: '4',
    name: 'Kelloggs Cornflakes 500g',
    price: 5.50,
    store: 'woolworths',
    brand: 'Kelloggs',
    size: '500g',
    category: 'breakfast',
    unit: '500 grams',
    is_available: true
  },
  {
    id: '5',
    name: 'Kelloggs Cornflakes 500g',
    price: 5.25,
    store: 'newworld',
    brand: 'Kelloggs',
    size: '500g',
    category: 'breakfast',
    unit: '500 grams',
    is_available: true
  },
  {
    id: '6',
    name: 'Coca Cola 2L',
    price: 3.99,
    store: 'woolworths',
    brand: 'Coca Cola',
    size: '2L',
    category: 'drinks',
    unit: '2 litres',
    is_available: true
  }
];

console.log('🧪 Testing Product Deduplication Service');
console.log('=======================================');

console.log(`📦 Input: ${mockProducts.length} products from ${new Set(mockProducts.map(p => p.store)).size} stores`);

try {
  // Test the deduplication service
  const unifiedProducts = productDeduplicationService.groupProducts(mockProducts);
  
  console.log(`✅ Output: ${unifiedProducts.length} unified products`);
  console.log('');
  
  // Display results for each unified product
  unifiedProducts.forEach((product, index) => {
    console.log(`Product ${index + 1}: ${product.name}`);
    console.log(`  Brand: ${product.brand}`);
    console.log(`  Category: ${product.category}`);
    console.log(`  Available in: ${product.allStores.join(', ')}`);
    console.log(`  Prices:`);
    
    Object.entries(product.storePrices).forEach(([store, price]) => {
      const indicator = price === product.lowestPrice ? ' 🏆 BEST' : '';
      console.log(`    ${store}: $${price.toFixed(2)}${indicator}`);
    });
    
    console.log(`  Price range: $${product.lowestPrice.toFixed(2)} - $${product.highestPrice.toFixed(2)}`);
    console.log(`  Max savings: $${product.maxSavings.toFixed(2)} (${product.savingsPercentage}%)`);
    console.log(`  Best store: ${product.bestStore}`);
    console.log(`  Confidence: ${(product.confidence * 100).toFixed(0)}%`);
    console.log('');
  });
  
  // Validation tests
  console.log('🔍 Validation Tests:');
  
  // Test 1: Should have 3 unified products (Anchor Milk, Kelloggs Cornflakes, Coca Cola)
  const expectedCount = 3;
  if (unifiedProducts.length === expectedCount) {
    console.log(`✅ Correct number of unified products: ${expectedCount}`);
  } else {
    console.log(`❌ Expected ${expectedCount} unified products, got ${unifiedProducts.length}`);
  }
  
  // Test 2: Anchor Milk should have 3 stores with correct best price
  const anchorMilk = unifiedProducts.find(p => p.name.includes('Anchor Milk'));
  if (anchorMilk) {
    if (anchorMilk.allStores.length === 3 && anchorMilk.lowestPrice === 2.99 && anchorMilk.bestStore === 'paknsave') {
      console.log(`✅ Anchor Milk correctly aggregated: 3 stores, best price $2.99 at paknsave`);
    } else {
      console.log(`❌ Anchor Milk aggregation failed`);
    }
  }
  
  // Test 3: Kelloggs Cornflakes should have 2 stores
  const cornflakes = unifiedProducts.find(p => p.name.includes('Cornflakes'));
  if (cornflakes && cornflakes.allStores.length === 2) {
    console.log(`✅ Kelloggs Cornflakes correctly aggregated: 2 stores`);
  } else {
    console.log(`❌ Kelloggs Cornflakes aggregation failed`);
  }
  
  // Test 4: Coca Cola should have 1 store (no deduplication needed)
  const cocaCola = unifiedProducts.find(p => p.name.includes('Coca Cola'));
  if (cocaCola && cocaCola.allStores.length === 1) {
    console.log(`✅ Coca Cola correctly handled: 1 store (no deduplication needed)`);
  } else {
    console.log(`❌ Coca Cola handling failed`);
  }
  
  console.log('');
  console.log('🎉 Product deduplication test completed successfully!');
  
} catch (error) {
  console.error('❌ Test failed:', error.message);
  console.error(error.stack);
}