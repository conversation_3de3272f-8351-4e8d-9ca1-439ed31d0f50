import { IProduct } from '../types/shoppingList';

// Mock product data for local development without Supabase
export const mockProducts: IProduct[] = [
  // Milk products - different sizes and brands
  {
    id: '1',
    name: 'Anchor Blue Milk 1L',
    price: 3.49,
    store: 'woolworths',
    category: 'Dairy',
    brand: 'Anchor',
    size: '1L',
    unit: '1L',
    image_url: '',
    is_available: true,
    is_on_sale: false,
    last_updated: new Date().toISOString()
  },
  {
    id: '2',
    name: 'Anchor Blue Milk 1 Litre',
    price: 3.59,
    store: 'newworld',
    category: 'Dairy',
    brand: 'Anchor',
    size: '1L',
    unit: '1L',
    image_url: '',
    is_available: true,
    is_on_sale: false,
    last_updated: new Date().toISOString()
  },
  {
    id: '3',
    name: 'Anchor Blue Milk 1L',
    price: 3.29,
    store: 'paknsave',
    category: 'Dairy',
    brand: 'Anchor',
    size: '1L',
    unit: '1L',
    image_url: '',
    is_available: true,
    is_on_sale: true,
    last_updated: new Date().toISOString()
  },
  {
    id: '4',
    name: 'Anchor Blue Milk 2L',
    price: 6.49,
    store: 'woolworths',
    category: 'Dairy',
    brand: 'Anchor',
    size: '2L',
    unit: '2L',
    image_url: '',
    is_available: true,
    is_on_sale: false,
    last_updated: new Date().toISOString()
  },
  {
    id: '5',
    name: 'Anchor Blue Milk 2 Litre',
    price: 6.59,
    store: 'newworld',
    category: 'Dairy',
    brand: 'Anchor',
    size: '2L',
    unit: '2L',
    image_url: '',
    is_available: true,
    is_on_sale: false,
    last_updated: new Date().toISOString()
  },
  {
    id: '6',
    name: 'Anchor Blue Milk 2L',
    price: 6.19,
    store: 'paknsave',
    category: 'Dairy',
    brand: 'Anchor',
    size: '2L',
    unit: '2L',
    image_url: '',
    is_available: true,
    is_on_sale: true,
    last_updated: new Date().toISOString()
  },

  // Bread products
  {
    id: '7',
    name: 'Vogels Original Mixed Grain Bread 750g',
    price: 4.99,
    store: 'woolworths',
    category: 'Bakery',
    brand: 'Vogels',
    size: '750g',
    unit: '750g',
    image_url: '',
    is_available: true,
    is_on_sale: false,
    last_updated: new Date().toISOString()
  },
  {
    id: '8',
    name: 'Vogels Original Mixed Grain 750g',
    price: 5.19,
    store: 'newworld',
    category: 'Bakery',
    brand: 'Vogels',
    size: '750g',
    unit: '750g',
    image_url: '',
    is_available: true,
    is_on_sale: false,
    last_updated: new Date().toISOString()
  },
  {
    id: '9',
    name: 'Vogels Original Mixed Grain Bread 750g',
    price: 4.79,
    store: 'paknsave',
    category: 'Bakery',
    brand: 'Vogels',
    size: '750g',
    unit: '750g',
    image_url: '',
    is_available: true,
    is_on_sale: true,
    last_updated: new Date().toISOString()
  },

  // Apples - per kg products
  {
    id: '10',
    name: 'Royal Gala Apples',
    price: 4.99,
    store: 'woolworths',
    category: 'Fresh Produce',
    brand: '',
    size: 'per kg',
    unit: 'per kg',
    image_url: '',
    is_available: true,
    is_on_sale: false,
    last_updated: new Date().toISOString()
  },
  {
    id: '11',
    name: 'Royal Gala Apples',
    price: 5.19,
    store: 'newworld',
    category: 'Fresh Produce',
    brand: '',
    size: 'per kg',
    unit: 'per kg',
    image_url: '',
    is_available: true,
    is_on_sale: false,
    last_updated: new Date().toISOString()
  },
  {
    id: '12',
    name: 'Royal Gala Apples',
    price: 4.49,
    store: 'paknsave',
    category: 'Fresh Produce',
    brand: '',
    size: 'per kg',
    unit: 'per kg',
    image_url: '',
    is_available: true,
    is_on_sale: true,
    last_updated: new Date().toISOString()
  },

  // Bananas
  {
    id: '13',
    name: 'Bananas',
    price: 3.99,
    store: 'woolworths',
    category: 'Fresh Produce',
    brand: '',
    size: 'per kg',
    unit: 'per kg',
    image_url: '',
    is_available: true,
    is_on_sale: false,
    last_updated: new Date().toISOString()
  },
  {
    id: '14',
    name: 'Bananas',
    price: 4.19,
    store: 'newworld',
    category: 'Fresh Produce',
    brand: '',
    size: 'per kg',
    unit: 'per kg',
    image_url: '',
    is_available: true,
    is_on_sale: false,
    last_updated: new Date().toISOString()
  },
  {
    id: '15',
    name: 'Bananas',
    price: 3.79,
    store: 'paknsave',
    category: 'Fresh Produce',
    brand: '',
    size: 'per kg',
    unit: 'per kg',
    image_url: '',
    is_available: true,
    is_on_sale: true,
    last_updated: new Date().toISOString()
  },

  // Chicken products
  {
    id: '16',
    name: 'Tegel Chicken Breast Fillets 1kg',
    price: 12.99,
    store: 'woolworths',
    category: 'Meat & Seafood',
    brand: 'Tegel',
    size: '1kg',
    unit: '1kg',
    image_url: '',
    is_available: true,
    is_on_sale: false,
    last_updated: new Date().toISOString()
  },
  {
    id: '17',
    name: 'Tegel Chicken Breast Fillets 1kg',
    price: 13.49,
    store: 'newworld',
    category: 'Meat & Seafood', 
    brand: 'Tegel',
    size: '1kg',
    unit: '1kg',
    image_url: '',
    is_available: true,
    is_on_sale: false,
    last_updated: new Date().toISOString()
  },
  {
    id: '18',
    name: 'Tegel Chicken Breast Fillets 1kg',
    price: 11.99,
    store: 'paknsave',
    category: 'Meat & Seafood',
    brand: 'Tegel',
    size: '1kg',
    unit: '1kg',
    image_url: '',
    is_available: true,
    is_on_sale: true,
    last_updated: new Date().toISOString()
  },

  // Rice products
  {
    id: '19',
    name: 'SunRice Long Grain White Rice 2kg',
    price: 6.99,
    store: 'woolworths',
    category: 'Pantry',
    brand: 'SunRice',
    size: '2kg',
    unit: '2kg',
    image_url: '',
    is_available: true,
    is_on_sale: false,
    last_updated: new Date().toISOString()
  },
  {
    id: '20',
    name: 'SunRice Long Grain White Rice 2kg',
    price: 7.19,
    store: 'newworld',
    category: 'Pantry',
    brand: 'SunRice',
    size: '2kg',
    unit: '2kg',
    image_url: '',
    is_available: true,
    is_on_sale: false,
    last_updated: new Date().toISOString()
  },
  {
    id: '21',
    name: 'SunRice Long Grain White Rice 2kg',
    price: 6.49,
    store: 'paknsave',
    category: 'Pantry',
    brand: 'SunRice',
    size: '2kg',
    unit: '2kg',
    image_url: '',
    is_available: true,
    is_on_sale: true,
    last_updated: new Date().toISOString()
  },

  // Pasta products
  {
    id: '22',
    name: 'Barilla Spaghetti 500g',
    price: 2.99,
    store: 'woolworths',
    category: 'Pantry',
    brand: 'Barilla',
    size: '500g',
    unit: '500g',
    image_url: '',
    is_available: true,
    is_on_sale: false,
    last_updated: new Date().toISOString()
  },
  {
    id: '23',
    name: 'Barilla Spaghetti 500g',
    price: 3.19,
    store: 'newworld',
    category: 'Pantry',
    brand: 'Barilla',
    size: '500g',
    unit: '500g',
    image_url: '',
    is_available: true,
    is_on_sale: false,
    last_updated: new Date().toISOString()
  },
  {
    id: '24',
    name: 'Barilla Spaghetti 500g',
    price: 2.79,
    store: 'paknsave',
    category: 'Pantry',
    brand: 'Barilla',
    size: '500g',
    unit: '500g',
    image_url: '',
    is_available: true,
    is_on_sale: true,
    last_updated: new Date().toISOString()
  }
];

// Mock categories for filtering
export const mockCategories = [
  'Dairy',
  'Bakery', 
  'Fresh Produce',
  'Meat & Seafood',
  'Pantry'
];

// Mock stores
export const mockStores = [
  { id: 'woolworths', name: 'woolworths', displayName: 'Woolworths' },
  { id: 'newworld', name: 'newworld', displayName: 'New World' },
  { id: 'paknsave', name: 'paknsave', displayName: "Pak'nSave" }
];