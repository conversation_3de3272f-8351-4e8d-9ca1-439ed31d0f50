import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';

// Simple mock recipe data
const mockRecipes = [
  {
    id: '1',
    title: 'Chicken Stir Fry',
    description: 'Quick and healthy chicken stir fry with vegetables',
    prep_time: 15,
    cook_time: 10,
    difficulty: 'Easy',
    cuisine: 'Asian',
    ingredients: ['Chicken breast', 'Mixed vegetables', 'Soy sauce', 'Garlic', 'Ginger'],
  },
  {
    id: '2',
    title: 'Pasta Carbonara',
    description: 'Classic Italian pasta with eggs, cheese, and pancetta',
    prep_time: 10,
    cook_time: 15,
    difficulty: 'Medium',
    cuisine: 'Italian',
    ingredients: ['Spaghetti', 'Eggs', 'Parmesan cheese', 'Pancetta', 'Black pepper'],
  },
  {
    id: '3',
    title: 'Green Smoothie',
    description: 'Healthy breakfast smoothie with spinach and fruit',
    prep_time: 5,
    cook_time: 0,
    difficulty: 'Easy',
    cuisine: 'Healthy',
    ingredients: ['Spinach', 'Banana', 'Apple', 'Almond milk', 'Honey'],
  },
];

const CATEGORIES = [
  { id: 'All', label: 'All Recipes' },
  { id: 'Breakfast', label: 'Breakfast' },
  { id: 'Lunch', label: 'Lunch' },
  { id: 'Dinner', label: 'Dinner' },
  { id: 'Snacks', label: 'Snacks' },
];

export const RecipesScreenSimple: React.FC = () => {
  const { colors } = useTheme();
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [recipes] = useState(mockRecipes);

  const renderRecipeCard = ({ item }: { item: any }) => (
    <View style={[styles.recipeCard, { backgroundColor: colors.surface }]}>
      <View style={[styles.recipePlaceholder, { backgroundColor: colors.border }]}>
        <Ionicons name="restaurant" size={32} color={colors.textSecondary} />
      </View>
      
      <View style={styles.recipeInfo}>
        <Text style={[styles.recipeTitle, { color: colors.text }]} numberOfLines={2}>
          {item.title}
        </Text>
        
        <Text style={[styles.recipeDescription, { color: colors.textSecondary }]} numberOfLines={2}>
          {item.description}
        </Text>
        
        <View style={styles.recipeMetadata}>
          <View style={styles.metadataItem}>
            <Ionicons name="time" size={14} color={colors.textSecondary} />
            <Text style={[styles.metadataText, { color: colors.textSecondary }]}>
              {item.prep_time + item.cook_time}m
            </Text>
          </View>
          
          <View style={styles.metadataItem}>
            <Ionicons name="star" size={14} color={colors.textSecondary} />
            <Text style={[styles.metadataText, { color: colors.textSecondary }]}>
              {item.difficulty}
            </Text>
          </View>
        </View>
        
        <Text style={[styles.cuisineTag, { color: colors.primary, backgroundColor: colors.primary + '20' }]}>
          {item.cuisine}
        </Text>
      </View>
    </View>
  );

  const renderHeader = () => (
    <View style={styles.header}>
      <Text style={[styles.headerTitle, { color: colors.text }]}>
        Cookbook
      </Text>
      
      <View style={[styles.searchContainer, { backgroundColor: colors.surface, borderColor: colors.border }]}>
        <Ionicons name="search" size={20} color={colors.textSecondary} />
        <Text style={[styles.searchPlaceholder, { color: colors.textSecondary }]}>
          Search recipes... (Demo Mode)
        </Text>
      </View>
      
      <View style={styles.categoriesContainer}>
        {CATEGORIES.map(category => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryPill,
              {
                backgroundColor: selectedCategory === category.id ? colors.primary : colors.surface,
                borderColor: colors.border,
              }
            ]}
            onPress={() => setSelectedCategory(category.id)}
          >
            <Text style={[
              styles.categoryText,
              { 
                color: selectedCategory === category.id ? colors.background : colors.text 
              }
            ]}>
              {category.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
      
      <Text style={[styles.resultsText, { color: colors.textSecondary }]}>
        {recipes.length} recipes found (Demo Mode)
      </Text>
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <FlatList
        data={recipes}
        renderItem={renderRecipeCard}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        numColumns={2}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  listContainer: {
    padding: 16,
  },
  header: {
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 16,
  },
  searchPlaceholder: {
    marginLeft: 12,
    fontSize: 16,
  },
  categoriesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  categoryPill: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '500',
  },
  resultsText: {
    fontSize: 14,
    marginTop: 8,
  },
  recipeCard: {
    flex: 1,
    margin: 4,
    padding: 12,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  recipePlaceholder: {
    height: 80,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  recipeInfo: {
    flex: 1,
  },
  recipeTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  recipeDescription: {
    fontSize: 12,
    marginBottom: 8,
    lineHeight: 16,
  },
  recipeMetadata: {
    flexDirection: 'row',
    marginBottom: 8,
    gap: 12,
  },
  metadataItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metadataText: {
    fontSize: 12,
  },
  cuisineTag: {
    fontSize: 10,
    fontWeight: '600',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    alignSelf: 'flex-start',
  },
});