/**
 * Clean Shopping List Adapter
 * 
 * This service adapts the existing enhanced shopping list data format
 * to work with the new clean, minimalistic shopping list interface.
 */

import { ShoppingListItem, enhancedShoppingListService } from './enhancedShoppingListService';
import { scrapedDataService, ScrapedProduct } from './scrapedDataService';
import { EnhancedShoppingListItem, StorePrice } from '../types/shoppingList';

export interface CleanStorePrice {
  store: 'woolworths' | 'newworld' | 'paknsave';
  price?: number;
  available: boolean;
  brand?: string;
  size?: string;
}

export interface CleanShoppingListItem {
  id: string;
  name: string;
  checked: boolean;
  quantity: number;
  unit?: string;
  category?: string;
  storePrices: CleanStorePrice[];
  addedAt: Date;
  originalItem?: EnhancedShoppingListItem; // Reference to original for compatibility
}

export class CleanShoppingListAdapter {
  /**
   * Convert enhanced shopping list items to clean format
   */
  static async convertToCleanFormat(
    enhancedItems: EnhancedShoppingListItem[]
  ): Promise<CleanShoppingListItem[]> {
    const cleanItems: CleanShoppingListItem[] = [];

    for (const item of enhancedItems) {
      const cleanItem = await this.convertSingleItem(item);
      if (cleanItem) {
        cleanItems.push(cleanItem);
      }
    }

    return cleanItems;
  }

  /**
   * Convert a single enhanced item to clean format
   */
  static async convertSingleItem(
    item: EnhancedShoppingListItem
  ): Promise<CleanShoppingListItem | null> {
    try {
      // Get price data for all stores
      const storePrices = await this.getStorePricesForItem(item);

      return {
        id: item.id,
        name: item.name,
        checked: item.checked,
        quantity: item.quantity || 1,
        unit: item.unit,
        category: item.category,
        storePrices,
        addedAt: item.addedAt,
        originalItem: item,
      };
    } catch (error) {
      console.error('Failed to convert item to clean format:', error);
      return null;
    }
  }

  /**
   * Get price data for all stores for a given item
   */
  static async getStorePricesForItem(
    item: EnhancedShoppingListItem
  ): Promise<CleanStorePrice[]> {
    const storePrices: CleanStorePrice[] = [];
    const stores: Array<'woolworths' | 'newworld' | 'paknsave'> = [
      'woolworths',
      'newworld', 
      'paknsave'
    ];

    for (const store of stores) {
      const storePrice = await this.getStorePriceData(item, store);
      storePrices.push(storePrice);
    }

    return storePrices;
  }

  /**
   * Get price data for a specific store
   */
  static async getStorePriceData(
    item: EnhancedShoppingListItem,
    store: 'woolworths' | 'newworld' | 'paknsave'
  ): Promise<CleanStorePrice> {
    try {
      // First try to get price from existing enhanced shopping list data
      if (item.priceData?.allStorePrices) {
        const existingPrice = item.priceData.allStorePrices.find(
          p => p.store === store && p.available
        );
        
        if (existingPrice) {
          return {
            store,
            price: existingPrice.price,
            available: true,
            brand: this.extractBrandForStore(item.name, store),
            size: existingPrice.size || item.selectedSize,
          };
        }
      }

      // Fallback to scraped data service
      const scrapedData = await this.searchScrapedData(item.baseProduct || item.name, store);
      if (scrapedData) {
        return {
          store,
          price: scrapedData.currentPrice,
          available: true,
          brand: scrapedData.brand,
          size: scrapedData.unit,
        };
      }

      // No data available
      return {
        store,
        price: undefined,
        available: false,
      };
    } catch (error) {
      console.error(`Failed to get price data for ${store}:`, error);
      return {
        store,
        price: undefined,
        available: false,
      };
    }
  }

  /**
   * Search scraped data for a product at a specific store
   */
  static async searchScrapedData(
    productName: string,
    store: 'woolworths' | 'newworld' | 'paknsave'
  ): Promise<ScrapedProduct | null> {
    try {
      // This is a simplified search - in reality you'd want more sophisticated matching
      const searchTerm = productName.toLowerCase().trim();
      
      // Get scraped data for the store
      const scrapedSnapshot = await scrapedDataService.getLatestData();
      const storeProducts = scrapedSnapshot.stores[store] || [];

      // Find matching product
      const matchingProduct = storeProducts.find(product => 
        product.name.toLowerCase().includes(searchTerm) ||
        product.brand.toLowerCase().includes(searchTerm)
      );

      return matchingProduct || null;
    } catch (error) {
      console.error('Failed to search scraped data:', error);
      return null;
    }
  }

  /**
   * Extract brand name for a specific store based on common patterns
   */
  static extractBrandForStore(
    productName: string,
    store: 'woolworths' | 'newworld' | 'paknsave'
  ): string {
    const name = productName.toLowerCase();
    
    // Store-specific brand patterns
    const brandPatterns = {
      woolworths: ['woolworths', 'essentials', 'countdown'],
      paknsave: ['pams', 'budget', 'value'],
      newworld: ['value', 'budget', 'signature range'],
    };

    const storePatterns = brandPatterns[store];
    for (const pattern of storePatterns) {
      if (name.includes(pattern)) {
        return pattern.charAt(0).toUpperCase() + pattern.slice(1);
      }
    }

    // Extract first word as brand fallback
    const words = productName.split(' ').filter(w => w.length > 0);
    return words.length > 0 ? words[0] : 'Unknown';
  }

  /**
   * Convert clean item back to enhanced format for compatibility
   */
  static convertToEnhancedFormat(
    cleanItem: CleanShoppingListItem
  ): EnhancedShoppingListItem {
    // If we have the original item, use it as base
    if (cleanItem.originalItem) {
      return {
        ...cleanItem.originalItem,
        checked: cleanItem.checked,
        quantity: cleanItem.quantity,
      };
    }

    // Otherwise create a new enhanced item
    return {
      id: cleanItem.id,
      name: cleanItem.name,
      baseProduct: cleanItem.name,
      checked: cleanItem.checked,
      quantity: cleanItem.quantity,
      unit: cleanItem.unit,
      category: cleanItem.category || 'Groceries',
      addedAt: cleanItem.addedAt,
      brandSelectionStatus: 'none',
      storePrices: cleanItem.storePrices.map(sp => ({
        store: sp.store,
        price: sp.price,
        available: sp.available,
        brand: sp.brand,
        size: sp.size,
      })),
      priceData: this.buildPriceDataFromStorePrices(cleanItem.storePrices) || {
        allStorePrices: [],
        selectedPrice: 0,
        selectedStore: 'woolworths',
        selectedStoreName: 'Woolworths',
        potentialSavings: 0,
      },
    };
  }

  /**
   * Build price data from store prices for enhanced format compatibility
   */
  static buildPriceDataFromStorePrices(storePrices: CleanStorePrice[]): {
    allStorePrices: StorePrice[];
    selectedPrice: number;
    selectedStore: string;
    selectedStoreName: string;
    potentialSavings: number;
  } | undefined {
    const availablePrices = storePrices.filter(sp => sp.available && sp.price);
    
    if (availablePrices.length === 0) {
      return undefined;
    }

    const lowestPriceStore = availablePrices.reduce((lowest, current) => 
      (current.price! < lowest.price!) ? current : lowest
    );

    return {
      selectedPrice: lowestPriceStore.price!,
      selectedStore: lowestPriceStore.store,
      selectedStoreName: this.getStoreName(lowestPriceStore.store),
      allStorePrices: storePrices.map(sp => ({
        store: sp.store,
        price: sp.price || 0,
        available: sp.available,
        size: sp.size,
      })),
      potentialSavings: availablePrices.length > 1 
        ? Math.max(...availablePrices.map(sp => sp.price!)) - lowestPriceStore.price!
        : 0,
    };
  }

  /**
   * Get display name for store
   */
  static getStoreName(store: 'woolworths' | 'newworld' | 'paknsave'): string {
    const storeNames = {
      woolworths: 'Woolworths',
      newworld: 'New World',
      paknsave: "Pak'nSave",
    };
    return storeNames[store];
  }

  /**
   * Generate empty data list - no more demo data
   */
  static generateCleanDemoData(): CleanShoppingListItem[] {
    return [];
  }
}