import { productFetchService } from './productFetchService';
import { priceComparisonService } from './priceComparisonService';

export interface SmartProductMatch {
  productName: string; // The user's input like "milk"
  matches: ProductBrand[];
  totalFound: number;
  category: string;
  commonAliases: string[];
}

export interface ProductBrand {
  brand: string;
  displayName: string; // Full product name
  bestPrice: number;
  bestPriceStore: string;
  availability: {
    woolworths: { available: boolean; price?: number; };
    newworld: { available: boolean; price?: number; };
    paknsave: { available: boolean; price?: number; };
  };
  size: string;
  category: string;
  isPopular: boolean; // Is this a commonly bought brand?
  storeCount: number; // How many stores have this brand
}

// Common product patterns that users search for
const PRODUCT_PATTERNS = {
  // Dairy Products
  milk: {
    keywords: ['milk', 'fresh milk', 'whole milk', 'trim milk', 'lite milk', 'full cream', 'standard milk'],
    category: 'Dairy',
    popularBrands: ['Anchor', 'Meadow Fresh', 'Lewis Road', 'Mainland', 'Puhoi Valley'],
    aliases: ['milk', 'whole milk', 'skim milk', 'low fat milk']
  },
  butter: {
    keywords: ['butter', 'salted butter', 'unsalted butter'],
    category: 'Dairy', 
    popularBrands: ['Anchor', 'Mainland', 'Tararua', 'Lewis Road'],
    aliases: ['butter', 'salted butter', 'unsalted butter']
  },
  cheese: {
    keywords: ['cheese', 'cheddar', 'tasty cheese', 'mild cheese', 'colby'],
    category: 'Dairy',
    popularBrands: ['Mainland', 'Anchor', 'Kapiti', 'Edam'],
    aliases: ['cheese', 'cheddar cheese', 'tasty cheese']
  },
  yogurt: {
    keywords: ['yogurt', 'yoghurt', 'greek yogurt', 'natural yogurt'],
    category: 'Dairy',
    popularBrands: ['Anchor', 'Yoplait', 'Five AM', 'Fresh n Fruity'],
    aliases: ['yogurt', 'greek yogurt', 'natural yogurt']
  },

  // Beverages
  coffee: {
    keywords: ['coffee', 'instant coffee', 'ground coffee', 'coffee beans'],
    category: 'Beverages',
    popularBrands: ['Nescafe', 'Moccona', 'Robert Harris', 'L\'affare', 'Gregg\'s'],
    aliases: ['coffee', 'instant coffee', 'ground coffee', 'coffee beans']
  },
  tea: {
    keywords: ['tea', 'tea bags', 'black tea', 'green tea'],
    category: 'Beverages',
    popularBrands: ['Twinings', 'Dilmah', 'Bell Tea', 'Lipton'],
    aliases: ['tea', 'tea bags', 'black tea']
  },

  // Bread & Bakery
  bread: {
    keywords: ['bread', 'white bread', 'wholemeal bread', 'sandwich bread', 'sliced bread'],
    category: 'Bakery',
    popularBrands: ['Tip Top', 'Wonder White', 'Molenberg', 'Mighty Soft'],
    aliases: ['bread', 'white bread', 'brown bread', 'sandwich bread']
  },

  // Protein
  chicken: {
    keywords: ['chicken', 'chicken breast', 'chicken thigh', 'whole chicken'],
    category: 'Meat',
    popularBrands: ['Tegel', 'Inghams', 'Fresh Choice', 'Free Range'],
    aliases: ['chicken', 'chicken breast', 'chicken pieces']
  },
  beef: {
    keywords: ['beef', 'mince', 'ground beef', 'beef mince', 'steak'],
    category: 'Meat',
    popularBrands: ['Hellers', 'Angus', 'Premium', 'Silver Fern Farms'],
    aliases: ['beef', 'mince', 'ground beef', 'steak']
  },

  // Produce
  bananas: {
    keywords: ['bananas', 'banana'],
    category: 'Produce',
    popularBrands: ['Dole', 'Chiquita', 'Fresh Choice', 'Local'],
    aliases: ['bananas', 'banana']
  },
  apples: {
    keywords: ['apples', 'apple', 'royal gala', 'granny smith', 'braeburn'],
    category: 'Produce',
    popularBrands: ['Jazz', 'Envy', 'Local', 'Rockit'],
    aliases: ['apples', 'royal gala apples', 'granny smith apples']
  },

  // Pantry
  rice: {
    keywords: ['rice', 'white rice', 'jasmine rice', 'basmati rice'],
    category: 'Pantry',
    popularBrands: ['Sunrice', 'Jasmine', 'Uncle Ben\'s', 'Basmati'],
    aliases: ['rice', 'white rice', 'jasmine rice']
  },
  pasta: {
    keywords: ['pasta', 'spaghetti', 'penne', 'fettuccine'],
    category: 'Pantry',
    popularBrands: ['Barilla', 'San Remo', 'De Cecco', 'La Molisana'],
    aliases: ['pasta', 'spaghetti', 'penne pasta']
  },
  oil: {
    keywords: ['oil', 'olive oil', 'vegetable oil', 'cooking oil'],
    category: 'Pantry',
    popularBrands: ['Olivado', 'Cobram Estate', 'Bertolli', 'Colavita'],
    aliases: ['oil', 'olive oil', 'cooking oil']
  }
};

class SmartProductMatcherService {
  
  /**
   * The main function that intelligently matches user input to actual products
   */
  async findMatches(userInput: string): Promise<SmartProductMatch> {
    console.log(`🧠 Smart matcher: Finding matches for "${userInput}"`);
    
    // Normalize the input
    const normalizedInput = userInput.toLowerCase().trim();
    
    // Find the best pattern match
    const pattern = this.findBestPattern(normalizedInput);
    
    if (pattern) {
      console.log(`✅ Found pattern match: ${pattern.key}`);
      return await this.searchWithPattern(userInput, pattern.key, pattern.data);
    } else {
      console.log(`🔍 No pattern match, using generic search for: ${userInput}`);
      return await this.genericSearch(userInput);
    }
  }

  /**
   * Find the best matching pattern for the user input
   */
  private findBestPattern(input: string): { key: string; data: any } | null {
    for (const [key, pattern] of Object.entries(PRODUCT_PATTERNS)) {
      // Check if input matches any keywords
      if (pattern.keywords.some(keyword => 
        input.includes(keyword) || keyword.includes(input)
      )) {
        return { key, data: pattern };
      }
      
      // Check if input matches any aliases
      if (pattern.aliases.some(alias => 
        input.includes(alias) || alias.includes(input)
      )) {
        return { key, data: pattern };
      }
    }
    return null;
  }

  /**
   * Search using a specific pattern (like milk, coffee, etc.)
   */
  private async searchWithPattern(
    userInput: string, 
    patternKey: string, 
    pattern: any
  ): Promise<SmartProductMatch> {
    console.log(`🎯 Searching with pattern: ${patternKey}`);
    
    const brands: ProductBrand[] = [];
    
    // Search for each popular brand
    for (const brand of pattern.popularBrands) {
      try {
        const searchQuery = `${userInput} ${brand}`;
        console.log(`🔍 Searching for: ${searchQuery}`);
        
        const result = await priceComparisonService.searchProductAcrossStores(searchQuery, {
          maxResults: 5,
          sortBy: 'price',
          includeOutOfStock: false,
          category: pattern.category
        });

        if (result.results.length > 0) {
          const productBrand = this.createProductBrand(brand, result.results, pattern.category);
          if (productBrand) {
            brands.push(productBrand);
          }
        }
      } catch (error) {
        console.warn(`❌ Failed to search for ${brand}:`, error);
      }
    }

    // Also do a general search to catch any brands we might have missed
    try {
      const generalResult = await priceComparisonService.searchProductAcrossStores(userInput, {
        maxResults: 10,
        sortBy: 'price',
        includeOutOfStock: false,
        category: pattern.category
      });

      // Extract additional brands not in our popular list
      const additionalBrands = this.extractAdditionalBrands(
        generalResult.results, 
        pattern.popularBrands,
        pattern.category
      );
      
      brands.push(...additionalBrands);
    } catch (error) {
      console.warn(`❌ Failed general search for ${userInput}:`, error);
    }

    // Remove duplicates and sort by popularity and price
    const uniqueBrands = this.deduplicateAndSort(brands, pattern.popularBrands);

    return {
      productName: userInput,
      matches: uniqueBrands,
      totalFound: uniqueBrands.length,
      category: pattern.category,
      commonAliases: pattern.aliases
    };
  }

  /**
   * Generic search for products that don't match known patterns
   */
  private async genericSearch(userInput: string): Promise<SmartProductMatch> {
    console.log(`🔄 Performing generic search for: ${userInput}`);
    
    try {
      const result = await priceComparisonService.searchProductAcrossStores(userInput, {
        maxResults: 15,
        sortBy: 'price',
        includeOutOfStock: false
      });

      const brands = this.extractAdditionalBrands(result.results, [], 'Other');
      
      return {
        productName: userInput,
        matches: brands,
        totalFound: brands.length,
        category: 'Other',
        commonAliases: [userInput]
      };
    } catch (error) {
      console.error(`❌ Generic search failed for ${userInput}:`, error);
      return {
        productName: userInput,
        matches: [],
        totalFound: 0,
        category: 'Other',
        commonAliases: [userInput]
      };
    }
  }

  /**
   * Create a ProductBrand from search results
   */
  private createProductBrand(
    brand: string, 
    searchResults: any[], 
    category: string
  ): ProductBrand | null {
    if (searchResults.length === 0) return null;

    // Group results by store
    const storeAvailability = {
      woolworths: { available: false } as { available: boolean; price?: number },
      newworld: { available: false } as { available: boolean; price?: number },
      paknsave: { available: false } as { available: boolean; price?: number }
    };

    let bestPrice = Infinity;
    let bestPriceStore = '';
    let displayName = '';
    let size = '';
    let storeCount = 0;

    searchResults.forEach(result => {
      const price = result.product.price;
      const store = result.store;
      
      if (price && price > 0) {
        if (price < bestPrice) {
          bestPrice = price;
          bestPriceStore = result.storeName;
          displayName = result.product.name;
          size = result.product.unit || '';
        }

        // Update store availability
        if (store === 'woolworths') {
          storeAvailability.woolworths = { available: true, price };
          storeCount++;
        } else if (store === 'newworld') {
          storeAvailability.newworld = { available: true, price };
          storeCount++;
        } else if (store === 'paknsave') {
          storeAvailability.paknsave = { available: true, price };
          storeCount++;
        }
      }
    });

    if (bestPrice === Infinity) return null;

    return {
      brand,
      displayName: displayName || `${brand} ${category}`,
      bestPrice,
      bestPriceStore,
      availability: storeAvailability,
      size,
      category,
      isPopular: true, // Since it's from our popular brands list
      storeCount
    };
  }

  /**
   * Extract additional brands from search results
   */
  private extractAdditionalBrands(
    searchResults: any[], 
    knownBrands: string[], 
    category: string
  ): ProductBrand[] {
    const brandMap = new Map<string, any>();

    searchResults.forEach(result => {
      const product = result.product;
      let brand = product.brand || this.extractBrandFromName(product.name);
      
      // Skip if we already have this brand in our known brands
      if (knownBrands.some(kb => kb.toLowerCase() === brand.toLowerCase())) {
        return;
      }

      if (!brandMap.has(brand)) {
        brandMap.set(brand, {
          brand,
          results: [],
          stores: new Set()
        });
      }

      brandMap.get(brand).results.push(result);
      brandMap.get(brand).stores.add(result.store);
    });

    const additionalBrands: ProductBrand[] = [];

    brandMap.forEach((data, brand) => {
      const productBrand = this.createProductBrand(brand, data.results, category);
      if (productBrand) {
        productBrand.isPopular = false; // These are not in our popular list
        additionalBrands.push(productBrand);
      }
    });

    return additionalBrands;
  }

  /**
   * Extract brand name from product name
   */
  private extractBrandFromName(productName: string): string {
    const words = productName.split(' ');
    
    // Common brand extraction patterns
    const knownBrands = [
      'Anchor', 'Mainland', 'Meadow Fresh', 'Lewis Road', 'Nescafe', 
      'Moccona', 'Tip Top', 'Wonder White', 'Barilla', 'Tegel', 'Pams'
    ];

    for (const brand of knownBrands) {
      if (productName.toLowerCase().includes(brand.toLowerCase())) {
        return brand;
      }
    }

    // Fallback: use first capitalized word
    const firstWord = words[0];
    if (firstWord && firstWord[0] === firstWord[0].toUpperCase()) {
      return firstWord;
    }

    return 'Generic';
  }

  /**
   * Remove duplicates and sort brands by popularity and price
   */
  private deduplicateAndSort(brands: ProductBrand[], popularBrands: string[]): ProductBrand[] {
    // Remove duplicates by brand name
    const uniqueMap = new Map<string, ProductBrand>();
    
    brands.forEach(brand => {
      const key = brand.brand.toLowerCase();
      if (!uniqueMap.has(key) || 
          (uniqueMap.get(key)!.bestPrice > brand.bestPrice)) {
        uniqueMap.set(key, brand);
      }
    });

    const uniqueBrands = Array.from(uniqueMap.values());

    // Sort: popular brands first, then by store count, then by price
    return uniqueBrands.sort((a, b) => {
      // Popular brands first
      if (a.isPopular && !b.isPopular) return -1;
      if (!a.isPopular && b.isPopular) return 1;
      
      // More stores available = better
      if (a.storeCount !== b.storeCount) {
        return b.storeCount - a.storeCount;
      }
      
      // Lower price = better
      return a.bestPrice - b.bestPrice;
    });
  }

  /**
   * Get suggestions for autocomplete
   */
  getProductSuggestions(): string[] {
    return Object.keys(PRODUCT_PATTERNS).sort();
  }

  /**
   * Check if a product has a known pattern
   */
  hasKnownPattern(input: string): boolean {
    const normalizedInput = input.toLowerCase().trim();
    return this.findBestPattern(normalizedInput) !== null;
  }
}

export const smartProductMatcher = new SmartProductMatcherService(); 