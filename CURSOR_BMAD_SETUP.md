# BMad Setup for Cursor IDE

## Quick Setup for Cursor

### Method 1: Direct Agent Loading (Recommended)

1. **Open Cursor** in your project directory
2. **Start a new chat** (Ctrl+L or Cmd+L)
3. **Load BMad Orchestrator**:
   ```
   @bmad-orchestrator
   ```
4. **If that doesn't work, try**:
   - Copy content from `web-bundles/agents/bmad-orchestrator.txt`
   - Paste into chat with: "Act as this agent and help with my React Native shopping app"

### Method 2: Project Context Setup

1. **Add BMad to your project context**:
   - Drag `web-bundles` folder into Cursor
   - Cursor will index all agent files
   - Reference agents with `@` in chat

2. **Start with orchestrator**:
   ```
   Load the bmad-orchestrator agent from web-bundles/agents/ and help me plan next features for my shopping app
   ```

### Method 3: Manual Agent Loading

If the above don't work:

1. **Copy agent content**:
   - Navigate to `web-bundles/agents/bmad-orchestrator.txt`
   - Copy entire file content
   
2. **Paste in Cursor chat**:
   ```
   You are now operating as the BMad Orchestrator agent. Here are your instructions:
   
   [paste full bmad-orchestrator.txt content]
   
   Help me with my React Native shopping app project.
   ```

## Cursor-Specific BMad Commands

Once loaded, use these commands:

- `*help` - Show available commands
- `*agent pm` - Switch to Product Manager
- `*agent architect` - Switch to Solution Architect  
- `*agent dev` - Switch to Developer
- `*status` - Show current context
- `*create-doc brownfield-prd` - Create requirements document

## Recommended Workflow in Cursor

### Session 1: Requirements (PM Agent)
```
*agent pm
*create-doc brownfield-prd
```
Focus: Smart Shopping List Organization

### Session 2: Architecture (Architect Agent)  
```
*agent architect
*create-doc brownfield-architecture
```
Plan: Technical implementation

### Session 3: Development (SM → Dev cycle)
```
*agent sm
*create-next-story

*agent dev
[implement story]
```

## Cursor Advantages for BMad

✅ **Direct file access** - Agents can read/write project files
✅ **Code context** - Full codebase awareness
✅ **Real-time editing** - Immediate implementation
✅ **Git integration** - Track changes per story
✅ **Multiple chats** - Fresh context per agent

## Your Next Steps

1. **Open Cursor** in your shopping app project
2. **Try**: `@bmad-orchestrator` or copy agent content
3. **Test**: `*help` command
4. **Start**: `*agent pm` for requirements planning

**Ready to start? Open Cursor and let's go!** 🚀