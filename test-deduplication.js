// Simple Node.js test for deduplication service
const { ProductDeduplicationService } = require('./src/services/productDeduplicationService.ts');

// Mock product data for testing
const mockProducts = [
  {
    id: '1',
    name: 'Milk 1L Anchor',
    price: 3.50,
    current_price: 3.50,
    store: 'woolworths',
    brand: 'Anchor',
    size: '1L',
    category: 'Dairy',
    is_available: true
  },
  {
    id: '2', 
    name: 'Anchor Milk 1 Litre',
    price: 3.60,
    current_price: 3.60,
    store: 'newworld',
    brand: 'Anchor',
    package_size: '1 Litre',
    category: 'Dairy',
    is_available: true
  },
  {
    id: '3',
    name: 'Anchor 1000ml Milk',
    price: 3.40,
    current_price: 3.40,
    store: 'paknsave',
    brand: 'Anchor',
    weight: '1000ml',
    category: 'Dairy', 
    is_available: true
  },
  {
    id: '4',
    name: 'Meadow Fresh Milk 2L',
    price: 6.20,
    current_price: 6.20,
    store: 'woolworths',
    brand: 'Meadow Fresh',
    size: '2L',
    category: 'Dairy',
    is_available: true
  }
];

console.log('🧪 Testing ProductDeduplicationService...');
console.log(`📦 Input: ${mockProducts.length} products`);

try {
  const service = ProductDeduplicationService.getInstance();
  const groups = service.groupProducts(mockProducts);
  
  console.log(`✅ Output: ${groups.length} product groups`);
  
  groups.forEach((group, index) => {
    console.log(`\n${index + 1}. ${group.name}`);
    console.log(`   🏪 Stores: ${group.stores.join(', ')}`);
    console.log(`   💰 Price: $${group.lowestPrice} - $${group.highestPrice}`);
    console.log(`   📦 Products: ${group.totalVariants}`);
  });
  
} catch (error) {
  console.error('❌ Test failed:', error.message);
}