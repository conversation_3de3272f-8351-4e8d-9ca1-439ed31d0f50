/**
 * Price Integration Service
 * 
 * Integrates price data from multiple sources for unified price display
 */

import { enhancedPriceService, EnhancedPriceData } from './enhancedPriceService';
import { scrapedDataService } from './scrapedDataService';

export interface BrandPriceGroup {
  brand: string;
  products: PriceProduct[];
  bestPrice: number;
  averagePrice: number;
}

export interface SizePriceGroup {
  size: string;
  products: PriceProduct[];
  pricePerUnit: number;
  bestValue: boolean;
}

export interface PriceProduct {
  id: string;
  name: string;
  brand: string;
  size: string;
  price: number;
  pricePerUnit?: number;
  store: string;
  inStock: boolean;
  lastUpdated: string;
}

export interface StoreOptimization {
  store: string;
  totalItems: number;
  totalPrice: number;
  savings: number;
  coverage: number; // percentage of items available
}

class PriceIntegrationService {
  async getProductPrices(baseProduct: string): Promise<any> {
    try {
      // Get enhanced price data for the base product
      const priceData = await enhancedPriceService.getEnhancedPriceData(baseProduct);
      
      // Convert to the format expected by BrandSelectorDropdown
      const availableBrands = new Map<string, any>();
      
      priceData.allPrices.forEach(price => {
        const product = this.convertToProduct(price);
        const brand = product.brand;
        
        if (!availableBrands.has(brand)) {
          availableBrands.set(brand, {
            brand,
            sizes: []
          });
        }
        
        const brandData = availableBrands.get(brand);
        let sizeGroup = brandData.sizes.find((s: any) => s.size === product.size);
        
        if (!sizeGroup) {
          sizeGroup = {
            size: product.size,
            allStorePrices: []
          };
          brandData.sizes.push(sizeGroup);
        }
        
        sizeGroup.allStorePrices.push({
          store: product.store,
          storeName: this.getStoreName(product.store),
          price: product.price,
          available: product.inStock,
          size: product.size
        });
      });
      
      return {
        baseProduct,
        availableBrands: Array.from(availableBrands.values()),
        userPreference: null, // Could be enhanced with user preference logic
        recommendedOption: null // Could be enhanced with recommendation logic
      };
    } catch (error) {
      console.error('Error getting product prices:', error);
      return {
        baseProduct,
        availableBrands: [],
        userPreference: null,
        recommendedOption: null
      };
    }
  }

  private getStoreName(store: string): string {
    const storeNames: { [key: string]: string } = {
      'woolworths': 'Woolworths',
      'newworld': 'New World',
      'paknsave': "Pak'nSave"
    };
    return storeNames[store] || store;
  }

  async getBrandGroups(searchTerm: string): Promise<BrandPriceGroup[]> {
    try {
      const priceData = await enhancedPriceService.getEnhancedPriceData(searchTerm);
      const products = priceData.allPrices.map(this.convertToProduct);
      
      const brandMap = new Map<string, PriceProduct[]>();
      products.forEach(product => {
        if (!brandMap.has(product.brand)) {
          brandMap.set(product.brand, []);
        }
        brandMap.get(product.brand)!.push(product);
      });

      return Array.from(brandMap.entries()).map(([brand, brandProducts]) => ({
        brand,
        products: brandProducts,
        bestPrice: Math.min(...brandProducts.map(p => p.price)),
        averagePrice: brandProducts.reduce((sum, p) => sum + p.price, 0) / brandProducts.length
      }));
    } catch (error) {
      console.error('Error getting brand groups:', error);
      return [];
    }
  }

  async getSizeGroups(searchTerm: string): Promise<SizePriceGroup[]> {
    try {
      const priceData = await enhancedPriceService.getEnhancedPriceData(searchTerm);
      const products = priceData.allPrices.map(this.convertToProduct);
      
      const sizeMap = new Map<string, PriceProduct[]>();
      products.forEach(product => {
        if (!sizeMap.has(product.size)) {
          sizeMap.set(product.size, []);
        }
        sizeMap.get(product.size)!.push(product);
      });

      const sizeGroups = Array.from(sizeMap.entries()).map(([size, sizeProducts]) => ({
        size,
        products: sizeProducts,
        pricePerUnit: Math.min(...sizeProducts.map(p => p.pricePerUnit || p.price)),
        bestValue: false
      }));

      // Mark best value
      const bestValueIndex = sizeGroups.reduce((bestIdx, group, idx) => 
        group.pricePerUnit < sizeGroups[bestIdx].pricePerUnit ? idx : bestIdx, 0);
      if (sizeGroups.length > 0) {
        sizeGroups[bestValueIndex].bestValue = true;
      }

      return sizeGroups;
    } catch (error) {
      console.error('Error getting size groups:', error);
      return [];
    }
  }

  async getStoreOptimization(shoppingList: string[]): Promise<StoreOptimization[]> {
    try {
      const storeData = new Map<string, { items: number; totalPrice: number; coverage: number }>();
      
      for (const item of shoppingList) {
        const priceData = await enhancedPriceService.getEnhancedPriceData(item);
        
        priceData.allPrices.forEach(price => {
          if (!storeData.has(price.store)) {
            storeData.set(price.store, { items: 0, totalPrice: 0, coverage: 0 });
          }
          const data = storeData.get(price.store)!;
          data.items++;
          data.totalPrice += price.product.price;
        });
      }

      return Array.from(storeData.entries()).map(([store, data]) => ({
        store,
        totalItems: data.items,
        totalPrice: data.totalPrice,
        savings: 0, // Calculate based on baseline
        coverage: (data.items / shoppingList.length) * 100
      }));
    } catch (error) {
      console.error('Error getting store optimization:', error);
      return [];
    }
  }

  private convertToProduct(priceResult: any): PriceProduct {
    // Handle StoreSearchResult structure
    const product = priceResult.product || priceResult;
    return {
      id: product.objectID || product.id || Math.random().toString(),
      name: product.name || priceResult.productName || '',
      brand: product.brand || 'Unknown',
      size: product.unit || product.size || '1 unit',
      price: product.price || 0,
      pricePerUnit: product.pricePerUnit,
      store: priceResult.store || product.store || 'Unknown',
      inStock: product.availability !== 'out_of_stock',
      lastUpdated: product.lastUpdated || new Date().toISOString()
    };
  }
}

export const priceIntegrationService = new PriceIntegrationService();