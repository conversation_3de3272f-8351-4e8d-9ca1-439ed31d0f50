/**
 * Enhanced Shopping List Service
 * 
 * Provides advanced shopping list functionality with price integration
 */

import { priceIntegrationService, PriceProduct } from './priceIntegrationService';
import { enhancedPriceService } from './enhancedPriceService';

export interface ShoppingListItem {
  id: string;
  name: string;
  quantity: number;
  unit: string;
  category?: string;
  completed: boolean;
  priority: 'low' | 'medium' | 'high';
  priceData?: PriceProduct[];
  selectedProduct?: PriceProduct;
  estimatedCost?: number;
  notes?: string;
  addedAt: Date;
  updatedAt: Date;
}

export interface ShoppingList {
  id: string;
  name: string;
  items: ShoppingListItem[];
  totalEstimatedCost: number;
  lastUpdated: Date;
  storeOptimization?: any[];
}

export interface ShoppingListStats {
  totalItems: number;
  completedItems: number;
  totalCost: number;
  potentialSavings: number;
  averageItemPrice: number;
}

class EnhancedShoppingListService {
  private lists: Map<string, ShoppingList> = new Map();
  private listeners: Set<(lists: ShoppingList[]) => void> = new Set();

  constructor() {
    this.loadFromStorage();
  }

  private loadFromStorage() {
    try {
      const stored = localStorage.getItem('enhancedShoppingLists');
      if (stored) {
        const lists = JSON.parse(stored);
        lists.forEach((list: any) => {
          this.lists.set(list.id, {
            ...list,
            lastUpdated: new Date(list.lastUpdated),
            items: list.items.map((item: any) => ({
              ...item,
              addedAt: new Date(item.addedAt),
              updatedAt: new Date(item.updatedAt)
            }))
          });
        });
      }
    } catch (error) {
      console.error('Error loading shopping lists:', error);
    }
  }

  private saveToStorage() {
    try {
      const lists = Array.from(this.lists.values());
      localStorage.setItem('enhancedShoppingLists', JSON.stringify(lists));
      this.notifyListeners();
    } catch (error) {
      console.error('Error saving shopping lists:', error);
    }
  }

  private notifyListeners() {
    const lists = Array.from(this.lists.values());
    this.listeners.forEach(listener => listener(lists));
  }

  createList(name: string): ShoppingList {
    const list: ShoppingList = {
      id: Math.random().toString(36).substr(2, 9),
      name,
      items: [],
      totalEstimatedCost: 0,
      lastUpdated: new Date()
    };
    
    this.lists.set(list.id, list);
    this.saveToStorage();
    return list;
  }

  getList(id: string): ShoppingList | undefined {
    return this.lists.get(id);
  }

  getAllLists(): ShoppingList[] {
    return Array.from(this.lists.values());
  }

  async addItem(listId: string, itemData: Partial<ShoppingListItem>): Promise<ShoppingListItem | null> {
    const list = this.lists.get(listId);
    if (!list) return null;

    const item: ShoppingListItem = {
      id: Math.random().toString(36).substr(2, 9),
      name: itemData.name || '',
      quantity: itemData.quantity || 1,
      unit: itemData.unit || 'unit',
      category: itemData.category,
      completed: false,
      priority: itemData.priority || 'medium',
      notes: itemData.notes,
      addedAt: new Date(),
      updatedAt: new Date()
    };

    // Fetch price data
    try {
      const priceData = await enhancedPriceService.getEnhancedPriceData(item.name);
      if (priceData.allPrices.length > 0) {
        item.priceData = priceData.allPrices.map((price: any) => ({
          id: price.id || Math.random().toString(),
          name: price.name || price.productName || item.name,
          brand: price.brand || 'Unknown',
          size: price.size || '1 unit',
          price: price.price || 0,
          pricePerUnit: price.pricePerUnit,
          store: price.store || 'Unknown',
          inStock: price.inStock !== false,
          lastUpdated: price.lastUpdated || new Date().toISOString()
        }));
        
        // Select best price as default
        if (priceData.bestPrice) {
          item.selectedProduct = item.priceData[0];
          item.estimatedCost = item.selectedProduct.price * item.quantity;
        }
      }
    } catch (error) {
      console.error('Error fetching price data for item:', error);
    }

    list.items.push(item);
    this.updateListTotals(list);
    this.saveToStorage();
    
    return item;
  }

  updateItem(listId: string, itemId: string, updates: Partial<ShoppingListItem>): boolean {
    const list = this.lists.get(listId);
    if (!list) return false;

    const itemIndex = list.items.findIndex(item => item.id === itemId);
    if (itemIndex === -1) return false;

    list.items[itemIndex] = {
      ...list.items[itemIndex],
      ...updates,
      updatedAt: new Date()
    };

    // Recalculate estimated cost if quantity or selected product changed
    if (updates.quantity !== undefined || updates.selectedProduct !== undefined) {
      const item = list.items[itemIndex];
      if (item.selectedProduct) {
        item.estimatedCost = item.selectedProduct.price * item.quantity;
      }
    }

    this.updateListTotals(list);
    this.saveToStorage();
    
    return true;
  }

  removeItem(listId: string, itemId: string): boolean {
    const list = this.lists.get(listId);
    if (!list) return false;

    const initialLength = list.items.length;
    list.items = list.items.filter(item => item.id !== itemId);
    
    if (list.items.length < initialLength) {
      this.updateListTotals(list);
      this.saveToStorage();
      return true;
    }
    
    return false;
  }

  private updateListTotals(list: ShoppingList) {
    list.totalEstimatedCost = list.items.reduce((total, item) => 
      total + (item.estimatedCost || 0), 0);
    list.lastUpdated = new Date();
  }

  async refreshPrices(listId: string): Promise<boolean> {
    const list = this.lists.get(listId);
    if (!list) return false;

    for (const item of list.items) {
      try {
        const priceData = await enhancedPriceService.getEnhancedPriceData(item.name);
        if (priceData.allPrices.length > 0) {
          item.priceData = priceData.allPrices.map((price: any) => ({
            id: price.id || Math.random().toString(),
            name: price.name || price.productName || item.name,
            brand: price.brand || 'Unknown',
            size: price.size || '1 unit',
            price: price.price || 0,
            pricePerUnit: price.pricePerUnit,
            store: price.store || 'Unknown',
            inStock: price.inStock !== false,
            lastUpdated: price.lastUpdated || new Date().toISOString()
          }));
        }
      } catch (error) {
        console.error(`Error refreshing prices for ${item.name}:`, error);
      }
    }

    this.updateListTotals(list);
    this.saveToStorage();
    return true;
  }

  getListStats(listId: string): ShoppingListStats | null {
    const list = this.lists.get(listId);
    if (!list) return null;

    const totalItems = list.items.length;
    const completedItems = list.items.filter(item => item.completed).length;
    const totalCost = list.totalEstimatedCost;
    const averageItemPrice = totalItems > 0 ? totalCost / totalItems : 0;

    return {
      totalItems,
      completedItems,
      totalCost,
      potentialSavings: 0, // TODO: Calculate based on alternative options
      averageItemPrice
    };
  }

  onListsChange(listener: (lists: ShoppingList[]) => void) {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }
}

export const enhancedShoppingListService = new EnhancedShoppingListService();