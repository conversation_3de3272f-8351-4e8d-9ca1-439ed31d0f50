import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native';
import { useTheme } from '../context/ThemeContext';
import { smartProductMatcher } from '../services/smartProductMatcher';
import { productFetchService } from '../services/productFetchService';

interface BrandOption {
  name: string;
  averagePrice: number;
  stores: {
    [key: string]: {
      price?: number;
      available?: boolean;
      size?: string;
    };
  };
}

interface SimpleBrandDropdownProps {
  visible: boolean;
  onClose: () => void;
  productName: string;
  onSelectBrand: (brandName: string) => void;
}

export const SimpleBrandDropdown: React.FC<SimpleBrandDropdownProps> = ({
  visible,
  onClose,
  productName,
  onSelectBrand,
}) => {
  const { colors } = useTheme();
  const [brands, setBrands] = useState<BrandOption[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  console.log('🔥 SimpleBrandDropdown render - visible:', visible, 'productName:', productName);

  useEffect(() => {
    if (visible && productName) {
      fetchBrands();
    }
  }, [visible, productName]);

  const fetchBrands = async () => {
    setLoading(true);
    setError(null);
    
    try {
      console.log('🔍 Smart fetching brands for product:', productName);
      
      // First try smart product matcher for better brand results
      const smartMatch = await smartProductMatcher.findMatches(productName);
      console.log('🧠 Smart matcher found', smartMatch.totalFound, 'brands for', productName);
      
      if (smartMatch.matches.length > 0) {
        // Convert smart matches to BrandOption format
        const smartBrands: BrandOption[] = smartMatch.matches.map(brand => {
          const stores: any = {};
          
          // Convert availability to store format
          if (brand.availability.woolworths.available) {
            stores.woolworths = {
              price: brand.availability.woolworths.price,
              available: true,
              size: brand.size
            };
          }
          if (brand.availability.newworld.available) {
            stores.newworld = {
              price: brand.availability.newworld.price,
              available: true,
              size: brand.size
            };
          }
          if (brand.availability.paknsave.available) {
            stores.paknsave = {
              price: brand.availability.paknsave.price,
              available: true,
              size: brand.size
            };
          }
          
          return {
            name: brand.brand,
            averagePrice: brand.bestPrice,
            stores: stores
          };
        });
        
        console.log('✅ Smart matcher converted to', smartBrands.length, 'brand options');
        console.log('🏷️ Smart brands:', smartBrands.map(b => b.name));
        setBrands(smartBrands);
        return; // Success! No need for fallback
      }
      
      // Fallback to direct Supabase search if smart matcher has no results
      console.log('🔄 Smart matcher found no results, trying direct Supabase search...');
      const productResults = await productFetchService.searchProducts(productName, {
        maxResults: 20
      });
      
      console.log('📊 Supabase search results:', productResults.products.length);
      
      if (productResults.products.length > 0) {
        // Extract unique brands from search results
        const brandMap = new Map<string, BrandOption>();
        
        productResults.products.forEach(product => {
          const brandName = product.brand || 'Generic';
          
          if (!brandMap.has(brandName)) {
            brandMap.set(brandName, {
              name: brandName,
              averagePrice: product.price,
              stores: {}
            });
          }
          
          const brandOption = brandMap.get(brandName)!;
          brandOption.stores[product.store] = {
            price: product.price,
            available: true,
            size: product.unit
          };
          
          // Update average price
          const storePrices = Object.values(brandOption.stores).map(s => s.price || 0).filter(p => p > 0);
          brandOption.averagePrice = storePrices.reduce((sum, price) => sum + price, 0) / storePrices.length;
        });
        
        const allBrands = Array.from(brandMap.values());
        // Sort brands by average price (cheapest first)
        allBrands.sort((a, b) => a.averagePrice - b.averagePrice);
        
        console.log('🏷️ Supabase brands:', allBrands.map(b => b.name));
        setBrands(allBrands);
      } else {
        console.log('❌ No results from Supabase search either');
        setBrands([]);
      }
      
    } catch (err) {
      console.error('❌ Error fetching brands:', err);
      setError('Failed to load brands');
      
      // Final fallback to basic brands if both services fail
      const fallbackBrands: BrandOption[] = [
        { name: 'Generic Brand', averagePrice: 2.99, stores: {} },
        { name: 'Store Brand', averagePrice: 3.49, stores: {} },
        { name: 'Premium Brand', averagePrice: 4.99, stores: {} },
      ];
      setBrands(fallbackBrands);
    } finally {
      setLoading(false);
    }
  };

  const handleBrandSelect = (brandName: string) => {
    console.log('🎯 Brand selected:', brandName);
    onSelectBrand(brandName);
    onClose();
  };

  const formatPrice = (price: number): string => {
    return `$${price.toFixed(2)}`;
  };

  const getStoreCount = (brand: BrandOption): number => {
    return Object.keys(brand.stores).length;
  };

  const getLowestPrice = (brand: BrandOption): number => {
    const prices = Object.values(brand.stores).map(store => store.price || 0);
    return Math.min(...prices, brand.averagePrice);
  };

  const getStoreIcon = (storeName: string): string => {
    switch (storeName.toLowerCase()) {
      case 'woolworths':
        return '🟢'; // Green circle for Woolworths
      case 'newworld':
        return '🔴'; // Red circle for New World
      case 'paknsave':
        return '🟡'; // Yellow circle for Pak'nSave
      default:
        return '🏬'; // Generic store icon
    }
  };

  const renderStorePrices = (brand: BrandOption) => {
    const storeEntries = Object.entries(brand.stores);
    
    if (storeEntries.length === 0) {
      return (
        <Text style={[styles.priceText, { color: colors.textSecondary }]}>
          from {formatPrice(brand.averagePrice)}
        </Text>
      );
    }

    return (
      <View style={styles.storePricesContainer}>
        {storeEntries.map(([storeName, storeData]) => (
          <View key={storeName} style={styles.storePrice}>
            <Text style={styles.storeIcon}>
              {getStoreIcon(storeName)}
            </Text>
            <Text style={[styles.priceText, { color: colors.textSecondary }]}>
              {formatPrice(storeData.price || brand.averagePrice)}
            </Text>
          </View>
        ))}
      </View>
    );
  };

  if (!visible) {
    console.log('❌ Modal not visible, returning null');
    return null;
  }

  console.log('✅ Modal IS visible, rendering content');

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <SafeAreaView style={styles.container}>
          <View style={[styles.modal, { backgroundColor: colors.surface }]}>
            
            {/* Header */}
            <View style={styles.header}>
              <Text style={[styles.title, { color: colors.text }]}>
                Select Brand for {productName}
              </Text>
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <Text style={styles.closeText}>✕</Text>
              </TouchableOpacity>
            </View>

            {/* Loading State */}
            {loading && (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.primary} />
                <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
                  Loading brands...
                </Text>
              </View>
            )}

            {/* Error State */}
            {error && !loading && (
              <View style={styles.errorContainer}>
                <Text style={[styles.errorText, { color: colors.error }]}>
                  {error}
                </Text>
              </View>
            )}

            {/* Brand List */}
            {!loading && !error && brands.length > 0 && (
              <ScrollView style={styles.brandList}>
                {brands.map((brand, index) => {
                  console.log(`🎨 Rendering brand ${index + 1}:`, brand.name);
                  return (
                    <TouchableOpacity
                      key={brand.name}
                      style={[styles.brandItem, { borderBottomColor: colors.border }]}
                      onPress={() => handleBrandSelect(brand.name)}
                    >
                      <View style={styles.brandInfo}>
                        <Text style={[styles.brandName, { color: colors.text }]}>
                          {brand.name}
                        </Text>
                        <View style={styles.priceInfo}>
                          {renderStorePrices(brand)}
                        </View>
                      </View>
                      <View style={styles.arrowContainer}>
                        <Text style={[styles.arrow, { color: colors.textSecondary }]}>
                          ›
                        </Text>
                      </View>
                    </TouchableOpacity>
                  );
                })}
              </ScrollView>
            )}

            {/* Empty State */}
            {!loading && !error && brands.length === 0 && (
              <View style={styles.emptyContainer}>
                <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
                  No brands found for "{productName}"
                </Text>
              </View>
            )}

            {/* Footer */}
            {!loading && brands.length > 0 && (
              <View style={styles.footer}>
                <Text style={[styles.footerText, { color: colors.textSecondary }]}>
                  {brands.length} brands available • Tap to select
                </Text>
              </View>
            )}

          </View>
        </SafeAreaView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  container: {
    width: '100%',
    maxWidth: 350,
  },
  modal: {
    borderRadius: 12,
    maxHeight: '80%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
  },
  closeButton: {
    width: 30,
    height: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeText: {
    fontSize: 18,
    color: '#666',
  },
  loadingContainer: {
    padding: 40,
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  errorContainer: {
    padding: 20,
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
  },
  brandList: {
    maxHeight: 300,
  },
  brandItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
  },
  brandInfo: {
    flex: 1,
  },
  brandName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  priceInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  priceText: {
    fontSize: 14,
  },
  storeCount: {
    fontSize: 14,
    marginLeft: 4,
  },
  arrowContainer: {
    paddingLeft: 12,
  },
  arrow: {
    fontSize: 18,
    fontWeight: '300',
  },
  emptyContainer: {
    padding: 40,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
  },
  footer: {
    padding: 16,
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  footerText: {
    fontSize: 14,
  },
  storePricesContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    gap: 8,
  },
  storePrice: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 8,
  },
  storeIcon: {
    fontSize: 16,
    marginRight: 4,
  },
}); 