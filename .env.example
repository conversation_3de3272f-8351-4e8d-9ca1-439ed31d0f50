# Environment Variables Configuration
# Copy this file to .env and update with your actual values

# Gemini AI API Key (optional - app works offline without it)
EXPO_PUBLIC_GEMINI_API_KEY=your_gemini_api_key_here

# Supabase Configuration (required)
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key

# Auth0 Configuration (required for Auth0 authentication)
# 1. Create an Auth0 account at https://auth0.com
# 2. Create a new application (choose "Native" application type)
# 3. Copy your domain and client ID below
EXPO_PUBLIC_AUTH0_DOMAIN=your-auth0-domain.auth0.com
EXPO_PUBLIC_AUTH0_CLIENT_ID=your_auth0_client_id

# Auth0 Setup Instructions:
# 1. In your Auth0 dashboard, go to Applications
# 2. Create a new application and select "Native" 
# 3. In the application settings:
#    - Add your app's redirect URIs (e.g., com.yourcompany.yourapp://auth0callback)
#    - Add allowed origins (e.g., http://localhost:19000)
#    - Enable the required grant types (Authorization Code, Refresh <PERSON>ken)
# 4. Copy the Domain and Client ID to the variables above
# 5. Configure any social connections (Google, Facebook, etc.) in the Auth0 dashboard

# Note: In development, the app will use mock authentication if Auth0 is not configured