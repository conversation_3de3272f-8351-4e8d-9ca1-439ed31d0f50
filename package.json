{"name": "ai-recipe-planner", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "ts:check": "tsc"}, "dependencies": {"@expo/metro-runtime": "~3.2.3", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "1.23.1", "@react-navigation/bottom-tabs": "^6.5.20", "@react-navigation/native": "^6.1.17", "@react-navigation/native-stack": "^6.10.0", "@react-navigation/stack": "^6.3.29", "@types/jest": "^30.0.0", "algoliasearch": "^4.23.3", "expo": "^51.0.39", "expo-auth-session": "~5.5.2", "expo-blur": "~13.0.3", "expo-constants": "~16.0.1", "expo-crypto": "~13.0.2", "expo-font": "~12.0.5", "expo-haptics": "~13.0.1", "expo-linear-gradient": "~13.0.2", "expo-linking": "~6.3.1", "expo-router": "~3.5.23", "expo-splash-screen": "~0.27.4", "expo-status-bar": "~1.12.1", "firebase": "^10.12.0", "fs-extra": "^11.2.0", "hermes-parser": "^0.29.1", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.74.5", "react-native-auth0": "^4.6.0", "react-native-gesture-handler": "~2.16.1", "react-native-reanimated": "~3.10.1", "react-native-safe-area-context": "4.10.5", "react-native-screens": "3.31.1", "react-native-ui-lib": "^7.19.0", "react-native-web": "~0.19.10"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/lodash": "^4.17.20", "@types/lodash.debounce": "^4.0.9", "@types/react": "~18.2.45", "lodash": "^4.17.21", "lodash.debounce": "^4.0.8", "ts-node": "^10.9.2", "typescript": "~5.3.3"}, "private": true}