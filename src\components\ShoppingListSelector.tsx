import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Alert,
  ActivityIndicator,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import { useTheme } from '../context/ThemeContext';
import { shoppingListService, ShoppingList, ProductToAdd } from '../services/shoppingListService';

interface ShoppingListSelectorProps {
  visible: boolean;
  onClose: () => void;
  product: ProductToAdd | null;
  onSuccess: (listName: string) => void;
}

const ICON_OPTIONS = [
  'list', 'cart', 'basket', 'bag', 'heart', 'star', 'home', 'restaurant',
  'wine', 'cafe', 'pizza', 'fast-food', 'flash', 'time', 'checkmark-circle'
];

const COLOR_OPTIONS = [
  '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', 
  '#06B6D4', '#84CC16', '#F97316', '#EC4899', '#6B7280'
];

export const ShoppingListSelector: React.FC<ShoppingListSelectorProps> = ({
  visible,
  onClose,
  product,
  onSuccess,
}) => {
  const { colors } = useTheme();
  const [lists, setLists] = useState<ShoppingList[]>([]);
  const [loading, setLoading] = useState(false);
  const [showCreateNew, setShowCreateNew] = useState(false);
  const [newListName, setNewListName] = useState('');
  const [newListDescription, setNewListDescription] = useState('');
  const [selectedColor, setSelectedColor] = useState(COLOR_OPTIONS[0]);
  const [selectedIcon, setSelectedIcon] = useState(ICON_OPTIONS[0]);
  const [quantity, setQuantity] = useState('1');
  const [notes, setNotes] = useState('');

  const scaleAnim = useState(new Animated.Value(0))[0];

  useEffect(() => {
    if (visible) {
      loadLists();
      Animated.spring(scaleAnim, {
        toValue: 1,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
    } else {
      scaleAnim.setValue(0);
    }
  }, [visible]);

  const loadLists = async () => {
    try {
      setLoading(true);
      const allLists = await shoppingListService.getAllLists();
      setLists(allLists);
    } catch (error) {
      console.error('Error loading lists:', error);
      Alert.alert('Error', 'Failed to load shopping lists');
    } finally {
      setLoading(false);
    }
  };

  const handleAddToList = async (listId: string) => {
    if (!product) return;
    
    try {
      setLoading(true);
      const quantityNum = parseInt(quantity) || 1;
      const result = await shoppingListService.addItemToList(
        listId,
        product,
        quantityNum,
        notes.trim() || undefined
      );
      
      if (result) {
        const list = lists.find(l => l.id === listId);
        onSuccess(list?.name || 'Shopping List');
        onClose();
        
        // Reset form
        setQuantity('1');
        setNotes('');
      } else {
        Alert.alert('Error', 'Failed to add item to shopping list');
      }
    } catch (error) {
      console.error('Error adding to list:', error);
      Alert.alert('Error', 'Failed to add item to shopping list');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateNewList = async () => {
    if (!newListName.trim()) {
      Alert.alert('Error', 'Please enter a list name');
      return;
    }
    
    try {
      setLoading(true);
      const newList = await shoppingListService.createList(
        newListName.trim(),
        newListDescription.trim() || undefined,
        selectedColor,
        selectedIcon
      );
      
      // Add product to new list
      if (product) {
        const quantityNum = parseInt(quantity) || 1;
        await shoppingListService.addItemToList(
          newList.id,
          product,
          quantityNum,
          notes.trim() || undefined
        );
      }
      
      onSuccess(newList.name);
      onClose();
      
      // Reset form
      setNewListName('');
      setNewListDescription('');
      setSelectedColor(COLOR_OPTIONS[0]);
      setSelectedIcon(ICON_OPTIONS[0]);
      setQuantity('1');
      setNotes('');
      setShowCreateNew(false);
      
    } catch (error) {
      console.error('Error creating list:', error);
      Alert.alert('Error', 'Failed to create shopping list');
    } finally {
      setLoading(false);
    }
  };

  const renderListItem = (list: ShoppingList) => (
    <TouchableOpacity
      key={list.id}
      style={[styles.listItem, { backgroundColor: colors.surface, borderColor: colors.border }]}
      onPress={() => handleAddToList(list.id)}
      disabled={loading}
    >
      <View style={[styles.listIcon, { backgroundColor: list.color }]}>
        <Ionicons name={list.icon as any} size={20} color="white" />
      </View>
      
      <View style={styles.listInfo}>
        <Text style={[styles.listName, { color: colors.text }]}>
          {list.name}
        </Text>
        {list.description && (
          <Text style={[styles.listDescription, { color: colors.textSecondary }]}>
            {list.description}
          </Text>
        )}
        <Text style={[styles.listStats, { color: colors.textSecondary }]}>
          {list.totalItems} items • ${list.totalPrice.toFixed(2)}
        </Text>
      </View>
      
      {list.isDefault && (
        <View style={[styles.defaultBadge, { backgroundColor: colors.primary }]}>
          <Text style={styles.defaultText}>Default</Text>
        </View>
      )}
      
      <Ionicons name="chevron-forward" size={16} color={colors.textSecondary} />
    </TouchableOpacity>
  );

  const renderCreateNewForm = () => (
    <View style={styles.createNewForm}>
      <Text style={[styles.formTitle, { color: colors.text }]}>
        Create New Shopping List
      </Text>
      
      <TextInput
        style={[styles.input, { 
          backgroundColor: colors.background, 
          borderColor: colors.border,
          color: colors.text 
        }]}
        placeholder="List name"
        placeholderTextColor={colors.textSecondary}
        value={newListName}
        onChangeText={setNewListName}
        maxLength={50}
      />
      
      <TextInput
        style={[styles.input, styles.multilineInput, { 
          backgroundColor: colors.background, 
          borderColor: colors.border,
          color: colors.text 
        }]}
        placeholder="Description (optional)"
        placeholderTextColor={colors.textSecondary}
        value={newListDescription}
        onChangeText={setNewListDescription}
        multiline
        numberOfLines={2}
        maxLength={200}
      />
      
      <Text style={[styles.sectionTitle, { color: colors.text }]}>
        Choose Icon
      </Text>
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={styles.iconSelector}
      >
        {ICON_OPTIONS.map(icon => (
          <TouchableOpacity
            key={icon}
            style={[
              styles.iconOption,
              { 
                backgroundColor: selectedIcon === icon ? selectedColor : colors.surface,
                borderColor: colors.border 
              }
            ]}
            onPress={() => setSelectedIcon(icon)}
          >
            <Ionicons 
              name={icon as any} 
              size={20} 
              color={selectedIcon === icon ? 'white' : colors.text} 
            />
          </TouchableOpacity>
        ))}
      </ScrollView>
      
      <Text style={[styles.sectionTitle, { color: colors.text }]}>
        Choose Color
      </Text>
      <View style={styles.colorSelector}>
        {COLOR_OPTIONS.map(color => (
          <TouchableOpacity
            key={color}
            style={[
              styles.colorOption,
              { backgroundColor: color },
              selectedColor === color && styles.selectedColor
            ]}
            onPress={() => setSelectedColor(color)}
          >
            {selectedColor === color && (
              <Ionicons name="checkmark" size={16} color="white" />
            )}
          </TouchableOpacity>
        ))}
      </View>
      
      <View style={styles.formButtons}>
        <TouchableOpacity
          style={[styles.formButton, styles.cancelButton, { backgroundColor: colors.surface }]}
          onPress={() => setShowCreateNew(false)}
        >
          <Text style={[styles.formButtonText, { color: colors.text }]}>
            Cancel
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.formButton, styles.createButton, { backgroundColor: colors.primary }]}
          onPress={handleCreateNewList}
          disabled={!newListName.trim() || loading}
        >
          <Text style={[styles.formButtonText, { color: 'white' }]}>
            Create & Add
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      statusBarTranslucent
      onRequestClose={onClose}
    >
      <BlurView intensity={20} style={styles.overlay}>
        <Animated.View 
          style={[
            styles.modalContainer,
            {
              transform: [{ scale: scaleAnim }]
            }
          ]}
        >
          <View style={[styles.modal, { backgroundColor: colors.surface }]}>
            <View style={styles.header}>
              <Text style={[styles.title, { color: colors.text }]}>
                Add to Shopping List
              </Text>
              <TouchableOpacity onPress={onClose}>
                <Ionicons name="close" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>
            
            {product && (
              <View style={[styles.productInfo, { backgroundColor: colors.background }]}>
                <Text style={[styles.productName, { color: colors.text }]}>
                  {product.name}
                </Text>
                <Text style={[styles.productDetails, { color: colors.textSecondary }]}>
                  ${product.price.toFixed(2)} • {product.store}
                </Text>
              </View>
            )}
            
            <View style={styles.quantitySection}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                Quantity
              </Text>
              <View style={styles.quantityContainer}>
                <TouchableOpacity
                  style={[styles.quantityButton, { backgroundColor: colors.primary }]}
                  onPress={() => setQuantity(Math.max(1, parseInt(quantity) - 1).toString())}
                >
                  <Ionicons name="remove" size={20} color="white" />
                </TouchableOpacity>
                <TextInput
                  style={[styles.quantityInput, { 
                    backgroundColor: colors.background, 
                    borderColor: colors.border,
                    color: colors.text 
                  }]}
                  value={quantity}
                  onChangeText={(text) => setQuantity(text.replace(/[^0-9]/g, '') || '1')}
                  keyboardType="numeric"
                  maxLength={3}
                />
                <TouchableOpacity
                  style={[styles.quantityButton, { backgroundColor: colors.primary }]}
                  onPress={() => setQuantity((parseInt(quantity) + 1).toString())}
                >
                  <Ionicons name="add" size={20} color="white" />
                </TouchableOpacity>
              </View>
            </View>
            
            <View style={styles.notesSection}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                Notes (optional)
              </Text>
              <TextInput
                style={[styles.input, { 
                  backgroundColor: colors.background, 
                  borderColor: colors.border,
                  color: colors.text 
                }]}
                placeholder="Add notes..."
                placeholderTextColor={colors.textSecondary}
                value={notes}
                onChangeText={setNotes}
                maxLength={100}
              />
            </View>
            
            <ScrollView style={styles.listsContainer} showsVerticalScrollIndicator={false}>
              {loading ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="large" color={colors.primary} />
                  <Text style={[styles.loadingText, { color: colors.text }]}>
                    Loading lists...
                  </Text>
                </View>
              ) : showCreateNew ? (
                renderCreateNewForm()
              ) : (
                <>
                  <Text style={[styles.sectionTitle, { color: colors.text }]}>
                    Select a List
                  </Text>
                  
                  {lists.map(renderListItem)}
                  
                  <TouchableOpacity
                    style={[styles.createNewButton, { backgroundColor: colors.primary }]}
                    onPress={() => setShowCreateNew(true)}
                  >
                    <Ionicons name="add" size={20} color="white" />
                    <Text style={[styles.createNewText, { color: 'white' }]}>
                      Create New List
                    </Text>
                  </TouchableOpacity>
                </>
              )}
            </ScrollView>
          </View>
        </Animated.View>
      </BlurView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    width: '100%',
    maxWidth: 400,
    maxHeight: '90%',
  },
  modal: {
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  productInfo: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  productName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  productDetails: {
    fontSize: 14,
  },
  quantitySection: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
  },
  quantityButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantityInput: {
    width: 80,
    height: 40,
    borderRadius: 8,
    borderWidth: 1,
    textAlign: 'center',
    fontSize: 16,
    fontWeight: '600',
  },
  notesSection: {
    marginBottom: 20,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  multilineInput: {
    height: 60,
    textAlignVertical: 'top',
  },
  listsContainer: {
    maxHeight: 300,
  },
  loadingContainer: {
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 8,
    fontSize: 14,
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 1,
  },
  listIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  listInfo: {
    flex: 1,
  },
  listName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  listDescription: {
    fontSize: 14,
    marginBottom: 4,
  },
  listStats: {
    fontSize: 12,
  },
  defaultBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
  },
  defaultText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '600',
  },
  createNewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
    gap: 8,
  },
  createNewText: {
    fontSize: 16,
    fontWeight: '600',
  },
  createNewForm: {
    marginTop: 8,
  },
  formTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  iconSelector: {
    marginBottom: 16,
  },
  iconOption: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
    borderWidth: 1,
  },
  colorSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 20,
  },
  colorOption: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedColor: {
    borderWidth: 2,
    borderColor: '#fff',
  },
  formButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  formButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButton: {
    borderWidth: 1,
    borderColor: '#ccc',
  },
  createButton: {
    // No additional styles needed
  },
  formButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ShoppingListSelector;    