import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  TouchableOpacity,
  Dimensions,
  SafeAreaView,
  TouchableWithoutFeedback,
  ScrollView,
  Switch,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';

const { width: SCREEN_WIDTH } = Dimensions.get('window');
const DRAWER_WIDTH = SCREEN_WIDTH * 0.8;

interface DrawerItem {
  id: string;
  title: string;
  icon: string;
  onPress: () => void;
}

interface SlideOutDrawerProps {
  visible: boolean;
  onClose: () => void;
  onNavigateToProfile: () => void;
}

export const SlideOutDrawer: React.FC<SlideOutDrawerProps> = ({
  visible,
  onClose,
  onNavigateToProfile,
}) => {
  const { colors: themeColors, isDark: isDarkMode, toggleTheme } = useTheme();
  const slideAnim = useRef(new Animated.Value(-DRAWER_WIDTH)).current;
  const overlayOpacity = useRef(new Animated.Value(0)).current;

  // Professional color scheme
  const colors = {
    light: {
      background: '#F8FAFC',
      surface: '#FFFFFF',
      primary: '#475569',
      text: '#0F172A',
      textSecondary: '#64748B',
      border: '#E2E8F0',
      shadow: 'rgba(15, 23, 42, 0.1)',
    },
    dark: {
      background: '#0F172A',
      surface: '#1E293B',
      primary: '#06B6D4',
      text: '#F8FAFC',
      textSecondary: '#94A3B8',
      border: '#334155',
      shadow: 'rgba(0, 0, 0, 0.3)',
    }
  };

  const currentColors = isDarkMode ? colors.dark : colors.light;

  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(overlayOpacity, {
          toValue: 0.5,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: -DRAWER_WIDTH,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(overlayOpacity, {
          toValue: 0,
          duration: 250,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible]);

  const drawerItems: DrawerItem[] = [
    {
      id: 'profile',
      title: 'Profile',
      icon: 'person-outline',
      onPress: () => {
        onNavigateToProfile();
        onClose();
      },
    },
    {
      id: 'settings',
      title: 'Settings',
      icon: 'settings-outline',
      onPress: () => {
        console.log('Navigate to Settings');
        onClose();
      },
    },
    {
      id: 'notifications',
      title: 'Notifications',
      icon: 'notifications-outline',
      onPress: () => {
        console.log('Navigate to Notifications');
        onClose();
      },
    },
    {
      id: 'help',
      title: 'Help & Support',
      icon: 'help-circle-outline',
      onPress: () => {
        console.log('Navigate to Help');
        onClose();
      },
    },
    {
      id: 'about',
      title: 'About',
      icon: 'information-circle-outline',
      onPress: () => {
        console.log('Navigate to About');
        onClose();
      },
    },
  ];

  if (!visible) {
    return null;
  }

  return (
    <View style={styles.container}>
      {/* Overlay */}
      <TouchableWithoutFeedback onPress={onClose}>
        <Animated.View
          style={[
            styles.overlay,
            {
              opacity: overlayOpacity,
            },
          ]}
        />
      </TouchableWithoutFeedback>

      {/* Drawer */}
      <Animated.View
        style={[
          styles.drawer,
          {
            transform: [{ translateX: slideAnim }],
            backgroundColor: currentColors.surface,
            shadowColor: currentColors.shadow,
          },
        ]}
      >
        <SafeAreaView style={styles.drawerContent}>
          {/* Header */}
          <View style={[styles.header, { backgroundColor: currentColors.background, borderBottomColor: currentColors.border }]}>
            <View style={styles.userInfo}>
              <View style={[styles.avatar, { backgroundColor: currentColors.primary + '15' }]}>
                <Ionicons name="person" size={32} color={currentColors.primary} />
              </View>
              <View>
                <Text style={[styles.userName, { color: currentColors.text }]}>John Doe</Text>
                <Text style={[styles.userEmail, { color: currentColors.textSecondary }]}><EMAIL></Text>
              </View>
            </View>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color={currentColors.textSecondary} />
            </TouchableOpacity>
          </View>

          {/* Menu Items */}
          <ScrollView style={styles.menuContainer} showsVerticalScrollIndicator={false}>
            {/* Dark Mode Toggle */}
            <View style={styles.menuSection}>
              <Text style={[styles.sectionTitle, { color: currentColors.textSecondary }]}>Appearance</Text>
              <View style={[styles.menuItem, { backgroundColor: currentColors.surface }]}>
                <Ionicons
                  name={isDarkMode ? "moon" : "sunny"}
                  size={24}
                  color={currentColors.primary}
                  style={styles.menuIcon}
                />
                <Text style={[styles.menuText, { color: currentColors.text }]}>
                  {isDarkMode ? 'Dark Mode' : 'Light Mode'}
                </Text>
                <Switch
                  value={isDarkMode}
                  onValueChange={toggleTheme}
                  trackColor={{ false: currentColors.border, true: currentColors.primary + '40' }}
                  thumbColor={isDarkMode ? currentColors.primary : '#f4f3f4'}
                />
              </View>
            </View>

            <View style={styles.menuSection}>
              <Text style={[styles.sectionTitle, { color: currentColors.textSecondary }]}>Menu</Text>
              {drawerItems.map((item) => (
                <TouchableOpacity
                  key={item.id}
                  style={[styles.menuItem, { backgroundColor: currentColors.surface, borderBottomColor: currentColors.border }]}
                  onPress={item.onPress}
                  activeOpacity={0.7}
                >
                  <Ionicons
                    name={item.icon as any}
                    size={24}
                    color={currentColors.primary}
                    style={styles.menuIcon}
                  />
                  <Text style={[styles.menuText, { color: currentColors.text }]}>{item.title}</Text>
                  <Ionicons name="chevron-forward" size={20} color={currentColors.textSecondary} />
                </TouchableOpacity>
              ))}
            </View>

            {/* App Info */}
            <View style={[styles.appInfo, { borderTopColor: currentColors.border }]}>
              <Text style={[styles.appName, { color: currentColors.text }]}>Recipe Planner</Text>
              <Text style={[styles.appVersion, { color: currentColors.textSecondary }]}>Version 1.0.0</Text>
            </View>
          </ScrollView>
        </SafeAreaView>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#000',
  },
  drawer: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    width: DRAWER_WIDTH,
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 2, height: 0 },
    shadowOpacity: 0.25,
    shadowRadius: 10,
    elevation: 10,
  },
  drawerContent: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
    backgroundColor: '#f8f9fa',
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#e3f2fd',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  userName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#212529',
    marginBottom: 2,
  },
  userEmail: {
    fontSize: 14,
    color: '#6c757d',
  },
  closeButton: {
    padding: 8,
  },
  menuContainer: {
    flex: 1,
  },
  menuSection: {
    paddingTop: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#495057',
    paddingHorizontal: 20,
    marginBottom: 16,
    textTransform: 'uppercase',
    letterSpacing: 1,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#f8f9fa',
  },
  menuIcon: {
    marginRight: 16,
    width: 24,
  },
  menuText: {
    flex: 1,
    fontSize: 16,
    color: '#495057',
    fontWeight: '500',
  },
  appInfo: {
    padding: 20,
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#e9ecef',
    marginTop: 'auto',
  },
  appName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#495057',
    marginBottom: 4,
  },
  appVersion: {
    fontSize: 14,
    color: '#adb5bd',
  },
});