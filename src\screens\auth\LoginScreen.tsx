import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';
import { AuthStackParamList } from '../../types/navigation';

type LoginScreenNavigationProp = NativeStackNavigationProp<AuthStackParamList, 'Login'>;

export const LoginScreen: React.FC = () => {
  const navigation = useNavigation<LoginScreenNavigationProp>();
  const { login, loginWithGoogle, loginWithAuth0, isLoading } = useAuth();
  const { colors: themeColors, isDark: isDarkMode } = useTheme();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [googleLoading, setGoogleLoading] = useState(false);
  const [auth0Loading, setAuth0Loading] = useState(false);

  // Professional color scheme
  const colors = {
    light: {
      background: '#F8FAFC',
      surface: '#FFFFFF',
      primary: '#475569',
      text: '#0F172A',
      textSecondary: '#64748B',
      border: '#E2E8F0',
      shadow: 'rgba(15, 23, 42, 0.1)',
      input: '#FFFFFF',
      placeholder: '#94A3B8',
    },
    dark: {
      background: '#0F172A',
      surface: '#1E293B',
      primary: '#06B6D4',
      text: '#F8FAFC',
      textSecondary: '#94A3B8',
      border: '#334155',
      shadow: 'rgba(0, 0, 0, 0.3)',
      input: '#334155',
      placeholder: '#64748B',
    }
  };

  const currentColors = isDarkMode ? colors.dark : colors.light;

  const handleLogin = async () => {
    if (!email || !password) {
      Alert.alert('Error', 'Please enter both email and password');
      return;
    }

    setLoading(true);
    try {
      const success = await login(email, password);
      if (!success) {
        Alert.alert('Login Failed', 'Invalid email or password');
      }
      // Navigation will be handled automatically by AuthContext state change
    } catch (error) {
      Alert.alert('Error', 'An error occurred during login');
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleLogin = async () => {
    setGoogleLoading(true);
    try {
      console.log('🔐 Starting Google login from LoginScreen...');
      const success = await loginWithGoogle();
      if (!success) {
        Alert.alert('Google Login Failed', 'Unable to sign in with Google. Please try again.');
      }
      // Navigation will be handled automatically by AuthContext state change
    } catch (error) {
      console.error('❌ Google login error in LoginScreen:', error);
      Alert.alert('Error', 'An error occurred during Google login');
    } finally {
      setGoogleLoading(false);
    }
  };

  const handleAuth0Login = async () => {
    setAuth0Loading(true);
    try {
      console.log('🔐 Starting Auth0 login from LoginScreen...');
      const success = await loginWithAuth0();
      if (!success) {
        Alert.alert('Auth0 Login Failed', 'Unable to sign in with Auth0. Please try again.');
      }
      // Navigation will be handled automatically by AuthContext state change
    } catch (error) {
      console.error('❌ Auth0 login error in LoginScreen:', error);
      Alert.alert('Error', 'An error occurred during Auth0 login');
    } finally {
      setAuth0Loading(false);
    }
  };

  const handleAppleLogin = () => {
    // Implement Apple login
    Alert.alert('Coming Soon', 'Apple login will be implemented soon!');
  };

  const isButtonDisabled = loading || isLoading || googleLoading || auth0Loading;

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: currentColors.background }]}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.header}>
            <View style={[styles.iconContainer, { backgroundColor: currentColors.primary + '15' }]}>
              <Ionicons name="restaurant" size={48} color={currentColors.primary} />
            </View>
            <Text style={[styles.title, { color: currentColors.text }]}>AI Recipe Planner</Text>
            <Text style={[styles.subtitle, { color: currentColors.textSecondary }]}>Welcome back!</Text>
            
            {/* Demo Credentials Info */}
            <View style={[styles.demoInfo, { backgroundColor: currentColors.surface, borderColor: currentColors.border }]}>
              <Text style={[styles.demoTitle, { color: currentColors.primary }]}>Demo Login:</Text>
              <Text style={[styles.demoText, { color: currentColors.textSecondary }]}>Email: <EMAIL></Text>
              <Text style={[styles.demoText, { color: currentColors.textSecondary }]}>Password: any password</Text>
            </View>
          </View>

          <View style={styles.form}>
            <View style={styles.inputContainer}>
              <Ionicons name="mail-outline" size={20} color={currentColors.textSecondary} style={styles.inputIcon} />
              <TextInput
                style={[
                  styles.input,
                  { 
                    backgroundColor: currentColors.input,
                    borderColor: currentColors.border,
                    color: currentColors.text
                  }
                ]}
                placeholder="Email"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                placeholderTextColor={currentColors.placeholder}
                editable={!isButtonDisabled}
              />
            </View>

            <View style={styles.inputContainer}>
              <Ionicons name="lock-closed-outline" size={20} color={currentColors.textSecondary} style={styles.inputIcon} />
              <TextInput
                style={[
                  styles.input,
                  { 
                    backgroundColor: currentColors.input,
                    borderColor: currentColors.border,
                    color: currentColors.text
                  }
                ]}
                placeholder="Password"
                value={password}
                onChangeText={setPassword}
                secureTextEntry
                placeholderTextColor={currentColors.placeholder}
                editable={!isButtonDisabled}
              />
            </View>

            <TouchableOpacity
              style={styles.forgotPassword}
              onPress={() => navigation.navigate('ForgotPassword')}
              disabled={isButtonDisabled}
            >
              <Text style={[
                styles.forgotPasswordText, 
                { color: currentColors.primary },
                isButtonDisabled && { color: currentColors.textSecondary }
              ]}>
                Forgot Password?
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.loginButton, 
                { backgroundColor: currentColors.primary, shadowColor: currentColors.shadow },
                isButtonDisabled && styles.disabledButton
              ]}
              onPress={handleLogin}
              disabled={isButtonDisabled}
            >
              {isButtonDisabled ? (
                <ActivityIndicator color="#fff" />
              ) : (
                <>
                  <Ionicons name="log-in-outline" size={18} color="white" style={{ marginRight: 8 }} />
                  <Text style={styles.loginButtonText}>Login</Text>
                </>
              )}
            </TouchableOpacity>

            <View style={styles.divider}>
              <View style={[styles.dividerLine, { backgroundColor: currentColors.border }]} />
              <Text style={[styles.dividerText, { color: currentColors.textSecondary }]}>OR</Text>
              <View style={[styles.dividerLine, { backgroundColor: currentColors.border }]} />
            </View>

            <TouchableOpacity 
              style={[
                styles.socialButton, 
                { backgroundColor: currentColors.surface, borderColor: currentColors.border },
                isButtonDisabled && styles.disabledButton
              ]} 
              onPress={handleGoogleLogin}
              disabled={isButtonDisabled}
            >
              {googleLoading ? (
                <ActivityIndicator color={currentColors.primary} />
              ) : (
                <>
                  <Ionicons name="logo-google" size={18} color={currentColors.primary} style={{ marginRight: 8 }} />
                  <Text style={[
                    styles.socialButtonText, 
                    { color: currentColors.text },
                    isButtonDisabled && { color: currentColors.textSecondary }
                  ]}>
                    Continue with Google
                  </Text>
                </>
              )}
            </TouchableOpacity>

            <TouchableOpacity 
              style={[
                styles.socialButton, 
                { backgroundColor: '#6C5CE7', borderColor: '#6C5CE7' },
                isButtonDisabled && styles.disabledButton
              ]} 
              onPress={handleAuth0Login}
              disabled={isButtonDisabled}
            >
              {auth0Loading ? (
                <ActivityIndicator color="#fff" />
              ) : (
                <>
                  <Ionicons name="shield-checkmark" size={18} color="#fff" style={{ marginRight: 8 }} />
                  <Text style={[
                    styles.socialButtonText, 
                    { color: '#fff' },
                    isButtonDisabled && { color: currentColors.textSecondary }
                  ]}>
                    Continue with Auth0
                  </Text>
                </>
              )}
            </TouchableOpacity>

            {Platform.OS === 'ios' && (
              <TouchableOpacity 
                style={[
                  styles.socialButton, 
                  styles.appleButton, 
                  { backgroundColor: currentColors.text, borderColor: currentColors.text },
                  isButtonDisabled && styles.disabledButton
                ]} 
                onPress={handleAppleLogin}
                disabled={isButtonDisabled}
              >
                <Ionicons name="logo-apple" size={18} color={currentColors.background} style={{ marginRight: 8 }} />
                <Text style={[
                  styles.socialButtonText, 
                  { color: currentColors.background },
                  isButtonDisabled && { color: currentColors.textSecondary }
                ]}>
                  Continue with Apple
                </Text>
              </TouchableOpacity>
            )}

            <View style={styles.signupContainer}>
              <Text style={[styles.signupText, { color: currentColors.textSecondary }]}>Don't have an account? </Text>
              <TouchableOpacity 
                onPress={() => navigation.navigate('Register')}
                disabled={isButtonDisabled}
              >
                <Text style={[
                  styles.signupLink, 
                  { color: currentColors.primary },
                  isButtonDisabled && { color: currentColors.textSecondary }
                ]}>
                  Sign up
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: 24,
  },
  header: {
    alignItems: 'center',
    marginBottom: 48,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 18,
    fontWeight: '500',
  },
  form: {
    width: '100%',
  },
  inputContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  inputIcon: {
    position: 'absolute',
    left: 16,
    top: 15,
    zIndex: 1,
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 48,
    fontSize: 16,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  forgotPassword: {
    alignSelf: 'flex-end',
    marginBottom: 24,
  },
  forgotPasswordText: {
    fontSize: 14,
    fontWeight: '500',
  },
  loginButton: {
    height: 50,
    borderRadius: 12,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  disabledButton: {
    opacity: 0.7,
  },
  loginButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  dividerLine: {
    flex: 1,
    height: 1,
  },
  dividerText: {
    marginHorizontal: 16,
    fontSize: 14,
    fontWeight: '500',
  },
  socialButton: {
    height: 50,
    borderWidth: 1,
    borderRadius: 12,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  appleButton: {
    // Applied inline with colors
  },
  socialButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  appleButtonText: {
    // Applied inline with colors
  },
  signupContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 24,
  },
  signupText: {
    fontSize: 14,
    fontWeight: '500',
  },
  signupLink: {
    fontSize: 14,
    fontWeight: '600',
  },
  disabledText: {
    // Applied inline with colors
  },
  demoInfo: {
    marginTop: 24,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  demoTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  demoText: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
    lineHeight: 16,
  },
}); 