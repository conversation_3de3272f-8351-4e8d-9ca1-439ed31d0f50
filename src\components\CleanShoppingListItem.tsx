import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
} from 'react-native';
import { Swipeable } from 'react-native-gesture-handler';
import { useTheme } from '../context/ThemeContext';
import { theme } from '../styles';
import { CleanPriceComparison } from './CleanPriceComparison';

interface StorePrice {
  store: 'woolworths' | 'newworld' | 'paknsave';
  price?: number;
  available: boolean;
  brand?: string;
  size?: string;
  specialPrice?: number;
  isSpecial?: boolean;
  imageUrl?: string;
}

interface CleanShoppingListItemProps {
  id: string;
  name: string;
  checked?: boolean;
  quantity?: number;
  unit?: string;
  category?: string;
  storePrices: StorePrice[];
  onToggleChecked: (itemId: string) => void;
  onQuantityChange?: (itemId: string, quantity: number) => void;
  onDelete: (itemId: string) => void;
  showPrices?: boolean;
  compact?: boolean;
  productImage?: string;
  showTwoPricesOnly?: boolean;
}

export const CleanShoppingListItem: React.FC<CleanShoppingListItemProps> = ({
  id,
  name,
  checked = false,
  quantity = 1,
  unit,
  category,
  storePrices,
  onToggleChecked,
  onQuantityChange,
  onDelete,
  showPrices = true,
  compact = false,
  productImage,
  showTwoPricesOnly = false,
}) => {
  const { colors } = useTheme();

  const handleCheckboxPress = () => {
    onToggleChecked(id);
  };

  const handleQuantityPress = () => {
    if (!onQuantityChange) return;
    
    // Simple quantity selector - cycles through 1-5
    const newQuantity = quantity >= 5 ? 1 : quantity + 1;
    onQuantityChange(id, newQuantity);
  };

  const renderRightAction = () => (
    <Animated.View style={[styles.deleteAction, { backgroundColor: '#FF6B6B' }]}>
      <TouchableOpacity
        style={styles.deleteButton}
        onPress={() => onDelete(id)}
      >
        <Text style={styles.deleteText}>🗑️</Text>
        <Text style={[styles.deleteLabel, { color: 'white' }]}>Delete</Text>
      </TouchableOpacity>
    </Animated.View>
  );

  const renderQuantityBadge = () => {
    if (quantity <= 1) return null;

    return (
      <TouchableOpacity
        style={[styles.quantityBadge, { backgroundColor: theme.colors.primary }]}
        onPress={handleQuantityPress}
      >
        <Text style={styles.quantityText}>
          {quantity}{unit && ` ${unit}`}
        </Text>
      </TouchableOpacity>
    );
  };

  const renderCategoryBadge = () => {
    if (compact || !category || category === 'Groceries') return null;

    return (
      <View style={[styles.categoryBadge, { backgroundColor: colors.backgroundSecondary }]}>
        <Text style={[styles.categoryText, { color: colors.textTertiary }]}>
          {category}
        </Text>
      </View>
    );
  };

  if (showPrices) {
    // Full price comparison view
    return (
      <Swipeable renderRightActions={renderRightAction}>
        <View style={[
          styles.priceComparisonContainer,
          { 
            backgroundColor: colors.surface,
            borderBottomColor: colors.border,
          },
          checked && styles.checkedContainer,
          compact && styles.compactContainer
        ]}>
          {/* Header with checkbox and item info */}
          <View style={styles.headerSection}>
            <TouchableOpacity
              style={[
                styles.checkbox,
                {
                  backgroundColor: checked ? theme.colors.primary : 'transparent',
                  borderColor: checked ? theme.colors.primary : colors.border,
                }
              ]}
              onPress={handleCheckboxPress}
            >
              {checked && (
                <Text style={styles.checkmark}>✓</Text>
              )}
            </TouchableOpacity>

            <View style={styles.itemInfo}>
              <View style={styles.itemHeader}>
                <Text style={[
                  styles.itemName,
                  { color: colors.text },
                  checked && styles.checkedText
                ]}>
                  {name}
                </Text>
                {renderQuantityBadge()}
              </View>
              
              {!compact && (
                <View style={styles.itemMeta}>
                  {renderCategoryBadge()}
                </View>
              )}
            </View>
          </View>

          {/* Price comparison section */}
          <CleanPriceComparison
            productName=""
            storePrices={storePrices}
            showPrices={true}
            compact={compact}
            productImage={productImage}
            showTwoPricesOnly={showTwoPricesOnly}
          />
        </View>
      </Swipeable>
    );
  }

  // Simple list view without prices
  return (
    <Swipeable renderRightActions={renderRightAction}>
      <View style={[
        styles.simpleContainer,
        { 
          backgroundColor: colors.surface,
          borderBottomColor: colors.border,
        },
        checked && styles.checkedContainer,
        compact && styles.compactContainer
      ]}>
        <TouchableOpacity
          style={[
            styles.checkbox,
            {
              backgroundColor: checked ? theme.colors.primary : 'transparent',
              borderColor: checked ? theme.colors.primary : colors.border,
            }
          ]}
          onPress={handleCheckboxPress}
        >
          {checked && (
            <Text style={styles.checkmark}>✓</Text>
          )}
        </TouchableOpacity>

        <View style={styles.simpleItemDetails}>
          <View style={styles.simpleItemHeader}>
            <Text style={[
              styles.itemName,
              { color: colors.text },
              checked && styles.checkedText
            ]}>
              {name}
            </Text>
            {renderQuantityBadge()}
          </View>
          
          {!compact && (
            <View style={styles.itemMeta}>
              {renderCategoryBadge()}
            </View>
          )}
        </View>
      </View>
    </Swipeable>
  );
};

export const CleanShoppingListItemSeparator: React.FC = () => {
  const { colors } = useTheme();
  
  return (
    <View style={[styles.separator, { backgroundColor: colors.border }]} />
  );
};

export const CleanShoppingListItemSkeleton: React.FC<{ compact?: boolean }> = ({ 
  compact = false 
}) => {
  const { colors } = useTheme();
  
  return (
    <View style={[
      styles.simpleContainer,
      { backgroundColor: colors.surface, borderBottomColor: colors.border },
      compact && styles.compactContainer
    ]}>
      <View style={[styles.skeleton, styles.checkboxSkeleton, { backgroundColor: colors.border }]} />
      <View style={styles.simpleItemDetails}>
        <View style={[styles.skeleton, styles.nameSkeleton, { backgroundColor: colors.border }]} />
        {!compact && (
          <View style={[styles.skeleton, styles.categorySkeleton, { backgroundColor: colors.border }]} />
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  priceComparisonContainer: {
    borderBottomWidth: 1,
    paddingBottom: theme.spacing.sm,
  },
  simpleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.base,
    paddingVertical: theme.spacing.sm,
    borderBottomWidth: 1,
    minHeight: 60,
  },
  compactContainer: {
    paddingVertical: theme.spacing.xs,
    minHeight: 48,
  },
  checkedContainer: {
    opacity: 0.6,
  },
  headerSection: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingHorizontal: theme.spacing.base,
    paddingTop: theme.spacing.sm,
    paddingBottom: theme.spacing.xs,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 6,
    borderWidth: 2,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: theme.spacing.sm,
    marginTop: 2, // Align with text baseline
  },
  checkmark: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  itemInfo: {
    flex: 1,
  },
  itemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  itemName: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.medium,
    flex: 1,
    lineHeight: 22,
  },
  checkedText: {
    textDecorationLine: 'line-through',
  },
  itemMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
    gap: theme.spacing.xs,
  },
  quantityBadge: {
    paddingHorizontal: theme.spacing.xs,
    paddingVertical: 4,
    borderRadius: theme.radius.sm,
    marginLeft: theme.spacing.xs,
  },
  quantityText: {
    color: 'white',
    fontSize: theme.typography.fontSize.xs,
    fontWeight: theme.typography.fontWeight.semibold,
  },
  categoryBadge: {
    paddingHorizontal: theme.spacing.xs,
    paddingVertical: 2,
    borderRadius: theme.radius.sm,
  },
  categoryText: {
    fontSize: theme.typography.fontSize.xs,
  },
  simpleItemDetails: {
    flex: 1,
  },
  simpleItemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  deleteAction: {
    justifyContent: 'center',
    alignItems: 'center',
    width: 80,
  },
  deleteButton: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    width: '100%',
  },
  deleteText: {
    fontSize: 24,
    marginBottom: 4,
  },
  deleteLabel: {
    fontSize: theme.typography.fontSize.xs,
    fontWeight: theme.typography.fontWeight.semibold,
  },
  separator: {
    height: 1,
    marginHorizontal: theme.spacing.base,
  },
  skeleton: {
    borderRadius: 4,
  },
  checkboxSkeleton: {
    width: 24,
    height: 24,
    borderRadius: 6,
    marginRight: theme.spacing.sm,
  },
  nameSkeleton: {
    height: 16,
    width: '70%',
    marginBottom: 4,
  },
  categorySkeleton: {
    height: 12,
    width: '40%',
  },
});