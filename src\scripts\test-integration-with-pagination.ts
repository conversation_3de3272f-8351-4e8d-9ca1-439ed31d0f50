/**
 * INTEGRATION TEST: Deduplication + Pagination
 * 
 * Tests the complete integration of the product deduplication service
 * with the pagination system we implemented to handle 10,000+ products.
 */

import { createClient } from '@supabase/supabase-js';
import { ProductDeduplicationService } from '../services/productDeduplicationService';
import { IProduct } from '../types/shoppingList';

const supabaseUrl = 'https://emjwniuqwhvohjassnrg.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVtanduaXVxd2h2b2hqYXNzbnJnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjQwMTYwNSwiZXhwIjoyMDY3OTc3NjA1fQ.oTowRoNAjlUmk10O9pSMK2BvL0XlyBb5orVRrlEryHs';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

/**
 * Simulate the pagination system from productFetchService
 */
async function fetchProductsWithPagination(maxResults: number): Promise<IProduct[]> {
  console.log(`📦 Fetching ${maxResults} products using pagination...`);
  
  const allProducts: IProduct[] = [];
  const chunkSize = 1000;
  let offset = 0;
  
  while (allProducts.length < maxResults && offset < maxResults) {
    const remainingResults = Math.min(chunkSize, maxResults - allProducts.length);
    
    console.log(`   📦 Fetching chunk: offset ${offset}, limit ${remainingResults}`);
    
    const { data: chunkData, error } = await supabase
      .from('products')
      .select('*')
      .not('price', 'is', null)
      .order('name', { ascending: true })
      .range(offset, offset + remainingResults - 1);
    
    if (error) {
      console.error(`   ❌ Error fetching chunk at offset ${offset}:`, error);
      break;
    }
    
    if (!chunkData || chunkData.length === 0) {
      console.log(`   ⚠️  No more products found at offset ${offset}`);
      break;
    }
    
    allProducts.push(...chunkData);
    console.log(`   ✅ Fetched ${chunkData.length} products, total: ${allProducts.length}`);
    
    offset += chunkSize;
  }
  
  return allProducts;
}

async function testIntegrationWithPagination() {
  console.log('🧪 Testing Deduplication + Pagination Integration...\n');

  const deduplicationService = new ProductDeduplicationService();

  try {
    // Test 1: Small dataset (simulating normal app usage)
    console.log('1. Testing with small dataset (1000 products)...');
    
    const smallStartTime = Date.now();
    const smallDataset = await fetchProductsWithPagination(1000);
    const smallFetchTime = Date.now() - smallStartTime;
    
    console.log(`   📊 Fetched ${smallDataset.length} products in ${smallFetchTime}ms`);
    
    const smallDeduplicationStart = Date.now();
    const smallResult = deduplicationService.deduplicateProducts(smallDataset);
    const smallDeduplicationTime = Date.now() - smallDeduplicationStart;
    
    console.log(`   📊 Deduplication results:`);
    console.log(`      Processing time: ${smallDeduplicationTime}ms`);
    console.log(`      Original products: ${smallResult.stats.originalCount}`);
    console.log(`      Unified products: ${smallResult.stats.unifiedCount}`);
    console.log(`      Reduction: ${((1 - smallResult.stats.unifiedCount / smallResult.stats.originalCount) * 100).toFixed(1)}%`);
    console.log(`      Cross-store matches: ${smallResult.groups.filter(g => g.stores.length > 1).length}`);

    // Test 2: Medium dataset (simulating power user)
    console.log('\n2. Testing with medium dataset (2500 products)...');
    
    const mediumStartTime = Date.now();
    const mediumDataset = await fetchProductsWithPagination(2500);
    const mediumFetchTime = Date.now() - mediumStartTime;
    
    console.log(`   📊 Fetched ${mediumDataset.length} products in ${mediumFetchTime}ms`);
    
    const mediumDeduplicationStart = Date.now();
    const mediumResult = deduplicationService.deduplicateProducts(mediumDataset);
    const mediumDeduplicationTime = Date.now() - mediumDeduplicationStart;
    
    console.log(`   📊 Deduplication results:`);
    console.log(`      Processing time: ${mediumDeduplicationTime}ms`);
    console.log(`      Original products: ${mediumResult.stats.originalCount}`);
    console.log(`      Unified products: ${mediumResult.stats.unifiedCount}`);
    console.log(`      Reduction: ${((1 - mediumResult.stats.unifiedCount / mediumResult.stats.originalCount) * 100).toFixed(1)}%`);
    console.log(`      Cross-store matches: ${mediumResult.groups.filter(g => g.stores.length > 1).length}`);

    // Test 3: Performance analysis
    console.log('\n3. Performance analysis...');
    
    const totalFetchTime = smallFetchTime + mediumFetchTime;
    const totalDeduplicationTime = smallDeduplicationTime + mediumDeduplicationTime;
    const totalProducts = smallDataset.length + mediumDataset.length;
    
    console.log(`   ⚡ Performance metrics:`);
    console.log(`      Total products processed: ${totalProducts}`);
    console.log(`      Total fetch time: ${totalFetchTime}ms`);
    console.log(`      Total deduplication time: ${totalDeduplicationTime}ms`);
    console.log(`      Fetch rate: ${Math.round(totalProducts / (totalFetchTime / 1000))} products/second`);
    console.log(`      Deduplication rate: ${Math.round(totalProducts / (totalDeduplicationTime / 1000))} products/second`);
    console.log(`      Total processing time: ${totalFetchTime + totalDeduplicationTime}ms`);

    // Test 4: Memory usage simulation
    console.log('\n4. Memory usage analysis...');
    
    const avgProductSize = JSON.stringify(smallDataset[0]).length;
    const estimatedMemoryUsage = (totalProducts * avgProductSize) / (1024 * 1024); // MB
    
    console.log(`   💾 Memory estimates:`);
    console.log(`      Average product size: ${avgProductSize} bytes`);
    console.log(`      Estimated memory usage: ${estimatedMemoryUsage.toFixed(2)} MB`);
    console.log(`      Memory efficiency: ${estimatedMemoryUsage < 50 ? 'GOOD' : estimatedMemoryUsage < 100 ? 'ACCEPTABLE' : 'HIGH'}`);

    // Test 5: Real-world scenario simulation
    console.log('\n5. Real-world scenario simulation...');
    
    // Simulate user searching for "milk" products
    const milkProducts = smallDataset.filter(p => 
      p.name.toLowerCase().includes('milk')
    );
    
    if (milkProducts.length > 0) {
      const milkResult = deduplicationService.deduplicateProducts(milkProducts);
      const milkCrossStore = milkResult.groups.filter(g => g.stores.length > 1);
      
      console.log(`   🥛 Milk products scenario:`);
      console.log(`      Found ${milkProducts.length} milk products`);
      console.log(`      After deduplication: ${milkResult.stats.unifiedCount} products`);
      console.log(`      Cross-store matches: ${milkCrossStore.length}`);
      console.log(`      User sees ${((1 - milkResult.stats.unifiedCount / milkResult.stats.originalCount) * 100).toFixed(1)}% fewer duplicate cards`);
    }

    // Test 6: Configuration optimization
    console.log('\n6. Configuration optimization test...');
    
    const testProducts = smallDataset.slice(0, 200); // Use subset for quick testing
    
    const configs = [
      { name: 'Conservative', nameSimilarityThreshold: 0.9, minimumConfidence: 0.8 },
      { name: 'Balanced', nameSimilarityThreshold: 0.85, minimumConfidence: 0.7 },
      { name: 'Aggressive', nameSimilarityThreshold: 0.75, minimumConfidence: 0.6 }
    ];
    
    console.log(`   🔧 Testing different configurations on ${testProducts.length} products:`);
    
    for (const config of configs) {
      const configResult = deduplicationService.deduplicateProducts(testProducts, config);
      const crossStoreMatches = configResult.groups.filter(g => g.stores.length > 1).length;
      
      console.log(`      ${config.name}: ${configResult.stats.unifiedCount} unified (${((1 - configResult.stats.unifiedCount / configResult.stats.originalCount) * 100).toFixed(1)}% reduction), ${crossStoreMatches} cross-store matches`);
    }

    console.log('\n✅ Integration test completed successfully!');
    
    return {
      smallDatasetSize: smallDataset.length,
      mediumDatasetSize: mediumDataset.length,
      totalProcessingTime: totalFetchTime + totalDeduplicationTime,
      smallReduction: ((1 - smallResult.stats.unifiedCount / smallResult.stats.originalCount) * 100),
      mediumReduction: ((1 - mediumResult.stats.unifiedCount / mediumResult.stats.originalCount) * 100),
      estimatedMemoryUsage
    };

  } catch (error) {
    console.error('❌ Integration test failed:', error);
    throw error;
  }
}

// Run the integration test
if (require.main === module) {
  testIntegrationWithPagination()
    .then((results) => {
      console.log('\n📊 INTEGRATION TEST SUMMARY:');
      console.log('='.repeat(60));
      console.log(`Small Dataset: ${results?.smallDatasetSize || 0} products`);
      console.log(`Medium Dataset: ${results?.mediumDatasetSize || 0} products`);
      console.log(`Total Processing Time: ${results?.totalProcessingTime || 0}ms`);
      console.log(`Small Dataset Reduction: ${results?.smallReduction?.toFixed(1) || 0}%`);
      console.log(`Medium Dataset Reduction: ${results?.mediumReduction?.toFixed(1) || 0}%`);
      console.log(`Estimated Memory Usage: ${results?.estimatedMemoryUsage?.toFixed(2) || 0} MB`);
      
      const isPerformanceGood = (results?.totalProcessingTime || 0) < 10000; // Under 10 seconds
      const isMemoryGood = (results?.estimatedMemoryUsage || 0) < 100; // Under 100 MB
      const isReductionGood = (results?.smallReduction || 0) > 20; // At least 20% reduction
      
      if (isPerformanceGood && isMemoryGood && isReductionGood) {
        console.log('\n🎉 INTEGRATION SUCCESS: System is ready for production!');
        console.log('   ✅ Pagination handles large datasets efficiently');
        console.log('   ✅ Deduplication provides significant reduction in visual clutter');
        console.log('   ✅ Performance is acceptable for real-world usage');
        console.log('   ✅ Memory usage is within reasonable limits');
        console.log('   ✅ Ready to implement consolidated product cards (Story 1.2)');
      } else {
        console.log('\n⚠️  INTEGRATION PARTIAL: Some optimizations may be needed');
        if (!isPerformanceGood) console.log('   - Consider performance optimizations');
        if (!isMemoryGood) console.log('   - Consider memory usage optimizations');
        if (!isReductionGood) console.log('   - Consider adjusting deduplication criteria');
      }
      console.log('='.repeat(60));
    })
    .catch((error) => {
      console.error('❌ Integration test failed:', error);
    });
}

export { testIntegrationWithPagination };
