/**
 * PRODUCT SYNC SCRIPT - ALGOLIA TO SUPABASE
 * 
 * This script is used to sync product data FROM Algolia TO Supabase.
 * It's a one-time migration/sync script, not part of the app's runtime.
 * 
 * The Algolia import here is intentional and should NOT be removed.
 * This script reads from Algolia and writes to Supabase.
 */

import { supabase } from './client';
// NOTE: This import is intentionally kept - this file syncs data FROM Algolia TO Supabase
// It's part of the data migration process, not the app's runtime dependencies
import algoliasearch from 'algoliasearch';

const ALGOLIA_APP_ID = 'CXNOUIM54D';
const ALGOLIA_API_KEY = '********************************';

const STORES = {
  woolworths: { indexName: 'woolworths', displayName: 'Woolworths', color: '#00A651', logo: '🍎' },
  newworld: { indexName: 'newworld', displayName: 'New World', color: '#E31E24', logo: '🛒' },
  paknsave: { indexName: 'products', displayName: "Pak'nSave", color: '#FFD100', logo: '💰' }
};

const BRANDS = [
  'Anchor', 'Mainland', 'Meadow Fresh', 'Lewis Road', 'Calci', 'Puhoi Valley', 'Cowpuccino', 
  'Fresh n Fruity', 'Yoplait', 'Tui', 'Kapiti', 'Watties', 'ETA', 'Heinz', 'Maggi', 
  'Continental', 'Greggs', 'McCain', 'Birds Eye', 'Edgell', 'SPC', 'Fountain', 
  'MasterFoods', 'Rosella', 'Tip Top', 'Much Moore', 'Freya', 'Molenberg', 'Wonder White', 
  'Mighty Soft', 'Cadbury', 'Whittakers', "Whittaker's", 'Griffins', "Griffin's", 
  'Arnotts', "Arnott's", 'Pascall', 'Nestle', 'Mars', 'Snickers', 'Kit Kat', 'Pams', 
  'Budget', 'Value', 'No Frills', 'Homebrand', 'Macro', 'Woolworths', 'Signature Range', 
  'New World', 'Essentials'
];

const SHOPPING_CATEGORIES = {
  essential: {
    milk: ['milk', 'fresh milk', 'whole milk', 'full cream milk', 'standard milk', 'trim milk', 'lite milk', 'light milk'],
    bread: ['bread', 'white bread', 'sandwich bread', 'sliced bread', 'loaf', 'wholemeal bread', 'brown bread'],
    eggs: ['eggs', 'free range eggs', 'cage free eggs', 'barn eggs', 'dozen eggs'],
    butter: ['butter', 'salted butter', 'unsalted butter'],
    cheese: ['cheese', 'cheddar', 'tasty cheese', 'mild cheese', 'colby'],
    yogurt: ['yogurt', 'yoghurt', 'greek yogurt', 'natural yogurt']
  },
  protein: {
    chicken: ['chicken', 'chicken breast', 'chicken thigh', 'whole chicken', 'chicken pieces'],
    beef: ['beef', 'mince', 'ground beef', 'beef mince', 'steak', 'roast'],
    lamb: ['lamb', 'lamb chops', 'leg of lamb', 'lamb roast'],
    fish: ['fish', 'salmon', 'tuna', 'snapper', 'fish fillets']
  },
  produce: {
    bananas: ['bananas', 'banana'],
    apples: ['apples', 'apple', 'royal gala', 'granny smith', 'braeburn'],
    potatoes: ['potatoes', 'potato', 'agria potatoes', 'red potatoes'],
    onions: ['onions', 'onion', 'brown onions', 'red onions'],
    carrots: ['carrots', 'carrot'],
    tomatoes: ['tomatoes', 'tomato', 'cherry tomatoes']
  },
  pantry: {
    rice: ['rice', 'white rice', 'jasmine rice', 'basmati rice'],
    pasta: ['pasta', 'spaghetti', 'penne', 'fettuccine'],
    flour: ['flour', 'plain flour', 'self raising flour'],
    sugar: ['sugar', 'white sugar', 'caster sugar'],
    oil: ['oil', 'olive oil', 'vegetable oil', 'cooking oil']
  }
};

function getCategoryDisplayName(categoryType: string) {
    const categoryMap: { [key: string]: string } = {
      essential: 'Dairy',
      protein: 'Meat',
      produce: 'Produce',
      pantry: 'Pantry'
    };
    return categoryMap[categoryType] || 'Other';
}

function enhanceBrand(productName: string, existingBrand: string) {
  if (existingBrand && existingBrand !== 'Generic' && existingBrand !== 'Unknown') {
    return existingBrand;
  }
  const name = productName.toLowerCase();
  for (const brand of BRANDS) {
    if (name.includes(brand.toLowerCase())) {
      return brand;
    }
  }
  return 'Generic';
}

function extractValidPrice(text: string) {
  if (!text) return null;
  const match = text.match(/\$(\d+\.?\d*)/);
  if (match) {
    const price = parseFloat(match[1]);
    if (!isNaN(price) && price > 0) {
      return price;
    }
  }
  return null;
}

function categorizeProduct(productName: string) {
  const name = productName.toLowerCase();
  for (const [categoryType, items] of Object.entries(SHOPPING_CATEGORIES)) {
    for (const [itemKey, aliases] of Object.entries(items)) {
      for (const alias of aliases) {
        if (name.includes(alias.toLowerCase())) {
          return {
            category: getCategoryDisplayName(categoryType),
            aliases: aliases,
            priority: categoryType === 'essential' ? 1 : 2,
          };
        }
      }
    }
  }
  return { category: 'Other', priority: 3, aliases: [] };
}

function normalizeProductData(rawProduct: any, store: string) {
  const storeConfig = STORES[store as keyof typeof STORES];
  let price = 0;
  if (rawProduct.price && !isNaN(parseFloat(rawProduct.price))) {
    price = parseFloat(rawProduct.price);
  } else {
    const extractedPrice = extractValidPrice(rawProduct.name) || extractValidPrice(rawProduct.description || '');
    if(extractedPrice) price = extractedPrice;
  }
  
  const categoryInfo = categorizeProduct(rawProduct.name);
  const brand = enhanceBrand(rawProduct.name, rawProduct.brand);
  
  return {
    objectID: rawProduct.objectID,
    name: rawProduct.name,
    price: price,
    category: categoryInfo.category,
    brand: brand,
    unit: rawProduct.unit || '',
    availability: rawProduct.availability || 'in_stock',
    store: store,
    store_name: storeConfig.displayName,
    store_color: storeConfig.color,
    store_logo: storeConfig.logo,
    last_updated: new Date().toISOString(),
    description: rawProduct.description || '',
    image_url: rawProduct.image_url || rawProduct.imageUrl,
    promotions: rawProduct.promotions || null,
    shopping_aliases: categoryInfo.aliases,
    shopping_priority: categoryInfo.priority,
  };
}


async function syncProducts() {
  console.log('🚀 Starting product sync with Supabase...');
  const algoliaClient = algoliasearch(ALGOLIA_APP_ID, ALGOLIA_API_KEY);

  for (const store of Object.keys(STORES)) {
    const storeConfig = STORES[store as keyof typeof STORES];
    console.log(`\n---\nProcessing store: ${storeConfig.displayName}`);
    
    const index = algoliaClient.initIndex(storeConfig.indexName);
    let allProducts: any[] = [];
    
    await index.browseObjects({
      batch: (batch) => {
        allProducts = allProducts.concat(batch);
      },
    });

    console.log(`Fetched ${allProducts.length} products from Algolia index: ${storeConfig.indexName}`);

    const processedProducts = allProducts.map(p => normalizeProductData(p, store));
    console.log(`Processed ${processedProducts.length} products.`);

    if (processedProducts.length > 0) {
      const { error } = await supabase.from('products').upsert(processedProducts, { onConflict: 'objectID' });

      if (error) {
        console.error(`Error upserting products for ${store}:`, error);
      } else {
        console.log(`Successfully synced ${processedProducts.length} products for ${store} to Supabase.`);
      }
    }
  }

  console.log('\n✅ Product sync completed.');
}

syncProducts(); 