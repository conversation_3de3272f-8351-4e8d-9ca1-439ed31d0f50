import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { GoogleAuthService } from '../services/googleAuthService';
import { Auth0Service } from '../services/auth0Service';

interface AuthContextType {
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  loginWithGoogle: () => Promise<boolean>;
  loginWithAuth0: () => Promise<boolean>;
  logout: () => Promise<void>;
  user: User | null;
}

interface User {
  id: string;
  email: string;
  name: string;
  picture?: string;
  authProvider?: 'email' | 'google' | 'auth0';
}

interface AuthProviderProps {
  children: ReactNode;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const AUTH_STORAGE_KEY = '@auth_token';
const USER_STORAGE_KEY = '@user_data';

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState<User | null>(null);

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const [authToken, userData] = await Promise.all([
        AsyncStorage.getItem(AUTH_STORAGE_KEY),
        AsyncStorage.getItem(USER_STORAGE_KEY),
      ]);

      if (authToken && userData) {
        setIsAuthenticated(true);
        setUser(JSON.parse(userData));
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      
      // Mock authentication - in a real app, this would call your API
      if (email && password) {
        const mockUser: User = {
          id: '1',
          email: email,
          name: email.split('@')[0], // Use part of email as name
          authProvider: 'email',
        };

        const authToken = 'mock_auth_token_' + Date.now();
        
        await Promise.all([
          AsyncStorage.setItem(AUTH_STORAGE_KEY, authToken),
          AsyncStorage.setItem(USER_STORAGE_KEY, JSON.stringify(mockUser)),
        ]);

        setIsAuthenticated(true);
        setUser(mockUser);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Login error:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const loginWithGoogle = async (): Promise<boolean> => {
    try {
      setIsLoading(true);
      console.log('🔐 Starting Google login...');
      
      const result = await GoogleAuthService.mockGoogleSignIn(); // Use mock for development
      
      if (result.success && result.user) {
        const user: User = {
          id: result.user.id,
          email: result.user.email,
          name: result.user.name,
          picture: result.user.picture,
          authProvider: 'google',
        };

        const authToken = result.accessToken || 'google_auth_token_' + Date.now();
        
        await Promise.all([
          AsyncStorage.setItem(AUTH_STORAGE_KEY, authToken),
          AsyncStorage.setItem(USER_STORAGE_KEY, JSON.stringify(user)),
        ]);

        setIsAuthenticated(true);
        setUser(user);
        console.log('✅ Google login successful');
        return true;
      } else {
        console.log('❌ Google login failed:', result.error);
        return false;
      }
    } catch (error) {
      console.error('❌ Google login error:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const loginWithAuth0 = async (): Promise<boolean> => {
    try {
      setIsLoading(true);
      console.log('🔐 Starting Auth0 login...');
      
      // Check if Auth0 is configured
      if (!Auth0Service.isConfigured()) {
        console.log('⚠️ Auth0 not configured, using mock login');
        const result = await Auth0Service.mockLogin();
        
        if (result.success && result.user) {
          const user: User = {
            id: result.user.id,
            email: result.user.email,
            name: result.user.name,
            picture: result.user.picture,
            authProvider: 'auth0',
          };

          const authToken = result.accessToken || 'auth0_mock_token_' + Date.now();
          
          await Promise.all([
            AsyncStorage.setItem(AUTH_STORAGE_KEY, authToken),
            AsyncStorage.setItem(USER_STORAGE_KEY, JSON.stringify(user)),
          ]);

          setIsAuthenticated(true);
          setUser(user);
          console.log('✅ Auth0 mock login successful');
          return true;
        }
        return false;
      }
      
      // Use real Auth0 login
      const result = await Auth0Service.authorize();
      
      if (result.success && result.user) {
        const user: User = {
          id: result.user.id,
          email: result.user.email,
          name: result.user.name,
          picture: result.user.picture,
          authProvider: 'auth0',
        };

        const authToken = result.accessToken || 'auth0_auth_token_' + Date.now();
        
        await Promise.all([
          AsyncStorage.setItem(AUTH_STORAGE_KEY, authToken),
          AsyncStorage.setItem(USER_STORAGE_KEY, JSON.stringify(user)),
        ]);

        setIsAuthenticated(true);
        setUser(user);
        console.log('✅ Auth0 login successful');
        return true;
      } else {
        console.log('❌ Auth0 login failed:', result.error);
        return false;
      }
    } catch (error) {
      console.error('❌ Auth0 login error:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    try {
      setIsLoading(true);
      
      // Sign out from the appropriate service based on auth provider
      if (user?.authProvider === 'google') {
        await GoogleAuthService.signOut();
      } else if (user?.authProvider === 'auth0') {
        await Auth0Service.clearSession();
      }
      
      await Promise.all([
        AsyncStorage.removeItem(AUTH_STORAGE_KEY),
        AsyncStorage.removeItem(USER_STORAGE_KEY),
      ]);

      setIsAuthenticated(false);
      setUser(null);
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const value: AuthContextType = {
    isAuthenticated,
    isLoading,
    login,
    loginWithGoogle,
    loginWithAuth0,
    logout,
    user,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}; 