import AsyncStorage from '@react-native-async-storage/async-storage';

export interface BrandPreference {
  productType: string; // "milk", "bread", etc.
  preferredBrand: string; // "Anchor", "Tip Top", etc.
  preferredSize: string; // "2L", "700g", etc.
  confidenceScore: number; // 0-1 based on usage
  lastUsed: string; // ISO date string
  timesSelected: number; // How many times user selected this
  createdAt: string; // ISO date string
}

export interface ShoppingPreferences {
  brandPreferences: BrandPreference[];
  defaultStrategy: 'cheapest' | 'convenience' | 'balanced';
  preferredStores: string[]; // Ordered by preference
  budgetAlerts: boolean;
  priceComparisonEnabled: boolean;
  autoSelectPreferredBrands: boolean;
  lastUpdated: string;
}

const PREFERENCES_KEY = 'user_shopping_preferences';
const BRAND_PREFERENCES_KEY = 'brand_preferences';

export class UserPreferences {
  
  /**
   * Set or update a brand preference for a product type
   */
  static async setBrandPreference(
    productType: string, 
    brand: string, 
    size: string
  ): Promise<void> {
    try {
      const preferences = await this.getAllBrandPreferences();
      const existingIndex = preferences.findIndex(p => p.productType === productType);
      
      if (existingIndex >= 0) {
        // Update existing preference
        const existing = preferences[existingIndex];
        preferences[existingIndex] = {
          ...existing,
          preferredBrand: brand,
          preferredSize: size,
          lastUsed: new Date().toISOString(),
          timesSelected: existing.timesSelected + 1,
          confidenceScore: Math.min(1.0, existing.confidenceScore + 0.1) // Increase confidence
        };
      } else {
        // Create new preference
        preferences.push({
          productType,
          preferredBrand: brand,
          preferredSize: size,
          confidenceScore: 0.3, // Start with low confidence
          lastUsed: new Date().toISOString(),
          timesSelected: 1,
          createdAt: new Date().toISOString()
        });
      }
      
      await AsyncStorage.setItem(BRAND_PREFERENCES_KEY, JSON.stringify(preferences));
      console.log(`✅ Brand preference saved: ${productType} -> ${brand} (${size})`);
      
    } catch (error) {
      console.error('❌ Error saving brand preference:', error);
    }
  }
  
  /**
   * Get brand preference for a specific product type
   */
  static async getBrandPreference(productType: string): Promise<BrandPreference | null> {
    try {
      const preferences = await this.getAllBrandPreferences();
      const preference = preferences.find(p => p.productType === productType);
      
      if (preference && preference.confidenceScore > 0.2) {
        console.log(`🎯 Found brand preference: ${productType} -> ${preference.preferredBrand}`);
        return preference;
      }
      
      return null;
    } catch (error) {
      console.error('❌ Error getting brand preference:', error);
      return null;
    }
  }
  
  /**
   * Get all brand preferences
   */
  static async getAllBrandPreferences(): Promise<BrandPreference[]> {
    try {
      const stored = await AsyncStorage.getItem(BRAND_PREFERENCES_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('❌ Error getting all brand preferences:', error);
      return [];
    }
  }
  
  /**
   * Remove a brand preference
   */
  static async removeBrandPreference(productType: string): Promise<void> {
    try {
      const preferences = await this.getAllBrandPreferences();
      const filtered = preferences.filter(p => p.productType !== productType);
      await AsyncStorage.setItem(BRAND_PREFERENCES_KEY, JSON.stringify(filtered));
      console.log(`🗑️ Removed brand preference for: ${productType}`);
    } catch (error) {
      console.error('❌ Error removing brand preference:', error);
    }
  }
  
  /**
   * Get general shopping preferences
   */
  static async getShoppingPreferences(): Promise<ShoppingPreferences> {
    try {
      const stored = await AsyncStorage.getItem(PREFERENCES_KEY);
      const brandPreferences = await this.getAllBrandPreferences();
      
      const defaultPrefs: ShoppingPreferences = {
        brandPreferences,
        defaultStrategy: 'balanced',
        preferredStores: ['paknsave', 'newworld', 'woolworths'], // Cheapest first
        budgetAlerts: true,
        priceComparisonEnabled: true,
        autoSelectPreferredBrands: true,
        lastUpdated: new Date().toISOString()
      };
      
      if (stored) {
        const parsed = JSON.parse(stored);
        return { ...defaultPrefs, ...parsed, brandPreferences };
      }
      
      return defaultPrefs;
    } catch (error) {
      console.error('❌ Error getting shopping preferences:', error);
      return {
        brandPreferences: [],
        defaultStrategy: 'balanced',
        preferredStores: ['paknsave', 'newworld', 'woolworths'],
        budgetAlerts: true,
        priceComparisonEnabled: true,
        autoSelectPreferredBrands: true,
        lastUpdated: new Date().toISOString()
      };
    }
  }
  
  /**
   * Update shopping preferences
   */
  static async setShoppingPreferences(
    preferences: Partial<Omit<ShoppingPreferences, 'brandPreferences'>>
  ): Promise<void> {
    try {
      const current = await this.getShoppingPreferences();
      const updated = {
        ...current,
        ...preferences,
        lastUpdated: new Date().toISOString()
      };
      
      // Don't store brand preferences here - they're stored separately
      const { brandPreferences, ...toStore } = updated;
      
      await AsyncStorage.setItem(PREFERENCES_KEY, JSON.stringify(toStore));
      console.log('✅ Shopping preferences updated');
    } catch (error) {
      console.error('❌ Error updating shopping preferences:', error);
    }
  }
  
  /**
   * Learn from user behavior - call this when user makes a choice
   */
  static async learnFromChoice(
    productType: string,
    selectedBrand: string,
    selectedSize: string,
    rejectedOptions: Array<{ brand: string; size: string; price: number }>
  ): Promise<void> {
    try {
      // Set the chosen preference
      await this.setBrandPreference(productType, selectedBrand, selectedSize);
      
      // Analyze the choice to improve learning
      const preferences = await this.getAllBrandPreferences();
      const preference = preferences.find(p => p.productType === productType);
      
      if (preference) {
        // If user consistently picks more expensive options, they might value quality
        const avgRejectedPrice = rejectedOptions.length > 0 
          ? rejectedOptions.reduce((sum, opt) => sum + opt.price, 0) / rejectedOptions.length
          : 0;
        
        if (avgRejectedPrice > 0) {
          // This is learning data we could use for more sophisticated recommendations
          console.log(`📊 Learning: ${productType} - chose ${selectedBrand} over ${rejectedOptions.length} options`);
        }
      }
      
    } catch (error) {
      console.error('❌ Error learning from choice:', error);
    }
  }
  
  /**
   * Get smart recommendations based on user history
   */
  static async getSmartRecommendations(
    productType: string,
    availableOptions: Array<{
      brand: string;
      size: string;
      price: number;
      store: string;
    }>
  ): Promise<Array<{
    brand: string;
    size: string;
    price: number;
    store: string;
    recommendationScore: number;
    reason: string;
  }>> {
    try {
      const preference = await this.getBrandPreference(productType);
      const prefs = await this.getShoppingPreferences();
      
      return availableOptions.map(option => {
        let score = 0;
        let reason = '';
        
        // Check brand preference
        if (preference && option.brand === preference.preferredBrand) {
          score += preference.confidenceScore * 50;
          reason = 'Your preferred brand';
        }
        
        // Check size preference
        if (preference && option.size === preference.preferredSize) {
          score += 20;
          reason += reason ? ' and size' : 'Your preferred size';
        }
        
        // Check store preference
        const storeIndex = prefs.preferredStores.indexOf(option.store);
        if (storeIndex >= 0) {
          score += (3 - storeIndex) * 10; // Higher score for preferred stores
        }
        
        // Price factor (lower price = higher score)
        const maxPrice = Math.max(...availableOptions.map(o => o.price));
        const priceScore = maxPrice > 0 ? ((maxPrice - option.price) / maxPrice) * 30 : 0;
        score += priceScore;
        
        if (!reason) {
          if (priceScore > 20) {
            reason = 'Great price';
          } else if (storeIndex === 0) {
            reason = 'Available at your preferred store';
          } else {
            reason = 'Good option';
          }
        }
        
        return {
          ...option,
          recommendationScore: Math.round(score),
          reason
        };
      }).sort((a, b) => b.recommendationScore - a.recommendationScore);
      
    } catch (error) {
      console.error('❌ Error getting smart recommendations:', error);
      return availableOptions.map(option => ({
        ...option,
        recommendationScore: 0,
        reason: 'Available option'
      }));
    }
  }
  
  /**
   * Clear all preferences (for testing or reset)
   */
  static async clearAllPreferences(): Promise<void> {
    try {
      await AsyncStorage.removeItem(PREFERENCES_KEY);
      await AsyncStorage.removeItem(BRAND_PREFERENCES_KEY);
      console.log('🗑️ All preferences cleared');
    } catch (error) {
      console.error('❌ Error clearing preferences:', error);
    }
  }
  
  /**
   * Export preferences for backup
   */
  static async exportPreferences(): Promise<string> {
    try {
      const prefs = await this.getShoppingPreferences();
      return JSON.stringify(prefs, null, 2);
    } catch (error) {
      console.error('❌ Error exporting preferences:', error);
      return '{}';
    }
  }
  
  /**
   * Import preferences from backup
   */
  static async importPreferences(preferencesJson: string): Promise<boolean> {
    try {
      const prefs = JSON.parse(preferencesJson) as ShoppingPreferences;
      
      // Validate the structure
      if (!prefs.brandPreferences || !Array.isArray(prefs.brandPreferences)) {
        throw new Error('Invalid preferences format');
      }
      
      // Store brand preferences
      await AsyncStorage.setItem(BRAND_PREFERENCES_KEY, JSON.stringify(prefs.brandPreferences));
      
      // Store other preferences
      const { brandPreferences, ...otherPrefs } = prefs;
      await AsyncStorage.setItem(PREFERENCES_KEY, JSON.stringify(otherPrefs));
      
      console.log('✅ Preferences imported successfully');
      return true;
    } catch (error) {
      console.error('❌ Error importing preferences:', error);
      return false;
    }
  }
  
  /**
   * Get usage statistics
   */
  static async getUsageStats(): Promise<{
    totalPreferences: number;
    mostUsedBrand: string | null;
    averageConfidence: number;
    recentActivity: number; // Preferences used in last 7 days
  }> {
    try {
      const preferences = await this.getAllBrandPreferences();
      
      if (preferences.length === 0) {
        return {
          totalPreferences: 0,
          mostUsedBrand: null,
          averageConfidence: 0,
          recentActivity: 0
        };
      }
      
      // Find most used brand
      const brandCounts = preferences.reduce((counts, pref) => {
        counts[pref.preferredBrand] = (counts[pref.preferredBrand] || 0) + pref.timesSelected;
        return counts;
      }, {} as Record<string, number>);
      
      const mostUsedBrand = Object.entries(brandCounts)
        .sort(([,a], [,b]) => b - a)[0]?.[0] || null;
      
      // Calculate average confidence
      const averageConfidence = preferences.reduce((sum, pref) => sum + pref.confidenceScore, 0) / preferences.length;
      
      // Count recent activity (last 7 days)
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      
      const recentActivity = preferences.filter(pref => 
        new Date(pref.lastUsed) > sevenDaysAgo
      ).length;
      
      return {
        totalPreferences: preferences.length,
        mostUsedBrand,
        averageConfidence: Math.round(averageConfidence * 100) / 100,
        recentActivity
      };
      
    } catch (error) {
      console.error('❌ Error getting usage stats:', error);
      return {
        totalPreferences: 0,
        mostUsedBrand: null,
        averageConfidence: 0,
        recentActivity: 0
      };
    }
  }
}