import { 
  saveShoppingList, 
  saveUserPreferences, 
  getRecipes,
  getShoppingList,
  getUserPreferences,
  ShoppingItem,
  UserPreferences 
} from '../utils/storage';

// Helper to fetch core application data concurrently.
// This avoids duplicating the Promise.all logic in multiple functions.
const _getCoreAppData = async () => {
  return Promise.all([
    getRecipes(),
    getShoppingList(),
    getUserPreferences(),
  ]);
};

const _initializeNonRecipeData = async (
  shoppingItems: ShoppingItem[],
  preferences: UserPreferences
): Promise<void> => {
  await Promise.all([
    saveShoppingList(shoppingItems),
    saveUserPreferences(preferences),
  ]);
};

// Data initialization service
export const dataInitializer = {
  /** Check if the app has any existing data */
  hasExistingData: async (): Promise<boolean> => {
    try {
      const [recipes, shoppingItems, userPrefs] = await _getCoreAppData();
      
      return recipes.length > 0 || shoppingItems.length > 0 || userPrefs !== null;
    } catch (error) {
      // Error checking existing data
      return false;
    }
  },

  /** Initialize app without sample data - use only Algolia */
  initializeSampleData: async (): Promise<void> => {
    const hasData = await dataInitializer.hasExistingData();
    
    // Only initialize shopping list and preferences, not demo recipes
    if (!hasData) {
      // Initializing with minimal sample data
      
      // Recipes come from Algolia; only initialize shopping list and preferences.
      const emptyShoppingItems: ShoppingItem[] = [];
      const defaultPreferences: UserPreferences = {
        dietaryRestrictions: [],
        kitchenEquipment: [],
        allergies: [],
        cuisinePreferences: [],
        defaultServings: 4,
        weeklyBudget: '$100',
        perMealBudget: '$25'
      };
      await _initializeNonRecipeData(emptyShoppingItems, defaultPreferences);
      
      // Sample data initialized successfully
    } else {
      // Existing data found, skipping initialization
    }
  },

  /** Reset all data and reinitialize with fresh sample data */
  resetToSampleData: async (): Promise<void> => {
    try {
      // Overwrites existing shopping list and preferences with default data.
      // No mock recipes will be added as they are managed in Algolia.
      // Resetting data to defaults
      
      const emptyShoppingItems: ShoppingItem[] = [];
      const defaultPreferences: UserPreferences = {
        dietaryRestrictions: [],
        kitchenEquipment: [],
        allergies: [],
        cuisinePreferences: [],
        defaultServings: 4,
        weeklyBudget: '$100',
        perMealBudget: '$25'
      };
      await _initializeNonRecipeData(emptyShoppingItems, defaultPreferences);
      
      // Data reset successfully
    } catch (error) {
      // Error resetting to sample data
      throw error;
    }
  },

  /** Add sample shopping items to the existing list */
  addSampleShoppingItems: async (): Promise<ShoppingItem[]> => {
    try {
      const existingItems = await getShoppingList();
      const existingNames = new Set(existingItems.map(item => item.name.toLowerCase()));
      
      // Get items that don't already exist
      const newItems: ShoppingItem[] = [];
      
      if (newItems.length > 0) {
        const updatedItems = [...existingItems, ...newItems];
        await saveShoppingList(updatedItems);
        // Sample shopping items added
      }
      
      return newItems;
    } catch (error) {
      // Error adding sample shopping items
      return [];
    }
  },

  /** Recipes are loaded from Algolia - no demo recipes needed */
  addSampleRecipes: async (count: number = 3): Promise<void> => {
    console.log(`Recipes are loaded from Algolia. No demo recipes to add.`);
    // Only add shopping items for testing purposes
    await dataInitializer.addSampleShoppingItems();
  },

  /** Seed app with demo preferences if none exist */
  initializeDemoPreferences: async (): Promise<void> => {
    try {
      const existingPrefs = await getUserPreferences();
      
      if (!existingPrefs) {
        const defaultPreferences: UserPreferences = {
          dietaryRestrictions: [],
          kitchenEquipment: [],
          allergies: [],
          cuisinePreferences: [],
          defaultServings: 4,
          weeklyBudget: '$100',
          perMealBudget: '$25'
        };
        await saveUserPreferences(defaultPreferences);
        // Demo preferences initialized
      }
    } catch (error) {
      // Error initializing demo preferences
    }
  },

  /** Get app data statistics */
  getDataStats: async (): Promise<{
    recipesCount: number;
    shoppingItemsCount: number;
    checkedItemsCount: number;
    hasPreferences: boolean;
  }> => {
    try {
      const [recipes, shoppingItems, userPrefs] = await _getCoreAppData();
      
      return {
        recipesCount: recipes.length,
        shoppingItemsCount: shoppingItems.length,
        checkedItemsCount: shoppingItems.filter(item => item.checked).length,
        hasPreferences: userPrefs !== null
      };
    } catch (error) {
      // Error getting data stats
      return {
        recipesCount: 0,
        shoppingItemsCount: 0,
        checkedItemsCount: 0,
        hasPreferences: false
      };
    }
  },

  /** Demo mode: populate with sample shopping data only - recipes from Algolia */
  enableDemoMode: async (): Promise<void> => {
    try {
      // Enabling demo mode with shopping data only
      
      // Set comprehensive user preferences
      const demoPreferences: UserPreferences = {
        dietaryRestrictions: ['Vegetarian', 'Gluten-Free'],
        kitchenEquipment: ['Oven', 'Stove', 'Microwave', 'Air Fryer', 'Instant Pot'],
        allergies: ['Nuts', 'Shellfish'],
        cuisinePreferences: ['Italian', 'Asian', 'Mediterranean', 'Mexican'],
        defaultServings: 4,
        weeklyBudget: '$100',
        perMealBudget: '$25'
      };

      // Add all sample shopping items and set demo preferences
      const emptyShoppingItems: ShoppingItem[] = [];
      await _initializeNonRecipeData(emptyShoppingItems, demoPreferences);
      
      // Demo mode enabled successfully
    } catch (error) {
      // Error enabling demo mode
      throw error;
    }
  }
};

/**
 * Initializes application data on startup.
 * This should be called from the main entry point of the application.
 */
export const initializeAppData = async (): Promise<void> => {
  try {
    console.log('📱 Starting data initialization...');
    
    // Quick timeout to prevent hanging
    const timeoutPromise = new Promise((_, reject) => 
      setTimeout(() => reject(new Error('Data init timeout')), 5000)
    );
    
    const initPromise = Promise.all([
      dataInitializer.initializeSampleData(),
      dataInitializer.initializeDemoPreferences()
    ]);
    
    await Promise.race([initPromise, timeoutPromise]);
    
    console.log('✅ Data initialization completed');
  } catch (error) {
    console.warn('⚠️ Data initialization failed (app will continue):', error);
    // Don't throw - let app continue without sample data
  }
}; 