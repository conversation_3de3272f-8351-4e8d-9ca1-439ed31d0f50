import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  FlatList,
  TouchableOpacity,
  Image,
  StyleSheet,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { PanGestureHandler } from 'react-native-gesture-handler';
import { useProductBrands } from '../hooks/useProductBrands';

const { width: screenWidth } = Dimensions.get('window');
const CARD_WIDTH = 120;
const CARD_MARGIN = 8;

interface BrandCard {
  brand: string;
  imageUrl?: string;
  isLoading?: boolean;
}

interface RealSwipeableBrandSelectorProps {
  userId: string;
  onBrandSelected: (product: string, brand: string) => void;
  placeholder?: string;
  style?: any;
}

export function RealSwipeableBrandSelector({
  userId,
  onBrandSelected,
  placeholder = "Search products...",
  style,
}: RealSwipeableBrandSelectorProps) {
  const [searchText, setSearchText] = useState('');
  const flatListRef = useRef<FlatList>(null);
  const brandGestureRef = useRef<PanGestureHandler>(null);
  
  const { brands, isLoading, pick } = useProductBrands(searchText, userId);

  const handleBrandSelect = async (brand: string) => {
    await pick(brand);
    onBrandSelected(searchText, brand);
    setSearchText('');
  };

  const renderBrandCard = ({ item: brand }: { item: string }) => (
    <TouchableOpacity
      style={styles.brandCard}
      onPress={() => handleBrandSelect(brand)}
      activeOpacity={0.8}
    >
      <View style={styles.imageContainer}>
        {/* For now using emojis, but you can replace with real images */}
        <View style={styles.placeholderImage}>
          <Text style={styles.placeholderText}>
            {brand.toLowerCase().includes('coffee') || brand.toLowerCase().includes('nescaf') || brand.toLowerCase().includes('moccona') ? '☕' :
             brand.toLowerCase().includes('milk') || brand.toLowerCase().includes('anchor') || brand.toLowerCase().includes('mainland') ? '🥛' :
             brand.toLowerCase().includes('bread') || brand.toLowerCase().includes('tip') || brand.toLowerCase().includes('vogel') ? '🍞' :
             brand.toLowerCase().includes('chip') || brand.toLowerCase().includes('bluebird') || brand.toLowerCase().includes('eta') ? '🥔' :
             '📦'}
          </Text>
        </View>
      </View>
      
      <Text style={styles.brandName} numberOfLines={2}>
        {brand}
      </Text>
      
      <Text style={styles.forText}>for {searchText}</Text>
    </TouchableOpacity>
  );

  const showCards = searchText.length > 1 && (brands.length > 0 || isLoading);

  return (
    <View style={[styles.container, style]}>
      <TextInput
        style={styles.searchInput}
        value={searchText}
        onChangeText={setSearchText}
        placeholder={placeholder}
        autoCapitalize="none"
        autoCorrect={false}
        returnKeyType="search"
      />
      
      {isLoading && (
        <View style={styles.searchLoadingContainer}>
          <ActivityIndicator size="small" color="#007AFF" />
          <Text style={styles.searchLoadingText}>Searching brands...</Text>
        </View>
      )}
      
      {showCards && !isLoading && (
        <View style={styles.cardsContainer}>
          <Text style={styles.cardsTitle}>
            👆 Swipe to browse {brands.length} brands for "{searchText}"
          </Text>
          
          <FlatList
            ref={flatListRef}
            data={brands}
            renderItem={renderBrandCard}
            keyExtractor={(item) => item}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.cardsList}
            snapToInterval={CARD_WIDTH + (CARD_MARGIN * 2)}
            decelerationRate="fast"
            snapToAlignment="start"
            ItemSeparatorComponent={() => <View style={{ width: CARD_MARGIN }} />}
            scrollEventThrottle={16}
            bounces={true}
            bouncesZoom={false}
            pagingEnabled={false}
          />
          
          {brands.length === 0 && (
            <View style={styles.noBrandsContainer}>
              <Text style={styles.noBrandsText}>
                No brands found for "{searchText}"
              </Text>
              <Text style={styles.tryAgainText}>
                Try a different search term
              </Text>
            </View>
          )}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    margin: 16,
  },
  searchInput: {
    height: 56,
    borderWidth: 2,
    borderColor: '#E5E5E5',
    borderRadius: 12,
    paddingHorizontal: 20,
    fontSize: 16,
    backgroundColor: '#FAFAFA',
    fontWeight: '500',
  },
  searchLoadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
  },
  searchLoadingText: {
    marginLeft: 8,
    color: '#666',
    fontSize: 14,
  },
  cardsContainer: {
    paddingTop: 16,
    paddingBottom: 8,
  },
  cardsTitle: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  cardsList: {
    paddingHorizontal: 16,
  },
  brandCard: {
    width: CARD_WIDTH,
    alignItems: 'center',
    backgroundColor: '#FAFAFA',
    borderRadius: 12,
    padding: 12,
    marginHorizontal: CARD_MARGIN,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  imageContainer: {
    width: 80,
    height: 80,
    marginBottom: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderImage: {
    width: 80,
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F0F0F0',
    borderRadius: 8,
  },
  placeholderText: {
    fontSize: 32,
  },
  brandName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
    marginBottom: 4,
    lineHeight: 18,
  },
  forText: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  noBrandsContainer: {
    alignItems: 'center',
    paddingVertical: 32,
    paddingHorizontal: 16,
  },
  noBrandsText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 8,
  },
  tryAgainText: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
  },
});