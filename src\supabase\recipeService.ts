import { supabase } from './client';
import { PostgrestError } from '@supabase/supabase-js';

export interface Recipe {
  id: string;
  objectID: string;
  title: string;
  description: string;
  ingredients: string[];
  instructions: string[];
  prep_time: string;
  cook_time: string;
  total_time: string;
  servings: string;
  difficulty: string;
  cuisine: string;
  author: string;
  image_url: string;
  source_url: string;
  category: string;
  tags: string[];
  rating: string;
  calories: string;
  created_at: string;
}

export const getRecipes = async (): Promise<{ data: Recipe[] | null; error: PostgrestError | null }> => {
  const { data, error } = await supabase.from('recipes').select('*');
  return { data: data as Recipe[] | null, error };
};

export const getRecipeById = async (id: string): Promise<{ data: Recipe | null; error: PostgrestError | null }> => {
  const { data, error } = await supabase.from('recipes').select('*').eq('id', id).single();
  return { data: data as Recipe | null, error };
};

export const getRecipesByCategory = async (category: string): Promise<{ data: Recipe[] | null; error: PostgrestError | null }> => {
  const { data, error } = await supabase.from('recipes').select('*').eq('category', category);
  return { data: data as Recipe[] | null, error };
}; 