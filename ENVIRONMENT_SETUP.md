# 🔐 Secure Environment Setup Guide

## 🚨 CURRENT SECURITY STATUS: **CRITICAL**

Your API keys have been **EXPOSED** in your repository. Follow the [SECURITY_REMEDIATION.md](./SECURITY_REMEDIATION.md) guide **immediately**.

---

## ✅ SECURE SETUP (Recommended)

### Step 1: Firebase Backend Setup

Instead of exposing API keys in your client app, use secure Firebase Cloud Functions:

```bash
# Install Firebase CLI
npm install -g firebase-tools

# Login and initialize
firebase login
firebase init functions

# Set environment variables securely on Firebase
firebase functions:config:set \
  gemini.api_key="YOUR_NEW_GEMINI_KEY" \
  algolia.app_id="YOUR_NEW_ALGOLIA_APP_ID" \
  algolia.api_key="YOUR_NEW_ALGOLIA_KEY" \
  algolia.index_name="recipes"

# Deploy secure functions
firebase deploy --only functions
```

### Step 2: Client Configuration

Create `.env` file with Firebase config (these are safe to expose):

```bash
# Firebase configuration (safe to expose)
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_API_KEY=your-firebase-web-api-key
FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
FIREBASE_STORAGE_BUCKET=your-project.appspot.com
FIREBASE_MESSAGING_SENDER_ID=*********
```

---

## ⚠️ DEVELOPMENT-ONLY SETUP (Insecure)

**WARNING**: This exposes API keys in your app bundle. Use only for local development.

### Create .env file:

```bash
# ⚠️ DEVELOPMENT ONLY - DO NOT USE IN PRODUCTION

# Gemini AI API (get from https://aistudio.google.com/app/apikey)
EXPO_PUBLIC_GEMINI_API_KEY=your_new_gemini_key

# Algolia Search (get from https://www.algolia.com/account/api-keys)
EXPO_PUBLIC_ALGOLIA_APP_ID=CXNOUIM54D
EXPO_PUBLIC_ALGOLIA_API_KEY=********************************
EXPO_PUBLIC_ALGOLIA_INDEX_NAME=recipes
```

**IMPORTANT**:
- Generate **NEW** API keys (revoke the exposed ones)
- Never commit `.env` to git
- Migrate to Firebase for production

---

## 🔄 Migration Commands

### 1. Emergency Cleanup
```bash
# Revoke exposed keys immediately at:
# - https://aistudio.google.com/app/apikey
# - https://www.algolia.com/account/api-keys

# Clean git history
git filter-branch --index-filter "git rm -rf --cached --ignore-unmatch .env" HEAD
git push --force-with-lease origin main
```

### 2. Setup Firebase
```bash
# Create Firebase project and get configuration
firebase projects:create ai-recipe-planner
firebase use ai-recipe-planner
firebase init functions
```

### 3. Deploy Secure Backend
```bash
cd functions
npm install
npm run build
firebase deploy --only functions
```

---

## 🛡️ Security Comparison

| Aspect | Exposed Keys (Current) | Firebase Secure |
|--------|----------------------|-----------------|
| **API Key Visibility** | ❌ Visible in app bundle | ✅ Hidden on server |
| **Rate Limiting** | ❌ No control | ✅ Server-side control |
| **Usage Monitoring** | ❌ Limited | ✅ Full Firebase analytics |
| **Cost Protection** | ❌ Vulnerable to abuse | ✅ Protected |
| **Access Control** | ❌ Anyone can use | ✅ Controlled access |

---

## 📋 Setup Checklist

### Immediate (Critical)
- [ ] **Revoke exposed Gemini API key**
- [ ] **Revoke exposed Algolia API keys**  
- [ ] **Clean git history**
- [ ] **Remove keys from all code files**

### Secure Setup
- [ ] Create Firebase project
- [ ] Deploy Firebase Cloud Functions
- [ ] Generate NEW API keys
- [ ] Set Firebase environment variables
- [ ] Update app to use secure service
- [ ] Test end-to-end functionality

### Verification
- [ ] Confirm old keys are revoked
- [ ] Verify new keys work with Firebase
- [ ] Test app functionality
- [ ] Monitor Firebase usage

---

## 🚨 Remember

**The exposed API keys in your repository are a critical security vulnerability.**

Complete the migration to Firebase backend as soon as possible to:
- Protect your API quota from abuse
- Prevent unexpected charges
- Secure your application properly

Follow the [SECURITY_REMEDIATION.md](./SECURITY_REMEDIATION.md) guide for step-by-step instructions. 