// Enhanced Shopping Data Service
// Provides enhanced product data using live Supabase product database

import { ShoppingItem } from '../utils/storage';
import { priceComparisonService } from './priceComparisonService';

export interface EnhancedShoppingItem {
  id: string;
  name: string;
  quantity: number;
  unit: string;
  category: string;
  subcategory?: string;
  storePreferences: StorePreference[];
  selectedBrand?: string;
  notes?: string;
  urgency: 'low' | 'medium' | 'high';
  estimatedPrice?: number;
  alternatives?: string[];
}

export interface StorePreference {
  store: 'woolworths' | 'newworld' | 'paknsave';
  price: number;
  available: boolean;
  brand?: string;
  size?: string;
  productId?: string;
  lastUpdated: Date;
}

export interface SubcategoryData {
  name: string;
  items: SubcategoryItem[];
}

export interface SubcategoryItem {
  name: string;
  brands: BrandOption[];
  defaultBrand?: string;
  averagePrice: number;
}

export interface BrandOption {
  name: string;
  stores: {
    woolworths?: { price: number; available: boolean; size?: string };
    newworld?: { price: number; available: boolean; size?: string };
    paknsave?: { price: number; available: boolean; size?: string };
  };
  averagePrice: number;
  productId?: string;
}

// Helper function to generate brand options with consistent pricing
function generateBrands(brandNames: string[], basePrice: number): BrandOption[] {
  return brandNames.map((brandName, index) => ({
    name: brandName,
    averagePrice: basePrice + (index * 0.50) - 0.25,
    stores: {
      woolworths: { price: (basePrice + (index * 0.50) - 0.25) * 1.05, available: true },
      newworld: { price: (basePrice + (index * 0.50) - 0.25) * 1.1, available: true },
      paknsave: { price: (basePrice + (index * 0.50) - 0.25) * 0.95, available: true }
    }
  }));
}

// Mock category data removed - now using Supabase exclusively

// Brand patterns removed - now using live Supabase brand data

class EnhancedShoppingDataService {
  private cache = new Map<string, SubcategoryData[]>();
  private searchAttempts = new Map<string, number>();

  async getEnhancedDataForItem(productName: string): Promise<SubcategoryData[]> {
    console.log('🔍 EnhancedShoppingDataService: Getting enhanced data for:', productName);

    // Check cache first
    const cacheKey = productName.toLowerCase();
    if (this.cache.has(cacheKey)) {
      console.log('✅ Found in cache:', productName);
      return this.cache.get(cacheKey)!;
    }

    // Generate enhanced data from Supabase search only
    console.log('🔄 Generating enhanced data from Supabase search for:', productName);
    try {
      const enhancedData = await this.generateEnhancedDataFromSearch(productName);
      this.cache.set(cacheKey, enhancedData);
      return enhancedData;
    } catch (error) {
      console.error('❌ Failed to generate enhanced data from Supabase:', error);
      
      // Return empty data - no fallbacks to mock data
      console.log('📋 No enhanced data available for:', productName);
      return [];
    }
  }

  // getPredefinedEnhancedData function removed - using Supabase exclusively

  private async generateEnhancedDataFromSearch(productName: string): Promise<SubcategoryData[]> {
    console.log('🔍 Searching for real products for:', productName);
    
    try {
      const searchResult = await priceComparisonService.searchProductAcrossStores(productName, {
        maxResults: 20,
        sortBy: 'price',
        includeOutOfStock: false
      });

      console.log(`Found ${searchResult.results.length} products for ${productName}`);
      
      if (searchResult.results.length === 0) {
        throw new Error('No products found in search');
      }

      // Group products by similarity and create subcategories
      const subcategories = this.createSubcategoriesFromProducts(productName, searchResult.results);
      console.log(`Created ${subcategories.length} subcategories for ${productName}`);
      
      return subcategories;
    } catch (error) {
      console.error('❌ Search failed for:', productName, error);
      throw error;
    }
  }

  private createSubcategoriesFromProducts(productName: string, products: any[]): SubcategoryData[] {
    // Group products by type/variant
    const productGroups = new Map<string, any[]>();
    
    products.forEach(product => {
      const productInfo = product.product || product;
      const name = productInfo.name || productInfo.title || '';
      
      // Determine subcategory based on product name
      const subcategory = this.determineSubcategory(name, productName);
      
      if (!productGroups.has(subcategory)) {
        productGroups.set(subcategory, []);
      }
      productGroups.get(subcategory)!.push(product);
    });

    // Convert groups to subcategory data
    const subcategories: SubcategoryData[] = [];
    
    productGroups.forEach((groupProducts, subcategoryName) => {
      const items = this.createItemsFromProducts(groupProducts);
      if (items.length > 0) {
        subcategories.push({
          name: subcategoryName,
          items: items
        });
      }
    });

    return subcategories;
  }

  private determineSubcategory(productName: string, searchTerm: string): string {
    const name = productName.toLowerCase();
    const term = searchTerm.toLowerCase();
    
    // Define subcategory patterns
    const subcategoryPatterns = [
      { keywords: ['instant', 'granules'], category: 'Instant' },
      { keywords: ['beans', 'whole bean'], category: 'Beans' },
      { keywords: ['pods', 'capsule'], category: 'Pods' },
      { keywords: ['fresh', 'chilled'], category: 'Fresh' },
      { keywords: ['frozen'], category: 'Frozen' },
      { keywords: ['organic'], category: 'Organic' },
      { keywords: ['lite', 'light', 'low fat'], category: 'Light' },
      { keywords: ['whole', 'full'], category: 'Whole' },
      { keywords: ['skim', 'trim'], category: 'Skim' },
      { keywords: ['white'], category: 'White' },
      { keywords: ['brown', 'multigrain'], category: 'Brown/Multigrain' },
      { keywords: ['gluten free'], category: 'Gluten Free' }
    ];

    for (const pattern of subcategoryPatterns) {
      if (pattern.keywords.some(keyword => name.includes(keyword))) {
        return pattern.category;
      }
    }

    // Default subcategory
    return this.capitalize(term);
  }

  private createItemsFromProducts(products: any[]): SubcategoryItem[] {
    // Group by brand
    const brandGroups = new Map<string, any[]>();
    
    products.forEach(product => {
      const productInfo = product.product || product;
      const brand = this.extractBrand(productInfo.name || productInfo.title || '');
      
      if (!brandGroups.has(brand)) {
        brandGroups.set(brand, []);
      }
      brandGroups.get(brand)!.push(product);
    });

    // Create items from brand groups
    const items: SubcategoryItem[] = [];
    
    brandGroups.forEach((brandProducts, brandName) => {
      const brands = this.createBrandOptionsFromProducts(brandProducts);
      const avgPrice = brands.reduce((sum, b) => sum + b.averagePrice, 0) / brands.length;
      
      items.push({
        name: brandProducts[0].product?.name || brandProducts[0].name || 'Product',
        brands: brands,
        defaultBrand: brandName,
        averagePrice: avgPrice
      });
    });

    return items;
  }

  private createBrandOptionsFromProducts(products: any[]): BrandOption[] {
    const brandMap = new Map<string, any[]>();
    
    // Group by brand
    products.forEach(product => {
      const productInfo = product.product || product;
      const brand = this.extractBrand(productInfo.name || productInfo.title || '');
      
      if (!brandMap.has(brand)) {
        brandMap.set(brand, []);
      }
      brandMap.get(brand)!.push(product);
    });

    const brandOptions: BrandOption[] = [];
    
    brandMap.forEach((brandProducts, brandName) => {
      const stores: any = {};
      let totalPrice = 0;
      let priceCount = 0;
      
      brandProducts.forEach(product => {
        const productInfo = product.product || product;
        const store = product.store || product.storeName;
        const price = productInfo.price || 0;
        
        if (store && price > 0) {
          stores[store] = {
            price: price,
            available: true,
            size: productInfo.size || productInfo.unit || ''
          };
          totalPrice += price;
          priceCount++;
        }
      });

      const avgPrice = priceCount > 0 ? totalPrice / priceCount : 0;
      
      brandOptions.push({
        name: brandName,
        stores: stores,
        averagePrice: avgPrice,
        productId: brandProducts[0].product?.id || brandProducts[0].id
      });
    });

    return brandOptions;
  }

  // Fallback function removed - using Supabase exclusively

  // getBrandsForProduct function removed - using live Supabase brands

  private extractBrand(productName: string): string {
    const name = productName.toLowerCase();
    
    // Known brand patterns
    const knownBrands = [
      'anchor', 'mainland', 'meadow fresh', 'lewis road', 'tip top', 'much moore',
      'nescafe', 'moccona', 'greggs', 'oatly', 'minor figures', 'barilla', 'san remo',
      'bluebird', 'eta', 'proper', 'kettle', 'doritos', 'pringles', 'sunrice',
      'griffin\'s', 'arnott\'s', 'carr\'s', 'vita-weat', 'sanitarium', 'kellogg\'s',
      'hubbards', 'uncle tobys', 'nestle', 'dilmah', 'twinings', 'bell tea'
    ];

    for (const brand of knownBrands) {
      if (name.includes(brand)) {
        return this.capitalize(brand);
      }
    }

    // Extract first word as potential brand
    const words = productName.split(' ');
    if (words.length > 0 && words[0].length > 2) {
      return this.capitalize(words[0]);
    }

    return 'Generic';
  }

  private estimatePrice(productName: string): number {
    const name = productName.toLowerCase();
    
    // Price estimates based on product type
    const priceEstimates: Record<string, number> = {
      'milk': 3.50, 'bread': 2.50, 'cheese': 8.00, 'butter': 4.50,
      'yogurt': 2.00, 'eggs': 5.00, 'coffee': 10.00, 'tea': 6.00,
      'rice': 4.00, 'pasta': 3.00, 'flour': 3.50, 'sugar': 2.50,
      'oil': 6.00, 'chips': 4.00, 'biscuits': 3.50, 'cereal': 7.00,
      'soup': 3.00, 'sauce': 4.00, 'jam': 5.00, 'honey': 8.00
    };

    for (const [product, price] of Object.entries(priceEstimates)) {
      if (name.includes(product)) {
        return price;
      }
    }

    return 5.00; // Default estimate
  }

  private capitalize(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  // Clear cache when needed
  clearCache(): void {
    this.cache.clear();
    this.searchAttempts.clear();
  }
}

export const enhancedShoppingDataService = new EnhancedShoppingDataService(); 