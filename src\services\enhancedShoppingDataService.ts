// Enhanced Shopping Data Service
// Provides sample data with subcategories for demonstration

import { ShoppingItem } from '../utils/storage';
import { priceComparisonService } from './priceComparisonService';

export interface EnhancedShoppingItem {
  id: string;
  name: string;
  quantity: number;
  unit: string;
  category: string;
  subcategory?: string;
  storePreferences: StorePreference[];
  selectedBrand?: string;
  notes?: string;
  urgency: 'low' | 'medium' | 'high';
  estimatedPrice?: number;
  alternatives?: string[];
}

export interface StorePreference {
  store: 'woolworths' | 'newworld' | 'paknsave';
  price: number;
  available: boolean;
  brand?: string;
  size?: string;
  productId?: string;
  lastUpdated: Date;
}

export interface SubcategoryData {
  name: string;
  items: SubcategoryItem[];
}

export interface SubcategoryItem {
  name: string;
  brands: BrandOption[];
  defaultBrand?: string;
  averagePrice: number;
}

export interface BrandOption {
  name: string;
  stores: {
    woolworths?: { price: number; available: boolean; size?: string };
    newworld?: { price: number; available: boolean; size?: string };
    paknsave?: { price: number; available: boolean; size?: string };
  };
  averagePrice: number;
  productId?: string;
}

// Helper function to generate brand options with consistent pricing
function generateBrands(brandNames: string[], basePrice: number): BrandOption[] {
  return brandNames.map((brandName, index) => ({
    name: brandName,
    averagePrice: basePrice + (index * 0.50) - 0.25,
    stores: {
      woolworths: { price: (basePrice + (index * 0.50) - 0.25) * 1.05, available: true },
      newworld: { price: (basePrice + (index * 0.50) - 0.25) * 1.1, available: true },
      paknsave: { price: (basePrice + (index * 0.50) - 0.25) * 0.95, available: true }
    }
  }));
}

// Comprehensive enhanced data for all supermarket categories with subcategories
const ENHANCED_CATEGORY_DATA: Record<string, SubcategoryData[]> = {
  // FRESH PRODUCE
  'Apple': [
    { name: 'Red Apples', items: [{ name: 'Gala Apple', brands: generateBrands(['Jazz', 'Gala', 'Braeburn'], 3.99), defaultBrand: 'Gala', averagePrice: 3.99 }] },
    { name: 'Green Apples', items: [{ name: 'Granny Smith', brands: generateBrands(['Granny Smith', 'Organic'], 4.49), defaultBrand: 'Granny Smith', averagePrice: 4.49 }] }
  ],
  'Banana': [
    { name: 'Fresh Bananas', items: [{ name: 'Cavendish Banana', brands: generateBrands(['Dole', 'Chiquita', 'Organic'], 2.99), defaultBrand: 'Dole', averagePrice: 2.99 }] }
  ],

  // DAIRY & EGGS  
  'Milk': [
    { name: 'Fresh Milk', items: [
      { name: 'Whole Milk 2L', brands: generateBrands(['Anchor', 'Meadow Fresh', 'Lewis Road'], 3.49), defaultBrand: 'Anchor', averagePrice: 3.49 },
      { name: 'Trim Milk 2L', brands: generateBrands(['Anchor', 'Meadow Fresh', 'Calci'], 3.49), defaultBrand: 'Anchor', averagePrice: 3.49 }
    ]},
    { name: 'Plant-Based Milk', items: [
      { name: 'Oat Milk 1L', brands: generateBrands(['Oatly', 'Minor Figures', 'So Good'], 4.99), defaultBrand: 'Oatly', averagePrice: 4.99 },
      { name: 'Almond Milk 1L', brands: generateBrands(['So Good', 'Blue Diamond', 'Vitasoy'], 4.49), defaultBrand: 'So Good', averagePrice: 4.49 }
    ]}
  ],
  'Cheese': [
    { name: 'Hard Cheese', items: [
      { name: 'Tasty Cheese 500g', brands: generateBrands(['Mainland', 'Anchor', 'Bega'], 8.99), defaultBrand: 'Mainland', averagePrice: 8.99 },
      { name: 'Edam Cheese 500g', brands: generateBrands(['Mainland', 'Anchor', 'Mercer'], 9.49), defaultBrand: 'Mainland', averagePrice: 9.49 }
    ]},
    { name: 'Soft Cheese', items: [
      { name: 'Cream Cheese 250g', brands: generateBrands(['Philadelphia', 'Anchor', 'Tararua'], 4.99), defaultBrand: 'Philadelphia', averagePrice: 4.99 }
    ]}
  ],
  'Eggs': [
    { name: 'Chicken Eggs', items: [
      { name: 'Free Range Eggs 12pk', brands: generateBrands(['Woodland', 'Frenz', 'Otago'], 7.99), defaultBrand: 'Woodland', averagePrice: 7.99 },
      { name: 'Cage Free Eggs 12pk', brands: generateBrands(['Zeagold', 'Mainland', 'Fresh'], 6.99), defaultBrand: 'Zeagold', averagePrice: 6.99 }
    ]}
  ],

  // MEAT & SEAFOOD
  'Chicken': [
    { name: 'Fresh Chicken', items: [
      { name: 'Chicken Breast 1kg', brands: generateBrands(['Tegel', 'Inghams', 'Free Range'], 12.99), defaultBrand: 'Tegel', averagePrice: 12.99 },
      { name: 'Chicken Thighs 1kg', brands: generateBrands(['Tegel', 'Inghams', 'Lilydale'], 8.99), defaultBrand: 'Tegel', averagePrice: 8.99 }
    ]}
  ],
  'Beef': [
    { name: 'Premium Cuts', items: [
      { name: 'Beef Mince 500g', brands: generateBrands(['Silver Fern Farms', 'ANZCO', 'Premium'], 8.99), defaultBrand: 'Silver Fern Farms', averagePrice: 8.99 },
      { name: 'Beef Steak 500g', brands: generateBrands(['Silver Fern Farms', 'Grass Fed', 'Premium'], 15.99), defaultBrand: 'Silver Fern Farms', averagePrice: 15.99 }
    ]}
  ],

  // PANTRY STAPLES
  'Rice': [
    { name: 'Long Grain Rice', items: [
      { name: 'Jasmine Rice 1kg', brands: generateBrands(['SunRice', 'Jasmine', 'Premium'], 4.99), defaultBrand: 'SunRice', averagePrice: 4.99 },
      { name: 'Basmati Rice 1kg', brands: generateBrands(['SunRice', 'Basmati', 'Organic'], 5.99), defaultBrand: 'SunRice', averagePrice: 5.99 }
    ]},
    { name: 'Short Grain Rice', items: [
      { name: 'Calrose Rice 1kg', brands: generateBrands(['SunRice', 'Calrose', 'Sushi'], 4.49), defaultBrand: 'SunRice', averagePrice: 4.49 }
    ]}
  ],
  'Pasta': [
    { name: 'Dried Pasta', items: [
      { name: 'Spaghetti 500g', brands: generateBrands(['Barilla', 'San Remo', 'La Molisana'], 2.99), defaultBrand: 'Barilla', averagePrice: 2.99 },
      { name: 'Penne 500g', brands: generateBrands(['Barilla', 'San Remo', 'De Cecco'], 2.99), defaultBrand: 'Barilla', averagePrice: 2.99 }
    ]},
    { name: 'Fresh Pasta', items: [
      { name: 'Fresh Fettuccine 400g', brands: generateBrands(['La Famiglia Rana', 'Latina Fresh', 'Giovanni'], 4.99), defaultBrand: 'La Famiglia Rana', averagePrice: 4.99 }
    ]}
  ],
  
  // BREAKFAST & CEREAL
  'Cereal': [
    { name: 'Flake Cereals', items: [
      { name: 'Cornflakes 500g', brands: generateBrands(['Kellogg\'s', 'Sanitarium', 'Uncle Tobys'], 5.99), defaultBrand: 'Kellogg\'s', averagePrice: 5.99 },
      { name: 'Bran Flakes 500g', brands: generateBrands(['Kellogg\'s', 'Sanitarium', 'Uncle Tobys'], 6.49), defaultBrand: 'Kellogg\'s', averagePrice: 6.49 }
    ]},
    { name: 'Muesli & Granola', items: [
      { name: 'Traditional Muesli 750g', brands: generateBrands(['Hubbards', 'Uncle Tobys', 'Sanitarium'], 7.99), defaultBrand: 'Hubbards', averagePrice: 7.99 }
    ]}
  ],
  'Bread': [
    { name: 'Sliced Bread', items: [
      { name: 'White Bread 700g', brands: generateBrands(['Tip Top', 'Much Moore', 'Baker\'s Collection'], 2.99), defaultBrand: 'Tip Top', averagePrice: 2.99 },
      { name: 'Wholemeal Bread 700g', brands: generateBrands(['Tip Top', 'Much Moore', 'Vogel\'s'], 3.49), defaultBrand: 'Tip Top', averagePrice: 3.49 }
    ]},
    { name: 'Specialty Bread', items: [
      { name: 'Sourdough Loaf', brands: generateBrands(['Baker\'s Collection', 'Artisan', 'Fresh'], 4.99), defaultBrand: 'Baker\'s Collection', averagePrice: 4.99 }
    ]}
  ],

  // BEVERAGES
  'Coffee': [
    { name: 'Instant Coffee', items: [
      { name: 'Instant Coffee 200g', brands: generateBrands(['Nescafe', 'Moccona', 'Greggs'], 10.99), defaultBrand: 'Nescafe', averagePrice: 10.99 }
    ]},
    { name: 'Coffee Beans', items: [
      { name: 'Whole Bean Coffee 1kg', brands: generateBrands(['L\'affare', 'Atomic', 'Allpress'], 18.99), defaultBrand: 'L\'affare', averagePrice: 18.99 }
    ]},
    { name: 'Coffee Pods', items: [
      { name: 'Nespresso Pods 10pk', brands: generateBrands(['Nespresso', 'L\'OR', 'Lavazza'], 9.99), defaultBrand: 'Nespresso', averagePrice: 9.99 }
    ]}
  ],
  'Tea': [
    { name: 'Black Tea', items: [
      { name: 'English Breakfast 100pk', brands: generateBrands(['Dilmah', 'Twinings', 'Bell Tea'], 4.99), defaultBrand: 'Dilmah', averagePrice: 4.99 },
      { name: 'Earl Grey 25pk', brands: generateBrands(['Twinings', 'Dilmah', 'Harney'], 3.99), defaultBrand: 'Twinings', averagePrice: 3.99 }
    ]},
    { name: 'Herbal Tea', items: [
      { name: 'Chamomile Tea 20pk', brands: generateBrands(['Twinings', 'Celestial', 'Traditional'], 3.49), defaultBrand: 'Twinings', averagePrice: 3.49 }
    ]}
  ],

  // SNACKS
  'Chips': [
    { name: 'Potato Chips', items: [
      { name: 'Ready Salted 150g', brands: generateBrands(['Bluebird', 'ETA', 'Proper'], 3.99), defaultBrand: 'Bluebird', averagePrice: 3.99 },
      { name: 'Salt & Vinegar 150g', brands: generateBrands(['Bluebird', 'ETA', 'Kettle'], 3.99), defaultBrand: 'Bluebird', averagePrice: 3.99 }
    ]},
    { name: 'Corn Chips', items: [
      { name: 'Nacho Cheese 200g', brands: generateBrands(['Doritos', 'CC\'s', 'Mission'], 4.49), defaultBrand: 'Doritos', averagePrice: 4.49 }
    ]}
  ],
  'Chocolate': [
    { name: 'Milk Chocolate', items: [
      { name: 'Milk Chocolate Block 250g', brands: generateBrands(['Cadbury', 'Whittaker\'s', 'Nestle'], 4.99), defaultBrand: 'Cadbury', averagePrice: 4.99 }
    ]},
    { name: 'Dark Chocolate', items: [
      { name: 'Dark Chocolate Block 250g', brands: generateBrands(['Whittaker\'s', 'Lindt', 'Green & Black\'s'], 5.99), defaultBrand: 'Whittaker\'s', averagePrice: 5.99 }
    ]}
  ],

  // FROZEN FOODS
  'Ice Cream': [
    { name: 'Premium Ice Cream', items: [
      { name: 'Vanilla Ice Cream 2L', brands: generateBrands(['Tip Top', 'Much Moore', 'Ben & Jerry\'s'], 8.99), defaultBrand: 'Tip Top', averagePrice: 8.99 },
      { name: 'Chocolate Ice Cream 2L', brands: generateBrands(['Tip Top', 'Much Moore', 'Haagen Dazs'], 8.99), defaultBrand: 'Tip Top', averagePrice: 8.99 }
    ]}
  ],
  'Frozen Vegetables': [
    { name: 'Mixed Vegetables', items: [
      { name: 'Mixed Vegetables 1kg', brands: generateBrands(['McCain', 'Watties', 'Pams'], 4.99), defaultBrand: 'McCain', averagePrice: 4.99 },
      { name: 'Green Peas 1kg', brands: generateBrands(['McCain', 'Watties', 'Frozen'], 3.99), defaultBrand: 'McCain', averagePrice: 3.99 }
    ]}
  ],

  // CANNED GOODS
  'Canned Tomatoes': [
    { name: 'Whole Tomatoes', items: [
      { name: 'Whole Peeled Tomatoes 400g', brands: generateBrands(['Watties', 'SPC', 'Mutti'], 1.99), defaultBrand: 'Watties', averagePrice: 1.99 },
      { name: 'Crushed Tomatoes 400g', brands: generateBrands(['Watties', 'SPC', 'Ardmona'], 1.99), defaultBrand: 'Watties', averagePrice: 1.99 }
    ]}
  ],
  'Canned Fish': [
    { name: 'Tuna', items: [
      { name: 'Tuna in Springwater 95g', brands: generateBrands(['Sealord', 'John West', 'Greenseas'], 2.49), defaultBrand: 'Sealord', averagePrice: 2.49 },
      { name: 'Tuna in Oil 95g', brands: generateBrands(['Sealord', 'John West', 'Sirena'], 2.49), defaultBrand: 'Sealord', averagePrice: 2.49 }
    ]},
    { name: 'Salmon', items: [
      { name: 'Pink Salmon 415g', brands: generateBrands(['Sealord', 'John West', 'Clover Leaf'], 4.99), defaultBrand: 'Sealord', averagePrice: 4.99 }
    ]}
  ],

  // CONDIMENTS
  'Sauce': [
    { name: 'Tomato Sauce', items: [
      { name: 'Tomato Sauce 500ml', brands: generateBrands(['Watties', 'ETA', 'Heinz'], 3.49), defaultBrand: 'Watties', averagePrice: 3.49 }
    ]},
    { name: 'BBQ Sauce', items: [
      { name: 'BBQ Sauce 500ml', brands: generateBrands(['Watties', 'ETA', 'Masterfoods'], 3.49), defaultBrand: 'Watties', averagePrice: 3.49 }
    ]}
  ],
  'Oil': [
    { name: 'Cooking Oil', items: [
      { name: 'Olive Oil 500ml', brands: generateBrands(['Olivado', 'Bertolli', 'Cobram Estate'], 6.99), defaultBrand: 'Olivado', averagePrice: 6.99 },
      { name: 'Vegetable Oil 1L', brands: generateBrands(['Sunfield', 'Pam', 'Crisco'], 3.99), defaultBrand: 'Sunfield', averagePrice: 3.99 }
    ]}
  ],

  // HOUSEHOLD
  'Toilet Paper': [
    { name: 'Premium Toilet Paper', items: [
      { name: '3-Ply Toilet Paper 24pk', brands: generateBrands(['Sorbent', 'Kleenex', 'Quilton'], 18.99), defaultBrand: 'Sorbent', averagePrice: 18.99 },
      { name: '2-Ply Toilet Paper 24pk', brands: generateBrands(['Sorbent', 'Purex', 'Tuffy'], 14.99), defaultBrand: 'Sorbent', averagePrice: 14.99 }
    ]}
  ],
  'Laundry Powder': [
    { name: 'Front Loader', items: [
      { name: 'Front Loader Powder 2kg', brands: generateBrands(['Persil', 'OMO', 'Cold Power'], 12.99), defaultBrand: 'Persil', averagePrice: 12.99 }
    ]},
    { name: 'Top Loader', items: [
      { name: 'Top Loader Powder 2kg', brands: generateBrands(['Persil', 'OMO', 'Surf'], 12.99), defaultBrand: 'Persil', averagePrice: 12.99 }
    ]}
  ],

  // HEALTH & BEAUTY
  'Shampoo': [
    { name: 'Everyday Shampoo', items: [
      { name: 'Shampoo 400ml', brands: generateBrands(['Pantene', 'Head & Shoulders', 'Herbal Essences'], 5.99), defaultBrand: 'Pantene', averagePrice: 5.99 }
    ]},
    { name: 'Specialty Shampoo', items: [
      { name: 'Anti-Dandruff Shampoo 400ml', brands: generateBrands(['Head & Shoulders', 'Selsun', 'Nizoral'], 7.99), defaultBrand: 'Head & Shoulders', averagePrice: 7.99 }
    ]}
  ],
  'Toothpaste': [
    { name: 'Regular Toothpaste', items: [
      { name: 'Fluoride Toothpaste 120g', brands: generateBrands(['Colgate', 'Oral-B', 'Macleans'], 3.99), defaultBrand: 'Colgate', averagePrice: 3.99 }
    ]},
    { name: 'Sensitive Toothpaste', items: [
      { name: 'Sensitive Toothpaste 120g', brands: generateBrands(['Sensodyne', 'Colgate Sensitive', 'Oral-B Sensitive'], 5.99), defaultBrand: 'Sensodyne', averagePrice: 5.99 }
    ]}
  ],
  'Vitamins': [
    { name: 'Multivitamins', items: [
      { name: 'Multivitamin 60 tablets', brands: generateBrands(['Blackmores', 'Swisse', 'Centrum'], 15.99), defaultBrand: 'Blackmores', averagePrice: 15.99 }
    ]},
    { name: 'Vitamin C', items: [
      { name: 'Vitamin C 1000mg 60 tablets', brands: generateBrands(['Blackmores', 'Swisse', 'Nature\'s Own'], 12.99), defaultBrand: 'Blackmores', averagePrice: 12.99 }
    ]}
  ],

  // INTERNATIONAL FOODS
  'Asian Food': [
    { name: 'Asian Sauces', items: [
      { name: 'Soy Sauce 500ml', brands: generateBrands(['Kikkoman', 'Lee Kum Kee', 'Maggi'], 4.99), defaultBrand: 'Kikkoman', averagePrice: 4.99 },
      { name: 'Sweet Chili Sauce 250ml', brands: generateBrands(['Mae Ploy', 'Trident', 'Thai Dancer'], 3.99), defaultBrand: 'Mae Ploy', averagePrice: 3.99 }
    ]},
    { name: 'Asian Noodles', items: [
      { name: 'Instant Noodles 85g', brands: generateBrands(['Maggi', 'Indomie', 'Shin Ramyun'], 1.99), defaultBrand: 'Maggi', averagePrice: 1.99 },
      { name: 'Rice Noodles 250g', brands: generateBrands(['Three Ladies', 'Erawan', 'Chef\'s Choice'], 2.99), defaultBrand: 'Three Ladies', averagePrice: 2.99 }
    ]}
  ],
  'Indian Food': [
    { name: 'Curry Pastes', items: [
      { name: 'Curry Paste 50g', brands: generateBrands(['Patak\'s', 'Sharwood\'s', 'Blue Dragon'], 2.99), defaultBrand: 'Patak\'s', averagePrice: 2.99 }
    ]},
    { name: 'Indian Spices', items: [
      { name: 'Garam Masala 100g', brands: generateBrands(['MDH', 'Shan', 'TRS'], 3.49), defaultBrand: 'MDH', averagePrice: 3.49 }
    ]}
  ],
  'Mexican Food': [
    { name: 'Mexican Sauces', items: [
      { name: 'Salsa 300g', brands: generateBrands(['Old El Paso', 'Doritos', 'Tostitos'], 3.99), defaultBrand: 'Old El Paso', averagePrice: 3.99 }
    ]},
    { name: 'Mexican Staples', items: [
      { name: 'Tortillas 8 pack', brands: generateBrands(['Mission', 'Old El Paso', 'La Tortilleria'], 3.49), defaultBrand: 'Mission', averagePrice: 3.49 }
    ]}
  ],

  // SPECIALTY DIETARY
  'Gluten Free': [
    { name: 'Gluten Free Bread', items: [
      { name: 'GF Bread Loaf 400g', brands: generateBrands(['Vogel\'s', 'Helga\'s', 'Freedom Foods'], 6.99), defaultBrand: 'Vogel\'s', averagePrice: 6.99 }
    ]},
    { name: 'GF Pasta', items: [
      { name: 'GF Pasta 350g', brands: generateBrands(['San Remo', 'Barilla', 'Freedom Foods'], 4.99), defaultBrand: 'San Remo', averagePrice: 4.99 }
    ]}
  ],
  'Organic': [
    { name: 'Organic Produce', items: [
      { name: 'Organic Carrots 1kg', brands: generateBrands(['Organic Times', 'Ceres', 'Fresh Choice'], 4.99), defaultBrand: 'Organic Times', averagePrice: 4.99 }
    ]},
    { name: 'Organic Pantry', items: [
      { name: 'Organic Pasta 500g', brands: generateBrands(['Spiral', 'Ceres', 'Organic Times'], 3.99), defaultBrand: 'Spiral', averagePrice: 3.99 }
    ]}
  ],
  'Vegan': [
    { name: 'Plant-Based Meat', items: [
      { name: 'Plant-Based Mince 300g', brands: generateBrands(['Sunfed', 'Fry\'s', 'Beyond Meat'], 7.99), defaultBrand: 'Sunfed', averagePrice: 7.99 }
    ]},
    { name: 'Vegan Dairy', items: [
      { name: 'Vegan Cheese 200g', brands: generateBrands(['Naturli', 'Violife', 'Bio Cheese'], 6.99), defaultBrand: 'Naturli', averagePrice: 6.99 }
    ]}
  ],

  // NON-FOOD ITEMS
  'Stationery': [
    { name: 'School Supplies', items: [
      { name: 'A4 Notebook', brands: generateBrands(['Warehouse Stationery', 'Spirax', 'Office Depot'], 2.99), defaultBrand: 'Warehouse Stationery', averagePrice: 2.99 },
      { name: 'Ballpoint Pens 4 pack', brands: generateBrands(['BIC', 'Papermate', 'Pilot'], 3.49), defaultBrand: 'BIC', averagePrice: 3.49 }
    ]}
  ],
  'Magazines': [
    { name: 'General Interest', items: [
      { name: 'Weekly Magazine', brands: generateBrands(['Woman\'s Day', 'New Zealand Life', 'North & South'], 4.99), defaultBrand: 'Woman\'s Day', averagePrice: 4.99 }
    ]}
  ],
  'Greeting Cards': [
    { name: 'Occasion Cards', items: [
      { name: 'Birthday Card', brands: generateBrands(['Hallmark', 'Papyrus', 'Carlton Cards'], 5.99), defaultBrand: 'Hallmark', averagePrice: 5.99 }
    ]}
  ],
  'Phone Cards': [
    { name: 'Mobile Top-Up', items: [
      { name: '$20 Mobile Credit', brands: generateBrands(['Vodafone', 'Spark', '2degrees'], 20.00), defaultBrand: 'Vodafone', averagePrice: 20.00 }
    ]}
  ],
  'Automotive': [
    { name: 'Car Care', items: [
      { name: 'Car Wash Soap 1L', brands: generateBrands(['Armor All', 'Meguiar\'s', 'Turtle Wax'], 8.99), defaultBrand: 'Armor All', averagePrice: 8.99 },
      { name: 'Engine Oil 4L', brands: generateBrands(['Castrol', 'Mobil 1', 'Valvoline'], 35.99), defaultBrand: 'Castrol', averagePrice: 35.99 }
    ]}
  ],
  'Flowers': [
    { name: 'Fresh Flowers', items: [
      { name: 'Bouquet Mixed', brands: generateBrands(['In-Store Florist', 'Fresh', 'Seasonal'], 12.99), defaultBrand: 'In-Store Florist', averagePrice: 12.99 }
    ]}
  ],

  // SEASONAL & SPECIAL OCCASIONS
  'Christmas': [
    { name: 'Christmas Food', items: [
      { name: 'Christmas Ham 2kg', brands: generateBrands(['Hellers', 'Shaved Ham Co', 'Premium'], 35.99), defaultBrand: 'Hellers', averagePrice: 35.99 },
      { name: 'Christmas Pudding 800g', brands: generateBrands(['Edmonds', 'Traditional', 'Luxury'], 12.99), defaultBrand: 'Edmonds', averagePrice: 12.99 }
    ]},
    { name: 'Christmas Decorations', items: [
      { name: 'Christmas Tree Decorations', brands: generateBrands(['Kmart', 'The Warehouse', 'Festive'], 9.99), defaultBrand: 'Kmart', averagePrice: 9.99 }
    ]}
  ],
  'Easter': [
    { name: 'Easter Chocolate', items: [
      { name: 'Easter Eggs 200g', brands: generateBrands(['Cadbury', 'Whittaker\'s', 'Lindt'], 8.99), defaultBrand: 'Cadbury', averagePrice: 8.99 }
    ]}
  ],
  'BBQ': [
    { name: 'BBQ Essentials', items: [
      { name: 'BBQ Sauce 500ml', brands: generateBrands(['Watties', 'ETA', 'Masterfoods'], 3.99), defaultBrand: 'Watties', averagePrice: 3.99 },
      { name: 'Charcoal 4kg', brands: generateBrands(['Weber', 'Heat Beads', 'BBQ Galore'], 12.99), defaultBrand: 'Weber', averagePrice: 12.99 }
    ]}
  ]
};

// Comprehensive brand patterns for all supermarket product types
// Based on MCP research of New Zealand supermarket chains and product categories
const BRAND_PATTERNS: Record<string, string[]> = {
  // FRESH PRODUCE
  'apples': ['Jazz', 'Gala', 'Braeburn', 'Granny Smith', 'Royal Gala', 'Envy', 'Pams', 'Essentials', 'Organic'],
  'bananas': ['Dole', 'Chiquita', 'Fresh Choice', 'Organic', 'Fair Trade', 'Pams', 'Essentials'],
  'oranges': ['Sunkist', 'Naval', 'Valencia', 'Blood Orange', 'Organic', 'Pams', 'Essentials'],
  'potatoes': ['Agria', 'Red Jacket', 'Wilcox', 'Rua', 'Organic', 'Pams', 'Essentials'],
  'carrots': ['Wilcox', 'Fresh Choice', 'Organic', 'Baby Carrots', 'Purple', 'Pams', 'Essentials'],
  'onions': ['Brown', 'Red', 'Spring', 'Organic', 'Shallots', 'Pams', 'Essentials'],
  'tomatoes': ['Beefsteak', 'Cherry', 'Roma', 'Organic', 'Kumato', 'Pams', 'Essentials'],
  'lettuce': ['Iceberg', 'Cos', 'Buttercrunch', 'Organic', 'Baby Leaf', 'Pams', 'Essentials'],

  // MEAT & SEAFOOD
  'beef': ['Silver Fern Farms', 'ANZCO', 'Grass Fed', 'Organic', 'Premium'],
  'chicken': ['Tegel', 'Inghams', 'Free Range', 'Organic', 'Lilydale'],
  'lamb': ['Silver Fern Farms', 'ANZCO', 'Spring Lamb', 'Organic', 'Grass Fed'],
  'pork': ['Farmland', 'Bacon', 'Free Range', 'Organic', 'Premium'],
  'salmon': ['Regal', 'Sanford', 'King Salmon', 'Organic', 'Atlantic'],
  'tuna': ['Sealord', 'John West', 'Greenseas', 'Sirena', 'Skipjack'],
  'fish': ['Sealord', 'Sanford', 'Fresh', 'Frozen', 'Organic'],

  // DAIRY & EGGS - Enhanced with comprehensive NZ private label brands from MCP research
  'milk': ['Anchor', 'Mainland', 'Meadow Fresh', 'Lewis Road', 'Fresh\'n Fruity', 'Pams', 'Pams Value', 'Essentials', 'Countdown Own Brand', 'Value Range', 'Calci'],
  'cheese': ['Mainland', 'Anchor', 'Kapiti', 'Mercer', 'Bega', 'Tasty', 'Pams', 'Pams Finest', 'Essentials', 'Countdown Own Brand', 'Value Range'],
  'butter': ['Anchor', 'Mainland', 'Lewis Road', 'Tararua', 'Lurpak', 'Pams', 'Pams Value', 'Essentials', 'Countdown Own Brand', 'Value Range'],
  'yogurt': ['Anchor', 'Yoplait', 'Meadow Fresh', 'Danone', 'Two Good', 'Pams', 'Essentials', 'Countdown Own Brand', 'Greek Style', 'Value Range'],
  'eggs': ['Woodland', 'Frenz', 'Free Range', 'Cage Free', 'Organic', 'Pams', 'Essentials', 'Countdown Own Brand', 'Zeagold', 'Otago', 'Value Range'],
  'cream': ['Anchor', 'Mainland', 'Meadow Fresh', 'Whipping', 'Sour', 'Pams', 'Essentials', 'Countdown Own Brand', 'Value Range'],

  // PANTRY STAPLES
  'rice': ['SunRice', 'Jasmine', 'Basmati', 'Calrose', 'Uncle Ben\'s', 'Brown'],
  'pasta': ['Barilla', 'San Remo', 'La Famiglia Rana', 'Latina Fresh', 'Spiral'],
  'flour': ['Edmonds', 'Champion', 'White Wings', 'Plain', 'Self Raising'],
  'sugar': ['Chelsea', 'Raw Sugar', 'Brown Sugar', 'Icing Sugar', 'Caster'],
  'oil': ['Olivado', 'Cobram Estate', 'Bertolli', 'Sunfield', 'Rice Bran'],
  'vinegar': ['Heinz', 'White', 'Apple Cider', 'Balsamic', 'Malt'],
  'salt': ['Cerebos', 'Saxa', 'Sea Salt', 'Rock Salt', 'Pink Himalayan'],
  'pepper': ['Masterfoods', 'Black', 'White', 'Cracked', 'Ground'],

  // CANNED GOODS
  'beans': ['Watties', 'Heinz', 'Baked Beans', 'Kidney Beans', 'Cannellini'],
  'corn': ['Watties', 'SPC', 'Creamed Corn', 'Kernels', 'Organic'],
  'canned tomatoes': ['Watties', 'SPC', 'Crushed', 'Whole', 'Paste', 'Diced'],
  'canned tuna': ['Sealord', 'John West', 'Greenseas', 'Sirena', 'Springwater'],
  'canned salmon': ['Sealord', 'John West', 'Red', 'Pink', 'Wild'],
  'soup': ['Watties', 'Heinz', 'Campbell\'s', 'Tomato', 'Chicken', 'Vegetable'],

  // CONDIMENTS & SAUCES
  'sauce': ['Watties', 'ETA', 'Heinz', 'Tomato Sauce', 'BBQ Sauce'],
  'mayonnaise': ['Best Foods', 'Praise', 'Whole Egg', 'Light', 'Garlic'],
  'mustard': ['Masterfoods', 'Dijon', 'Wholegrain', 'English', 'American'],
  'jam': ['Barker\'s', 'Cottee\'s', 'Watties', 'Strawberry', 'Apricot'],
  'honey': ['Airborne', 'Comvita', 'Manuka', 'Clover', 'Native'],
  'peanut butter': ['Pic\'s', 'ETA', 'Skippy', 'Kraft', 'Crunchy', 'Smooth'],

  // BREAKFAST & CEREAL - Enhanced with comprehensive NZ brands from MCP research
  'cereal': ['Sanitarium', 'Kellogg\'s', 'Hubbards', 'Uncle Tobys', 'Nestle', 'Pams', 'Pams Value', 'Essentials', 'Countdown Own Brand', 'Value Range'],
  'oats': ['Hubbards', 'Uncle Tobys', 'Rolled Oats', 'Quick Oats', 'Steel Cut', 'Pams', 'Essentials', 'Value Range', 'Ceres Organics'],
  'muesli': ['Hubbards', 'Uncle Tobys', 'Sanitarium', 'Bircher', 'Toasted', 'Pams', 'Essentials', 'Value Range', 'Ceres Organics'],
  'cornflakes': ['Kellogg\'s', 'Sanitarium', 'Uncle Tobys', 'Organic', 'Honey', 'Pams', 'Essentials', 'Value Range'],

  // BEVERAGES
  'coffee': ['Nescafe', 'Moccona', 'Greggs', 'L\'affare', 'Atomic'],
  'tea': ['Dilmah', 'Twinings', 'Bell Tea', 'English Breakfast', 'Earl Grey'],
  'juice': ['Just Juice', 'Keri', 'Fresh Up', 'Orange', 'Apple', 'Tropical'],
  'soft drink': ['Coca Cola', 'Pepsi', 'Fanta', 'Sprite', 'L&P'],
  'water': ['Pump', 'Evian', 'Fiji', 'Sparkling', 'Still'],
  'wine': ['Oyster Bay', 'Whitehaven', 'Montana', 'Sauvignon Blanc', 'Pinot Noir'],
  'beer': ['Steinlager', 'DB Export', 'Speight\'s', 'Corona', 'Heineken'],

  // SNACKS & CONFECTIONERY
  'chips': ['Bluebird', 'ETA', 'Proper', 'Kettle', 'Doritos', 'Pringles'],
  'crackers': ['Griffin\'s', 'Arnott\'s', 'Carr\'s', 'Vita-Weat', 'Water'],
  'biscuits': ['Griffin\'s', 'Arnott\'s', 'Toffee Pops', 'Krispie', 'Chocolate Chip'],
  'chocolate': ['Cadbury', 'Whittaker\'s', 'Nestle', 'Mars', 'Ferrero'],
  'lollies': ['Pascall', 'Cadbury', 'Nestle', 'Haribo', 'Mentos'],
  'nuts': ['Pic\'s', 'Almond', 'Cashew', 'Walnut', 'Mixed Nuts', 'Pistachios'],
  'popcorn': ['Watties', 'Orville', 'Sweet', 'Salted', 'Caramel'],

  // FROZEN FOODS
  'frozen vegetables': ['McCain', 'Watties', 'Peas', 'Corn', 'Mixed Vegetables'],
  'frozen fruit': ['McCain', 'Mixed Berries', 'Strawberries', 'Mango', 'Raspberries'],
  'ice cream': ['Tip Top', 'Much Moore', 'Ben & Jerry\'s', 'Haagen Dazs', 'New Zealand Natural'],
  'frozen meals': ['McCain', 'Lean Cuisine', 'Weight Watchers', 'Watties', 'Healthy Choice'],
  'fish fingers': ['Birds Eye', 'McCain', 'Sealord', 'Crumbed', 'Battered'],
  'pizza': ['Dr Oetker', 'McCain', 'Pams', 'Margherita', 'Pepperoni'],

  // HEALTH & BEAUTY
  'shampoo': ['Pantene', 'Head & Shoulders', 'Herbal Essences', 'L\'Oreal', 'Aussie'],
  'conditioner': ['Pantene', 'Head & Shoulders', 'Herbal Essences', 'L\'Oreal', 'Aussie'],
  'soap': ['Sunlight', 'Palmolive', 'Imperial Leather', 'Dove', 'Lux'],
  'toothpaste': ['Colgate', 'Oral-B', 'Sensodyne', 'Close Up', 'Macleans'],
  'deodorant': ['Rexona', 'Lynx', 'Dove', 'Sure', 'Nivea'],
  'moisturizer': ['Nivea', 'Olay', 'Neutrogena', 'Cetaphil', 'Aveeno'],
  'sunscreen': ['Banana Boat', 'Cancer Society', 'Nivea', 'Neutrogena', 'SPF 50+'],

  // HOUSEHOLD & CLEANING - Enhanced with comprehensive NZ brands from MCP research
  'laundry powder': ['Persil', 'OMO', 'Cold Power', 'Surf', 'Fab', 'Pams', 'Essentials', 'Countdown Own Brand', 'Ecostore'],
  'dishwashing liquid': ['Sunlight', 'Palmolive', 'Morning Fresh', 'Earth Choice', 'Fairy', 'Pams', 'Essentials', 'Countdown Own Brand'],
  'toilet paper': ['Sorbent', 'Kleenex', 'Quilton', 'Purex', 'Tuffy', 'Pams', 'Essentials', 'Countdown Own Brand', 'Value Range'],
  'paper towels': ['Handee', 'Sorbent', 'Kleenex', 'Pams', 'Essentials', 'Countdown Own Brand'],
  'tissues': ['Kleenex', 'Sorbent', 'Quilton', 'Pams', 'Essentials', 'Countdown Own Brand'],
  'hand soap': ['Palmolive', 'Dettol', 'Dove', 'Imperial Leather', 'Pams', 'Essentials', 'Countdown Own Brand'],
  'all purpose cleaner': ['Dettol', 'Jif', 'Mr Muscle', 'Handy Andy', 'Pams', 'Essentials', 'Countdown Own Brand', 'Ecostore'],
  'bathroom cleaner': ['Dettol', 'Jif', 'Duck', 'Harpic', 'Pams', 'Essentials', 'Countdown Own Brand'],
  'glass cleaner': ['Windex', 'Mr Muscle', 'Dettol', 'Pams', 'Essentials', 'Countdown Own Brand'],
  'floor cleaner': ['Pine O Cleen', 'Dettol', 'Mr Muscle', 'Pams', 'Essentials', 'Countdown Own Brand'],

  // SPECIALTY DIETARY - Based on comprehensive MCP research of NZ specialty brands
  'gluten free bread': ['Venerdi Gluten Freedom', 'Gluten Free Choice', 'Vogel\'s GF', 'Helga\'s GF', 'Freedom Foods', 'Free From'],
  'gluten free pasta': ['San Remo GF', 'Barilla GF', 'Freedom Foods', 'Ceres Organics', 'Free From'],
  'gluten free flour': ['Edmonds GF', 'White Wings GF', 'Bob\'s Red Mill', 'Free From'],
  'organic produce': ['Ceres Organics', 'Organic Times', 'Fresh Choice Organic', 'Macro Organic', 'The Odd Bunch'],
  'organic dairy': ['Anchor Organic', 'Lewis Road Organic', 'Organic Valley', 'Macro Organic'],
  'organic pantry': ['Ceres Organics', 'Spiral Organics', 'Organic Times', 'Macro Organic'],
  'plant based milk': ['Oatly', 'So Good', 'Vitasoy', 'Blue Diamond', 'Minor Figures', 'Plantitude'],
  'plant based meat': ['Sunfed', 'Fry\'s Family Food', 'Beyond Meat', 'Impossible', 'Plantitude'],
  'vegan cheese': ['Naturli', 'Violife', 'Bio Cheese', 'Sheese', 'Plantitude'],
  'keto products': ['Atkins', 'Low Carb', 'Quest', 'Keto Naturals'],
  'paleo products': ['Paleo Way', 'Base Culture', 'Primal Kitchen'],

  // BABY & CHILDREN - Based on MCP research findings
  'baby formula': ['Aptamil', 'S26', 'Karicare', 'A2 Platinum', 'Only Organic'],
  'baby food': ['Watties Baby', 'Only Organic', 'Heinz Baby', 'Plum Organics'],
  'nappies': ['Huggies', 'Pampers', 'Babylove', 'Treasures', 'Libero'],
  'baby wipes': ['Huggies', 'Pampers', 'Water Wipes', 'Treasures'],

  // PET SUPPLIES - Comprehensive category from MCP research
  'dog food dry': ['Royal Canin', 'Hills', 'Pedigree', 'Purina', 'Black Hawk', 'Eukanuba'],
  'dog food wet': ['Pedigree', 'Purina', 'Hills', 'Royal Canin', 'Schmackos'],
  'cat food dry': ['Whiskas', 'Purina', 'Hills', 'Royal Canin', 'Fancy Feast'],
  'cat food wet': ['Whiskas', 'Fancy Feast', 'Dine', 'Purina', 'Hills'],
  'cat litter': ['Catsan', 'Trouble n Trix', 'Kitty Fresh', 'Breeders Choice'],
  'dog treats': ['Schmackos', 'Pedigree', 'Dentastix', 'Jerky Time'],
  'cat treats': ['Temptations', 'Whiskas', 'Dreamies', 'Dine'],

  // VITAMINS & SUPPLEMENTS - Based on MCP research
  'multivitamins': ['Blackmores', 'Swisse', 'Centrum', 'Nature\'s Own', 'Radiance'],
  'vitamin c': ['Blackmores', 'Swisse', 'Nature\'s Own', 'Radiance', 'Healtheries'],
  'vitamin d': ['Blackmores', 'Swisse', 'Nature\'s Own', 'Radiance'],
  'fish oil': ['Blackmores', 'Swisse', 'Nature\'s Own', 'Hi Health'],
  'probiotics': ['Inner Health Plus', 'Swisse', 'Blackmores', 'Life Space'],

  // AUTOMOTIVE - New category from MCP research
  'car care': ['Armor All', 'Meguiar\'s', 'Turtle Wax', 'Mothers', 'Chemical Guys'],
  'engine oil': ['Castrol', 'Mobil 1', 'Valvoline', 'Shell', 'BP'],
  'windscreen wash': ['Armor All', 'SCA', 'Prestone'],

  // SEASONAL & OCCASIONS - Enhanced from MCP findings
  'christmas food': ['Hellers Christmas Ham', 'Edmonds Christmas Pudding', 'Traditional Christmas Cake'],
  'easter chocolate': ['Cadbury Easter', 'Whittaker\'s Easter', 'Lindt Easter'],
  'bbq essentials': ['Weber BBQ', 'Heat Beads Charcoal', 'BBQ Galore', 'Watties BBQ Sauce'],

  // INTERNATIONAL FOODS - Expanded from MCP research
  'asian sauces': ['Kikkoman', 'Lee Kum Kee', 'Maggi', 'Blue Dragon', 'Ayam'],
  'indian spices': ['MDH', 'Shan', 'TRS', 'Patak\'s', 'Sharwood\'s'],
    'mexican food': ['Old El Paso', 'Mission', 'Doritos Mexican', 'La Tortilleria'],
  'italian food': ['Barilla', 'San Remo', 'Mutti', 'De Cecco', 'La Molisana'],
};

class EnhancedShoppingDataService {
  private cache = new Map<string, SubcategoryData[]>();
  private searchAttempts = new Map<string, number>();

  async getEnhancedDataForItem(productName: string): Promise<SubcategoryData[]> {
    console.log('🔍 EnhancedShoppingDataService: Getting enhanced data for:', productName);

    // Check cache first
    const cacheKey = productName.toLowerCase();
    if (this.cache.has(cacheKey)) {
      console.log('✅ Found in cache:', productName);
      return this.cache.get(cacheKey)!;
    }

    // Check if we have predefined enhanced data
    const predefinedData = this.getPredefinedEnhancedData(productName);
    if (predefinedData.length > 0) {
      console.log('✅ Found predefined enhanced data for:', productName);
      this.cache.set(cacheKey, predefinedData);
      return predefinedData;
    }

    // Generate enhanced data from real search
    console.log('🔄 Generating enhanced data from search for:', productName);
    try {
      const enhancedData = await this.generateEnhancedDataFromSearch(productName);
      this.cache.set(cacheKey, enhancedData);
      return enhancedData;
    } catch (error) {
      console.error('❌ Failed to generate enhanced data:', error);
      
      // Fallback: Generate basic enhanced data with common brands
      const fallbackData = this.generateFallbackEnhancedData(productName);
      this.cache.set(cacheKey, fallbackData);
      return fallbackData;
    }
  }

  private getPredefinedEnhancedData(productName: string): SubcategoryData[] {
    const name = productName.toLowerCase();
    for (const [category, data] of Object.entries(ENHANCED_CATEGORY_DATA)) {
      if (name.includes(category.toLowerCase())) {
        console.log(`Found predefined data for category: ${category}`);
        return data;
      }
    }
    return [];
  }

  private async generateEnhancedDataFromSearch(productName: string): Promise<SubcategoryData[]> {
    console.log('🔍 Searching for real products for:', productName);
    
    try {
      const searchResult = await priceComparisonService.searchProductAcrossStores(productName, {
        maxResults: 20,
        sortBy: 'price',
        includeOutOfStock: false
      });

      console.log(`Found ${searchResult.results.length} products for ${productName}`);
      
      if (searchResult.results.length === 0) {
        throw new Error('No products found in search');
      }

      // Group products by similarity and create subcategories
      const subcategories = this.createSubcategoriesFromProducts(productName, searchResult.results);
      console.log(`Created ${subcategories.length} subcategories for ${productName}`);
      
      return subcategories;
    } catch (error) {
      console.error('❌ Search failed for:', productName, error);
      throw error;
    }
  }

  private createSubcategoriesFromProducts(productName: string, products: any[]): SubcategoryData[] {
    // Group products by type/variant
    const productGroups = new Map<string, any[]>();
    
    products.forEach(product => {
      const productInfo = product.product || product;
      const name = productInfo.name || productInfo.title || '';
      
      // Determine subcategory based on product name
      const subcategory = this.determineSubcategory(name, productName);
      
      if (!productGroups.has(subcategory)) {
        productGroups.set(subcategory, []);
      }
      productGroups.get(subcategory)!.push(product);
    });

    // Convert groups to subcategory data
    const subcategories: SubcategoryData[] = [];
    
    productGroups.forEach((groupProducts, subcategoryName) => {
      const items = this.createItemsFromProducts(groupProducts);
      if (items.length > 0) {
        subcategories.push({
          name: subcategoryName,
          items: items
        });
      }
    });

    return subcategories;
  }

  private determineSubcategory(productName: string, searchTerm: string): string {
    const name = productName.toLowerCase();
    const term = searchTerm.toLowerCase();
    
    // Define subcategory patterns
    const subcategoryPatterns = [
      { keywords: ['instant', 'granules'], category: 'Instant' },
      { keywords: ['beans', 'whole bean'], category: 'Beans' },
      { keywords: ['pods', 'capsule'], category: 'Pods' },
      { keywords: ['fresh', 'chilled'], category: 'Fresh' },
      { keywords: ['frozen'], category: 'Frozen' },
      { keywords: ['organic'], category: 'Organic' },
      { keywords: ['lite', 'light', 'low fat'], category: 'Light' },
      { keywords: ['whole', 'full'], category: 'Whole' },
      { keywords: ['skim', 'trim'], category: 'Skim' },
      { keywords: ['white'], category: 'White' },
      { keywords: ['brown', 'multigrain'], category: 'Brown/Multigrain' },
      { keywords: ['gluten free'], category: 'Gluten Free' }
    ];

    for (const pattern of subcategoryPatterns) {
      if (pattern.keywords.some(keyword => name.includes(keyword))) {
        return pattern.category;
      }
    }

    // Default subcategory
    return this.capitalize(term);
  }

  private createItemsFromProducts(products: any[]): SubcategoryItem[] {
    // Group by brand
    const brandGroups = new Map<string, any[]>();
    
    products.forEach(product => {
      const productInfo = product.product || product;
      const brand = this.extractBrand(productInfo.name || productInfo.title || '');
      
      if (!brandGroups.has(brand)) {
        brandGroups.set(brand, []);
      }
      brandGroups.get(brand)!.push(product);
    });

    // Create items from brand groups
    const items: SubcategoryItem[] = [];
    
    brandGroups.forEach((brandProducts, brandName) => {
      const brands = this.createBrandOptionsFromProducts(brandProducts);
      const avgPrice = brands.reduce((sum, b) => sum + b.averagePrice, 0) / brands.length;
      
      items.push({
        name: brandProducts[0].product?.name || brandProducts[0].name || 'Product',
        brands: brands,
        defaultBrand: brandName,
        averagePrice: avgPrice
      });
    });

    return items;
  }

  private createBrandOptionsFromProducts(products: any[]): BrandOption[] {
    const brandMap = new Map<string, any[]>();
    
    // Group by brand
    products.forEach(product => {
      const productInfo = product.product || product;
      const brand = this.extractBrand(productInfo.name || productInfo.title || '');
      
      if (!brandMap.has(brand)) {
        brandMap.set(brand, []);
      }
      brandMap.get(brand)!.push(product);
    });

    const brandOptions: BrandOption[] = [];
    
    brandMap.forEach((brandProducts, brandName) => {
      const stores: any = {};
      let totalPrice = 0;
      let priceCount = 0;
      
      brandProducts.forEach(product => {
        const productInfo = product.product || product;
        const store = product.store || product.storeName;
        const price = productInfo.price || 0;
        
        if (store && price > 0) {
          stores[store] = {
            price: price,
            available: true,
            size: productInfo.size || productInfo.unit || ''
          };
          totalPrice += price;
          priceCount++;
        }
      });

      const avgPrice = priceCount > 0 ? totalPrice / priceCount : 0;
      
      brandOptions.push({
        name: brandName,
        stores: stores,
        averagePrice: avgPrice,
        productId: brandProducts[0].product?.id || brandProducts[0].id
      });
    });

    return brandOptions;
  }

  private generateFallbackEnhancedData(productName: string): SubcategoryData[] {
    console.log('🔄 Generating fallback enhanced data for:', productName);
    
    const name = productName.toLowerCase();
    const possibleBrands = this.getBrandsForProduct(name);
    
    if (possibleBrands.length === 0) {
      return [];
    }

    // Create a single subcategory with common brands
    const brands: BrandOption[] = possibleBrands.map(brandName => ({
      name: brandName,
      averagePrice: this.estimatePrice(productName),
      stores: {
        woolworths: { price: this.estimatePrice(productName) * 1.05, available: true },
        newworld: { price: this.estimatePrice(productName) * 1.1, available: true },
        paknsave: { price: this.estimatePrice(productName) * 0.95, available: true }
      }
    }));

    return [{
      name: this.capitalize(productName),
      items: [{
        name: productName,
        brands: brands,
        defaultBrand: possibleBrands[0],
        averagePrice: this.estimatePrice(productName)
      }]
    }];
  }

  private getBrandsForProduct(productName: string): string[] {
    const name = productName.toLowerCase();
    
    for (const [category, brands] of Object.entries(BRAND_PATTERNS)) {
      if (name.includes(category) || category.includes(name)) {
        return brands;
      }
    }
    
    // Generic fallback brands
    return ['Generic', 'House Brand', 'Premium'];
  }

  private extractBrand(productName: string): string {
    const name = productName.toLowerCase();
    
    // Known brand patterns
    const knownBrands = [
      'anchor', 'mainland', 'meadow fresh', 'lewis road', 'tip top', 'much moore',
      'nescafe', 'moccona', 'greggs', 'oatly', 'minor figures', 'barilla', 'san remo',
      'bluebird', 'eta', 'proper', 'kettle', 'doritos', 'pringles', 'sunrice',
      'griffin\'s', 'arnott\'s', 'carr\'s', 'vita-weat', 'sanitarium', 'kellogg\'s',
      'hubbards', 'uncle tobys', 'nestle', 'dilmah', 'twinings', 'bell tea'
    ];

    for (const brand of knownBrands) {
      if (name.includes(brand)) {
        return this.capitalize(brand);
      }
    }

    // Extract first word as potential brand
    const words = productName.split(' ');
    if (words.length > 0 && words[0].length > 2) {
      return this.capitalize(words[0]);
    }

    return 'Generic';
  }

  private estimatePrice(productName: string): number {
    const name = productName.toLowerCase();
    
    // Price estimates based on product type
    const priceEstimates: Record<string, number> = {
      'milk': 3.50, 'bread': 2.50, 'cheese': 8.00, 'butter': 4.50,
      'yogurt': 2.00, 'eggs': 5.00, 'coffee': 10.00, 'tea': 6.00,
      'rice': 4.00, 'pasta': 3.00, 'flour': 3.50, 'sugar': 2.50,
      'oil': 6.00, 'chips': 4.00, 'biscuits': 3.50, 'cereal': 7.00,
      'soup': 3.00, 'sauce': 4.00, 'jam': 5.00, 'honey': 8.00
    };

    for (const [product, price] of Object.entries(priceEstimates)) {
      if (name.includes(product)) {
        return price;
      }
    }

    return 5.00; // Default estimate
  }

  private capitalize(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  // Clear cache when needed
  clearCache(): void {
    this.cache.clear();
    this.searchAttempts.clear();
  }
}

export const enhancedShoppingDataService = new EnhancedShoppingDataService(); 