import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert,
  StyleSheet,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '../context/ThemeContext';
import { theme } from '../styles';
import { priceIntegrationService } from '../services/priceIntegrationService';
import { ProductPriceData } from '../types/shoppingList';
import { UserPreferences } from '../utils/userPreferences';
import { createArrayKey } from '../utils/uniqueId';

interface BrandOption {
  brand: string;
  size: string;
  price: number;
  store: string;
  storeName: string;
  recommended: boolean;
  reason: string;
  isUserPreference: boolean;
  savings?: number;
}

interface BrandSelectorDropdownProps {
  visible: boolean;
  onClose: () => void;
  productName: string;
  baseProduct: string;
  currentSelection?: { brand: string; size: string };
  onBrandSelect: (brand: string, size: string, rejectedOptions: Array<{ brand: string; size: string; price: number }>) => void;
}

export const BrandSelectorDropdown: React.FC<BrandSelectorDropdownProps> = ({
  visible,
  onClose,
  productName,
  baseProduct,
  currentSelection,
  onBrandSelect,
}) => {
  const { colors } = useTheme();
  const [loading, setLoading] = useState(false);
  const [productData, setProductData] = useState<ProductPriceData | null>(null);
  const [options, setOptions] = useState<BrandOption[]>([]);
  const [rememberChoice, setRememberChoice] = useState(true);
  const [selectedOption, setSelectedOption] = useState<BrandOption | null>(null);

  useEffect(() => {
    if (visible && baseProduct) {
      loadProductOptions();
    }
  }, [visible, baseProduct]);

  const loadProductOptions = async () => {
    setLoading(true);
    try {
      console.log(`🔍 Loading brand options for: ${baseProduct}`);
      
      const data = await priceIntegrationService.getProductPrices(baseProduct);
      setProductData(data);
      
      // Convert to flat options list
      const allOptions: BrandOption[] = [];
      const userPreference = data.userPreference;
      
      data.availableBrands.forEach((brandGroup: any) => {
        brandGroup.sizes.forEach((sizeGroup: any) => {
          const cheapestStorePrice = sizeGroup.allStorePrices
            .filter((p: any) => p.available)
            .reduce((min: any, current: any) => current.price < min.price ? current : min);
          
          if (cheapestStorePrice) {
            const isUserPreference = userPreference &&
              userPreference.brand === brandGroup.brand &&
              userPreference.size === sizeGroup.size;
            
            const isRecommended = data.recommendedOption &&
              data.recommendedOption.brand === brandGroup.brand &&
              data.recommendedOption.size === sizeGroup.size;
            
            // Calculate savings vs most expensive option
            const allPrices = data.availableBrands.flatMap((b: any) => 
              b.sizes.flatMap((s: any) => 
                s.allStorePrices.filter((p: any) => p.available).map((p: any) => p.price)
              )
            );
            const maxPrice = Math.max(...allPrices);
            const savings = maxPrice > cheapestStorePrice.price ? maxPrice - cheapestStorePrice.price : 0;
            
            allOptions.push({
              brand: brandGroup.brand,
              size: sizeGroup.size,
              price: cheapestStorePrice.price,
              store: cheapestStorePrice.store,
              storeName: cheapestStorePrice.storeName,
              recommended: isRecommended || false,
              reason: isUserPreference 
                ? `Your preferred choice (${userPreference.confidence * 100}% confidence)`
                : isRecommended 
                  ? data.recommendedOption!.reason
                  : `$${cheapestStorePrice.price.toFixed(2)} at ${cheapestStorePrice.storeName}`,
              isUserPreference: isUserPreference || false,
              savings: savings > 0 ? savings : undefined
            });
          }
        });
      });
      
      // Sort options: user preference first, then recommended, then by price
      allOptions.sort((a, b) => {
        if (a.isUserPreference && !b.isUserPreference) return -1;
        if (!a.isUserPreference && b.isUserPreference) return 1;
        if (a.recommended && !b.recommended) return -1;
        if (!a.recommended && b.recommended) return 1;
        return a.price - b.price;
      });
      
      setOptions(allOptions);
      
      // Auto-select current selection or user preference
      if (currentSelection) {
        const current = allOptions.find(opt => 
          opt.brand === currentSelection.brand && opt.size === currentSelection.size
        );
        setSelectedOption(current || null);
      } else if (allOptions.length > 0 && allOptions[0].isUserPreference) {
        setSelectedOption(allOptions[0]);
      }
      
    } catch (error) {
      console.error('❌ Error loading product options:', error);
      Alert.alert('Error', 'Failed to load brand options. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSelectOption = (option: BrandOption) => {
    setSelectedOption(option);
  };

  const handleConfirm = () => {
    if (!selectedOption) {
      Alert.alert('Selection Required', 'Please select a brand and size.');
      return;
    }

    // Prepare rejected options for learning
    const rejectedOptions = options
      .filter(opt => opt !== selectedOption)
      .map(opt => ({
        brand: opt.brand,
        size: opt.size,
        price: opt.price
      }));

    // Call the selection handler
    onBrandSelect(selectedOption.brand, selectedOption.size, rejectedOptions);
    
    onClose();
  };

  const getStoreColor = (store: string): string => {
    const storeColors = {
      woolworths: '#00A651',
      newworld: '#E31E24',
      paknsave: '#FFD100',
    };
    return storeColors[store as keyof typeof storeColors] || theme.colors.primary;
  };

  const getStoreEmoji = (store: string): string => {
    const storeEmojis = {
      woolworths: '🍎',
      newworld: '🛒',
      paknsave: '💰',
    };
    return storeEmojis[store as keyof typeof storeEmojis] || '🏪';
  };

  const renderCategoryHeader = (title: string, emoji: string) => (
    <View style={[styles.categoryHeader, { borderBottomColor: colors.border }]}>
      <Text style={[styles.categoryTitle, { color: colors.text }]}>
        {emoji} {title}
      </Text>
    </View>
  );

  const renderOption = (option: BrandOption, keyPrefix: string | number) => {
    const isSelected = selectedOption === option;
    
    return (
      <TouchableOpacity
        key={createArrayKey(`${option.brand}-${option.size}`, typeof keyPrefix === 'number' ? keyPrefix : parseInt(keyPrefix) || 0)}
        style={[
          styles.optionCard,
          {
            backgroundColor: isSelected ? theme.colors.primary + '20' : colors.surface,
            borderColor: isSelected ? theme.colors.primary : colors.border,
            borderWidth: isSelected ? 2 : 1,
          }
        ]}
        onPress={() => handleSelectOption(option)}
      >
        <View style={styles.optionHeader}>
          <View style={styles.brandInfo}>
            <Text style={[styles.brandName, { color: colors.text }]}>
              {option.brand} {option.size}
            </Text>
            {option.isUserPreference && (
              <View style={[styles.badge, { backgroundColor: '#4CAF50' }]}>
                <Text style={styles.badgeText}>⭐ Your Choice</Text>
              </View>
            )}
            {option.recommended && !option.isUserPreference && (
              <View style={[styles.badge, { backgroundColor: theme.colors.primary }]}>
                <Text style={styles.badgeText}>✨ Recommended</Text>
              </View>
            )}
          </View>
          
          <View style={styles.priceInfo}>
            <Text style={[styles.price, { color: colors.text }]}>
              ${option.price.toFixed(2)}
            </Text>
            {option.savings && option.savings > 0 && (
              <Text style={[styles.savings, { color: '#4CAF50' }]}>
                Save ${option.savings.toFixed(2)}
              </Text>
            )}
          </View>
        </View>
        
        <View style={styles.storeInfo}>
          <Text style={styles.storeEmoji}>{getStoreEmoji(option.store)}</Text>
          <Text style={[styles.storeName, { color: getStoreColor(option.store) }]}>
            {option.storeName}
          </Text>
        </View>
        
        <Text style={[styles.reason, { color: colors.textSecondary }]}>
          {option.reason}
        </Text>
        
        {isSelected && (
          <View style={[styles.selectedIndicator, { backgroundColor: theme.colors.primary }]}>
            <Text style={styles.selectedText}>✓ Selected</Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const renderOptions = () => {
    if (options.length === 0) {
      return (
        <View style={styles.emptyState}>
          <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
            No brand options available for this product.
          </Text>
        </View>
      );
    }

    // Group options by category
    const userPreferences = options.filter(opt => opt.isUserPreference);
    const recommended = options.filter(opt => opt.recommended && !opt.isUserPreference);
    const budgetOptions = options.filter(opt => 
      !opt.isUserPreference && !opt.recommended && opt.price <= 5
    );
    const premiumOptions = options.filter(opt => 
      !opt.isUserPreference && !opt.recommended && opt.price > 5
    );

    return (
      <ScrollView style={styles.optionsContainer} showsVerticalScrollIndicator={false}>
        {userPreferences.length > 0 && (
          <>
            {renderCategoryHeader('Your Preferences', '⭐')}
            {userPreferences.map((option, index) => renderOption(option, `pref-${index}`))}
          </>
        )}
        
        {recommended.length > 0 && (
          <>
            {renderCategoryHeader('Recommended', '✨')}
            {recommended.map((option, index) => renderOption(option, `rec-${index}`))}
          </>
        )}
        
        {budgetOptions.length > 0 && (
          <>
            {renderCategoryHeader('Budget Options', '💰')}
            {budgetOptions.map((option, index) => renderOption(option, `budget-${index}`))}
          </>
        )}
        
        {premiumOptions.length > 0 && (
          <>
            {renderCategoryHeader('Premium Options', '🥉')}
            {premiumOptions.map((option, index) => renderOption(option, `premium-${index}`))}
          </>
        )}
      </ScrollView>
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView style={[styles.container, { backgroundColor: colors.backgroundSecondary }]}>
        {/* Header */}
        <View style={[styles.header, { backgroundColor: colors.surface, borderBottomColor: colors.border }]}>
          <TouchableOpacity onPress={onClose}>
            <Text style={[styles.cancelButton, { color: colors.textSecondary }]}>
              Cancel
            </Text>
          </TouchableOpacity>
          
          <View style={styles.headerTitle}>
            <Text style={[styles.title, { color: colors.text }]}>
              Choose Brand
            </Text>
            <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
              {productName}
            </Text>
          </View>
          
          <TouchableOpacity 
            onPress={handleConfirm}
            disabled={!selectedOption}
            style={[
              styles.confirmButton,
              { 
                backgroundColor: selectedOption ? theme.colors.primary : colors.border,
                opacity: selectedOption ? 1 : 0.5
              }
            ]}
          >
            <Text style={[styles.confirmText, { color: 'white' }]}>
              Select
            </Text>
          </TouchableOpacity>
        </View>

        {/* Content */}
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
              Loading brand options...
            </Text>
          </View>
        ) : (
          <>
            {renderOptions()}
            
            {/* Remember Choice Toggle */}
            {selectedOption && (
              <View style={[styles.rememberSection, { backgroundColor: colors.surface, borderTopColor: colors.border }]}>
                <TouchableOpacity
                  style={styles.rememberToggle}
                  onPress={() => setRememberChoice(!rememberChoice)}
                >
                  <View style={[
                    styles.checkbox,
                    { 
                      backgroundColor: rememberChoice ? theme.colors.primary : 'transparent',
                      borderColor: rememberChoice ? theme.colors.primary : colors.border
                    }
                  ]}>
                    {rememberChoice && (
                      <Text style={styles.checkmark}>✓</Text>
                    )}
                  </View>
                  <Text style={[styles.rememberText, { color: colors.text }]}>
                    Remember my choice for {baseProduct}
                  </Text>
                </TouchableOpacity>
              </View>
            )}
          </>
        )}
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.base,
    paddingVertical: theme.spacing.base,
    borderBottomWidth: 1,
  },
  cancelButton: {
    fontSize: theme.typography.fontSize.base,
    minWidth: 60,
  },
  headerTitle: {
    alignItems: 'center',
    flex: 1,
  },
  title: {
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.semibold,
  },
  subtitle: {
    fontSize: theme.typography.fontSize.sm,
    marginTop: 2,
  },
  confirmButton: {
    paddingHorizontal: theme.spacing.base,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.radius.base,
    minWidth: 60,
    alignItems: 'center',
  },
  confirmText: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.semibold,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: theme.spacing['2xl'],
  },
  loadingText: {
    fontSize: theme.typography.fontSize.base,
    marginTop: theme.spacing.base,
  },
  optionsContainer: {
    flex: 1,
    padding: theme.spacing.base,
  },
  categoryHeader: {
    paddingVertical: theme.spacing.sm,
    borderBottomWidth: 1,
    marginBottom: theme.spacing.sm,
    marginTop: theme.spacing.base,
  },
  categoryTitle: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.semibold,
  },
  optionCard: {
    borderRadius: theme.radius.lg,
    padding: theme.spacing.base,
    marginBottom: theme.spacing.sm,
    position: 'relative',
  },
  optionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: theme.spacing.xs,
  },
  brandInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  brandName: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.semibold,
    marginRight: theme.spacing.xs,
  },
  badge: {
    paddingHorizontal: theme.spacing.xs,
    paddingVertical: 2,
    borderRadius: theme.radius.sm,
    marginLeft: theme.spacing.xs,
  },
  badgeText: {
    color: 'white',
    fontSize: theme.typography.fontSize.xs,
    fontWeight: theme.typography.fontWeight.bold,
  },
  priceInfo: {
    alignItems: 'flex-end',
  },
  price: {
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.bold,
  },
  savings: {
    fontSize: theme.typography.fontSize.xs,
    fontWeight: theme.typography.fontWeight.semibold,
    marginTop: 2,
  },
  storeInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.xs,
  },
  storeEmoji: {
    fontSize: 16,
    marginRight: theme.spacing.xs,
  },
  storeName: {
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.semibold,
  },
  reason: {
    fontSize: theme.typography.fontSize.sm,
    lineHeight: 18,
  },
  selectedIndicator: {
    position: 'absolute',
    top: theme.spacing.xs,
    right: theme.spacing.xs,
    paddingHorizontal: theme.spacing.xs,
    paddingVertical: 2,
    borderRadius: theme.radius.sm,
  },
  selectedText: {
    color: 'white',
    fontSize: theme.typography.fontSize.xs,
    fontWeight: theme.typography.fontWeight.bold,
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: theme.spacing['2xl'],
  },
  emptyText: {
    fontSize: theme.typography.fontSize.base,
    textAlign: 'center',
  },
  rememberSection: {
    padding: theme.spacing.base,
    borderTopWidth: 1,
  },
  rememberToggle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 4,
    borderWidth: 2,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: theme.spacing.sm,
  },
  checkmark: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  rememberText: {
    fontSize: theme.typography.fontSize.base,
    flex: 1,
  },
});