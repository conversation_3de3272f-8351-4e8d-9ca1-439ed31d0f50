/**
 * Smart Search Service
 * 
 * Core search service that provides intelligent product search capabilities
 * using PostgreSQL full-text search, fuzzy matching, and popularity ranking.
 */

import { supabase } from '../supabase/client';

export interface SmartSearchResult {
  id: string;
  name: string;
  brand: string;
  category: string;
  store: string;
  price: number;
  image_url?: string;
  relevance_score: number;
  match_type: 'exact' | 'alias' | 'fuzzy';
  shopping_aliases?: string[];
  tags?: string[];
}

export interface SearchOptions {
  maxResults?: number;
  storeFilter?: 'woolworths' | 'newworld' | 'paknsave';
  categoryFilter?: string;
  minPrice?: number;
  maxPrice?: number;
  includeImages?: boolean;
  preferredStore?: 'woolworths' | 'newworld' | 'paknsave';
}

export interface SearchSuggestion {
  query: string;
  category: string;
  popularity: number;
  type: 'product' | 'brand' | 'alias' | 'category';
}

export interface PopularTerm {
  query: string;
  search_count: number;
  category?: string;
}

export interface SearchAnalytics {
  id: string;
  query: string;
  results_count: number;
  selected_product_id?: string;
  search_type: 'text' | 'fuzzy' | 'alias' | 'category';
  response_time_ms?: number;
}

class SmartSearchService {
  private cache = new Map<string, { data: any; timestamp: number }>();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  /**
   * Perform intelligent product search
   */
  async searchProducts(query: string, options: SearchOptions = {}): Promise<SmartSearchResult[]> {
    const {
      maxResults = 20,
      storeFilter,
      categoryFilter,
      minPrice,
      maxPrice
    } = options;

    if (!query.trim()) {
      return [];
    }

    const cacheKey = `search:${query}:${JSON.stringify(options)}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      // Try the enhanced search function first
      const { data, error } = await supabase.rpc('search_products_enhanced', {
        search_query: query.trim(),
        max_results: maxResults,
        store_filter: storeFilter || null,
        category_filter: categoryFilter || null,
        min_price: minPrice || null,
        max_price: maxPrice || null
      });

      if (!error && data) {
        const results: SmartSearchResult[] = (data || []).map(item => ({
          id: item.id,
          name: item.name,
          brand: item.brand,
          category: item.category,
          store: item.store,
          price: item.price,
          image_url: item.image_url,
          relevance_score: item.relevance_score,
          match_type: item.match_type as 'exact' | 'alias' | 'fuzzy'
        }));

        this.setCachedData(cacheKey, results);
        return results;
      }

      // Fallback to basic search if enhanced function not available
      console.log('Using fallback search method');
      return await this.fallbackSearch(query, options);

    } catch (error) {
      console.error('Search service error, using fallback:', error);
      return await this.fallbackSearch(query, options);
    }
  }

  /**
   * Fallback search method using basic Supabase queries
   */
  private async fallbackSearch(query: string, options: SearchOptions): Promise<SmartSearchResult[]> {
    const {
      maxResults = 20,
      storeFilter,
      categoryFilter,
      minPrice,
      maxPrice
    } = options;

    try {
      let supabaseQuery = supabase
        .from('products')
        .select('id, name, brand, category, store, price, image_url, description')
        .not('price', 'is', null);

      // Add text search
      supabaseQuery = supabaseQuery.textSearch('name', query, { type: 'websearch' });

      // Add filters
      if (storeFilter) {
        supabaseQuery = supabaseQuery.eq('store', storeFilter);
      }
      if (categoryFilter) {
        supabaseQuery = supabaseQuery.eq('category', categoryFilter);
      }
      if (minPrice) {
        supabaseQuery = supabaseQuery.gte('price', minPrice);
      }
      if (maxPrice) {
        supabaseQuery = supabaseQuery.lte('price', maxPrice);
      }

      // Order and limit
      supabaseQuery = supabaseQuery
        .order('price', { ascending: true })
        .limit(maxResults);

      const { data, error } = await supabaseQuery;

      if (error) {
        console.error('Fallback search error:', error);
        return [];
      }

      const results: SmartSearchResult[] = (data || []).map(item => ({
        id: item.id,
        name: item.name,
        brand: item.brand || '',
        category: item.category || '',
        store: item.store,
        price: item.price,
        image_url: item.image_url,
        relevance_score: this.calculateRelevanceScore(item.name, query),
        match_type: 'exact' as const
      }));

      this.setCachedData(`search:${query}:${JSON.stringify(options)}`, results);
      return results;

    } catch (error) {
      console.error('Fallback search failed:', error);
      return [];
    }
  }

  /**
   * Calculate relevance score for fallback search
   */
  private calculateRelevanceScore(name: string, query: string): number {
    const lowerName = name.toLowerCase();
    const lowerQuery = query.toLowerCase();
    
    if (lowerName === lowerQuery) return 1.0;
    if (lowerName.startsWith(lowerQuery)) return 0.9;
    if (lowerName.includes(lowerQuery)) return 0.8;
    return 0.5;
  }

  /**
   * Get autocomplete suggestions
   */
  async getSuggestions(partialQuery: string, maxSuggestions: number = 10): Promise<SearchSuggestion[]> {
    if (!partialQuery.trim()) {
      return await this.getPopularSuggestions(maxSuggestions);
    }

    const cacheKey = `suggestions:${partialQuery}:${maxSuggestions}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      // Try the enhanced autocomplete function first
      const { data, error } = await supabase.rpc('get_autocomplete_suggestions', {
        partial_query: partialQuery.trim(),
        max_suggestions: maxSuggestions
      });

      if (!error && data) {
        const suggestions: SearchSuggestion[] = (data || []).map(item => ({
          query: item.suggestion,
          category: item.category,
          popularity: item.popularity,
          type: this.determineType(item.category)
        }));

        this.setCachedData(cacheKey, suggestions);
        return suggestions;
      }

      // Fallback to basic suggestions
      console.log('Using fallback suggestions method');
      return await this.fallbackSuggestions(partialQuery, maxSuggestions);

    } catch (error) {
      console.error('Suggestions service error, using fallback:', error);
      return await this.fallbackSuggestions(partialQuery, maxSuggestions);
    }
  }

  /**
   * Fallback suggestions method using basic Supabase queries
   */
  private async fallbackSuggestions(partialQuery: string, maxSuggestions: number): Promise<SearchSuggestion[]> {
    try {
      const { data, error } = await supabase
        .from('products')
        .select('name, category')
        .ilike('name', `${partialQuery}%`)
        .not('price', 'is', null)
        .limit(maxSuggestions * 2); // Get more to deduplicate

      if (error) {
        console.error('Fallback suggestions error:', error);
        return [];
      }

      // Deduplicate and format
      const uniqueNames = new Map<string, { name: string; category: string }>();
      (data || []).forEach(item => {
        if (!uniqueNames.has(item.name)) {
          uniqueNames.set(item.name, { name: item.name, category: item.category || '' });
        }
      });

      const suggestions: SearchSuggestion[] = Array.from(uniqueNames.values())
        .slice(0, maxSuggestions)
        .map(item => ({
          query: item.name,
          category: item.category,
          popularity: 0,
          type: 'product' as const
        }));

      this.setCachedData(`suggestions:${partialQuery}:${maxSuggestions}`, suggestions);
      return suggestions;

    } catch (error) {
      console.error('Fallback suggestions failed:', error);
      return [];
    }
  }

  /**
   * Get popular search terms
   */
  async getPopularTerms(maxTerms: number = 10): Promise<PopularTerm[]> {
    const cacheKey = `popular:${maxTerms}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const { data, error } = await supabase
        .from('search_analytics')
        .select('query, COUNT(*) as search_count')
        .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString())
        .order('search_count', { ascending: false })
        .limit(maxTerms);

      if (error) {
        console.error('Popular terms error:', error);
        return [];
      }

      const terms: PopularTerm[] = (data || []).map(item => ({
        query: item.query,
        search_count: item.search_count as number
      }));

      this.setCachedData(cacheKey, terms);
      return terms;

    } catch (error) {
      console.error('Popular terms service error:', error);
      return [];
    }
  }

  /**
   * Get spelling corrections for a query
   */
  async getSpellingCorrections(query: string): Promise<string[]> {
    if (!query.trim()) {
      return [];
    }

    try {
      // Use trigram similarity to find similar product names
      const { data, error } = await supabase
        .from('products')
        .select('name')
        .textSearch('name', query, { type: 'websearch' })
        .limit(5);

      if (error) {
        console.error('Spelling corrections error:', error);
        return [];
      }

      // Extract unique corrected terms
      const corrections = new Set<string>();
      (data || []).forEach(item => {
        if (item.name && item.name.toLowerCase() !== query.toLowerCase()) {
          corrections.add(item.name);
        }
      });

      return Array.from(corrections).slice(0, 3);

    } catch (error) {
      console.error('Spelling corrections service error:', error);
      return [];
    }
  }

  /**
   * Record search analytics
   */
  async recordSearchAnalytics(
    query: string,
    resultsCount: number,
    selectedProductId?: string,
    searchType: 'text' | 'fuzzy' | 'alias' | 'category' = 'text',
    responseTimeMs?: number
  ): Promise<void> {
    try {
      const { error } = await supabase.rpc('record_search_analytics', {
        search_query: query,
        user_id: null, // Could be added later with auth
        results_count: resultsCount,
        selected_product_id: selectedProductId || null,
        search_type: searchType,
        response_time_ms: responseTimeMs || null
      });

      if (error) {
        console.error('Analytics recording error:', error);
      }
    } catch (error) {
      console.error('Analytics service error:', error);
    }
  }

  /**
   * Get popular products by category
   */
  async getPopularProductsByCategory(category: string, maxResults: number = 20): Promise<SmartSearchResult[]> {
    const cacheKey = `popular_category:${category}:${maxResults}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const { data, error } = await supabase.rpc('get_popular_products_by_category', {
        target_category: category,
        max_results: maxResults
      });

      if (error) {
        console.error('Popular products error:', error);
        return [];
      }

      const results: SmartSearchResult[] = (data || []).map(item => ({
        id: item.id,
        name: item.name,
        brand: item.brand,
        category: category,
        store: '', // Not returned by this function
        price: item.price,
        image_url: item.image_url,
        relevance_score: 0.8, // Default relevance score
        match_type: 'exact' as const
      }));

      this.setCachedData(cacheKey, results);
      return results;

    } catch (error) {
      console.error('Popular products service error:', error);
      return [];
    }
  }

  /**
   * Clear search cache
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * Get cached data if still valid
   */
  private getCachedData(key: string): any | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.data;
    }
    this.cache.delete(key);
    return null;
  }

  /**
   * Set cached data
   */
  private setCachedData(key: string, data: any): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  /**
   * Get popular suggestions for empty queries
   */
  private async getPopularSuggestions(maxSuggestions: number): Promise<SearchSuggestion[]> {
    try {
      const { data, error } = await supabase
        .from('products')
        .select('name, category')
        .not('price', 'is', null)
        .order('name')
        .limit(maxSuggestions);

      if (error) {
        console.error('Popular suggestions error:', error);
        return [];
      }

      return (data || []).map(item => ({
        query: item.name,
        category: item.category || '',
        popularity: 0,
        type: 'product' as const
      }));

    } catch (error) {
      console.error('Popular suggestions service error:', error);
      return [];
    }
  }

  /**
   * Determine suggestion type based on category
   */
  private determineType(category: string): 'product' | 'brand' | 'alias' | 'category' {
    if (category === 'Brand') {
      return 'brand';
    }
    if (category === 'Category') {
      return 'category';
    }
    return 'product';
  }
}

export const smartSearchService = new SmartSearchService();
export { SmartSearchService };