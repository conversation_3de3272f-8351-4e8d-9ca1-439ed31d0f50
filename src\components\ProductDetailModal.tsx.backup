import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  Image,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';

interface Product {
  id: string;
  name: string;
  price: number;
  current_price?: number;
  original_price?: number;
  store: string;
  category?: string;
  brand?: string;
  brand_name?: string;
  unit?: string;
  size?: string;
  availability?: string;
  image_url?: string;
  is_available?: boolean;
  is_on_sale?: boolean;
  promotion_text?: string;
  last_updated?: string;
}

import { IUnifiedProduct } from '../services/productDeduplicationService';

interface SizeVariant {
  size: string;
  unit: string;
  products: IUnifiedProduct[];
  lowestPrice: number;
  highestPrice: number;
  bestProduct: IUnifiedProduct;
  stores: string[];
}

interface SelectedVariant {
  sizeVariant: SizeVariant;
  selectedProduct: IUnifiedProduct;
}

interface ProductDetailModalProps {
  visible: boolean;
  productGroup: IUnifiedProduct | null;
  onClose: () => void;
  onAddToList: (productGroup: IUnifiedProduct, selectedVariant?: SelectedVariant) => void;
}

const STORE_CONFIG = {
  woolworths: {
    name: 'Woolworths',
    color: '#00A651',
    logo: 'W',
    backgroundColor: '#00A651',
    textColor: '#FFFFFF',
  },
  newworld: {
    name: 'New World',
    color: '#E31E24',
    logo: 'NW',
    backgroundColor: '#E31E24',
    textColor: '#FFFFFF',
  },
  paknsave: {
    name: "Pak'nSave",
    color: '#FFD100',
    logo: 'PS',
    backgroundColor: '#FFD100',
    textColor: '#000000',
  },
};

export const ProductDetailModal: React.FC<ProductDetailModalProps> = ({
  visible,
  productGroup,
  onClose,
  onAddToList,
}) => {
  const { colors } = useTheme();
  const [selectedVariant, setSelectedVariant] = useState<SelectedVariant | null>(null);

  // Group products by size variants - always call useMemo
  const sizeVariants = useMemo(() => {
    if (!productGroup) return [];

    const variants = new Map<string, IUnifiedProduct[]>();

    // This logic needs to be adapted since we don't have a products array anymore
    // For now, we'll just create a single variant

    const sizeKey = `${productGroup.size || 'Standard'}_${productGroup.unit || 'each'}`;
    variants.set(sizeKey, [productGroup]);

    return Array.from(variants.entries()).map(([sizeKey, products]) => {
      const [size, unit] = sizeKey.split('_');
      const bestProduct = products[0];
      const prices = Object.values(products[0].storePrices);
      const stores = products[0].allStores;

      return {
        size: size === 'Standard' ? '' : size,
        unit: unit === 'each' ? '' : unit,
        products: products,
        lowestPrice: Math.min(...prices),
        highestPrice: Math.max(...prices),
        bestProduct,
        stores,
      } as any;
    }).sort((a, b) => {
      // Sort by size numerically if possible
      const aNum = parseFloat(a.size);
      const bNum = parseFloat(b.size);
      if (!isNaN(aNum) && !isNaN(bNum)) {
        return aNum - bNum;
      }
      return a.size.localeCompare(b.size);
    });
  }, [productGroup]);

  // Set default selected variant - always call useEffect
  React.useEffect(() => {
    if (sizeVariants.length > 0 && !selectedVariant) {
      const defaultVariant = sizeVariants[0];
      setSelectedVariant({
        sizeVariant: defaultVariant,
        selectedProduct: defaultVariant.bestProduct,
      });
    }
  }, [sizeVariants, selectedVariant]);

  // Early return after all hooks are called
  if (!productGroup) return null;

  const renderSizeVariantSelector = () => {
    if (sizeVariants.length <= 1) return null;

    return (
      <View style={styles.sizeVariantSection}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Available Sizes
        </Text>

        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.sizeVariantContainer}
          contentContainerStyle={styles.sizeVariantContent}
        >
          {sizeVariants.map((variant, index) => {
            const isSelected = selectedVariant?.sizeVariant === variant;
            const displaySize = variant.size && variant.unit
              ? `${variant.size}${variant.unit}`
              : variant.size || variant.unit || 'Standard';

            return (
              <TouchableOpacity
                key={index}
                style={[
                  styles.sizeVariantButton,
                  {
                    backgroundColor: isSelected ? colors.primary : colors.surface,
                    borderColor: isSelected ? colors.primary : colors.border,
                  }
                ]}
                onPress={() => setSelectedVariant({
                  sizeVariant: variant,
                  selectedProduct: variant.bestProduct,
                })}
              >
                <Text style={[
                  styles.sizeVariantText,
                  { color: isSelected ? 'white' : colors.text }
                ]}>
                  {displaySize}
                </Text>
                <Text style={[
                  styles.sizeVariantPrice,
                  { color: isSelected ? 'rgba(255,255,255,0.9)' : colors.primary }
                ]}>
                  ${variant.lowestPrice.toFixed(2)}
                </Text>
                {variant.lowestPrice !== variant.highestPrice && (
                  <Text style={[
                    styles.sizeVariantSavings,
                    { color: isSelected ? 'rgba(255,255,255,0.7)' : colors.textSecondary }
                  ]}>
                    Save ${(variant.highestPrice - variant.lowestPrice).toFixed(2)}
                  </Text>
                )}
              </TouchableOpacity>
            );
          })}
        </ScrollView>
      </View>
    );
  };

  const renderStoreComparison = () => {
    const sectionTitle = 'Price Comparison';

    return (
      <View style={styles.storeComparison}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          {sectionTitle}
        </Text>

        {Object.entries(productGroup.storePrices).map(([store, storePrice], index) => {
          const isLowest = index === 0;
          const price = storePrice;
          const originalPrice = productGroup.originalPrice;
          const isOnSale = originalPrice && price < originalPrice;
          
          return (
            <View
              key={store}
              style={[
                styles.storeItem,
                {
                  backgroundColor: colors.background,
                  borderColor: isLowest ? colors.primary : colors.border,
                  borderWidth: isLowest ? 2 : 1,
                }
              ]}
            >
              <View style={styles.storeHeader}>
                <View style={[
                  styles.storeIndicator,
                  { backgroundColor: STORE_CONFIG[store as keyof typeof STORE_CONFIG]?.backgroundColor || '#666' }
                ]}>
                  <Text style={[
                    styles.storeIndicatorText,
                    { color: STORE_CONFIG[store as keyof typeof STORE_CONFIG]?.textColor || '#FFF' }
                  ]}>
                    {STORE_CONFIG[store as keyof typeof STORE_CONFIG]?.logo || store.charAt(0).toUpperCase()}
                  </Text>
                </View>
                <Text style={[styles.storeName, { color: colors.text }]}>
                  {STORE_CONFIG[store as keyof typeof STORE_CONFIG]?.name || store}
                </Text>
                {isLowest && (
                  <View style={[styles.bestBadge, { backgroundColor: colors.primary }]}>
                    <Text style={styles.bestBadgeText}>BEST PRICE</Text>
                  </View>
                )}
              </View>
              
              <View style={styles.priceContainer}>
                <Text style={[styles.currentPrice, { color: colors.text }]}>
                  ${price.toFixed(2)}
                </Text>
              </View>
              
              <View style={styles.productMeta}>
                {productGroup.unit && (
                  <Text style={[styles.metaText, { color: colors.textSecondary }]}>
                    {productGroup.unit}
                  </Text>
                )}
                {productGroup.size && (
                  <Text style={[styles.metaText, { color: colors.textSecondary }]}>
                    {productGroup.size}
                  </Text>
                )}
                {productGroup.brand && (
                  <Text style={[styles.metaText, { color: colors.textSecondary }]}>
                    {productGroup.brand}
                  </Text>
                )}
              </View>
              
              {productGroup.promotionText && (
                <View style={[styles.promotionBadge, { backgroundColor: colors.primary }]}>
                  <Text style={styles.promotionText}>
                    {productGroup.promotionText}
                  </Text>
                </View>
              )}
            </View>
          );
        })}
      </View>
    );
  };

  const renderSavings = () => {
    const lowestPrice = productGroup.lowestPrice;
    const highestPrice = productGroup.highestPrice;

    if (lowestPrice === highestPrice) return null;

    const savings = highestPrice - lowestPrice;
    const savingsPercent = ((savings / highestPrice) * 100).toFixed(0);
    
    return (
      <View style={[styles.savingsContainer, { backgroundColor: colors.primary }]}>
        <Ionicons name="trending-down" size={20} color="white" />
        <Text style={styles.savingsText}>
          Save ${savings.toFixed(2)} ({savingsPercent}%) by choosing the best price
        </Text>
      </View>
    );
  };

  const getStoreDisplayInfo = (store: string) => {
    return STORE_CONFIG[store as keyof typeof STORE_CONFIG] || {
      name: store,
      color: '#666',
      logo: store.charAt(0).toUpperCase(),
      backgroundColor: '#666',
      textColor: '#FFFFFF'
    };
  };

  const renderProductInfo = () => {
    const firstProduct = productGroup.products[0];
    const imageUrl = productGroup.products.find(p => p.image_url)?.image_url;
    
    return (
      <View style={styles.productInfo}>
        <View style={styles.productHeader}>
          {imageUrl ? (
            <Image
              source={{ uri: imageUrl }}
              style={styles.productImage}
              resizeMode="contain"
            />
          ) : (
            <View style={[styles.productImagePlaceholder, { backgroundColor: colors.border }]}>
              <Ionicons name="image" size={48} color={colors.textSecondary} />
            </View>
          )}
          
          <View style={styles.productDetails}>
            <Text style={[styles.productName, { color: colors.text }]}>
              {productGroup.name}
            </Text>
            
            {productGroup.category && (
              <Text style={[styles.productCategory, { color: colors.textSecondary }]}>
                {productGroup.category}
              </Text>
            )}
            
            <View style={styles.storeAvailability}>
              <Text style={[styles.availabilityText, { color: colors.textSecondary }]}>
                Available at {productGroup.allStores.length} store{productGroup.allStores.length > 1 ? 's' : ''}
              </Text>
              <View style={styles.storeIndicators}>
                {productGroup.allStores.map(store => (
                  <View
                    key={store}
                    style={[
                      styles.miniStoreIndicator,
                      { backgroundColor: STORE_CONFIG[store as keyof typeof STORE_CONFIG]?.backgroundColor || '#666' }
                    ]}
                  >
                    <Text style={[
                      styles.miniStoreIndicatorText,
                      { color: STORE_CONFIG[store as keyof typeof STORE_CONFIG]?.textColor || '#FFF' }
                    ]}>
                      {STORE_CONFIG[store as keyof typeof STORE_CONFIG]?.logo || store.charAt(0).toUpperCase()}
                    </Text>
                  </View>
                ))}
              </View>
            </View>
          </View>
        </View>
        
        {renderSavings()}
      </View>
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={[styles.header, { backgroundColor: colors.surface }]}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            Product Details
          </Text>
          <View style={styles.headerSpacer} />
        </View>
        
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {renderProductInfo()}
          {renderSizeVariantSelector()}
          {renderStoreComparison()}
        </ScrollView>
        
        <View style={[styles.footer, { backgroundColor: colors.surface }]}>
          <TouchableOpacity
            style={[styles.addToListButton, { backgroundColor: colors.primary }]}
            onPress={() => {
              onAddToList(productGroup, selectedVariant || undefined);
              onClose();
            }}
          >
            <Ionicons name="add-circle" size={24} color="white" />
            <Text style={styles.addToListText}>Add to Shopping List</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  closeButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
    textAlign: 'center',
  },
  headerSpacer: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  productInfo: {
    marginBottom: 24,
  },
  productHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  productImage: {
    width: 100,
    height: 100,
    borderRadius: 12,
    marginRight: 16,
  },
  productImagePlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 12,
    marginRight: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  productDetails: {
    flex: 1,
  },
  productName: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
    lineHeight: 24,
  },
  productCategory: {
    fontSize: 14,
    marginBottom: 8,
  },
  storeAvailability: {
    marginTop: 8,
  },
  availabilityText: {
    fontSize: 14,
    marginBottom: 4,
  },
  storeIndicators: {
    flexDirection: 'row',
    gap: 6,
  },
  miniStoreIndicator: {
    width: 16,
    height: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 4,
  },
  miniStoreIndicatorText: {
    fontSize: 8,
    fontWeight: 'bold',
  },
  savingsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    gap: 8,
  },
  savingsText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    flex: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  storeComparison: {
    gap: 12,
  },
  storeItem: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  storeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  storeIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    marginRight: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  storeIndicatorText: {
    fontSize: 10,
    fontWeight: 'bold',
  },
  storeName: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  bestBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  bestBadgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  currentPrice: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  originalPrice: {
    fontSize: 16,
    textDecorationLine: 'line-through',
  },
  productMeta: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  metaText: {
    fontSize: 14,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  promotionBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    marginTop: 8,
  },
  promotionText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  addToListButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    gap: 8,
  },
  addToListText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },

  sizeVariantSection: {
    marginBottom: 24,
  },
  sizeVariantContainer: {
    maxHeight: 120,
  },
  sizeVariantContent: {
    paddingRight: 16,
    gap: 12,
  },
  sizeVariantButton: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 2,
    minWidth: 100,
    alignItems: 'center',
    justifyContent: 'center',
  },
  sizeVariantText: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  sizeVariantPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  sizeVariantSavings: {
    fontSize: 12,
    fontWeight: '500',
  },
});