import React, { createContext, useState, useEffect, useContext } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface Product {
  id: string;
  name: string;
  price: number;
  current_price?: number;
  original_price?: number;
  store: string;
  category?: string;
  brand?: string;
  brand_name?: string;
  unit?: string;
  size?: string;
  availability?: string;
  image_url?: string;
  is_available?: boolean;
  is_on_sale?: boolean;
  promotion_text?: string;
  last_updated?: string;
}

export interface ShoppingList {
  id: string;
  name: string;
  description?: string;
  items: Product[];
  createdAt: string;
  updatedAt: string;
  isDefault: boolean;
  color: string;
  icon: string;
  totalItems: number;
  totalPrice: number;
  completedItems: number;
}

interface ShoppingContextType {
  shoppingLists: ShoppingList[];
  createList: (name: string, description?: string, color?: string, icon?: string) => Promise<ShoppingList>;
  addProductToList: (listId: string, product: Product) => Promise<void>;
  removeItemFromList: (listId: string, itemId: string) => void;
  deleteList: (listId: string) => void;
}

const ShoppingContext = createContext<ShoppingContextType | undefined>(undefined);

export const ShoppingProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [shoppingLists, setShoppingLists] = useState<ShoppingList[]>([]);

  useEffect(() => {
    loadLists();
  }, []);

  const loadLists = async () => {
    try {
      const stored = await AsyncStorage.getItem('shoppingLists');
      if (stored) {
        const parsedLists = JSON.parse(stored);
        // Migrate old shopping lists to new format
        const migratedLists = parsedLists.map((list: any) => ({
          id: list.id,
          name: list.name,
          description: list.description || '',
          items: list.items || [],
          createdAt: list.createdAt || new Date().toISOString(),
          updatedAt: list.updatedAt || new Date().toISOString(),
          isDefault: list.isDefault || false,
          color: list.color || '#3B82F6',
          icon: list.icon || 'cart',
          totalItems: list.items ? list.items.length : 0,
          totalPrice: (list.items && Array.isArray(list.items)) ? list.items.reduce((sum: number, item: any) => {
            const price = item.current_price || item.price || 0;
            return sum + (isNaN(price) ? 0 : price);
          }, 0) : 0,
          completedItems: 0, // Default to 0 for existing lists
        }));
        setShoppingLists(migratedLists);
      } else {
        // Initialize with a default list if none exist
        const defaultList = {
          id: 'default',
          name: 'Default List',
          description: 'Your default shopping list',
          items: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          isDefault: true,
          color: '#3B82F6',
          icon: 'cart',
          totalItems: 0,
          totalPrice: 0,
          completedItems: 0,
        };
        saveLists([defaultList]);
      }
    } catch (error) {
      console.error('Failed to load shopping lists:', error);
    }
  };

  const saveLists = async (lists: ShoppingList[]) => {
    try {
      await AsyncStorage.setItem('shoppingLists', JSON.stringify(lists));
      setShoppingLists(lists);
    } catch (error) {
      console.error('Failed to save shopping lists:', error);
    }
  };

  const createList = async (name: string, description?: string, color?: string, icon?: string): Promise<ShoppingList> => {
    const newList: ShoppingList = {
      id: Date.now().toString(),
      name,
      description: description || '',
      items: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isDefault: false,
      color: color || '#3B82F6',
      icon: icon || 'cart',
      totalItems: 0,
      totalPrice: 0,
      completedItems: 0,
    };
    await saveLists([...shoppingLists, newList]);
    return newList;
  };

  const addProductToList = async (listId: string, product: Product): Promise<void> => {
    const updated = shoppingLists.map(list => {
      if (list.id === listId) {
        // Check for duplicates
        const existingItems = list.items || [];
        const exists = existingItems.some(item => item.id === product.id);
        if (!exists) {
          const newItems = [...existingItems, product];
          const totalPrice = newItems.reduce((sum, item) => {
            const price = item.current_price || item.price || 0;
            return sum + (isNaN(price) ? 0 : price);
          }, 0);
          return { 
            ...list, 
            items: newItems,
            totalItems: newItems.length,
            totalPrice,
            updatedAt: new Date().toISOString(),
          };
        }
      }
      return list;
    });
    await saveLists(updated);
  };

  const removeItemFromList = (listId: string, itemId: string) => {
    const updated = shoppingLists.map(list => {
      if (list.id === listId) {
        const existingItems = list.items || [];
        const newItems = existingItems.filter(item => item.id !== itemId);
        const totalPrice = newItems.reduce((sum, item) => {
          const price = item.current_price || item.price || 0;
          return sum + (isNaN(price) ? 0 : price);
        }, 0);
        return { 
          ...list, 
          items: newItems,
          totalItems: newItems.length,
          totalPrice,
          updatedAt: new Date().toISOString(),
        };
      }
      return list;
    });
    saveLists(updated);
  };

  const deleteList = (listId: string) => {
    const updated = shoppingLists.filter(list => list.id !== listId);
    saveLists(updated);
  };

  return (
    <ShoppingContext.Provider value={{ shoppingLists, createList, addProductToList, removeItemFromList, deleteList }}>
      {children}
    </ShoppingContext.Provider>
  );
};

export const useShopping = () => {
  const context = useContext(ShoppingContext);
  if (undefined === context) {
    throw new Error('useShopping must be used within a ShoppingProvider');
  }
  return context;
};
