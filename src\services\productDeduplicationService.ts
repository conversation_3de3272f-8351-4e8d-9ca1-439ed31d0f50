
import { IProduct } from '../types/shoppingList';

// Export Product type for tests (alias to IProduct)
export type Product = IProduct;

// Define a unified product structure that includes prices from multiple stores
export interface IUnifiedProduct extends IProduct {
  storePrices: { [store: string]: number };
  allStores: string[];
  lowestPrice: number;
  highestPrice: number;
  // Add other properties that are expected by UI components
  // such as name, brand, category, unit, size, etc.
  // These should ideally come from the 'best' product in the group
}

// Define ProductGroup interface for compatibility
export interface ProductGroup {
  products: IProduct[];
  stores: string[];
  name: string;
  brand?: string;
  category?: string;
  bestPrice?: number;
  bestImage?: string;
  totalVariants?: number;
  availableCount?: number;
}

export class ProductDeduplicationService {
  // Main function to deduplicate a list of products
  public groupProducts(products: IProduct[]): IUnifiedProduct[] {
    if (!products || products.length === 0) {
      return [];
    }

    const productMap = new Map<string, IUnifiedProduct>();

    products.forEach(product => {
      const normalizedName = this.normalizeName(product.name);
      let matched = false;

      for (const [key, unifiedProduct] of productMap.entries()) {
        if (this.isMatch(normalizedName, this.normalizeName(unifiedProduct.name))) {
          // Merge the new product into the existing unified product
          unifiedProduct.storePrices[product.store] = product.price;
          if (!unifiedProduct.allStores.includes(product.store)) {
            unifiedProduct.allStores.push(product.store);
          }
          productMap.set(key, unifiedProduct);
          matched = true;
          break;
        }
      }

      if (!matched) {
        // Create a new unified product
        const newUnifiedProduct: IUnifiedProduct = {
          ...product,
          storePrices: { [product.store]: product.price },
          allStores: [product.store],
          lowestPrice: product.price,
          highestPrice: product.price,
          imageUrl: product.imageUrl,
          // Copy other relevant properties from the first product
          name: product.name,
          brand: product.brand,
          category: product.category,
          unit: product.unit,
          size: product.size,
        };
        productMap.set(normalizedName, newUnifiedProduct);
      }
    });

    // After initial grouping, calculate lowestPrice and highestPrice for each unified product
    productMap.forEach(unifiedProduct => {
      const prices = Object.values(unifiedProduct.storePrices);
      unifiedProduct.lowestPrice = Math.min(...prices);
      unifiedProduct.highestPrice = Math.max(...prices);
    });

    return Array.from(productMap.values());
  }

  // Normalize product names for better matching
  private normalizeName(name: string): string {
    return name.toLowerCase().replace(/\s+/g, ' ').trim();
  }

  // Check if two normalized product names are a match
  private isMatch(name1: string, name2: string): boolean {
    // Basic matching logic (can be improved with more sophisticated algorithms)
    return name1 === name2;
  }
}

export const productDeduplicationService = new ProductDeduplicationService();
