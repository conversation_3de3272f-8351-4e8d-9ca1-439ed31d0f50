/**
 * Scraped Data Sync Utility
 * 
 * Synchronizes scraped data from the scraper system
 * into the app's price comparison system
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { scrapedDataService } from '../services/scrapedDataService';

// In a real app, these would be file system operations
// For React Native, we simulate the data sync process

export interface SyncResult {
  success: boolean;
  updatedProducts: number;
  priceChanges: number;
  dataSource: string;
  timestamp: string;
  error?: string;
}

class ScrapedDataSync {
  private lastSyncTime: string | null = null;
  private syncInProgress = false;

  constructor() {
    this.loadLastSyncTime();
  }

  private async loadLastSyncTime() {
    try {
      this.lastSyncTime = await AsyncStorage.getItem('last_sync_time');
    } catch (error) {
      console.error('Failed to load last sync time:', error);
    }
  }

  /**
   * Check if new scraped data is available
   */
  async isNewDataAvailable(): Promise<boolean> {
    // In production, this would check file modification times
    // For demo, we simulate checking every 5 minutes
    if (!this.lastSyncTime) return true;
    
    const lastSync = new Date(this.lastSyncTime).getTime();
    const now = Date.now();
    const timeDiff = now - lastSync;
    
    // New data available every 5 minutes for demo
    return timeDiff > 5 * 60 * 1000;
  }

  /**
   * Sync scraped data into the app
   */
  async syncData(): Promise<SyncResult> {
    if (this.syncInProgress) {
      return {
        success: false,
        updatedProducts: 0,
        priceChanges: 0,
        dataSource: 'none',
        timestamp: new Date().toISOString(),
        error: 'Sync already in progress'
      };
    }

    this.syncInProgress = true;

    try {
      console.log('🔄 Starting data sync...');
      
      // Force refresh the scraped data service
      await scrapedDataService.forceRefresh();
      
      // Get recent price changes for reporting
      const recentChanges = await scrapedDataService.getRecentPriceChanges();
      
      // Update sync time
      this.lastSyncTime = new Date().toISOString();
      await AsyncStorage.setItem('last_sync_time', this.lastSyncTime);
      
      console.log(`✅ Sync completed: ${recentChanges.length} price changes detected`);
      
      return {
        success: true,
        updatedProducts: 30, // Simulated - would be actual count from files
        priceChanges: recentChanges.length,
        dataSource: 'scraper',
        timestamp: this.lastSyncTime
      };

    } catch (error) {
      console.error('❌ Sync failed:', error);
      
      return {
        success: false,
        updatedProducts: 0,
        priceChanges: 0,
        dataSource: 'none',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * Auto-sync data at regular intervals
   */
  startAutoSync(intervalMinutes: number = 30) {
    setInterval(async () => {
      try {
        const hasNewData = await this.isNewDataAvailable();
        if (hasNewData) {
          console.log('🔄 Auto-sync triggered');
          await this.syncData();
        }
      } catch (error) {
        console.error('Auto-sync error:', error);
      }
    }, intervalMinutes * 60 * 1000);

    console.log(`🔄 Auto-sync started (every ${intervalMinutes} minutes)`);
  }

  /**
   * Get sync status information
   */
  getSyncStatus() {
    return {
      lastSyncTime: this.lastSyncTime,
      syncInProgress: this.syncInProgress,
      isStale: this.lastSyncTime ? 
        (Date.now() - new Date(this.lastSyncTime).getTime()) > 24 * 60 * 60 * 1000 : true
    };
  }

  /**
   * Manual trigger for data refresh
   */
  async refreshNow(): Promise<SyncResult> {
    console.log('🔄 Manual refresh triggered');
    return this.syncData();
  }
}

export const scrapedDataSync = new ScrapedDataSync();

// Auto-start sync when the module loads
scrapedDataSync.startAutoSync(30); // Sync every 30 minutes 