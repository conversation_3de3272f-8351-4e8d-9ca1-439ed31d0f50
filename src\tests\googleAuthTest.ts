// Test Google OAuth Integration
import { GoogleAuthService } from '../services/googleAuthService';

export const testGoogleAuth = async () => {
  console.log('🧪 Testing Google OAuth Integration...');
  
  try {
    // Test mock Google sign in
    console.log('Testing mock Google sign in...');
    const mockResult = await GoogleAuthService.mockGoogleSignIn();
    
    console.log('Mock Result:', {
      success: mockResult.success,
      user: mockResult.user ? {
        id: mockResult.user.id,
        email: mockResult.user.email,
        name: mockResult.user.name,
        verified: mockResult.user.verified_email
      } : null,
      hasAccessToken: !!mockResult.accessToken,
      error: mockResult.error
    });
    
    if (mockResult.success) {
      console.log('✅ Mock Google login test passed');
    } else {
      console.log('❌ Mock Google login test failed');
    }
    
    // Test sign out
    console.log('Testing Google sign out...');
    await GoogleAuthService.signOut();
    console.log('✅ Google sign out test passed');
    
    return {
      mockLogin: mockResult.success,
      signOut: true
    };
    
  } catch (error) {
    console.error('❌ Google OAuth test failed:', error);
    return {
      mockLogin: false,
      signOut: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

// Test the complete auth flow
export const testCompleteAuthFlow = async () => {
  console.log('🧪 Testing complete auth flow...');
  
  try {
    // This would normally be called from the AuthContext
    const result = await GoogleAuthService.mockGoogleSignIn();
    
    if (result.success && result.user) {
      console.log('✅ Complete auth flow test passed');
      console.log('User data:', {
        id: result.user.id,
        email: result.user.email,
        name: result.user.name,
        picture: result.user.picture,
        verified: result.user.verified_email
      });
      
      return true;
    } else {
      console.log('❌ Complete auth flow test failed');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Complete auth flow test failed:', error);
    return false;
  }
};