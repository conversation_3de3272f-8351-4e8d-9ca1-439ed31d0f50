import React, { useEffect, useRef } from 'react';
import {
  View,
  Animated,
  StyleSheet,
  ViewStyle,
} from 'react-native';
import { useTheme } from '../context/ThemeContext';

interface SkeletonLoaderProps {
  style?: ViewStyle | ViewStyle[];
  children?: React.ReactNode;
  shimmerColors?: string[];
  duration?: number;
}

const SkeletonLoader: React.FC<SkeletonLoaderProps> = ({
  style,
  children,
  shimmerColors,
  duration = 1500,
}) => {
  const { colors } = useTheme();
  const shimmerAnimation = useRef(new Animated.Value(0)).current;

  const defaultShimmerColors = shimmerColors || [
    colors.border,
    colors.surface,
    colors.border,
  ];

  useEffect(() => {
    const startShimmer = () => {
      shimmerAnimation.setValue(0);
      Animated.loop(
        Animated.timing(shimmerAnimation, {
          toValue: 1,
          duration,
          useNativeDriver: false,
        })
      ).start();
    };

    startShimmer();
  }, [shimmerAnimation, duration]);

  const shimmerStyle = {
    backgroundColor: shimmerAnimation.interpolate({
      inputRange: [0, 0.5, 1],
      outputRange: defaultShimmerColors,
    }),
  };

  if (children) {
    return (
      <View style={style}>
        <Animated.View style={[StyleSheet.absoluteFill, shimmerStyle]} />
        {children}
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      <Animated.View style={[styles.shimmer, shimmerStyle]} />
      
      {/* Default skeleton content */}
      <View style={styles.content}>
        <Animated.View style={[styles.imagePlaceholder, shimmerStyle]} />
        <View style={styles.textContainer}>
          <Animated.View style={[styles.titlePlaceholder, shimmerStyle]} />
          <Animated.View style={[styles.subtitlePlaceholder, shimmerStyle]} />
          <View style={styles.priceContainer}>
            <Animated.View style={[styles.pricePlaceholder, shimmerStyle]} />
            <Animated.View style={[styles.badgePlaceholder, shimmerStyle]} />
          </View>
        </View>
      </View>
    </View>
  );
};

const ProductCardSkeleton: React.FC<{ style?: ViewStyle | ViewStyle[] }> = ({ style }) => {
  const { colors } = useTheme();
  
  return (
    <SkeletonLoader style={[styles.productCard, { backgroundColor: colors.surface }, style].filter(Boolean) as ViewStyle[]}>
      <View style={styles.productContent}>
        <View style={[styles.productImage, { backgroundColor: colors.border }]} />
        <View style={styles.productInfo}>
          <View style={[styles.productTitle, { backgroundColor: colors.border }]} />
          <View style={[styles.productPrice, { backgroundColor: colors.border }]} />
          <View style={styles.storeIndicators}>
            <View style={[styles.storeIndicator, { backgroundColor: colors.border }]} />
            <View style={[styles.storeIndicator, { backgroundColor: colors.border }]} />
          </View>
        </View>
        <View style={[styles.addButton, { backgroundColor: colors.border }]} />
      </View>
    </SkeletonLoader>
  );
};

const ListSkeleton: React.FC<{ count?: number; style?: ViewStyle | ViewStyle[] }> = ({ 
  count = 6, 
  style 
}) => {
  return (
    <View style={style}>
      {Array.from({ length: count }, (_, index) => (
        <ProductCardSkeleton key={index} style={styles.skeletonItem} />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
    borderRadius: 8,
  },
  shimmer: {
    ...StyleSheet.absoluteFillObject,
  },
  content: {
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  imagePlaceholder: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 16,
  },
  textContainer: {
    flex: 1,
  },
  titlePlaceholder: {
    height: 16,
    borderRadius: 4,
    marginBottom: 8,
    width: '80%',
  },
  subtitlePlaceholder: {
    height: 12,
    borderRadius: 4,
    marginBottom: 8,
    width: '60%',
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  pricePlaceholder: {
    height: 14,
    width: 60,
    borderRadius: 4,
  },
  badgePlaceholder: {
    height: 20,
    width: 20,
    borderRadius: 10,
  },
  
  // Product card skeleton styles
  productCard: {
    borderRadius: 12,
    padding: 12,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  productContent: {
    alignItems: 'center',
  },
  productImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginBottom: 8,
  },
  productInfo: {
    width: '100%',
    alignItems: 'center',
  },
  productTitle: {
    height: 14,
    width: '80%',
    borderRadius: 4,
    marginBottom: 6,
  },
  productPrice: {
    height: 16,
    width: '60%',
    borderRadius: 4,
    marginBottom: 8,
  },
  storeIndicators: {
    flexDirection: 'row',
    gap: 4,
    marginBottom: 8,
  },
  storeIndicator: {
    width: 20,
    height: 20,
    borderRadius: 10,
  },
  addButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
  },
  skeletonItem: {
    marginBottom: 8,
  },
});

export { SkeletonLoader, ProductCardSkeleton, ListSkeleton };
