import { supabase } from '../supabase/client';

interface StorePrice {
  store: 'woolworths' | 'newworld' | 'paknsave';
  price?: number;
  available: boolean;
  brand?: string;
  size?: string;
}

interface PriceProduct {
  id: string;
  name: string;
  price: number;
  store: string;
  brand?: string;
  size?: string;
  unit?: string;
  availability?: string;
}

export class SimplePriceService {
  /**
   * Fetch price data for a product across all stores
   */
  static async fetchPricesForProduct(productName: string, preferredBrand?: string): Promise<StorePrice[]> {
    try {
      console.log(`🔍 Fetching prices for: ${productName}${preferredBrand ? ` (${preferredBrand})` : ''}`);
      
      let query = supabase
        .from('products')
        .select('id, name, price, current_price, store, brand, brand_name, size, unit, availability, is_available')
        .not('price', 'is', null)
        .limit(200); // Increased from 20 to 200 to show more products

      // Search by product name - make this more flexible for better matching
      const searchTerm = productName.toLowerCase().trim();
      query = query.ilike('name', `%${searchTerm}%`);
      
      // Add brand filter if specified - check both brand fields AND product name
      if (preferredBrand) {
        const brandTerm = preferredBrand.toLowerCase().trim();
        query = query.or(`brand.ilike.%${brandTerm}%,brand_name.ilike.%${brandTerm}%,name.ilike.%${brandTerm}%`);
      }

      const { data: products, error } = await query;

      if (error) {
        console.error('Error fetching prices:', error);
        return [];
      }

      if (!products || products.length === 0) {
        console.log(`⚠️ No products found for: ${productName}`);
        return [];
      }

      console.log(`✅ Found ${products.length} products for: ${productName}`);

      // Convert to StorePrice format
      const storePrices: StorePrice[] = products.map(product => {
        const price = product.current_price || product.price;
        const isAvailable = product.is_available !== false && product.availability !== 'Out of Stock';
        
        return {
          store: product.store as 'woolworths' | 'newworld' | 'paknsave',
          price: price || undefined,
          available: isAvailable && price > 0,
          brand: product.brand_name || product.brand || undefined,
          size: product.size || product.unit || undefined,
        };
      }).filter(price => price.price && price.price > 0); // Only include products with valid prices

      // Group by store and pick best price for each store
      const storeGroups = storePrices.reduce((groups, price) => {
        const store = price.store;
        if (!groups[store]) {
          groups[store] = [];
        }
        groups[store].push(price);
        return groups;
      }, {} as Record<string, StorePrice[]>);

      // Get best price for each store
      const bestPricesPerStore: StorePrice[] = Object.entries(storeGroups).map(([store, prices]) => {
        // Sort by price (ascending) and prefer exact brand matches
        const sortedPrices = prices.sort((a, b) => {
          if (preferredBrand) {
            const aMatchesBrand = a.brand?.toLowerCase().includes(preferredBrand.toLowerCase());
            const bMatchesBrand = b.brand?.toLowerCase().includes(preferredBrand.toLowerCase());
            
            if (aMatchesBrand && !bMatchesBrand) return -1;
            if (!aMatchesBrand && bMatchesBrand) return 1;
          }
          
          return (a.price || 0) - (b.price || 0);
        });

        return sortedPrices[0]; // Return the best price for this store
      });

      console.log(`✅ Processed prices for ${bestPricesPerStore.length} stores:`, 
        bestPricesPerStore.map(p => `${p.store}: $${p.price}`).join(', '));

      return bestPricesPerStore;

    } catch (error) {
      console.error('Failed to fetch prices:', error);
      return [];
    }
  }

  /**
   * Get the cheapest price from store prices
   */
  static getBestPrice(storePrices: StorePrice[]): StorePrice | null {
    const availablePrices = storePrices.filter(p => p.available && p.price);
    if (availablePrices.length === 0) return null;

    return availablePrices.reduce((best, current) => 
      (current.price || 0) < (best.price || 0) ? current : best
    );
  }

  /**
   * Calculate potential savings compared to most expensive store
   */
  static calculateSavings(storePrices: StorePrice[]): number {
    const availablePrices = storePrices.filter(p => p.available && p.price);
    if (availablePrices.length < 2) return 0;

    const prices = availablePrices.map(p => p.price || 0);
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    
    return maxPrice - minPrice;
  }
}