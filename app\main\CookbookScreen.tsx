import React, { useEffect, useState } from 'react';
import { View, Text, SafeAreaView, ActivityIndicator, Modal, TouchableOpacity, StyleSheet, Dimensions, FlatList, Alert, Image, TextInput } from 'react-native';
import { RecipeDetailCard } from '../../src/components/RecipeDetailCard';
import { addToShoppingList, getShoppingLists, createShoppingList, toggleRecipeSaved, getSavedRecipeIds, isRecipeSaved } from '../../src/utils/storage';
import { getRecipes, Recipe } from '../../src/supabase/recipeService';

const screenWidth = Dimensions.get('window').width;
const CARD_WIDTH = (screenWidth - 48) / 2; // 2 cards per row, 16px padding

const getIngredientObjects = (ingredients: string[] = []) =>
  ingredients.map((ing) => ({
    name: ing,
    quantity: '',
    icon: '',
  }));

const mapRecipeToDetailCardProps = (recipe: any) => ({
  imageUrl: recipe.image_url || recipe.imageUrl,
  title: recipe.title,
  description: recipe.description || '',
  ingredients: getIngredientObjects(recipe.ingredients || []),
  instructions: recipe.instructions || recipe.method || [],
});

const RecipeCard = ({ recipe, onPress, isFavorite, onToggleFavorite }: { recipe: any; onPress: () => void; isFavorite?: boolean; onToggleFavorite?: (recipeId: string) => void }) => (
  <TouchableOpacity style={styles.card} onPress={onPress} activeOpacity={0.85}>
    <View style={styles.cardImageContainer}>
      {recipe.image_url || recipe.imageUrl ? (
        <Image
          source={{ uri: recipe.image_url || recipe.imageUrl }}
          style={styles.cardImage}
        />
      ) : (
        <View style={[styles.cardImage, { backgroundColor: '#e0e0e0' }]} />
      )}
      <TouchableOpacity 
        style={styles.favoriteBtn}
        onPress={() => onToggleFavorite && onToggleFavorite(recipe.objectID || recipe.id)}
      >
        <Text style={{ fontSize: 20 }}>{isFavorite ? '❤️' : '🤍'}</Text>
      </TouchableOpacity>
    </View>
    <View style={{ padding: 12 }}>
      <Text style={styles.cardTitle} numberOfLines={2}>{recipe.title}</Text>
      <View style={styles.cardRow}>
        <Text style={styles.cardMeta}>⏱️ {recipe.cook_time || recipe.cookTime || '20m'}</Text>
        <Text style={styles.cardMeta}>👥 {recipe.servings || '4'}</Text>
      </View>
      <View style={styles.cardRow}>
        <Text style={styles.cardMeta}>🌍 {recipe.cuisine_type || recipe.cuisine || 'Unknown'}</Text>
        <Text style={styles.cardRating}>⭐ {recipe.rating || '4.8'}</Text>
      </View>
    </View>
  </TouchableOpacity>
);

const handleAddToShoppingList = (recipe: any) => {
  const newIngredients = (recipe.ingredients || []).map((ingredient: string) => ({
    id: `${Date.now()}-${Math.random()}`,
    name: ingredient,
    checked: false,
    category: 'recipe-ingredients',
    fromRecipe: recipe.title
  }));
  addToShoppingList(newIngredients).then(() => {
    Alert.alert('✅ Added to Shopping List', `${newIngredients.length} ingredients from "${recipe.title}" have been added to your shopping list.`);
  }).catch(() => {
    Alert.alert('Error', 'Failed to add ingredients to shopping list.');
  });
};

export const CookbookScreen: React.FC = () => {
  const [recipes, setRecipes] = useState<Recipe[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedRecipe, setSelectedRecipe] = useState<any | null>(null);
  const [shoppingLists, setShoppingLists] = useState<any[]>([]);
  const [showListModal, setShowListModal] = useState(false);
  const [showNewListModal, setShowNewListModal] = useState(false);
  const [newListName, setNewListName] = useState('');
  const [pendingRecipe, setPendingRecipe] = useState<any | null>(null);
  const [savedRecipeIds, setSavedRecipeIds] = useState<string[]>([]);

  useEffect(() => {
    const fetchRecipes = async () => {
      setLoading(true);
      setError(null);
      try {
        const { data, error } = await getRecipes();
        if (error) {
          throw new Error(error.message);
        }
        if (data) {
          const valid = data.filter((r: any) => r.image_url && typeof r.image_url === 'string' && r.image_url.startsWith('http'));
          setRecipes(valid);
        }
        
        // Load saved recipe IDs
        const savedIds = await getSavedRecipeIds();
        setSavedRecipeIds(savedIds);
      } catch (e: any) {
        setError(e.message || 'Unknown error');
      } finally {
        setLoading(false);
      }
    };
    fetchRecipes();
    // Fetch shopping lists for modal
    getShoppingLists().then(setShoppingLists);
  }, []);

  const handleAddToShoppingList = (recipe: any) => {
    setPendingRecipe(recipe);
    getShoppingLists().then(setShoppingLists);
    setShowListModal(true);
  };

  const handleSelectList = async (listId: string) => {
    if (!pendingRecipe) return;
    const newIngredients = (pendingRecipe.ingredients || []).map((ingredient: string) => ({
      id: `${Date.now()}-${Math.random()}-${ingredient}`,
      name: ingredient,
      checked: false,
      category: 'recipe-ingredients',
      fromRecipe: pendingRecipe.title,
      listId,
    }));
    await addToShoppingList(newIngredients);
    setShowListModal(false);
    setPendingRecipe(null);
    Alert.alert('✅ Added to Shopping List', `${newIngredients.length} ingredients from "${pendingRecipe.title}" have been added to your shopping list.`);
  };

  const handleCreateNewList = async () => {
    if (!newListName.trim()) {
      Alert.alert('Error', 'Please enter a list name');
      return;
    }
    const newList = await createShoppingList({ name: newListName.trim(), color: '#667EEA' });
    setShowNewListModal(false);
    setNewListName('');
    setShowListModal(false);
    if (pendingRecipe) {
      handleSelectList(newList.id);
    }
    // Refresh lists
    getShoppingLists().then(setShoppingLists);
  };

  const handleToggleFavorite = async (recipeId: string) => {
    try {
      const newSavedState = await toggleRecipeSaved(recipeId);
      // Update the local state
      if (newSavedState) {
        setSavedRecipeIds(prev => [...prev, recipeId]);
      } else {
        setSavedRecipeIds(prev => prev.filter(id => id !== recipeId));
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to save recipe');
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" />
        <Text>Loading recipes...</Text>
      </SafeAreaView>
    );
  }
  if (error) {
    return (
      <SafeAreaView style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text style={{ color: 'red' }}>Error: {error}</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#fff' }}>
      <Text style={{ fontSize: 28, fontWeight: 'bold', marginBottom: 16, marginLeft: 16 }}>All Recipes</Text>
      <FlatList
        data={recipes}
        keyExtractor={(item) => item.id || item.objectID}
        numColumns={2}
        contentContainerStyle={{ paddingHorizontal: 8, paddingBottom: 24 }}
        columnWrapperStyle={{ justifyContent: 'space-between', marginBottom: 16 }}
        renderItem={({ item }) => (
          <View style={{ width: CARD_WIDTH, marginHorizontal: 4 }}>
            <RecipeCard 
              recipe={item} 
              onPress={() => setSelectedRecipe(item)} 
              isFavorite={savedRecipeIds.includes(item.objectID || item.id)}
              onToggleFavorite={handleToggleFavorite}
            />
          </View>
        )}
      />
      <Modal visible={!!selectedRecipe} animationType="slide" onRequestClose={() => setSelectedRecipe(null)}>
        <SafeAreaView style={{ flex: 1, backgroundColor: '#fff' }}>
          <View style={{ flex: 1 }}>
            {selectedRecipe && (
              <RecipeDetailCard
                {...mapRecipeToDetailCardProps(selectedRecipe)}
                onAddToShoppingList={() => handleAddToShoppingList(selectedRecipe)}
                onClose={() => setSelectedRecipe(null)}
              />
            )}
          </View>
        </SafeAreaView>
      </Modal>

      {/* Shopping List Selection Modal */}
      <Modal visible={showListModal} transparent animationType="fade" onRequestClose={() => setShowListModal(false)}>
        <View style={{ flex: 1, backgroundColor: 'rgba(0,0,0,0.3)', justifyContent: 'center', alignItems: 'center' }}>
          <View style={{ backgroundColor: '#fff', borderRadius: 18, padding: 24, minWidth: 280 }}>
            <Text style={{ fontWeight: 'bold', fontSize: 18, marginBottom: 12 }}>Select a Shopping List</Text>
            {shoppingLists.map((list) => (
              <TouchableOpacity key={list.id} style={{ paddingVertical: 10 }} onPress={() => handleSelectList(list.id)}>
                <Text style={{ fontSize: 16 }}>{list.name}</Text>
              </TouchableOpacity>
            ))}
            <TouchableOpacity style={{ marginTop: 16 }} onPress={() => { setShowListModal(false); setShowNewListModal(true); }}>
              <Text style={{ color: '#2ecc71', fontWeight: 'bold', fontSize: 16 }}>+ Create New List</Text>
            </TouchableOpacity>
            <TouchableOpacity style={{ marginTop: 12 }} onPress={() => setShowListModal(false)}>
              <Text style={{ color: '#888', fontSize: 15 }}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* New List Creation Modal */}
      <Modal visible={showNewListModal} transparent animationType="fade" onRequestClose={() => setShowNewListModal(false)}>
        <View style={{ flex: 1, backgroundColor: 'rgba(0,0,0,0.3)', justifyContent: 'center', alignItems: 'center' }}>
          <View style={{ backgroundColor: '#fff', borderRadius: 18, padding: 24, minWidth: 280 }}>
            <Text style={{ fontWeight: 'bold', fontSize: 18, marginBottom: 12 }}>Create New Shopping List</Text>
            <TextInput
              placeholder="List Name"
              value={newListName}
              onChangeText={setNewListName}
              style={{ borderWidth: 1, borderColor: '#eee', borderRadius: 8, padding: 10, marginBottom: 16, fontSize: 16 }}
            />
            <TouchableOpacity style={{ backgroundColor: '#2ecc71', borderRadius: 12, paddingVertical: 10, alignItems: 'center' }} onPress={handleCreateNewList}>
              <Text style={{ color: '#fff', fontWeight: 'bold', fontSize: 16 }}>Create</Text>
            </TouchableOpacity>
            <TouchableOpacity style={{ marginTop: 12 }} onPress={() => setShowNewListModal(false)}>
              <Text style={{ color: '#888', fontSize: 15 }}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#fff',
    borderRadius: 18,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOpacity: 0.06,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
    marginBottom: 8,
  },
  cardImageContainer: {
    width: '100%',
    height: 90,
    backgroundColor: '#f2f2f2',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  cardImage: {
    width: '100%',
    height: '100%',
    backgroundColor: '#d0d0d0',
    borderTopLeftRadius: 18,
    borderTopRightRadius: 18,
  },
  favoriteBtn: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 2,
    elevation: 2,
  },
  cardTitle: {
    fontWeight: 'bold',
    fontSize: 16,
    marginBottom: 4,
    color: '#222',
  },
  cardRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 2,
  },
  cardMeta: {
    fontSize: 13,
    color: '#888',
  },
  cardRating: {
    fontSize: 13,
    color: '#f5a623',
    fontWeight: 'bold',
  },
}); 