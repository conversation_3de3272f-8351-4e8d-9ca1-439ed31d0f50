import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Modal,
  ScrollView,
  Alert,
} from 'react-native';

interface ProductFormData {
  id?: string;
  name: string;
  quantity: number;
  unit: string;
  category: string;
}

interface Product {
  id: string;
  name: string;
  quantity: number;
  unit: string;
  category: string;
  checked: boolean;
}

interface SimpleProductFormProps {
  visible: boolean;
  onClose: () => void;
  onSave: (product: ProductFormData) => void;
  editingProduct?: Product | null;
  title?: string;
}

const CATEGORIES = [
  'Produce',
  'Meat & Seafood',
  'Dairy & Eggs',
  'Pantry',
  'Frozen',
  'Bakery',
  'Beverages',
  'Snacks',
  'Other'
];

const UNITS = ['each', 'kg', 'g', 'ltr', 'ml'];

export const SimpleProductForm: React.FC<SimpleProductFormProps> = ({
  visible,
  onClose,
  onSave,
  editingProduct,
  title
}) => {
  const [name, setName] = useState('');
  const [quantity, setQuantity] = useState('1');
  const [unit, setUnit] = useState('each');
  const [category, setCategory] = useState('Other');
  const [showCategoryPicker, setShowCategoryPicker] = useState(false);
  const [showUnitPicker, setShowUnitPicker] = useState(false);

  // Reset form when editingProduct changes or when modal becomes visible
  useEffect(() => {
    if (visible) {
      if (editingProduct) {
        setName(editingProduct.name);
        setQuantity(editingProduct.quantity.toString());
        setUnit(editingProduct.unit);
        setCategory(editingProduct.category);
      } else {
        setName('');
        setQuantity('1');
        setUnit('each');
        setCategory('Other');
      }
    }
  }, [visible, editingProduct]);

  const handleSave = () => {
    if (!name.trim()) {
      Alert.alert('Error', 'Please enter a product name');
      return;
    }

    const product: ProductFormData = {
      id: editingProduct?.id,
      name: name.trim(),
      quantity: parseInt(quantity) || 1,
      unit,
      category
    };

    onSave(product);
    handleClose();
  };

  const handleClose = () => {
    onClose();
  };

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={handleClose} style={styles.cancelButton}>
            <Text style={styles.cancelText}>Cancel</Text>
          </TouchableOpacity>
          <Text style={styles.title}>{title || (editingProduct ? 'Edit Product' : 'Add Product')}</Text>
          <TouchableOpacity onPress={handleSave} style={styles.saveButton}>
            <Text style={styles.saveText}>Save</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.form} showsVerticalScrollIndicator={false}>
          <View style={styles.section}>
            <Text style={styles.label}>Product Name</Text>
            <TextInput
              style={styles.input}
              value={name}
              onChangeText={setName}
              placeholder="Enter product name"
              autoFocus
              autoCapitalize="words"
            />
          </View>

          <View style={styles.row}>
            <View style={[styles.section, { flex: 1, marginRight: 8 }]}>
              <Text style={styles.label}>Quantity</Text>
              <TextInput
                style={styles.input}
                value={quantity}
                onChangeText={setQuantity}
                placeholder="1"
                keyboardType="numeric"
              />
            </View>

            <View style={[styles.section, { flex: 1, marginLeft: 8 }]}>
              <Text style={styles.label}>Unit</Text>
              <TouchableOpacity
                style={styles.picker}
                onPress={() => setShowUnitPicker(true)}
              >
                <Text style={styles.pickerText}>{unit}</Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.label}>Category</Text>
            <TouchableOpacity
              style={styles.picker}
              onPress={() => setShowCategoryPicker(true)}
            >
              <Text style={styles.pickerText}>{category}</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>

        {/* Category Picker Modal */}
        <Modal visible={showCategoryPicker} animationType="slide" presentationStyle="pageSheet">
          <View style={styles.pickerModal}>
            <View style={styles.pickerHeader}>
              <TouchableOpacity onPress={() => setShowCategoryPicker(false)}>
                <Text style={styles.pickerDone}>Done</Text>
              </TouchableOpacity>
            </View>
            <ScrollView>
              {CATEGORIES.map((cat) => (
                <TouchableOpacity
                  key={cat}
                  style={[styles.pickerOption, category === cat && styles.selectedOption]}
                  onPress={() => {
                    setCategory(cat);
                    setShowCategoryPicker(false);
                  }}
                >
                  <Text style={[styles.pickerOptionText, category === cat && styles.selectedOptionText]}>
                    {cat}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </Modal>

        {/* Unit Picker Modal */}
        <Modal visible={showUnitPicker} animationType="slide" presentationStyle="pageSheet">
          <View style={styles.pickerModal}>
            <View style={styles.pickerHeader}>
              <TouchableOpacity onPress={() => setShowUnitPicker(false)}>
                <Text style={styles.pickerDone}>Done</Text>
              </TouchableOpacity>
            </View>
            <ScrollView>
              {UNITS.map((u) => (
                <TouchableOpacity
                  key={u}
                  style={[styles.pickerOption, unit === u && styles.selectedOption]}
                  onPress={() => {
                    setUnit(u);
                    setShowUnitPicker(false);
                  }}
                >
                  <Text style={[styles.pickerOptionText, unit === u && styles.selectedOptionText]}>
                    {u}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </Modal>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#212529',
  },
  cancelButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  cancelText: {
    fontSize: 16,
    color: '#6c757d',
  },
  saveButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: '#007bff',
    borderRadius: 8,
  },
  saveText: {
    fontSize: 16,
    color: 'white',
    fontWeight: '500',
  },
  form: {
    flex: 1,
    padding: 20,
  },
  section: {
    marginBottom: 24,
  },
  row: {
    flexDirection: 'row',
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: '#495057',
    marginBottom: 8,
  },
  input: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#dee2e6',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    fontSize: 16,
    color: '#212529',
  },
  picker: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#dee2e6',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
  },
  pickerText: {
    fontSize: 16,
    color: '#212529',
  },
  pickerModal: {
    flex: 1,
    backgroundColor: 'white',
  },
  pickerHeader: {
    alignItems: 'flex-end',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  pickerDone: {
    fontSize: 16,
    color: '#007bff',
    fontWeight: '500',
  },
  pickerOption: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f8f9fa',
  },
  selectedOption: {
    backgroundColor: '#e3f2fd',
  },
  pickerOptionText: {
    fontSize: 16,
    color: '#212529',
  },
  selectedOptionText: {
    color: '#1976d2',
    fontWeight: '500',
  },
});