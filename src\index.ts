/**
 * React Native App Entry Point
 * This file serves as the main index for the React Native application
 */

// Export main app components and utilities
export { default as App } from '../App';

// Export main contexts
export { AuthProvider, useAuth } from './context/AuthContext';
export { ThemeProvider, useTheme } from './context/ThemeContext';
export { ShoppingProvider, useShopping } from './context/ShoppingContext';

// Export main types
export type { IProduct, ShoppingListItem, ShoppingList } from './types/shoppingList';

// Export main services  
export { productDeduplicationService } from './services/productDeduplicationService';
export { supabase } from './supabase/client';

// NOTE: Firebase Functions code moved to /functions directory
// This is the React Native app index, not Firebase Functions
