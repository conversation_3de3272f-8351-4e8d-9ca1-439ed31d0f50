import { supabase } from './client';
import { PostgrestError } from '@supabase/supabase-js';

export interface Product {
  id: string;
  objectID: string;
  name: string;
  price: number;
  category: string;
  brand: string;
  unit: string;
  availability: string;
  store: string;
  store_name: string;
  store_color: string;
  store_logo: string;
  last_updated: string;
  description: string;
  image_url: string;
  promotions: any;
  nutrition_info: any;
  searchable_text: string;
  shopping_aliases: string[];
  shopping_priority: number;
  is_common_item: boolean;
}

export const getProducts = async (): Promise<{ data: Product[] | null; error: PostgrestError | null }> => {
  const { data, error } = await supabase.from('products').select('*');
  return { data: data as Product[] | null, error };
};

export const getProductById = async (id: string): Promise<{ data: Product | null; error: PostgrestError | null }> => {
  const { data, error } = await supabase.from('products').select('*').eq('id', id).single();
  return { data: data as Product | null, error };
};

export const getProductsByCategory = async (category: string): Promise<{ data: Product[] | null; error: PostgrestError | null }> => {
  const { data, error } = await supabase.from('products').select('*').eq('category', category);
  return { data: data as Product[] | null, error };
};

export const searchProducts = async (query: string): Promise<{ data: Product[] | null; error: PostgrestError | null }> => {
  const { data, error } = await supabase
    .from('products')
    .select()
    .textSearch('searchable_text', query);
  return { data: data as Product[] | null, error };
}; 