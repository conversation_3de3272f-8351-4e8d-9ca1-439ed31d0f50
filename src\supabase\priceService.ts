import { supabase } from './client';
import { PostgrestError } from '@supabase/supabase-js';
import type { Database } from './types';

// Temporary types until consolidated schema is added to types.ts
export type ConsolidatedProduct = any;
export type Store = Database['public']['Tables']['stores']['Row'];
export type ConsolidatedPrice = any;
export type ProductVariant = any;
export type CategoryHierarchy = any;
export type Brand = any;

// Legacy types for backwards compatibility
export type Product = Database['public']['Tables']['products']['Row'];
export type Price = Database['public']['Tables']['prices']['Row'];

export interface ConsolidatedProductWithPrices extends ConsolidatedProduct {
  brand?: Brand | null;
  category?: CategoryHierarchy | null;
  store_prices?: Array<{
    store: Store;
    price: number;
    recorded_at: string;
    is_special: boolean;
  }>;
}

// Legacy interface for backwards compatibility
export interface ProductWithPrice extends Product {
  store_info?: Store | null;
}

export interface PriceHistory {
  price: number;
  recorded_at: string;
  store_name: string;
}

export interface PriceComparison {
  product_name: string;
  store: string;
  price: number;
  brand: string;
  availability: string;
  id: string;
}

export const getConsolidatedProducts = async (): Promise<{ data: ConsolidatedProductWithPrices[] | null; error: PostgrestError | null }> => {
  const { data, error } = await supabase
    .from('consolidated_products')
    .select(`
      *,
      brands (name),
      category_hierarchy (name, parent_id, level),
      consolidated_prices (
        price,
        recorded_at,
        is_special,
        was_available,
        stores (name, logo_url)
      )
    `)
    .order('display_name', { ascending: true });
  return { data: data as any, error };
};

// Legacy function for backwards compatibility
export const getProducts = async (): Promise<{ data: Product[] | null; error: PostgrestError | null }> => {
  const { data, error } = await supabase
    .from('products')
    .select('*')
    .order('last_updated', { ascending: false });
  return { data, error };
};

export const getProductById = async (id: string): Promise<{ data: Product | null; error: PostgrestError | null }> => {
  const { data, error } = await supabase
    .from('products')
    .select('*')
    .eq('id', id)
    .single();
  return { data, error };
};

export const getProductsByStore = async (storeName: string): Promise<{ data: Product[] | null; error: PostgrestError | null }> => {
  const { data, error } = await supabase
    .from('products')
    .select('*')
    .eq('store', storeName)
    .order('last_updated', { ascending: false });
  return { data, error };
};

export const getConsolidatedProductsByCategory = async (categoryName: string): Promise<{ data: ConsolidatedProductWithPrices[] | null; error: PostgrestError | null }> => {
  const { data, error } = await supabase
    .from('consolidated_products')
    .select(`
      *,
      brands (name),
      category_hierarchy!inner (
        id,
        name,
        parent_id,
        level
      ),
      consolidated_prices (
        price,
        recorded_at,
        is_special,
        was_available,
        stores (name, logo_url)
      )
    `)
    .or(`category_hierarchy.name.ilike.%${categoryName}%`)
    .order('display_name', { ascending: true });
  return { data: data as any, error };
};

// Legacy function for backwards compatibility
export const getProductsByCategory = async (category: string): Promise<{ data: Product[] | null; error: PostgrestError | null }> => {
  const { data, error } = await supabase
    .from('products')
    .select('*')
    .ilike('category', `%${category}%`)
    .order('price', { ascending: true });
  return { data, error };
};

export const searchConsolidatedProducts = async (query: string): Promise<{ data: ConsolidatedProductWithPrices[] | null; error: PostgrestError | null }> => {
  const { data, error } = await supabase
    .from('consolidated_products')
    .select(`
      *,
      brands (name),
      category_hierarchy (name, parent_id, level),
      consolidated_prices (
        price,
        recorded_at,
        is_special,
        was_available,
        stores (name, logo_url)
      )
    `)
    .or(`display_name.ilike.%${query}%,normalized_name.ilike.%${query}%`)
    .order('display_name', { ascending: true });
  return { data: data as any, error };
};

// Legacy function for backwards compatibility
export const searchProducts = async (query: string): Promise<{ data: Product[] | null; error: PostgrestError | null }> => {
  const { data, error } = await supabase
    .rpc('search_products', { search_query: query });
  return { data, error };
};

export const getLatestPricesForProducts = async (): Promise<{ data: any[] | null; error: PostgrestError | null }> => {
  const { data, error } = await supabase
    .from('prices')
    .select(`
      price,
      recorded_at,
      products (
        name,
        size,
        brand,
        category
      ),
      stores (
        name,
        display_name
      )
    `)
    .order('recorded_at', { ascending: false });
  return { data, error };
};

export const getPriceHistory = async (productId: string, limit: number = 30): Promise<{ data: PriceHistory[] | null; error: PostgrestError | null }> => {
  const { data, error } = await supabase
    .from('prices')
    .select(`
      price,
      recorded_at,
      stores (
        name
      )
    `)
    .eq('product_id', productId)
    .order('recorded_at', { ascending: false })
    .limit(limit);

  const transformedData = data?.map(item => ({
    price: item.price,
    recorded_at: item.recorded_at || '',
    store_name: (item.stores as any)?.name || ''
  })) || null;

  return { data: transformedData, error };
};

export const compareProductPrices = async (productName: string): Promise<{ data: PriceComparison[] | null; error: PostgrestError | null }> => {
  const { data, error } = await supabase
    .rpc('compare_product_prices', { product_name: productName });
  return { data, error };
};

export const getCheapestPrice = async (productName: string): Promise<{ data: any[] | null; error: PostgrestError | null }> => {
  const { data, error } = await supabase
    .rpc('get_cheapest_price', { product_name: productName });
  return { data, error };
};

export const getStores = async (): Promise<{ data: Store[] | null; error: PostgrestError | null }> => {
  const { data, error } = await supabase
    .from('stores')
    .select('*')
    .eq('is_active', true)
    .order('name');
  return { data, error };
};

export const getCategoryHierarchy = async (): Promise<{ data: CategoryHierarchy[] | null; error: PostgrestError | null }> => {
  const { data, error } = await supabase
    .from('category_hierarchy')
    .select('*')
    .order('level, sort_order');
  return { data, error };
};

export const getProductImage = (productId: string): string => {
  const { data } = supabase.storage
    .from('product-images')
    .getPublicUrl(`products/${productId}.jpg`);
  return data.publicUrl;
};

export const downloadProductImage = async (productId: string): Promise<{ data: Blob | null; error: any }> => {
  const { data, error } = await supabase.storage
    .from('product-images')
    .download(`products/${productId}.jpg`);
  return { data, error };
};

export const getPriceTrends = async (productNamePattern: string, days: number = 30): Promise<{ data: any[] | null; error: PostgrestError | null }> => {
  const { data, error } = await supabase
    .from('prices')
    .select(`
      recorded_at,
      price,
      products!inner (
        name
      )
    `)
    .ilike('products.name', `%${productNamePattern}%`)
    .gte('recorded_at', new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString())
    .order('recorded_at', { ascending: false });
  
  return { data, error };
}; 