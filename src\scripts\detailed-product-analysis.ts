/**
 * DETAILED PRODUCT ANALYSIS
 * 
 * Deep dive into product naming patterns to understand how to extract
 * brands, sizes, and identify potential duplicates across stores.
 */

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://emjwniuqwhvohjassnrg.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVtanduaXVxd2h2b2hqYXNzbnJnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjQwMTYwNSwiZXhwIjoyMDY3OTc3NjA1fQ.oTowRoNAjlUmk10O9pSMK2BvL0XlyBb5orVRrlEryHs';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function detailedProductAnalysis() {
  console.log('🔍 Detailed Product Analysis for Deduplication Patterns...\n');

  // Get products that might be duplicates by searching for common brands/products
  const commonTerms = ['milk', 'bread', 'butter', 'cheese', 'chicken', 'beef', 'apple', 'banana', 'rice', 'pasta'];
  
  for (const term of commonTerms.slice(0, 3)) { // Test with first 3 terms
    console.log(`\n🔍 Analyzing "${term}" products across all stores...`);
    
    const { data: products, error } = await supabase
      .from('products')
      .select('*')
      .ilike('name', `%${term}%`)
      .not('price', 'is', null)
      .limit(50);

    if (error) {
      console.error(`   ❌ Error fetching ${term} products:`, error);
      continue;
    }

    if (!products || products.length === 0) {
      console.log(`   ⚠️  No ${term} products found`);
      continue;
    }

    // Group by store
    const byStore = products.reduce((acc: any, product) => {
      if (!acc[product.store]) acc[product.store] = [];
      acc[product.store].push(product);
      return acc;
    }, {});

    console.log(`   📊 Found ${products.length} ${term} products:`);
    Object.entries(byStore).forEach(([store, storeProducts]: [string, any]) => {
      console.log(`      ${store}: ${storeProducts.length} products`);
    });

    // Show examples from each store
    console.log(`   📝 Sample ${term} products by store:`);
    Object.entries(byStore).forEach(([store, storeProducts]: [string, any]) => {
      console.log(`      ${store.toUpperCase()}:`);
      storeProducts.slice(0, 3).forEach((product: any, index: number) => {
        console.log(`         ${index + 1}. "${product.name}" - $${product.price}`);
      });
    });

    // Look for potential matches
    console.log(`   🎯 Potential ${term} duplicates:`);
    const stores = Object.keys(byStore);
    let duplicateCount = 0;

    for (let i = 0; i < stores.length; i++) {
      for (let j = i + 1; j < stores.length; j++) {
        const store1Products = byStore[stores[i]];
        const store2Products = byStore[stores[j]];

        for (const p1 of store1Products.slice(0, 5)) {
          for (const p2 of store2Products.slice(0, 5)) {
            const similarity = calculateSimpleSimilarity(p1.name, p2.name);
            if (similarity > 0.6) {
              console.log(`      "${p1.name}" (${p1.store}) vs "${p2.name}" (${p2.store})`);
              console.log(`         Similarity: ${(similarity * 100).toFixed(1)}% | Prices: $${p1.price} vs $${p2.price}`);
              duplicateCount++;
            }
          }
        }
      }
    }

    if (duplicateCount === 0) {
      console.log(`      No obvious duplicates found for ${term}`);
    }
  }

  // Analyze brand extraction patterns
  console.log('\n🏷️  Brand Extraction Analysis...');
  
  const { data: sampleProducts, error: sampleError } = await supabase
    .from('products')
    .select('name, store, price')
    .not('price', 'is', null)
    .limit(100);

  if (sampleError) {
    console.error('   ❌ Error fetching sample products:', sampleError);
    return;
  }

  // Common NZ brands to look for
  const nzBrands = [
    'Anchor', 'Mainland', 'Tip Top', 'Watties', 'Maggi', 'Cadbury', 'Whittakers', 
    'Pams', 'Woolworths', 'Countdown', 'Tegel', 'Hellers', 'Bluebird', 'Griffin',
    'Sanitarium', 'Uncle Tobys', 'Arnott', 'Nestle', 'Coca Cola', 'Pepsi'
  ];

  console.log('   🔍 Extracting brands from product names...');
  const brandMatches: any[] = [];

  sampleProducts?.forEach(product => {
    const name = product.name.toLowerCase();
    const foundBrands = nzBrands.filter(brand => 
      name.includes(brand.toLowerCase())
    );
    
    if (foundBrands.length > 0) {
      brandMatches.push({
        name: product.name,
        store: product.store,
        extractedBrands: foundBrands,
        price: product.price
      });
    }
  });

  console.log(`   📊 Found ${brandMatches.length} products with identifiable brands:`);
  brandMatches.slice(0, 10).forEach((match, index) => {
    console.log(`      ${index + 1}. "${match.name}" (${match.store}) - Brands: [${match.extractedBrands.join(', ')}] - $${match.price}`);
  });

  // Size extraction analysis
  console.log('\n📏 Size Extraction Analysis...');
  
  const sizePatterns = [
    /(\d+(?:\.\d+)?)\s*(ml|millilitre|milliliter)/gi,
    /(\d+(?:\.\d+)?)\s*(l|litre|liter)(?!\w)/gi,
    /(\d+(?:\.\d+)?)\s*(g|gram)(?!\w)/gi,
    /(\d+(?:\.\d+)?)\s*(kg|kilogram)/gi,
    /(\d+(?:\.\d+)?)\s*(pack|pk|ea|each|count|ct)/gi,
    /(\d+)\s*x\s*(\d+(?:\.\d+)?)\s*(ml|l|g|kg)/gi
  ];

  const sizeMatches: any[] = [];
  
  sampleProducts?.slice(0, 50).forEach(product => {
    const extractedSizes: string[] = [];
    
    sizePatterns.forEach(pattern => {
      const matches = product.name.match(pattern);
      if (matches) {
        extractedSizes.push(...matches);
      }
    });

    if (extractedSizes.length > 0) {
      sizeMatches.push({
        name: product.name,
        store: product.store,
        extractedSizes: extractedSizes.slice(0, 3), // Limit to first 3 matches
        price: product.price
      });
    }
  });

  console.log(`   📊 Found ${sizeMatches.length} products with extractable sizes:`);
  sizeMatches.slice(0, 10).forEach((match, index) => {
    console.log(`      ${index + 1}. "${match.name}" (${match.store}) - Sizes: [${match.extractedSizes.join(', ')}] - $${match.price}`);
  });

  console.log('\n✅ Detailed analysis complete!');
}

function calculateSimpleSimilarity(str1: string, str2: string): number {
  const words1 = str1.toLowerCase().split(/\s+/).filter(w => w.length > 2);
  const words2 = str2.toLowerCase().split(/\s+/).filter(w => w.length > 2);
  
  const commonWords = words1.filter(word => words2.includes(word));
  const totalWords = Math.max(words1.length, words2.length);
  
  return totalWords > 0 ? commonWords.length / totalWords : 0;
}

// Run the analysis
if (require.main === module) {
  detailedProductAnalysis()
    .catch((error) => {
      console.error('❌ Detailed analysis failed:', error);
    });
}

export { detailedProductAnalysis };
