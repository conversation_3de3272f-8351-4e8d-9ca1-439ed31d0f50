#!/bin/bash

# 🚀 Firebase AI Recipe Planner Deployment Script
# This script sets up and deploys Firebase Cloud Functions with proper API keys

set -e

echo "🔥 Firebase AI Recipe Planner Deployment"
echo "======================================="

# Check if firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI is not installed. Please install it first:"
    echo "npm install -g firebase-tools"
    exit 1
fi

echo "✅ Firebase CLI found"

# Check if user is logged in
if ! firebase projects:list &> /dev/null; then
    echo "🔑 Please login to Firebase first:"
    firebase login
fi

echo "✅ Firebase authentication verified"

# Set your API keys (replace with your actual keys)
echo "🔧 Setting up secure API configuration..."

# Your actual API keys (from the file you showed me)
GEMINI_API_KEY="AIzaSyCG2iZ87fcBIJp1DmHvo68uR0kT8TwU"
ALGOLIA_APP_ID="CXNOUIM54D"
ALGOLIA_API_KEY="********************************"

echo "Setting Gemini API key..."
firebase functions:config:set gemini.api_key="$GEMINI_API_KEY"

echo "Setting Algolia configuration..."
firebase functions:config:set algolia.app_id="$ALGOLIA_APP_ID"
firebase functions:config:set algolia.api_key="$ALGOLIA_API_KEY"

echo "✅ API keys configured securely"

# Build and deploy functions
echo "🔨 Building functions..."
cd functions

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing function dependencies..."
    npm install
fi

# Build TypeScript
echo "🔨 Building TypeScript..."
npm run build

echo "🚀 Deploying functions..."
cd ..
firebase deploy --only functions

echo "🎉 Deployment completed successfully!"
echo ""
echo "📋 Your Firebase functions are now available at:"
echo "https://us-central1-apptest1-752af.cloudfunctions.net/categorizeRecipe"
echo "https://us-central1-apptest1-752af.cloudfunctions.net/suggestRecipeTitle" 
echo "https://us-central1-apptest1-752af.cloudfunctions.net/getCookingTip"
echo "https://us-central1-apptest1-752af.cloudfunctions.net/healthCheck"
echo ""
echo "🧪 Test your deployment with:"
echo 'curl -X POST "https://us-central1-apptest1-752af.cloudfunctions.net/healthCheck" \\'
echo '  -H "Content-Type: application/json" \\'
echo '  -d "{\"data\":{}}"'
echo ""
echo "🔐 Your API keys are securely stored on Firebase and never exposed to clients!" 