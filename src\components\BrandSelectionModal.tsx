import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  TextInput,
  Alert,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '../context/ThemeContext';
import { theme } from '../styles';
import { PlatformTouchable } from './PlatformTouchable';
import { 
  priceComparisonService, 
  StoreSearchResult 
} from '../services/priceComparisonService';
import { enhancedShoppingDataService } from '../services/enhancedShoppingDataService';
import { smartProductMatcher } from '../services/smartProductMatcher';
import { productImageService, ProductImage } from '../services/productImageService';

interface BrandSelectionModalProps {
  visible: boolean;
  onClose: () => void;
  productName: string;
  quantity: number;
  unit: string;
  onSelectBrand: (brand: string, productDetails: StoreSearchResult) => void;
}

interface StoreGroup {
  store: 'woolworths' | 'newworld' | 'paknsave';
  storeName: string;
  storeEmoji: string;
  products: StoreSearchResult[];
  cheapestPrice: number;
  cheapestProduct: StoreSearchResult | null;
}

export const BrandSelectionModal: React.FC<BrandSelectionModalProps> = ({
  visible,
  onClose,
  productName,
  quantity,
  unit,
  onSelectBrand,
}) => {
  const { colors } = useTheme();
  const [loading, setLoading] = useState(false);
  const [storeGroups, setStoreGroups] = useState<StoreGroup[]>([]);
  const [searchResults, setSearchResults] = useState<StoreSearchResult[]>([]);
  const [fallbackUsed, setFallbackUsed] = useState(false);
  const [brandImages, setBrandImages] = useState<Map<string, ProductImage>>(new Map());

  useEffect(() => {
    if (visible && productName) {
      searchForBrands();
    }
  }, [visible, productName]);

  const loadBrandImages = async (products: StoreSearchResult[]) => {
    console.log('🖼️ Loading brand images for products');
    const imageMap = new Map<string, ProductImage>();
    
    // Get unique brands
    const uniqueBrands = [...new Set(products.map(p => extractBrand(p.product.name)))];
    
    // Load images for each brand
    const imagePromises = uniqueBrands.map(async (brand) => {
      if (brand && brand !== 'Generic') {
        try {
          const image = await productImageService.getBrandImage({
            productName,
            brand,
            size: 'thumbnail',
            fallbackToGeneric: true
          });
          
          if (image) {
            imageMap.set(brand, image);
          }
        } catch (error) {
          console.warn(`Failed to load image for brand ${brand}:`, error);
        }
      }
    });
    
    await Promise.allSettled(imagePromises);
    setBrandImages(imageMap);
    console.log(`✅ Loaded ${imageMap.size} brand images`);
  };

  const searchForBrands = async () => {
    setLoading(true);
    setFallbackUsed(false);
    console.log('🔍 BrandSelectionModal: Starting smart search for:', productName);
    
    try {
      // First try smart product matcher for intelligent brand matching
      const smartMatch = await smartProductMatcher.findMatches(productName);
      console.log('🧠 Smart matcher results:', smartMatch.totalFound, 'brands found');
      
      if (smartMatch.matches.length > 0) {
        // Convert smart matches to StoreSearchResult format
        const smartResults: StoreSearchResult[] = [];
        
        smartMatch.matches.forEach(brand => {
          // Create results for each store where this brand is available
          Object.entries(brand.availability).forEach(([store, availability]) => {
            if (availability.available && availability.price) {
              smartResults.push({
                store: store as 'woolworths' | 'newworld' | 'paknsave',
                storeName: store === 'woolworths' ? 'Woolworths' : 
                          store === 'newworld' ? 'New World' : "Pak'nSave",
                product: {
                  objectID: `${brand.brand}-${store}`,
                  name: brand.displayName,
                  price: availability.price,
                  unit: brand.size,
                  description: brand.displayName,
                  category: brand.category,
                  brand: brand.brand,
                  imageUrl: '',
                  availability: 'in_stock' as const,
                  promotions: []
                },
                searchQuery: productName,
                relevance_score: brand.isPopular ? 1.0 : 0.5
              });
            }
          });
        });

        console.log('✅ Smart matcher converted to', smartResults.length, 'store results');
        setSearchResults(smartResults);
        
        // Group by supermarket
        const storeGroups = groupByStore(smartResults);
        console.log('✅ Smart matcher created', storeGroups.length, 'store groups');
        setStoreGroups(storeGroups);
        
        if (storeGroups.length > 0) {
          return; // Success! No need for fallback
        }
      }

      // Fallback to regular search if smart matcher didn't find enough
      console.log('🔄 Smart matcher found limited results, trying regular search...');
      const results = await priceComparisonService.searchProductAcrossStores(productName, {
        maxResults: 20,
        sortBy: 'price',
        includeOutOfStock: false
      });

      console.log('🔍 Regular search results:', results.results.length, 'products found');
      
      if (results.results.length > 0) {
        setSearchResults(results.results);
        
        // Group by supermarket
        const storeGroups = groupByStore(results.results);
        console.log('🔍 Regular search store groups:', storeGroups.length);
        setStoreGroups(storeGroups);
        
        // Load images for each brand
        await loadBrandImages(results.results);
        
        if (storeGroups.length === 0) {
          console.log('❌ Regular search: NO STORES FOUND - trying enhanced fallback');
          await tryEnhancedFallback();
        }
      } else {
        console.log('❌ No results from regular search - trying enhanced fallback');
        await tryEnhancedFallback();
      }
    } catch (error) {
      console.error('❌ BrandSelectionModal: Error in search:', error);
      console.log('🔄 BrandSelectionModal: Trying enhanced fallback due to error');
      await tryEnhancedFallback();
    } finally {
      setLoading(false);
    }
  };

  const tryEnhancedFallback = async () => {
    console.log('🔄 BrandSelectionModal: Using enhanced fallback for:', productName);
    setFallbackUsed(true);
    
    try {
      const enhancedData = await enhancedShoppingDataService.getEnhancedDataForItem(productName);
      console.log('🔄 Enhanced data subcategories:', enhancedData.length);
      
      if (enhancedData.length > 0) {
        // Convert enhanced data to store groups format
        const fallbackStoreGroups = convertEnhancedDataToStoreGroups(enhancedData);
        console.log('✅ Created fallback store groups:', fallbackStoreGroups.length);
        setStoreGroups(fallbackStoreGroups);
      } else {
        console.log('❌ No enhanced data available either');
        Alert.alert('No Products Found', `No products found for "${productName}". This might be a data issue.`);
      }
    } catch (error) {
      console.error('❌ Enhanced fallback also failed:', error);
      Alert.alert('Error', 'Failed to load brands. Please try again.');
    }
  };

  const convertEnhancedDataToStoreGroups = (enhancedData: any[]): StoreGroup[] => {
    const storeGroups: StoreGroup[] = [];
    const storeConfig = {
      woolworths: { emoji: '🍎', name: 'Woolworths' },
      newworld: { emoji: '🛒', name: 'New World' },
      paknsave: { emoji: '💰', name: "Pak'nSave" }
    };

    // Process each subcategory
    enhancedData.forEach(subcategory => {
      subcategory.items.forEach((item: any) => {
        item.brands.forEach((brand: any) => {
          // Create store search results for each store this brand is available in
          Object.entries(brand.stores).forEach(([storeName, storeData]: [string, any]) => {
            if (storeData.available) {
              const existingGroup = storeGroups.find(g => g.store === storeName);
              
                             const mockResult: StoreSearchResult = {
                 store: storeName as 'woolworths' | 'newworld' | 'paknsave',
                 storeName: storeConfig[storeName as keyof typeof storeConfig]?.name || storeName,
                 product: {
                   objectID: brand.productId || `${storeName}-${brand.name}`,
                   name: `${brand.name} ${item.name}`,
                   price: storeData.price,
                   brand: brand.name,
                   unit: storeData.size || unit,
                   imageUrl: '',
                   description: `${brand.name} brand ${item.name}`,
                   category: subcategory.name,
                   availability: 'in_stock' as const
                 },
                 searchQuery: productName
               };

              if (existingGroup) {
                existingGroup.products.push(mockResult);
                if (storeData.price < existingGroup.cheapestPrice) {
                  existingGroup.cheapestPrice = storeData.price;
                  existingGroup.cheapestProduct = mockResult;
                }
              } else {
                storeGroups.push({
                  store: storeName as 'woolworths' | 'newworld' | 'paknsave',
                  storeName: storeConfig[storeName as keyof typeof storeConfig]?.name || storeName,
                  storeEmoji: storeConfig[storeName as keyof typeof storeConfig]?.emoji || '🏪',
                  products: [mockResult],
                  cheapestPrice: storeData.price,
                  cheapestProduct: mockResult
                });
              }
            }
          });
        });
      });
    });

    // Sort each store's products by price
    storeGroups.forEach(group => {
      group.products.sort((a, b) => a.product.price - b.product.price);
    });

    // Sort stores by cheapest price
    return storeGroups.sort((a, b) => a.cheapestPrice - b.cheapestPrice);
  };

  const groupByStore = (results: StoreSearchResult[]): StoreGroup[] => {
    const storeMap = new Map<string, StoreSearchResult[]>();

    // Group results by store
    results.forEach(result => {
      const storeKey = result.store;
      if (!storeMap.has(storeKey)) {
        storeMap.set(storeKey, []);
      }
      storeMap.get(storeKey)!.push(result);
    });

    const storeGroups: StoreGroup[] = [];
    const storeConfig = {
      woolworths: { emoji: '🍎', name: 'Woolworths' },
      newworld: { emoji: '🛒', name: 'New World' },
      paknsave: { emoji: '💰', name: "Pak'nSave" }
    };

    storeMap.forEach((products, storeKey) => {
      const config = storeConfig[storeKey as keyof typeof storeConfig];
      if (config && products.length > 0) {
        // Sort products by price within each store
        const sortedProducts = products.sort((a, b) => a.product.price - b.product.price);
        const cheapest = sortedProducts[0];

        storeGroups.push({
          store: storeKey as 'woolworths' | 'newworld' | 'paknsave',
          storeName: config.name,
          storeEmoji: config.emoji,
          products: sortedProducts,
          cheapestPrice: cheapest.product.price,
          cheapestProduct: cheapest,
        });
      }
    });

    // Sort stores by cheapest price
    return storeGroups.sort((a, b) => a.cheapestPrice - b.cheapestPrice);
  };

  const extractBrand = (productName: string): string | null => {
    // Extract brand from product name
    const words = productName.split(' ');
    
    // Common brand patterns
    const knownBrands = [
      'Anchor', 'Mainland', 'Meadow Fresh', 'Lewis Road', 'Calci', 'Puhoi Valley',
      'Watties', 'ETA', 'Heinz', 'Maggi', 'Continental', 'Greggs',
      'Tip Top', 'Much Moore', 'Cadbury', 'Whittaker\'s', 'Griffin\'s',
      'Arnott\'s', 'Edmonds', 'Chelsea', 'Tararua', 'Mainland',
      'Nescafe', 'Moccona', 'L\'affare', 'Oatly', 'Minor Figures'
    ];

    for (const brand of knownBrands) {
      if (productName.toLowerCase().includes(brand.toLowerCase())) {
        return brand;
      }
    }

    // Fallback: use first word as brand if it's capitalized
    if (words[0] && words[0][0] === words[0][0].toUpperCase()) {
      return words[0];
    }

    return 'Generic';
  };

  const formatPrice = (price: number): string => {
    return `$${price.toFixed(2)}`;
  };

  const getStoreEmoji = (store: string): string => {
    const storeEmojis = {
      woolworths: '🍎',
      newworld: '🛒',
      paknsave: '💰',
    };
    return storeEmojis[store as keyof typeof storeEmojis] || '🏪';
  };

  const handleSelectProduct = (product: StoreSearchResult) => {
    // Extract brand from product name for the callback
    const brand = extractBrand(product.product.name) || product.product.brand || 'Unknown';
    onSelectBrand(brand, product);
  };

  const renderProductItem = ({ item }: { item: StoreSearchResult }) => {
    const brand = extractBrand(item.product.name) || item.product.brand || 'Generic';
    const brandImage = brandImages.get(brand);
    
    return (
      <PlatformTouchable
        style={[styles.productItem, { backgroundColor: colors.backgroundSecondary, borderColor: colors.border }] as any}
        onPress={() => handleSelectProduct(item)}
        hapticType="light"
      >
        <View style={styles.productContent}>
          <View style={styles.productHeader}>
            <View style={styles.brandSection}>
              {brandImage && (
                <View style={styles.brandImageContainer}>
                  <Image
                    source={{ uri: brandImage.imageUrl }}
                    style={styles.brandImage}
                    resizeMode="contain"
                  />
                </View>
              )}
              <View style={styles.brandTextSection}>
                <Text style={[styles.brandName, { color: colors.text }]} numberOfLines={1}>
                  {brand}
                </Text>
                <Text style={[styles.productDescription, { color: colors.textSecondary }]} numberOfLines={2}>
                  {item.product.name}
                </Text>
              </View>
            </View>
            <View style={styles.priceSection}>
              <Text style={styles.price}>
                {formatPrice(item.product.price)}
              </Text>
              <Text style={[styles.selectText, { color: theme.colors.primary }]}>
                Select
              </Text>
            </View>
          </View>
          
          {item.product.unit && (
            <Text style={[styles.sizeInfo, { color: colors.textSecondary }]}>
              {item.product.unit}
            </Text>
          )}
        </View>
      </PlatformTouchable>
    );
  };

  const renderStoreGroup = ({ item }: { item: StoreGroup }) => (
    <View style={[styles.storeGroup, { backgroundColor: colors.surface }]}>
      <View style={styles.storeHeader}>
        <View style={styles.storeInfo}>
          <Text style={styles.storeEmoji}>{item.storeEmoji}</Text>
          <View style={styles.storeTitleSection}>
            <Text style={[styles.storeName, { color: colors.text }]}>
              {item.storeName}
            </Text>
            <Text style={[styles.productCount, { color: colors.textSecondary }]}>
              {item.products.length} brand{item.products.length !== 1 ? 's' : ''} available
            </Text>
          </View>
        </View>
      </View>
      
      <View style={styles.productsContainer}>
        {item.products.map((product, index) => (
          <View key={`${item.store}-${index}`}>
            {renderProductItem({ item: product })}
          </View>
        ))}
      </View>
    </View>
  );

  const styles = useMemo(() => StyleSheet.create({
    modal: {
      flex: 1,
      backgroundColor: colors.backgroundSecondary,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: theme.spacing.base,
      paddingVertical: theme.spacing.base,
      backgroundColor: colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    title: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: theme.typography.fontWeight.semibold,
      color: colors.text,
    },
    subtitle: {
      fontSize: theme.typography.fontSize.sm,
      color: colors.textSecondary,
      marginTop: 2,
    },
    cancelButton: {
      color: colors.textSecondary,
      fontSize: theme.typography.fontSize.base,
    },
    content: {
      flex: 1,
      padding: theme.spacing.base,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      marginTop: theme.spacing.base,
      color: colors.textSecondary,
    },
    storeGroup: {
      borderRadius: theme.radius.lg,
      borderWidth: 1,
      borderColor: colors.border,
      marginBottom: theme.spacing.base,
      overflow: 'hidden',
    },
    storeHeader: {
      padding: theme.spacing.base,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    storeInfo: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    storeEmoji: {
      fontSize: 24,
      marginRight: theme.spacing.sm,
    },
    storeTitleSection: {
      flex: 1,
    },
    storeName: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: theme.typography.fontWeight.semibold,
    },
    productCount: {
      fontSize: theme.typography.fontSize.sm,
      marginTop: 2,
    },
    productsContainer: {
      padding: theme.spacing.xs,
    },
    productItem: {
      borderRadius: theme.radius.base,
      borderWidth: 1,
      padding: theme.spacing.sm,
      marginBottom: theme.spacing.xs,
    },
    productContent: {
      flex: 1,
    },
    productHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
    },
    productName: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: theme.typography.fontWeight.medium,
      flex: 1,
      marginRight: theme.spacing.sm,
    },
    brandName: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: theme.typography.fontWeight.semibold,
      flex: 1,
    },
    brandSection: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    brandImageContainer: {
      width: 48,
      height: 48,
      borderRadius: theme.radius.base,
      overflow: 'hidden',
      backgroundColor: '#f5f5f5',
      marginRight: theme.spacing.sm,
    },
    brandImage: {
      width: '100%',
      height: '100%',
    },
    brandTextSection: {
      flex: 1,
    },
    selectText: {
      fontSize: theme.typography.fontSize.sm,
      fontWeight: theme.typography.fontWeight.medium,
      marginTop: 4,
    },
    productDescription: {
      fontSize: theme.typography.fontSize.sm,
      marginTop: theme.spacing.xs,
      fontStyle: 'italic',
    },
    sizeInfo: {
      fontSize: theme.typography.fontSize.xs,
      marginTop: theme.spacing.xs,
    },
    brandInfo: {
      fontSize: theme.typography.fontSize.sm,
      marginTop: theme.spacing.xs,
      fontStyle: 'italic',
    },
    priceSection: {
      alignItems: 'flex-end',
    },
    price: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: theme.typography.fontWeight.bold,
      color: '#00C851',
    },
    unit: {
      fontSize: theme.typography.fontSize.xs,
      marginTop: 2,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: theme.spacing['2xl'],
    },
    emptyText: {
      fontSize: theme.typography.fontSize.base,
      color: colors.textSecondary,
      textAlign: 'center',
      marginTop: theme.spacing.base,
    },
    fallbackIndicator: {
      backgroundColor: '#FFF3CD',
      padding: theme.spacing.sm,
      margin: theme.spacing.base,
      borderRadius: theme.radius.base,
      borderWidth: 1,
      borderColor: '#FFEAA7',
    },
    fallbackText: {
      color: '#856404',
      fontSize: theme.typography.fontSize.sm,
      textAlign: 'center',
    },
  }), [colors]);

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.modal}>
        <View style={styles.header}>
          <View style={{ flex: 1 }}>
            <Text style={styles.title}>Choose Product</Text>
            <Text style={styles.subtitle}>
              {productName} • {quantity} {unit}
            </Text>
          </View>
          <TouchableOpacity onPress={onClose}>
            <Text style={styles.cancelButton}>Cancel</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.content}>
          {fallbackUsed && (
            <View style={styles.fallbackIndicator}>
              <Text style={styles.fallbackText}>
                📦 Showing sample brands (live search unavailable)
              </Text>
            </View>
          )}
          
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={theme.colors.primary} />
              <Text style={styles.loadingText}>Finding products...</Text>
            </View>
          ) : storeGroups.length > 0 ? (
            <ScrollView showsVerticalScrollIndicator={false}>
              {storeGroups.map((storeGroup) => (
                <View key={storeGroup.store}>
                  {renderStoreGroup({ item: storeGroup })}
                </View>
              ))}
            </ScrollView>
          ) : (
            <View style={styles.emptyContainer}>
              <Text style={{ fontSize: 48 }}>🛒</Text>
              <Text style={styles.emptyText}>
                No products found for "{productName}".{'\n'}
                Try a more specific search term.
              </Text>
            </View>
          )}
        </View>
      </SafeAreaView>
    </Modal>
  );
};