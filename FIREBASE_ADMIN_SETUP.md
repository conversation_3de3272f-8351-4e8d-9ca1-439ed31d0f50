# 🔐 Firebase Admin API Setup Guide

## Quick Setup Commands

```bash
# 1. Configure Firebase Functions with your API keys (SECURE)
firebase functions:config:set gemini.api_key="AIzaSyCG2iZ87fcBIJp1DmHvo68uR0kT8TwU"
firebase functions:config:set algolia.app_id="CXNOUIM54D"
firebase functions:config:set algolia.api_key="********************************"

# 2. Deploy functions
cd functions
npm run build
firebase deploy --only functions

# 3. Test deployment
curl https://us-central1-YOUR_PROJECT_ID.cloudfunctions.net/healthCheck
```

## Why Use Firebase Admin APIs?

✅ **Security**: API keys stay on server, never exposed to client  
✅ **Scalability**: Handles traffic spikes automatically  
✅ **Cost Control**: Better rate limiting and caching  
✅ **Reliability**: Server-side processing with retries  

## Setup Steps

### 1. Update functions/src/index.ts

```typescript
import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';

admin.initializeApp();

// Secure recipe categorization endpoint
export const categorizeRecipe = functions.https.onRequest(async (req, res) => {
  res.set('Access-Control-Allow-Origin', '*');
  
  if (req.method === 'OPTIONS') {
    res.status(200).send('');
    return;
  }

  try {
    const { ingredients } = req.body;
    const geminiApiKey = functions.config().gemini?.api_key;
    
    if (!geminiApiKey) {
      res.status(500).json({ error: 'API not configured' });
      return;
    }

    // Call Gemini API securely from server
    const categorization = await callGeminiAPI(ingredients, geminiApiKey);
    res.status(200).json({ categorization });
    
  } catch (error) {
    console.error('Categorization error:', error);
    res.status(500).json({ error: 'Failed to categorize recipe' });
  }
});

async function callGeminiAPI(ingredients: string[], apiKey: string) {
  const response = await fetch(
    `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`,
    {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: `Categorize recipe ingredients: ${ingredients.join(', ')}`
          }]
        }]
      })
    }
  );
  
  const data = await response.json();
  return JSON.parse(data.candidates[0].content.parts[0].text);
}
```

### 2. Create Client Service

```typescript
// src/services/firebaseApiService.ts
class FirebaseApiService {
  private baseUrl = `https://us-central1-YOUR_PROJECT_ID.cloudfunctions.net`;

  async categorizeRecipe(ingredients: string[]) {
    const response = await fetch(`${this.baseUrl}/categorizeRecipe`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ ingredients }),
    });
    
    const data = await response.json();
    return data.categorization;
  }
}

export const firebaseApiService = new FirebaseApiService();
```

### 3. Update Your Recipe Service

```typescript
// In recipeOrganizationService.ts
import { firebaseApiService } from '../services/firebaseApiService';

private static async analyzeRecipeWithAI(recipe: Recipe): Promise<any> {
  try {
    // Use secure Firebase function
    const categorization = await firebaseApiService.categorizeRecipe(recipe.ingredients);
    return categorization;
  } catch (error) {
    // Fallback to local analysis
    return this.inferOrganizationFromContent(recipe, []);
  }
}
```

This setup keeps your API keys secure while providing reliable AI-powered recipe categorization! 🚀 