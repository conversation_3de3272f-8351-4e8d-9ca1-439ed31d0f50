/**
 * Master Products Hook
 * Provides access to consolidated products from the master catalog
 * Integrates with the new master product matching system
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { masterProductMatchingService, ConsolidatedProduct } from '../services/masterProductMatchingService';
import { dataCleaningService } from '../services/dataCleaningService';

interface UseMasterProductsOptions {
  pageSize?: number;
  searchQuery?: string;
  selectedCategory?: string;
  selectedStores?: string[];
  sortBy?: 'name' | 'price_low' | 'price_high' | 'savings';
  autoRefresh?: boolean;
}

interface LoadingState {
  initial: boolean;
  loadingMore: boolean;
  refreshing: boolean;
  error: string | null;
}

interface UseMasterProductsResult {
  products: ConsolidatedProduct[];
  loading: LoadingState;
  hasMore: boolean;
  page: number;
  totalProducts: number;
  // Actions
  loadMore: () => Promise<void>;
  refresh: () => Promise<void>;
  runMatching: () => Promise<void>;
  cleanData: () => Promise<void>;
  // Statistics
  matchingStats: {
    total_scraped: number;
    total_matched: number;
    match_rate: number;
    unmatched_count: number;
  } | null;
  dataQuality: {
    quality_score: number;
    cleaned_listings: number;
    total_listings: number;
  } | null;
}

export const useMasterProducts = ({
  pageSize = 50,
  searchQuery = '',
  selectedCategory = 'All',
  selectedStores = ['woolworths', 'newworld', 'paknsave'],
  sortBy = 'name',
  autoRefresh = false,
}: UseMasterProductsOptions): UseMasterProductsResult => {
  const [products, setProducts] = useState<ConsolidatedProduct[]>([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [totalProducts, setTotalProducts] = useState(0);
  const [loadingState, setLoadingState] = useState<LoadingState>({
    initial: true,
    loadingMore: false,
    refreshing: false,
    error: null,
  });
  const [matchingStats, setMatchingStats] = useState<any>(null);
  const [dataQuality, setDataQuality] = useState<any>(null);

  const abortControllerRef = useRef<AbortController | null>(null);
  const lastSearchRef = useRef<string>('');

  // Debounced search
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState(searchQuery);
  const searchTimeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    
    searchTimeoutRef.current = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 300);

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [searchQuery]);

  // Load products function
  const loadProducts = useCallback(async (
    isRefresh: boolean = false,
    isLoadMore: boolean = false
  ) => {
    try {
      // Cancel previous request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      abortControllerRef.current = new AbortController();

      // Set loading state
      if (isRefresh) {
        setLoadingState(prev => ({ ...prev, refreshing: true, error: null }));
      } else if (isLoadMore) {
        setLoadingState(prev => ({ ...prev, loadingMore: true, error: null }));
      } else {
        setLoadingState(prev => ({ ...prev, initial: true, error: null }));
      }

      const currentPage = isRefresh || !isLoadMore ? 1 : page;
      const offset = (currentPage - 1) * pageSize;

      console.log('🔍 Loading products:', {
        search: debouncedSearchQuery,
        category: selectedCategory,
        stores: selectedStores,
        page: currentPage,
        offset
      });

      // Load consolidated products from master catalog
      const newProducts = await masterProductMatchingService.getConsolidatedProducts({
        limit: pageSize,
        offset: offset,
        search: debouncedSearchQuery || undefined,
        category: selectedCategory === 'All' ? undefined : selectedCategory,
        stores: selectedStores.length > 0 ? selectedStores : undefined,
      });

      // Sort products based on sortBy parameter
      const sortedProducts = sortProducts(newProducts, sortBy);

      if (isRefresh || !isLoadMore) {
        setProducts(sortedProducts);
        setPage(2);
        setTotalProducts(sortedProducts.length);
      } else {
        setProducts(prev => [...prev, ...sortedProducts]);
        setPage(prev => prev + 1);
      }

      // Determine if there are more products
      setHasMore(sortedProducts.length === pageSize);

      // Reset loading states
      setLoadingState(prev => ({
        ...prev,
        initial: false,
        refreshing: false,
        loadingMore: false,
        error: null
      }));

      // Track search for deduplication
      lastSearchRef.current = `${debouncedSearchQuery}-${selectedCategory}-${selectedStores.join(',')}`;

    } catch (error) {
      if ((error as any)?.name === 'AbortError') {
        return; // Request was cancelled, ignore
      }

      console.error('Error loading products:', error);
      setLoadingState(prev => ({
        ...prev,
        initial: false,
        refreshing: false,
        loadingMore: false,
        error: error instanceof Error ? error.message : 'Failed to load products'
      }));
    }
  }, [debouncedSearchQuery, selectedCategory, selectedStores, sortBy, pageSize, page]);

  // Sort products helper
  const sortProducts = (products: ConsolidatedProduct[], sortBy: string): ConsolidatedProduct[] => {
    const sorted = [...products];
    
    switch (sortBy) {
      case 'price_low':
        return sorted.sort((a, b) => (a.lowest_price || 0) - (b.lowest_price || 0));
      case 'price_high':
        return sorted.sort((a, b) => (b.lowest_price || 0) - (a.lowest_price || 0));
      case 'savings':
        return sorted.sort((a, b) => (b.max_savings || 0) - (a.max_savings || 0));
      case 'name':
      default:
        return sorted.sort((a, b) => a.name.localeCompare(b.name));
    }
  };

  // Load more products
  const loadMore = useCallback(async () => {
    if (!hasMore || loadingState.loadingMore) return;
    await loadProducts(false, true);
  }, [hasMore, loadingState.loadingMore, loadProducts]);

  // Refresh products
  const refresh = useCallback(async () => {
    await loadProducts(true, false);
  }, [loadProducts]);

  // Run product matching pipeline
  const runMatching = useCallback(async () => {
    try {
      setLoadingState(prev => ({ ...prev, refreshing: true, error: null }));
      
      console.log('🔄 Running product matching pipeline...');
      
      // Run the full matching pipeline
      const result = await masterProductMatchingService.runFullMatchingPipeline();
      
      console.log('✅ Matching pipeline completed:', result);
      
      // Refresh products to show updated matches
      await loadProducts(true, false);
      
      // Update matching statistics
      const stats = await masterProductMatchingService.getMatchingStatistics();
      setMatchingStats(stats);
      
    } catch (error) {
      console.error('Error running matching pipeline:', error);
      setLoadingState(prev => ({
        ...prev,
        refreshing: false,
        error: `Matching failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      }));
    }
  }, [loadProducts]);

  // Clean and normalize data
  const cleanData = useCallback(async () => {
    try {
      setLoadingState(prev => ({ ...prev, refreshing: true, error: null }));
      
      console.log('🧹 Cleaning and normalizing data...');
      
      // Run data cleaning
      const cleaningResult = await dataCleaningService.cleanScrapedProductData();
      console.log('✅ Data cleaning completed:', cleaningResult);
      
      // Run price normalization
      const normalizationResult = await dataCleaningService.normalizeProductPricesForComparison();
      console.log('✅ Price normalization completed:', normalizationResult);
      
      // Update data quality metrics
      const qualityMetrics = await dataCleaningService.getDataQualityMetrics();
      setDataQuality(qualityMetrics);
      
      // Refresh products
      await loadProducts(true, false);
      
    } catch (error) {
      console.error('Error cleaning data:', error);
      setLoadingState(prev => ({
        ...prev,
        refreshing: false,
        error: `Data cleaning failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      }));
    }
  }, [loadProducts]);

  // Load matching statistics
  const loadMatchingStatistics = useCallback(async () => {
    try {
      const stats = await masterProductMatchingService.getMatchingStatistics();
      setMatchingStats(stats);
      
      const quality = await dataCleaningService.getDataQualityMetrics();
      setDataQuality(quality);
    } catch (error) {
      console.error('Error loading statistics:', error);
    }
  }, []);

  // Initial load and dependency-based reloading
  useEffect(() => {
    const currentSearch = `${debouncedSearchQuery}-${selectedCategory}-${selectedStores.join(',')}`;
    if (currentSearch !== lastSearchRef.current) {
      loadProducts();
    }
  }, [debouncedSearchQuery, selectedCategory, selectedStores, sortBy, loadProducts]);

  // Load statistics on mount
  useEffect(() => {
    loadMatchingStatistics();
  }, [loadMatchingStatistics]);

  // Auto-refresh functionality
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      if (!loadingState.refreshing && !loadingState.loadingMore) {
        loadMatchingStatistics();
      }
    }, 30000); // Refresh stats every 30 seconds

    return () => clearInterval(interval);
  }, [autoRefresh, loadingState.refreshing, loadingState.loadingMore, loadMatchingStatistics]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  return {
    products,
    loading: loadingState,
    hasMore,
    page,
    totalProducts,
    loadMore,
    refresh,
    runMatching,
    cleanData,
    matchingStats,
    dataQuality,
  };
};