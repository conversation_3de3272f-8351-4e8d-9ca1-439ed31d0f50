# 🎨 HOW TO USE YOUR FIGMA-STYLE DESIGN SYSTEM

## 🚀 **QUICK START GUIDE**

### **1. Import the Design System**
```typescript
import { designSystem, createStyle } from './src/styles/designSystem';
```

### **2. Use Pre-built Styles (Like Figma Components)**
```typescript
// Instead of writing complex StyleSheet objects:
const styles = StyleSheet.create({
  myCard: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 4,
  }
});

// Use this simple approach:
const styles = StyleSheet.create({
  myCard: createStyle.card('medium'), // Instant professional card!
});
```

## 🎯 **PRACTICAL EXAMPLES**

### **Recipe Card Enhancement**
```typescript
// In your HomeScreen.tsx, find this:
<View style={styles.discoverCard}>

// Replace with:
<View style={[createStyle.card('large'), { backgroundColor: designSystem.colors.primary[100] }]}>
```

### **Button Styling**
```typescript
// Old way:
<TouchableOpacity style={{
  backgroundColor: '#667EEA',
  paddingHorizontal: 16,
  paddingVertical: 12,
  borderRadius: 8,
}}>

// New way:
<TouchableOpacity style={createStyle.button('primary', 'medium')}>
```

### **Typography Hierarchy**
```typescript
// Headers
<Text style={createStyle.text('2xl', 'bold')}>Main Title</Text>
<Text style={createStyle.text('lg', 'semibold')}>Section Title</Text>
<Text style={createStyle.text('md', 'normal')}>Body Text</Text>
<Text style={createStyle.text('sm', 'medium')}>Caption</Text>
```

### **Color Theming**
```typescript
// Primary colors
backgroundColor: designSystem.colors.primary[500],  // Main brand
backgroundColor: designSystem.colors.primary[100],  // Light brand
backgroundColor: designSystem.colors.primary[900],  // Dark brand

// Semantic colors
backgroundColor: designSystem.colors.success,  // Green
backgroundColor: designSystem.colors.warning,  // Orange
backgroundColor: designSystem.colors.error,    // Red

// Neutral colors
backgroundColor: designSystem.colors.gray[50],   // Almost white
backgroundColor: designSystem.colors.gray[500],  // Medium gray
backgroundColor: designSystem.colors.gray[900],  // Almost black
```

### **Spacing System**
```typescript
// Consistent spacing throughout your app
padding: designSystem.spacing.xs,     // 4px
padding: designSystem.spacing.sm,     // 8px
padding: designSystem.spacing.md,     // 12px
padding: designSystem.spacing.lg,     // 16px
padding: designSystem.spacing.xl,     // 20px
padding: designSystem.spacing['2xl'], // 24px
```

## 🔧 **ADVANCED CUSTOMIZATION**

### **Creating Custom Variants**
```typescript
// In designSystem.ts, add new component variants:
components: {
  recipeCard: {
    compact: { width: 120, height: 160, padding: 8 },
    standard: { width: 160, height: 200, padding: 12 },
    featured: { width: 200, height: 240, padding: 16 },
  }
}
```

### **Custom Color Schemes**
```typescript
// Add theme variations:
themes: {
  light: {
    background: '#FFFFFF',
    text: '#111827',
    surface: '#F9FAFB',
  },
  dark: {
    background: '#111827',
    text: '#FFFFFF',
    surface: '#1F2937',
  }
}
```

## 🎨 **LIVE TESTING**

### **Option 1: Add StyleEditor to Your App**
1. Import StyleEditor in any screen
2. Add a button to open it
3. See all design tokens live!

### **Option 2: Quick Testing**
1. Open any component file
2. Change a `createStyle.card('medium')` to `createStyle.card('large')`
3. Save and see instant changes!

### **Option 3: Color Experimentation**
```typescript
// Try different color combinations:
backgroundColor: designSystem.colors.primary[100],  // Light blue
backgroundColor: designSystem.colors.primary[500],  // Medium blue  
backgroundColor: designSystem.colors.primary[900],  // Dark blue
```

## 🚀 **NEXT STEPS**

1. **Start Small**: Replace one component's styles with design system
2. **Test Changes**: See how it looks on your Android device
3. **Iterate Fast**: Use the design tokens to quickly try variations
4. **Build Consistency**: Apply the same patterns across all screens

This gives you **Figma-level control** with **instant feedback** - the ultimate vibe coding experience! 🔥
