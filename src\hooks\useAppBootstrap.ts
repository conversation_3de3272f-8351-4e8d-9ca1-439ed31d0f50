/**
 * useAppBootstrap Hook
 * 
 * Centralizes app initialization logic including:
 * - First launch detection
 * - Authentication status checking
 * - Loading state management
 * 
 * Returns a single source of truth for app readiness and navigation state.
 */

import { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { isFirstLaunch } from '../utils/storage';
import { initializeAppData } from '../services/dataInitializer';
import { AppState } from 'react-native';

export interface AppBootstrapState {
  isAppReady: boolean;
  shouldShowOnboarding: boolean;
  isAuthenticated: boolean;
  error: string | null;
}

export const useAppBootstrap = (): AppBootstrapState => {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const [isFirstTime, setIsFirstTime] = useState<boolean | null>(null);
  const [firstLaunchLoading, setFirstLaunchLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    checkAppBootstrap();
  }, []);

  // TEMPORARILY DISABLED: Re-check onboarding status when app becomes active
  // This was causing infinite loops - commenting out for now
  // useEffect(() => {
  //   const handleAppStateChange = (nextAppState: any) => {
  //     if (nextAppState === 'active') {
  //       // Re-check onboarding status when app becomes active
  //       checkAppBootstrap();
  //     }
  //   };

  //   const subscription = AppState.addEventListener('change', handleAppStateChange);
  //   return () => subscription?.remove();
  // }, []);

  const checkAppBootstrap = async () => {
    try {
      setError(null);
      
      // Initialize application data in background (non-blocking)
      initializeAppData().catch(err => console.warn('Background data init failed:', err));
      
      // Check if this is the user's first time opening the app
      const firstTime = await isFirstLaunch();
      console.log('🚀 App Bootstrap: First time check result:', firstTime);
      setIsFirstTime(firstTime);
      
    } catch (err) {
      console.error('Error during app bootstrap:', err);
      setError('Failed to initialize app. Please try again.');
      
      // On error, default to not showing onboarding
      // This prevents users from getting stuck on errors
      setIsFirstTime(false);
      
    } finally {
      setFirstLaunchLoading(false);
    }
  };

  // Calculate app readiness
  const isAppReady = !firstLaunchLoading && !authLoading && isFirstTime !== null;
  
  // Determine if onboarding should be shown
  const shouldShowOnboarding = isAppReady && isFirstTime === true;

  console.log('🚀 App Bootstrap State:', {
    isAppReady,
    shouldShowOnboarding,
    isAuthenticated,
    isFirstTime,
    authLoading,
    firstLaunchLoading
  });

  return {
    isAppReady,
    shouldShowOnboarding,
    isAuthenticated: isAuthenticated || false,
    error,
  };
};

// Additional hook for more granular control if needed
export const useAppBootstrapDetailed = () => {
  const { isAuthenticated, isLoading: authLoading, user } = useAuth();
  const [isFirstTime, setIsFirstTime] = useState<boolean | null>(null);
  const [firstLaunchLoading, setFirstLaunchLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  useEffect(() => {
    checkAppBootstrap();
  }, [retryCount]);

  const checkAppBootstrap = async () => {
    try {
      setError(null);
      
      console.log('🚀 App Bootstrap: Starting...');
      
      // Initialize application data in background (non-blocking)
      initializeAppData().catch(err => console.warn('Background data init failed:', err));
      
      console.log('🚀 App Bootstrap: Checking first launch...');
      const firstTime = await isFirstLaunch();
      
      console.log('🚀 App Bootstrap: First time?', firstTime);
      setIsFirstTime(firstTime);
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error('🚨 App Bootstrap Error:', errorMessage);
      
      setError(`Bootstrap failed: ${errorMessage}`);
      setIsFirstTime(false); // Safe default
      
    } finally {
      setFirstLaunchLoading(false);
    }
  };

  const retry = () => {
    setFirstLaunchLoading(true);
    setRetryCount(prev => prev + 1);
  };

  const isAppReady = !firstLaunchLoading && !authLoading && isFirstTime !== null;
  const shouldShowOnboarding = isAppReady && isFirstTime === true;

  return {
    // Ready state
    isAppReady,
    shouldShowOnboarding,
    isAuthenticated: isAuthenticated || false,
    
    // Loading states (for debugging/detailed control)
    authLoading,
    firstLaunchLoading,
    
    // User data
    user,
    
    // Error handling
    error,
    retry,
    
    // Debug info
    debugInfo: {
      isFirstTime,
      authLoading,
      firstLaunchLoading,
      isAuthenticated,
      retryCount,
    },
  };
}; 