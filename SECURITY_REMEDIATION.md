# 🚨 CRITICAL SECURITY REMEDIATION GUIDE

## IMMEDIATE ACTION REQUIRED ⚡

Your API keys have been **EXPOSED** in your repository. Take these steps **IMMEDIATELY**:

### 1. 🔒 REVOKE EXPOSED KEYS NOW

**Gemini API Key**: `AIzaSyB5COrzGrglzVnS03VZ1TDzLXKCyvU8Jt` ⚠️ **COMPROMISED**
- Go to [Google AI Studio](https://aistudio.google.com/app/apikey)
- **DELETE** this key immediately
- Generate a new key

**Algolia Keys**: App ID `CXNOUIM54D` and API key ⚠️ **COMPROMISED**
- Go to [Algolia Dashboard](https://www.algolia.com/account/api-keys)
- **DELETE** the exposed API key immediately
- Generate a new search-only API key

### 2. 🧹 CLEAN GIT HISTORY

```bash
# Remove .env from entire git history
git filter-branch --index-filter "git rm -rf --cached --ignore-unmatch .env" HEAD

# Remove cursor rules file that contains keys
git filter-branch --index-filter "git rm -rf --cached --ignore-unmatch .cursor/rules/project.mdc" HEAD

# Force push to overwrite remote history
git push --force-with-lease origin main

# Clean up local references
rm -rf .git/refs/original/
git reflog expire --expire=now --all
git gc --prune=now --aggressive
```

### 3. 🔥 REMOVE EXPOSED KEYS FROM CODE

Update these files immediately:
- Remove API keys from `.cursor/rules/project.mdc`
- Ensure `.env` is properly ignored
- Remove any hardcoded keys in source files

---

## 🔐 SECURE FIREBASE SOLUTION

Replace insecure client-side API calls with secure Firebase backend:

### Step 1: Create Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com)
2. Create new project: "ai-recipe-planner"
3. Enable Cloud Functions
4. Enable Authentication (optional)

### Step 2: Install Firebase CLI

```bash
npm install -g firebase-tools
firebase login
cd ai-recipe-planner
firebase init functions
```

### Step 3: Configure Firebase Functions

Update `functions/src/index.ts` with your project structure (already created in your repo).

### Step 4: Set Environment Variables SECURELY

```bash
# Set your NEW API keys as Firebase environment variables
firebase functions:config:set \
  gemini.api_key="YOUR_NEW_GEMINI_KEY" \
  algolia.app_id="YOUR_NEW_ALGOLIA_APP_ID" \
  algolia.api_key="YOUR_NEW_ALGOLIA_KEY" \
  algolia.index_name="recipes"
```

### Step 5: Deploy Secure Functions

```bash
cd functions
npm install
npm run build
firebase deploy --only functions
```

### Step 6: Update Client Configuration

Update `firebaseService.ts` with your Firebase project details:

```typescript
const FIREBASE_CONFIG = {
  projectId: "your-actual-project-id",
  // ... other config
};
```

### Step 7: Switch to Secure Service

Replace your current API calls:

```typescript
// OLD - INSECURE ❌
import { aiApiService } from './services/aiApiService';

// NEW - SECURE ✅  
import { secureAiApiService } from './services/secureAiApiService';

// Usage remains the same
const recipes = await secureAiApiService.generateBulkRecipes(params);
```

---

## 🛡️ SECURITY BENEFITS

### Before (INSECURE) ❌
```typescript
// API keys exposed in client bundle
EXPO_PUBLIC_GEMINI_API_KEY=AIzaSyB5COrzGrglzVnS03VZ1TDzLXKCyvU8Jt
EXPO_PUBLIC_ALGOLIA_API_KEY=********************************
```
- ⚠️ Keys visible to anyone who inspects your app
- ⚠️ Malicious users can abuse your API quota
- ⚠️ Unexpected charges from API abuse
- ⚠️ No rate limiting or access control

### After (SECURE) ✅
```typescript
// Client calls secure Firebase functions
await fetch('https://your-project.cloudfunctions.net/generateRecipes', {
  method: 'POST',
  body: JSON.stringify(params)
});
```
- ✅ API keys hidden on server side
- ✅ Rate limiting and access control
- ✅ Request validation and sanitization
- ✅ Detailed logging and monitoring
- ✅ No risk of API key abuse

---

## 📱 UPDATED APP ARCHITECTURE

### Client-Side (React Native)
```
┌─────────────────────────────────┐
│          React Native App      │
│                                 │
│  ┌───────────────────────────┐  │
│  │   secureAiApiService      │  │
│  │                           │  │
│  │   • No API keys           │  │
│  │   • Calls Firebase       │  │
│  │   • Offline fallback     │  │
│  └───────────────────────────┘  │
└─────────────────────────────────┘
                  │
                  │ HTTPS
                  ▼
┌─────────────────────────────────┐
│       Firebase Functions       │
│                                 │
│  ┌───────────────────────────┐  │
│  │   Secure API Proxy        │  │
│  │                           │  │
│  │   • API keys secured     │  │
│  │   • Rate limiting        │  │
│  │   • Request validation   │  │
│  └───────────────────────────┘  │
└─────────────────────────────────┘
                  │
                  │ Secure API calls
                  ▼
┌─────────────────────────────────┐
│      External APIs              │
│  • Gemini AI                   │
│  • Algolia Search              │
└─────────────────────────────────┘
```

---

## 🚀 MIGRATION CHECKLIST

- [ ] **URGENT**: Revoke all exposed API keys
- [ ] **URGENT**: Clean git history
- [ ] **URGENT**: Remove keys from code files
- [ ] Create Firebase project
- [ ] Install Firebase CLI
- [ ] Deploy secure Cloud Functions
- [ ] Generate NEW API keys
- [ ] Set Firebase environment variables
- [ ] Update client to use secure service
- [ ] Test end-to-end functionality
- [ ] Monitor Firebase usage and costs

---

## 🔍 PREVENTION MEASURES

### 1. Proper .gitignore
```gitignore
# Environment variables
.env
.env.local
.env.*.local

# IDE configurations that might contain secrets
.cursor/
.vscode/settings.json

# API keys and secrets
config/secrets.json
```

### 2. Environment Variable Validation
```typescript
// Use server-side validation only
if (typeof window === 'undefined') {
  // Server-side: check for required env vars
  if (!process.env.GEMINI_API_KEY) {
    throw new Error('GEMINI_API_KEY required');
  }
}
```

### 3. API Key Rotation
- Rotate API keys monthly
- Use different keys for development/production
- Monitor API usage for anomalies

### 4. Rate Limiting
```typescript
// Implement in Firebase Functions
const rateLimit = require('express-rate-limit');
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
});
```

---

## 📞 SUPPORT

If you need help with this migration:

1. **Firebase Setup Issues**: Check [Firebase Documentation](https://firebase.google.com/docs/functions)
2. **API Key Recovery**: Contact Google Cloud Support or Algolia Support
3. **Git History Cleanup**: Use tools like [BFG Repo-Cleaner](https://rtyley.github.io/bfg-repo-cleaner/)

**Remember**: This is a critical security vulnerability. Complete this migration as soon as possible to protect your API quota and prevent unauthorized usage.

---

## ⚡ QUICK START COMMANDS

```bash
# 1. Clean up repository
git filter-branch --index-filter "git rm -rf --cached --ignore-unmatch .env .cursor/rules/project.mdc" HEAD
git push --force-with-lease origin main

# 2. Setup Firebase
npm install -g firebase-tools
firebase login
firebase init functions

# 3. Deploy secure backend
cd functions && npm install && firebase deploy --only functions

# 4. Test secure API
curl -X POST "https://your-project.cloudfunctions.net/healthCheck"
```

🔒 **Your app will be secure once this migration is complete!** 