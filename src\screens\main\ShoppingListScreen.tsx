import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Modal,
  TextInput,
  Alert,
  ActivityIndicator,
  ScrollView,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';
import { useShopping, ShoppingList } from '../../context/ShoppingContext';
import { theme } from '../../styles';

const LIST_ICONS = [
  'home', 'business', 'cart', 'restaurant', 'fitness', 'medical', 
  'school', 'car', 'gift', 'heart', 'star', 'bookmark'
];

const LIST_COLORS = [
  '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', 
  '#06B6D4', '#84CC16', '#F97316', '#EC4899', '#6B7280'
];

interface CreateListModalProps {
  visible: boolean;
  onClose: () => void;
  onCreateList: (name: string, description: string, color: string, icon: string) => void;
}

const CreateListModal: React.FC<CreateListModalProps> = ({ visible, onClose, onCreateList }) => {
  const { colors } = useTheme();
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [selectedColor, setSelectedColor] = useState(LIST_COLORS[0]);
  const [selectedIcon, setSelectedIcon] = useState(LIST_ICONS[0]);

  const handleCreate = () => {
    if (!name.trim()) {
      Alert.alert('Error', 'Please enter a list name');
      return;
    }
    onCreateList(name.trim(), description.trim(), selectedColor, selectedIcon);
    setName('');
    setDescription('');
    setSelectedColor(LIST_COLORS[0]);
    setSelectedIcon(LIST_ICONS[0]);
    onClose();
  };

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={[styles.modalContainer, { backgroundColor: colors.background }]}>
        <View style={[styles.modalHeader, { borderBottomColor: colors.border }]}>
          <Text style={[styles.modalTitle, { color: colors.text }]}>Create New List</Text>
          <TouchableOpacity onPress={onClose}>
            <Ionicons name="close" size={24} color={colors.text} />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.modalContent} showsVerticalScrollIndicator={false}>
          <View style={styles.inputSection}>
            <Text style={[styles.inputLabel, { color: colors.text }]}>List Name</Text>
            <TextInput
              style={[styles.textInput, { 
                backgroundColor: colors.surface, 
                borderColor: colors.border, 
                color: colors.text 
              }]}
              value={name}
              onChangeText={setName}
              placeholder="e.g., Home Shopping, Office Supplies"
              placeholderTextColor={colors.textSecondary}
              maxLength={50}
            />
          </View>

          <View style={styles.inputSection}>
            <Text style={[styles.inputLabel, { color: colors.text }]}>Description (Optional)</Text>
            <TextInput
              style={[styles.textInput, { 
                backgroundColor: colors.surface, 
                borderColor: colors.border, 
                color: colors.text 
              }]}
              value={description}
              onChangeText={setDescription}
              placeholder="Brief description of this list"
              placeholderTextColor={colors.textSecondary}
              maxLength={100}
              multiline
            />
          </View>

          <View style={styles.inputSection}>
            <Text style={[styles.inputLabel, { color: colors.text }]}>Color</Text>
            <View style={styles.colorGrid}>
              {LIST_COLORS.map((color) => (
                <TouchableOpacity
                  key={color}
                  style={[
                    styles.colorOption,
                    { backgroundColor: color },
                    selectedColor === color && styles.selectedColorOption
                  ]}
                  onPress={() => setSelectedColor(color)}
                >
                  {selectedColor === color && (
                    <Ionicons name="checkmark" size={20} color="white" />
                  )}
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.inputSection}>
            <Text style={[styles.inputLabel, { color: colors.text }]}>Icon</Text>
            <View style={styles.iconGrid}>
              {LIST_ICONS.map((icon) => (
                <TouchableOpacity
                  key={icon}
                  style={[
                    styles.iconOption,
                    { backgroundColor: selectedIcon === icon ? selectedColor : colors.surface },
                    { borderColor: colors.border }
                  ]}
                  onPress={() => setSelectedIcon(icon)}
                >
                  <Ionicons 
                    name={icon as any} 
                    size={24} 
                    color={selectedIcon === icon ? 'white' : colors.text} 
                  />
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </ScrollView>

        <View style={[styles.modalFooter, { borderTopColor: colors.border }]}>
          <TouchableOpacity
            style={[styles.createButton, { backgroundColor: selectedColor }]}
            onPress={handleCreate}
          >
            <Text style={styles.createButtonText}>Create List</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

interface ShoppingListCardProps {
  list: ShoppingList;
  onPress: () => void;
  onDelete: () => void;
}

const ShoppingListCard: React.FC<ShoppingListCardProps> = ({ list, onPress, onDelete }) => {
  const { colors } = useTheme();
  const totalItems = list.totalItems || 0;
  const completedItems = list.completedItems || 0;
  const progress = totalItems > 0 ? (completedItems / totalItems) * 100 : 0;

  return (
    <TouchableOpacity style={[styles.listCard, { backgroundColor: colors.surface }]} onPress={onPress}>
      <View style={styles.listCardHeader}>
        <View style={styles.listCardIcon}>
          <View style={[styles.iconContainer, { backgroundColor: list.color }]}>
            <Ionicons name={list.icon as any} size={24} color="white" />
          </View>
        </View>
        
        <View style={styles.listCardInfo}>
          <Text style={[styles.listCardTitle, { color: colors.text }]}>{list.name}</Text>
          {list.description && (
            <Text style={[styles.listCardDescription, { color: colors.textSecondary }]} numberOfLines={1}>
              {list.description}
            </Text>
          )}
        </View>

        <TouchableOpacity
          style={styles.deleteButton}
          onPress={onDelete}
        >
          <Ionicons name="trash-outline" size={20} color={colors.textSecondary} />
        </TouchableOpacity>
      </View>

      <View style={styles.listCardStats}>
        <View style={styles.statsRow}>
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: colors.text }]}>{totalItems}</Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Items</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: colors.text }]}>{completedItems}</Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Done</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: colors.text }]}>${(list.totalPrice || 0).toFixed(2)}</Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Total</Text>
          </View>
        </View>

        <View style={styles.progressContainer}>
          <View style={[styles.progressBar, { backgroundColor: colors.border }]}>
            <View 
              style={[
                styles.progressFill, 
                { backgroundColor: list.color, width: `${progress}%` }
              ]} 
            />
          </View>
          <Text style={[styles.progressText, { color: colors.textSecondary }]}>
            {progress.toFixed(0)}% Complete
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export const ShoppingListScreen: React.FC = () => {
  const { colors } = useTheme();
  const { shoppingLists, createList, deleteList } = useShopping();
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleCreateList = async (name: string, description: string, color: string, icon: string) => {
    setLoading(true);
    try {
      await createList(name, description, color, icon);
    } catch (error) {
      Alert.alert('Error', 'Failed to create list. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteList = (list: ShoppingList) => {
    Alert.alert(
      'Delete List',
      `Are you sure you want to delete "${list.name}"? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Delete', 
          style: 'destructive',
          onPress: () => deleteList(list.id)
        }
      ]
    );
  };

  const handleListPress = (list: ShoppingList) => {
    // Navigate to list details screen
    console.log('Navigate to list:', list.name);
    // TODO: Implement navigation to list details
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="list" size={64} color={colors.border} />
      <Text style={[styles.emptyTitle, { color: colors.text }]}>
        No Shopping Lists Yet
      </Text>
      <Text style={[styles.emptySubtext, { color: colors.textSecondary }]}>
        Create your first shopping list to get started
      </Text>
      <TouchableOpacity
        style={[styles.createFirstButton, { backgroundColor: colors.primary }]}
        onPress={() => setShowCreateModal(true)}
      >
        <Ionicons name="add" size={20} color="white" />
        <Text style={styles.createFirstButtonText}>Create Your First List</Text>
      </TouchableOpacity>
    </View>
  );

  const renderHeader = () => (
    <View style={[styles.header, { backgroundColor: colors.surface }]}>
      <View style={styles.headerContent}>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Shopping Lists</Text>
        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: colors.primary }]}
          onPress={() => setShowCreateModal(true)}
        >
          <Ionicons name="add" size={24} color="white" />
        </TouchableOpacity>
      </View>
      <Text style={[styles.headerSubtitle, { color: colors.textSecondary }]}>
        {shoppingLists.length} {shoppingLists.length === 1 ? 'list' : 'lists'}
      </Text>
    </View>
  );

  const renderListCard = ({ item }: { item: ShoppingList }) => (
    <ShoppingListCard
      list={item}
      onPress={() => handleListPress(item)}
      onDelete={() => handleDeleteList(item)}
    />
  );

  if (loading) {
    return (
      <View style={[styles.container, styles.loadingContainer, { backgroundColor: colors.background }]}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.text }]}>Creating list...</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {shoppingLists.length === 0 ? (
        renderEmptyState()
      ) : (
        <FlatList
          data={shoppingLists}
          renderItem={renderListCard}
          keyExtractor={(item) => item.id}
          ListHeaderComponent={renderHeader}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
        />
      )}

      <CreateListModal
        visible={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onCreateList={handleCreateList}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  listContainer: {
    padding: 16,
  },
  header: {
    padding: 16,
    marginBottom: 16,
    borderRadius: 12,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerSubtitle: {
    fontSize: 14,
  },
  addButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  listCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  listCardIcon: {
    marginRight: 12,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listCardInfo: {
    flex: 1,
  },
  listCardTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 2,
  },
  listCardDescription: {
    fontSize: 14,
  },
  deleteButton: {
    padding: 8,
  },
  listCardStats: {
    marginTop: 8,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 18,
    fontWeight: '700',
  },
  statLabel: {
    fontSize: 12,
    marginTop: 2,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  progressBar: {
    flex: 1,
    height: 6,
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  progressText: {
    fontSize: 12,
    fontWeight: '500',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 32,
  },
  createFirstButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 25,
    gap: 8,
  },
  createFirstButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  // Modal styles
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  inputSection: {
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    minHeight: 48,
  },
  colorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  colorOption: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedColorOption: {
    borderWidth: 3,
    borderColor: 'white',
  },
  iconGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  iconOption: {
    width: 52,
    height: 52,
    borderRadius: 26,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
  },
  modalFooter: {
    padding: 16,
    borderTopWidth: 1,
  },
  createButton: {
    borderRadius: 8,
    paddingVertical: 14,
    alignItems: 'center',
  },
  createButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});