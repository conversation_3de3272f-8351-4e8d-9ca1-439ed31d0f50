import { NavigatorScreenParams } from '@react-navigation/native';

export type RootStackParamList = {
  Auth: NavigatorScreenParams<AuthStackParamList>;
  Onboarding: undefined;
  Main: NavigatorScreenParams<MainTabParamList>;
};

export type AuthStackParamList = {
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
};

export type MainTabParamList = {
  Home: NavigatorScreenParams<HomeStackParamList>;
  Cookbook: undefined;
  ShoppingList: undefined;
  Profile: undefined;
};

export type HomeStackParamList = {
  HomeMain: undefined;
  RecipeGenerator: {
    cookingTime?: string;
    craving?: string;
    mealType?: string;
    ingredients?: string;
  } | undefined;
  CategoryRecipes: {
    category: 'healthy' | 'desserts' | 'quick' | 'comfort' | 'italian' | 'asian' | 'vegetarian' | 'breakfast' | 'main-courses' | 'international' | 'soups-stews' | 'appetizers-snacks' | 'quick-easy' | 'breakfast-brunch' | 'desserts-baking' | 'vegetarian-vegan' | 'meal-prep-friendly';
  };
  ImportedRecipes: undefined;
  TestIngredients: undefined;
};

export type MainStackParamList = {
  HomeScreen: undefined;
  RecipeDetail: { recipeId: string };
  RecipeGenerator: undefined;
  CookbookScreen: undefined;
  ShoppingListScreen: undefined;
  SettingsScreen: undefined;
  Profile: undefined;
}; 