import React, { useEffect, useState } from 'react';
import { View, Text, ScrollView, StyleSheet, TouchableOpacity } from 'react-native';
import { productDeduplicationService, Product, ProductGroup } from '../services/productDeduplicationService';

interface DeduplicationTestProps {
  visible: boolean;
  onClose: () => void;
}

export const DeduplicationTest: React.FC<DeduplicationTestProps> = ({ visible, onClose }) => {
  const [testResults, setTestResults] = useState<{
    input: Product[];
    output: ProductGroup[];
    stats: {
      inputCount: number;
      outputCount: number;
      accuracy: string;
      processing: string;
    };
  } | null>(null);

  useEffect(() => {
    if (visible) {
      runDeduplicationTest();
    }
  }, [visible]);

  const runDeduplicationTest = () => {
    // Create test data that should deduplicate
    const testProducts: Product[] = [
      {
        id: '1',
        name: 'Anchor Milk 1L',
        price: 3.50,
        store: 'woolworths',
        brand: 'Anchor',
        size: '1L',
        category: 'Dairy',
      },
      {
        id: '2',
        name: 'Anchor 1 Litre Milk',
        price: 3.60,
        store: 'newworld',
        brand: 'Anchor',
        size: '1 Litre',
        category: 'Dairy',
      },
      {
        id: '3',
        name: 'Anchor Milk 1000ml',
        price: 3.40,
        store: 'paknsave',
        brand: 'Anchor',
        size: '1000ml',
        category: 'Dairy',
      },
      {
        id: '4',
        name: 'Meadow Fresh Milk 2L',
        price: 6.20,
        store: 'woolworths',
        brand: 'Meadow Fresh',
        size: '2L',
        category: 'Dairy',
      },
      {
        id: '5',
        name: 'Bread White 700g',
        price: 2.50,
        store: 'woolworths',
        brand: 'Molenberg',
        size: '700g',
        category: 'Bakery',
      },
    ];

    const startTime = performance.now();
    const groups = productDeduplicationService.groupProducts(testProducts);
    const endTime = performance.now();

    setTestResults({
      input: testProducts,
      output: groups,
      stats: {
        inputCount: testProducts.length,
        outputCount: groups.length,
        accuracy: `${Math.round((1 - groups.length / testProducts.length) * 100)}%`,
        processing: `${(endTime - startTime).toFixed(2)}ms`,
      },
    });
  };

  if (!visible || !testResults) {
    return null;
  }

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <Text style={styles.title}>🧪 Deduplication Test Results</Text>
        
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statLabel}>Input Products:</Text>
            <Text style={styles.statValue}>{testResults.stats.inputCount}</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statLabel}>Output Groups:</Text>
            <Text style={styles.statValue}>{testResults.stats.outputCount}</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statLabel}>Deduplication:</Text>
            <Text style={styles.statValue}>{testResults.stats.accuracy}</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statLabel}>Processing:</Text>
            <Text style={styles.statValue}>{testResults.stats.processing}</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>📦 Product Groups:</Text>
        {testResults.output.map((group, index) => (
          <View key={group.id} style={styles.groupContainer}>
            <Text style={styles.groupTitle}>
              {index + 1}. {group.name}
            </Text>
            <Text style={styles.groupInfo}>
              🏪 Stores: {group.stores.join(', ')} ({group.stores.length})
            </Text>
            <Text style={styles.groupInfo}>
              💰 Price: ${group.lowestPrice.toFixed(2)} - ${group.highestPrice.toFixed(2)}
            </Text>
            <Text style={styles.groupInfo}>
              📦 Brand: {group.brand || 'Unknown'} | Size: {group.size || 'Unknown'}
            </Text>
            <Text style={styles.groupInfo}>
              ✅ Available: {group.availableCount}/{group.totalVariants}
            </Text>
            
            <View style={styles.productsContainer}>
              {Object.entries(group.storePrices).map(([store, price]) => (
                <Text key={store} style={styles.productItem}>
                  • {store}: {group.name} - ${price}
                </Text>
              ))}
            </View>
          </View>
        ))}
      </ScrollView>
      
      <TouchableOpacity style={styles.closeButton} onPress={onClose}>
        <Text style={styles.closeButtonText}>Close Test</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.8)',
    zIndex: 1000,
  },
  scrollView: {
    flex: 1,
    padding: 20,
    paddingTop: 60,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 20,
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 10,
    padding: 15,
    marginBottom: 20,
  },
  statItem: {
    alignItems: 'center',
    minWidth: '40%',
    marginBottom: 10,
  },
  statLabel: {
    color: '#ccc',
    fontSize: 12,
  },
  statValue: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 15,
  },
  groupContainer: {
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
  },
  groupTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 8,
  },
  groupInfo: {
    color: '#ccc',
    fontSize: 14,
    marginBottom: 4,
  },
  productsContainer: {
    marginTop: 10,
    paddingLeft: 10,
  },
  productItem: {
    color: '#aaa',
    fontSize: 12,
    marginBottom: 2,
  },
  closeButton: {
    backgroundColor: '#007AFF',
    padding: 15,
    margin: 20,
    borderRadius: 10,
    alignItems: 'center',
  },
  closeButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});