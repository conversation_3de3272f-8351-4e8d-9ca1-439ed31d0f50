import AsyncStorage from '@react-native-async-storage/async-storage';
import { markOnboardingCompleted } from '../utils/storage';

export interface UserPreferences {
  userId: string;
  shoppingMode: 'budgeting' | 'bulk_buying' | 'convenience';
  familySize: '1' | '2' | '3-4' | '5+';
  dietaryPreferences: 'none' | 'vegetarian' | 'vegan' | 'gluten_free';
  notificationSettings: 'all' | 'price_alerts' | 'none';
  preferredStores: string[];
  budgetSettings: {
    weeklyBudget?: number;
    monthlyBudget?: number;
    priceDropThreshold: number; // Percentage
  };
  onboardingCompleted: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ShoppingOptimization {
  totalSavings: number;
  recommendedStores: string[];
  optimizedRoute: string[];
  timeSaved: number;
  costSaved: number;
}

const PREFERENCES_STORAGE_KEY = '@user_preferences';

export class UserPreferencesService {
  private static instance: UserPreferencesService;

  public static getInstance(): UserPreferencesService {
    if (!UserPreferencesService.instance) {
      UserPreferencesService.instance = new UserPreferencesService();
    }
    return UserPreferencesService.instance;
  }

  async saveUserPreferences(preferences: Partial<UserPreferences>): Promise<boolean> {
    try {
      const currentPreferences = await this.getUserPreferences(preferences.userId!);
      
      const updatedPreferences: UserPreferences = {
        ...currentPreferences,
        ...preferences,
        updatedAt: new Date(),
      };

      await AsyncStorage.setItem(
        `${PREFERENCES_STORAGE_KEY}_${preferences.userId}`,
        JSON.stringify(updatedPreferences)
      );

      console.log('✅ User preferences saved:', updatedPreferences);
      return true;
    } catch (error) {
      console.error('❌ Failed to save user preferences:', error);
      return false;
    }
  }

  async getUserPreferences(userId: string): Promise<UserPreferences> {
    try {
      const stored = await AsyncStorage.getItem(`${PREFERENCES_STORAGE_KEY}_${userId}`);
      
      if (stored) {
        const parsed = JSON.parse(stored);
        return {
          ...parsed,
          createdAt: new Date(parsed.createdAt),
          updatedAt: new Date(parsed.updatedAt),
        };
      }

      // Return default preferences if none exist
      return this.getDefaultPreferences(userId);
    } catch (error) {
      console.error('❌ Failed to get user preferences:', error);
      return this.getDefaultPreferences(userId);
    }
  }

  private getDefaultPreferences(userId: string): UserPreferences {
    return {
      userId,
      shoppingMode: 'budgeting',
      familySize: '2',
      dietaryPreferences: 'none',
      notificationSettings: 'all',
      preferredStores: ['woolworths', 'newworld', 'paknsave'],
      budgetSettings: {
        priceDropThreshold: 10, // 10% drop triggers alert
      },
      onboardingCompleted: false,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  async updateShoppingMode(userId: string, mode: 'budgeting' | 'bulk_buying' | 'convenience'): Promise<boolean> {
    const preferences = await this.getUserPreferences(userId);
    preferences.shoppingMode = mode;
    preferences.updatedAt = new Date();
    
    return await this.saveUserPreferences(preferences);
  }

  async updateNotificationSettings(userId: string, settings: 'all' | 'price_alerts' | 'none'): Promise<boolean> {
    const preferences = await this.getUserPreferences(userId);
    preferences.notificationSettings = settings;
    preferences.updatedAt = new Date();
    
    return await this.saveUserPreferences(preferences);
  }

  async updateBudgetSettings(userId: string, budgetSettings: Partial<UserPreferences['budgetSettings']>): Promise<boolean> {
    const preferences = await this.getUserPreferences(userId);
    preferences.budgetSettings = {
      ...preferences.budgetSettings,
      ...budgetSettings,
    };
    preferences.updatedAt = new Date();
    
    return await this.saveUserPreferences(preferences);
  }

  async completeOnboarding(userId: string, onboardingData: {
    shoppingMode: string;
    familySize: string;
    dietaryPreferences: string;
    notificationSettings: string;
  }): Promise<boolean> {
    try {
      const preferences: UserPreferences = {
        userId,
        shoppingMode: onboardingData.shoppingMode as 'budgeting' | 'bulk_buying' | 'convenience',
        familySize: onboardingData.familySize as '1' | '2' | '3-4' | '5+',
        dietaryPreferences: onboardingData.dietaryPreferences as 'none' | 'vegetarian' | 'vegan' | 'gluten_free',
        notificationSettings: onboardingData.notificationSettings as 'all' | 'price_alerts' | 'none',
        preferredStores: this.getPreferredStores(onboardingData.shoppingMode),
        budgetSettings: this.getBudgetSettings(onboardingData.shoppingMode, onboardingData.familySize),
        onboardingCompleted: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const success = await this.saveUserPreferences(preferences);
      
      if (success) {
        // Mark onboarding as completed globally
        await markOnboardingCompleted();
        console.log('✅ Onboarding completed and preferences saved');
      }
      
      return success;
    } catch (error) {
      console.error('❌ Failed to complete onboarding:', error);
      return false;
    }
  }

  private getPreferredStores(shoppingMode: string): string[] {
    switch (shoppingMode) {
      case 'budgeting':
        return ['paknsave', 'woolworths', 'newworld']; // Cheapest first
      case 'bulk_buying':
        return ['paknsave', 'woolworths', 'newworld']; // Bulk-friendly stores
      case 'convenience':
        return ['woolworths', 'newworld', 'paknsave']; // Convenient locations
      default:
        return ['woolworths', 'newworld', 'paknsave'];
    }
  }

  private getBudgetSettings(shoppingMode: string, familySize: string): UserPreferences['budgetSettings'] {
    const familyMultiplier = {
      '1': 1,
      '2': 1.8,
      '3-4': 3.2,
      '5+': 5,
    }[familySize] || 1;

    const baseBudget = {
      budgeting: 80,
      bulk_buying: 120,
      convenience: 100,
    }[shoppingMode as 'budgeting' | 'bulk_buying' | 'convenience'] || 100;

    return {
      weeklyBudget: Math.round(baseBudget * familyMultiplier),
      monthlyBudget: Math.round(baseBudget * familyMultiplier * 4.33),
      priceDropThreshold: shoppingMode === 'budgeting' ? 5 : 10,
    };
  }

  async getShoppingOptimization(userId: string, cartItems: any[]): Promise<ShoppingOptimization> {
    try {
      const preferences = await this.getUserPreferences(userId);
      
      // Mock optimization based on user preferences
      const optimization: ShoppingOptimization = {
        totalSavings: 0,
        recommendedStores: [],
        optimizedRoute: [],
        timeSaved: 0,
        costSaved: 0,
      };

      switch (preferences.shoppingMode) {
        case 'budgeting':
          optimization.totalSavings = cartItems.length * 2.5; // Mock $2.50 savings per item
          optimization.recommendedStores = ['paknsave', 'woolworths'];
          optimization.optimizedRoute = ['paknsave', 'woolworths'];
          optimization.costSaved = optimization.totalSavings;
          optimization.timeSaved = 0; // May take more time but saves money
          break;
          
        case 'bulk_buying':
          optimization.totalSavings = cartItems.length * 1.8; // Mock $1.80 savings per item
          optimization.recommendedStores = ['paknsave'];
          optimization.optimizedRoute = ['paknsave'];
          optimization.costSaved = optimization.totalSavings;
          optimization.timeSaved = 15; // Fewer stores = time saved
          break;
          
        case 'convenience':
          optimization.totalSavings = cartItems.length * 0.5; // Mock $0.50 savings per item
          optimization.recommendedStores = ['woolworths'];
          optimization.optimizedRoute = ['woolworths'];
          optimization.costSaved = optimization.totalSavings;
          optimization.timeSaved = 30; // Single store = most time saved
          break;
      }

      return optimization;
    } catch (error) {
      console.error('❌ Failed to get shopping optimization:', error);
      return {
        totalSavings: 0,
        recommendedStores: [],
        optimizedRoute: [],
        timeSaved: 0,
        costSaved: 0,
      };
    }
  }

  async isOnboardingCompleted(userId: string): Promise<boolean> {
    const preferences = await this.getUserPreferences(userId);
    return preferences.onboardingCompleted;
  }

  async resetPreferences(userId: string): Promise<boolean> {
    try {
      await AsyncStorage.removeItem(`${PREFERENCES_STORAGE_KEY}_${userId}`);
      console.log('✅ User preferences reset');
      return true;
    } catch (error) {
      console.error('❌ Failed to reset preferences:', error);
      return false;
    }
  }

  async exportPreferences(userId: string): Promise<string | null> {
    try {
      const preferences = await this.getUserPreferences(userId);
      return JSON.stringify(preferences, null, 2);
    } catch (error) {
      console.error('❌ Failed to export preferences:', error);
      return null;
    }
  }

  async importPreferences(userId: string, preferencesJson: string): Promise<boolean> {
    try {
      const preferences = JSON.parse(preferencesJson);
      preferences.userId = userId; // Ensure correct user ID
      preferences.updatedAt = new Date();
      
      return await this.saveUserPreferences(preferences);
    } catch (error) {
      console.error('❌ Failed to import preferences:', error);
      return false;
    }
  }

  // Mock method for testing
  async testUserPreferences(): Promise<void> {
    console.log('🧪 Testing user preferences...');

    const testUserId = 'test-user-123';
    
    // Test onboarding completion
    const onboardingData = {
      shoppingMode: 'budgeting',
      familySize: '3-4',
      dietaryPreferences: 'vegetarian',
      notificationSettings: 'price_alerts',
    };

    const success = await this.completeOnboarding(testUserId, onboardingData);
    console.log('Onboarding completed:', success);

    // Test getting preferences
    const preferences = await this.getUserPreferences(testUserId);
    console.log('User preferences:', preferences);

    // Test shopping optimization
    const mockCartItems = [
      { name: 'Milk', price: 4.50 },
      { name: 'Bread', price: 2.99 },
      { name: 'Eggs', price: 5.99 },
    ];

    const optimization = await this.getShoppingOptimization(testUserId, mockCartItems);
    console.log('Shopping optimization:', optimization);

    console.log('✅ User preferences test completed');
  }
}