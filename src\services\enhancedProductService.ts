/**
 * Enhanced Product Service - Combines products from multiple stores
 * 
 * This service aggregates products from different stores and provides
 * enhanced shopping list items with combined pricing information,
 * special offers, and loyalty card prices.
 */

import { supabase } from '../supabase/client';
import { priceComparisonService, SupermarketProduct } from './priceComparisonService';

export interface EnhancedStorePrice {
  store: 'woolworths' | 'newworld' | 'paknsave';
  price?: number;
  specialPrice?: number;
  isSpecial?: boolean;
  available: boolean;
  brand?: string;
  size?: string;
  imageUrl?: string;
  productId?: string;
}

export interface EnhancedShoppingItem {
  id: string;
  name: string;
  category?: string;
  storePrices: EnhancedStorePrice[];
  bestImage?: string; // Best quality image from any store
  lowestPrice?: number;
  highestPrice?: number;
  savings?: number;
  hasSpecialOffers: boolean;
  quantity?: number;
  unit?: string;
  checked?: boolean;
}

export interface ProductGroup {
  productName: string;
  products: SupermarketProduct[];
  normalizedName: string;
  category?: string;
  brand?: string;
}

class EnhancedProductService {
  /**
   * Normalize product names for matching across stores
   */
  private normalizeProductName(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ') // Remove special characters
      .replace(/\s+/g, ' ') // Normalize whitespace
      .replace(/\b(woolworths|coles|pak'?n'?save|new\s?world)\b/gi, '') // Remove store names
      .replace(/\b(select|essentials|home\s?brand|signature)\b/gi, '') // Remove store brands
      .trim();
  }

  /**
   * Group products by similar names across stores
   */
  private groupSimilarProducts(products: SupermarketProduct[]): ProductGroup[] {
    const groups: Map<string, ProductGroup> = new Map();

    products.forEach(product => {
      const normalizedName = this.normalizeProductName(product.name);
      
      if (!groups.has(normalizedName)) {
        groups.set(normalizedName, {
          productName: product.name, // Use first product's name
          products: [],
          normalizedName,
          category: product.category,
          brand: product.brand
        });
      }

      groups.get(normalizedName)!.products.push(product);
    });

    return Array.from(groups.values())
      .filter(group => group.products.length > 0) // Only include groups with products
      .sort((a, b) => b.products.length - a.products.length); // Sort by number of stores available
  }

  /**
   * Get best quality image from available products
   */
  private getBestImage(products: SupermarketProduct[]): string {
    // Prefer Woolworths images (usually highest quality)
    const woolworthsProduct = products.find(p => p.imageUrl && p.objectID.includes('woolworths'));
    if (woolworthsProduct?.imageUrl) return woolworthsProduct.imageUrl;

    // Then New World
    const newWorldProduct = products.find(p => p.imageUrl && p.objectID.includes('newworld'));
    if (newWorldProduct?.imageUrl) return newWorldProduct.imageUrl;

    // Finally Pak'nSave
    const paknsaveProduct = products.find(p => p.imageUrl && p.objectID.includes('paknsave'));
    if (paknsaveProduct?.imageUrl) return paknsaveProduct.imageUrl;

    // Return any available image
    const anyProduct = products.find(p => p.imageUrl);
    return anyProduct?.imageUrl || '';
  }

  /**
   * Convert ProductGroup to EnhancedShoppingItem
   */
  private convertToShoppingItem(group: ProductGroup): EnhancedShoppingItem {
    const storePrices: EnhancedStorePrice[] = [];
    const storeMap = new Map<string, SupermarketProduct>();

    // Create map of store to best product
    group.products.forEach(product => {
      const storeKey = product.objectID.includes('woolworths') ? 'woolworths' :
                      product.objectID.includes('newworld') ? 'newworld' : 'paknsave';
      
      const existing = storeMap.get(storeKey);
      if (!existing || (product.specialPrice && product.specialPrice < (existing.specialPrice || existing.price))) {
        storeMap.set(storeKey, product);
      }
    });

    // Create store prices for all stores
    ['woolworths', 'newworld', 'paknsave'].forEach(store => {
      const product = storeMap.get(store);
      
      if (product) {
        const displayPrice = product.specialPrice || product.price;
        storePrices.push({
          store: store as 'woolworths' | 'newworld' | 'paknsave',
          price: product.price,
          specialPrice: product.specialPrice,
          isSpecial: product.isSpecial || false,
          available: true,
          brand: product.brand,
          size: product.unit,
          imageUrl: product.imageUrl,
          productId: product.objectID
        });
      } else {
        storePrices.push({
          store: store as 'woolworths' | 'newworld' | 'paknsave',
          available: false
        });
      }
    });

    // Calculate price statistics
    const availablePrices = storePrices
      .filter(sp => sp.available && (sp.specialPrice || sp.price))
      .map(sp => sp.specialPrice || sp.price!);
    
    const lowestPrice = availablePrices.length > 0 ? Math.min(...availablePrices) : undefined;
    const highestPrice = availablePrices.length > 0 ? Math.max(...availablePrices) : undefined;
    const savings = (lowestPrice && highestPrice) ? highestPrice - lowestPrice : undefined;
    const hasSpecialOffers = storePrices.some(sp => sp.isSpecial);

    return {
      id: group.normalizedName,
      name: group.productName,
      category: group.category,
      storePrices,
      bestImage: this.getBestImage(group.products),
      lowestPrice,
      highestPrice,
      savings,
      hasSpecialOffers,
      quantity: 1,
      checked: false
    };
  }

  /**
   * Search and combine products from all stores
   */
  async searchCombinedProducts(
    query: string,
    options: {
      maxResults?: number;
      category?: string;
      sortBy?: 'price' | 'savings' | 'availability';
      showTwoPricesOnly?: boolean;
    } = {}
  ): Promise<EnhancedShoppingItem[]> {
    try {
      console.log(`🔍 Enhanced search for: "${query}" with options:`, options);

      // Search across all stores
      const searchResult = await priceComparisonService.searchProductAcrossStores(query, {
        maxResults: (options.maxResults || 20) * 3, // Get more to account for grouping
        category: options.category,
        sortBy: 'relevance',
        includeOutOfStock: false
      });

      if (searchResult.results.length === 0) {
        console.log(`❌ No products found for: "${query}"`);
        return [];
      }

      // Extract products from search results
      const products = searchResult.results.map(result => result.product);
      
      // Group similar products
      const productGroups = this.groupSimilarProducts(products);
      console.log(`📦 Grouped ${products.length} products into ${productGroups.length} groups`);

      // Convert to enhanced shopping items
      let enhancedItems = productGroups.map(group => this.convertToShoppingItem(group));

      // Apply sorting
      if (options.sortBy === 'price') {
        enhancedItems.sort((a, b) => (a.lowestPrice || Infinity) - (b.lowestPrice || Infinity));
      } else if (options.sortBy === 'savings') {
        enhancedItems.sort((a, b) => (b.savings || 0) - (a.savings || 0));
      } else if (options.sortBy === 'availability') {
        enhancedItems.sort((a, b) => {
          const aAvailable = a.storePrices.filter(sp => sp.available).length;
          const bAvailable = b.storePrices.filter(sp => sp.available).length;
          return bAvailable - aAvailable;
        });
      }

      // Limit results
      if (options.maxResults) {
        enhancedItems = enhancedItems.slice(0, options.maxResults);
      }

      console.log(`✅ Enhanced search complete: ${enhancedItems.length} items with combined pricing`);
      return enhancedItems;

    } catch (error) {
      console.error('Enhanced product search failed:', error);
      return [];
    }
  }

  /**
   * Get shopping list recommendations based on total savings
   */
  async getShoppingListRecommendations(
    items: string[],
    strategy: 'max_savings' | 'convenience' | 'balanced' = 'max_savings'
  ): Promise<{
    items: EnhancedShoppingItem[];
    totalSavings: number;
    recommendedStores: string[];
    summary: {
      totalItems: number;
      averageSavings: number;
      specialOffers: number;
    };
  }> {
    try {
      console.log(`🛒 Getting shopping list recommendations for ${items.length} items`);

      // Search for each item
      const searchPromises = items.map(item => 
        this.searchCombinedProducts(item, { maxResults: 1 })
      );

      const searchResults = await Promise.all(searchPromises);
      const enhancedItems = searchResults
        .filter(results => results.length > 0)
        .map(results => results[0]);

      // Calculate totals
      const totalSavings = enhancedItems.reduce((sum, item) => sum + (item.savings || 0), 0);
      const specialOffers = enhancedItems.filter(item => item.hasSpecialOffers).length;
      const averageSavings = totalSavings / enhancedItems.length;

      // Determine recommended stores based on strategy
      const storeFrequency = new Map<string, number>();
      enhancedItems.forEach(item => {
        const cheapestStore = item.storePrices
          .filter(sp => sp.available)
          .sort((a, b) => (a.specialPrice || a.price!) - (b.specialPrice || b.price!))[0];
        
        if (cheapestStore) {
          storeFrequency.set(cheapestStore.store, (storeFrequency.get(cheapestStore.store) || 0) + 1);
        }
      });

      const recommendedStores = Array.from(storeFrequency.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, strategy === 'convenience' ? 1 : 3)
        .map(([store]) => store);

      return {
        items: enhancedItems,
        totalSavings,
        recommendedStores,
        summary: {
          totalItems: enhancedItems.length,
          averageSavings,
          specialOffers
        }
      };

    } catch (error) {
      console.error('Shopping list recommendations failed:', error);
      return {
        items: [],
        totalSavings: 0,
        recommendedStores: [],
        summary: {
          totalItems: 0,
          averageSavings: 0,
          specialOffers: 0
        }
      };
    }
  }

  /**
   * Test the enhanced product service
   */
  async testService(): Promise<void> {
    console.log('🧪 Testing Enhanced Product Service...');
    
    try {
      // Test with a common product
      const testItems = await this.searchCombinedProducts('milk', { maxResults: 5 });
      console.log(`✅ Test search returned ${testItems.length} enhanced items`);
      
      if (testItems.length > 0) {
        const firstItem = testItems[0];
        console.log('📊 Sample enhanced item:', {
          name: firstItem.name,
          storesAvailable: firstItem.storePrices.filter(sp => sp.available).length,
          lowestPrice: firstItem.lowestPrice,
          savings: firstItem.savings,
          hasSpecialOffers: firstItem.hasSpecialOffers,
          bestImage: firstItem.bestImage ? 'Available' : 'None'
        });
      }

      // Test shopping list recommendations
      const recommendations = await this.getShoppingListRecommendations(['milk', 'bread', 'eggs']);
      console.log('✅ Shopping list recommendations:', {
        totalItems: recommendations.items.length,
        totalSavings: recommendations.totalSavings,
        recommendedStores: recommendations.recommendedStores
      });

    } catch (error) {
      console.error('❌ Enhanced Product Service test failed:', error);
    }
  }
}

export const enhancedProductService = new EnhancedProductService();