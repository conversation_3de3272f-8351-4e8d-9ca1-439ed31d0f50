import { supabase } from './client';
import * as fs from 'fs';
import * as path from 'path';

const RECIPES_FILE_PATH = path.join(__dirname, '../../processed-all-complete-recipes.json');

// Define an interface for the recipe structure
interface Recipe {
  objectID: string;
  title: string;
  description: string;
  ingredients: string[];
  instructions: string[];
  prepTime: string;
  cookTime: string;
  totalTime: string;
  servings: string;
  difficulty: string;
  cuisine: string;
  author: string;
  imageUrl: string;
  url: string;
  category: string;
  tags: string[];
  rating: string;
  calories: string;
  createdAt: string;
}

async function migrateRecipes() {
  console.log('Starting recipe migration...');

  try {
    // Read the recipes from the JSON file
    const recipesData = fs.readFileSync(RECIPES_FILE_PATH, 'utf-8');
    const recipes: Recipe[] = JSON.parse(recipesData);

    console.log(`Found ${recipes.length} recipes to migrate.`);

    // Prepare the data for Supabase
    const supabaseRecipes = recipes.map((recipe: Recipe) => ({
      objectID: recipe.objectID,
      title: recipe.title,
      description: recipe.description,
      ingredients: recipe.ingredients,
      instructions: recipe.instructions,
      prep_time: recipe.prepTime,
      cook_time: recipe.cookTime,
      total_time: recipe.totalTime,
      servings: recipe.servings,
      difficulty: recipe.difficulty,
      cuisine: recipe.cuisine,
      author: recipe.author,
      image_url: recipe.imageUrl,
      source_url: recipe.url,
      category: recipe.category,
      tags: recipe.tags,
      rating: recipe.rating,
      calories: recipe.calories,
      created_at: recipe.createdAt,
    }));

    // Insert the data into the recipes table
    const { error } = await supabase.from('recipes').insert(supabaseRecipes);

    if (error) {
      console.error('Error migrating recipes:', error);
    } else {
      console.log('Recipe migration completed successfully.');
      console.log(`Inserted ${supabaseRecipes.length} recipes.`);
    }
  } catch (error) {
    console.error('An error occurred during migration:', error);
  }
}

migrateRecipes(); 