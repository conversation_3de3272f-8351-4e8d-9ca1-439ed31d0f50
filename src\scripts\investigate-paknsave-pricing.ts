/**
 * INVESTIGATE PAK'NSAVE PRICING ISSUES
 * 
 * Analyzes Pak'nSave product data to identify pricing and size-related issues
 * that may be causing incorrect price calculations or comparisons.
 */

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://emjwniuqwhvohjassnrg.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVtanduaXVxd2h2b2hqYXNzbnJnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjQwMTYwNSwiZXhwIjoyMDY3OTc3NjA1fQ.oTowRoNAjlUmk10O9pSMK2BvL0XlyBb5orVRrlEryHs';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function investigatePaknsavePricing() {
  console.log('🔍 INVESTIGATING PAK\'NSAVE PRICING ISSUES...\n');

  try {
    // 1. Analyze the suspicious high-priced Pak'nSave products
    console.log('1. Analyzing High-Priced Pak\'nSave Products...');
    const { data: highPriced, error: highPricedError } = await supabase
      .from('product_matches')
      .select('*')
      .gte('pakn_price', 30)
      .order('pakn_price', { ascending: false })
      .limit(10);

    if (highPricedError) {
      console.error('Error fetching high-priced products:', highPricedError);
      return;
    }

    if (highPriced && highPriced.length > 0) {
      console.log('   High-priced Pak\'nSave products:');
      highPriced.forEach((product, i) => {
        console.log(`   ${i+1}. ${product.pakn_name}`);
        console.log(`      Pak'nSave: $${product.pakn_price} | New World: $${product.neww_price || 'N/A'} | Woolworths: $${product.wool_price || 'N/A'}`);
        console.log(`      Sizes: PS=${product.pakn_size || 'N/A'} | NW=${product.neww_size || 'N/A'} | WW=${product.wool_size || 'N/A'}`);
        console.log(`      Max Savings: $${product.max_savings || 0}`);
        console.log('');
      });
    } else {
      console.log('   No high-priced products found in matches');
    }

    // 2. Check for size inconsistencies in matches
    console.log('2. Checking Size Consistency in Matches...');
    const { data: allMatches } = await supabase
      .from('product_matches')
      .select('*')
      .not('neww_price', 'is', null)
      .limit(100);

    if (allMatches) {
      let sizeInconsistencies = 0;
      const inconsistentProducts = [];

      allMatches.forEach(product => {
        const paknSize = product.pakn_size || '';
        const newwSize = product.neww_size || '';
        const woolSize = product.wool_size || '';

        // Check if sizes are different (ignoring empty sizes)
        if (paknSize && newwSize && paknSize !== newwSize) {
          sizeInconsistencies++;
          inconsistentProducts.push(product);
        }
        if (paknSize && woolSize && paknSize !== woolSize) {
          sizeInconsistencies++;
          inconsistentProducts.push(product);
        }
      });

      console.log(`   Found ${sizeInconsistencies} size inconsistencies in ${allMatches.length} matches`);
      
      if (inconsistentProducts.length > 0) {
        console.log('   Sample size inconsistencies:');
        inconsistentProducts.slice(0, 5).forEach((product, i) => {
          console.log(`   ${i+1}. ${product.pakn_name}`);
          console.log(`      Sizes: PS=${product.pakn_size} | NW=${product.neww_size} | WW=${product.wool_size}`);
          console.log(`      Prices: PS=$${product.pakn_price} | NW=$${product.neww_price} | WW=$${product.wool_price}`);
          console.log('');
        });
      }
    }

    // 3. Analyze Pak'nSave product data directly
    console.log('3. Analyzing Pak\'nSave Product Data...');
    const { data: paknProducts } = await supabase
      .from('products')
      .select('*')
      .eq('store', 'paknsave')
      .gte('price', 30)
      .order('price', { ascending: false })
      .limit(15);

    if (paknProducts && paknProducts.length > 0) {
      console.log('   High-priced Pak\'nSave products in database:');
      paknProducts.forEach((product, i) => {
        console.log(`   ${i+1}. ${product.name}`);
        console.log(`      Price: $${product.price} | Size: ${product.size || product.unit || 'N/A'}`);
        console.log(`      Category: ${product.category || 'N/A'} | Brand: ${product.brand || 'N/A'}`);
        
        // Calculate unit price if possible
        const size = product.size || product.unit || '';
        let unitPrice = 'N/A';
        
        const sizeMatch = size.match(/(\d+(?:\.\d+)?)\s*(ml|l|g|kg|pack)/i);
        if (sizeMatch) {
          const value = parseFloat(sizeMatch[1]);
          const unit = sizeMatch[2].toLowerCase();
          
          if (unit === 'l') {
            unitPrice = `$${(product.price / value).toFixed(2)}/L`;
          } else if (unit === 'ml') {
            unitPrice = `$${(product.price / value * 1000).toFixed(2)}/L`;
          } else if (unit === 'kg') {
            unitPrice = `$${(product.price / value).toFixed(2)}/kg`;
          } else if (unit === 'g') {
            unitPrice = `$${(product.price / value * 1000).toFixed(2)}/kg`;
          } else if (unit === 'pack') {
            unitPrice = `$${(product.price / value).toFixed(2)}/item`;
          }
        }
        
        console.log(`      Unit Price: ${unitPrice}`);
        console.log('');
      });
    } else {
      console.log('   No high-priced Pak\'nSave products found');
    }

    // 4. Check specific $34.99 products
    console.log('4. Investigating $34.99 Products...');
    const { data: specificProducts } = await supabase
      .from('products')
      .select('*')
      .eq('store', 'paknsave')
      .eq('price', 34.99)
      .limit(10);

    if (specificProducts && specificProducts.length > 0) {
      console.log(`   Found ${specificProducts.length} products priced at $34.99:`);
      specificProducts.forEach((product, i) => {
        console.log(`   ${i+1}. ${product.name}`);
        console.log(`      Size: ${product.size || product.unit || 'N/A'}`);
        console.log(`      Category: ${product.category || 'N/A'}`);
        console.log(`      ID: ${product.id}`);
        console.log('');
      });
    }

    // 5. Compare similar products across stores
    console.log('5. Comparing Similar Products Across Stores...');
    const { data: crossStoreComparison } = await supabase
      .from('products')
      .select('*')
      .ilike('name', '%milk%')
      .not('price', 'is', null)
      .order('name')
      .limit(30);

    if (crossStoreComparison) {
      const productGroups = new Map();
      
      crossStoreComparison.forEach(product => {
        const normalizedName = product.name.toLowerCase()
          .replace(/\b(fresh|premium|select|value)\b/g, '')
          .replace(/[^\w\s]/g, ' ')
          .replace(/\s+/g, ' ')
          .trim();
        
        if (!productGroups.has(normalizedName)) {
          productGroups.set(normalizedName, []);
        }
        productGroups.get(normalizedName).push(product);
      });

      console.log('   Cross-store price comparison for similar products:');
      let groupCount = 0;
      for (const [name, products] of productGroups) {
        if (products.length > 1 && groupCount < 5) {
          console.log(`   Group: "${name}"`);
          products.forEach(product => {
            console.log(`     ${product.store}: $${product.price} (${product.size || product.unit || 'N/A'})`);
          });
          console.log('');
          groupCount++;
        }
      }
    }

    console.log('✅ Pak\'nSave pricing investigation completed!');

  } catch (error) {
    console.error('❌ Investigation failed:', error);
  }
}

// Run the investigation
if (require.main === module) {
  investigatePaknsavePricing()
    .then(() => {
      console.log('\n📊 INVESTIGATION SUMMARY:');
      console.log('='.repeat(60));
      console.log('1. Analyzed high-priced Pak\'nSave products');
      console.log('2. Checked for size inconsistencies in matches');
      console.log('3. Examined unit price calculations');
      console.log('4. Investigated specific pricing anomalies');
      console.log('5. Compared cross-store pricing patterns');
      console.log('='.repeat(60));
    })
    .catch((error) => {
      console.error('❌ Investigation failed:', error);
    });
}

export { investigatePaknsavePricing };
