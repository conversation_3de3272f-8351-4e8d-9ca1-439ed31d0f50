/**
 * Data Cleaning and Unit Normalization Service
 * Implements comprehensive data cleaning techniques from the research blueprint:
 * - Text normalization and cleanup
 * - Unit of measure standardization
 * - Price normalization for accurate comparison
 * - Data quality validation
 */

import { supabase } from '../supabase/client';

export interface CleaningResult {
  success: boolean;
  processed_count: number;
  cleaned_count: number;
  error_count: number;
  errors: string[];
  execution_time_ms: number;
}

export interface UnitConversionRule {
  pattern: RegExp;
  standardUnit: string;
  conversionFactor: number;
  category: 'weight' | 'volume' | 'count' | 'length';
}

export interface NormalizedProduct {
  original_name: string;
  cleaned_name: string;
  extracted_size?: string;
  extracted_unit?: string;
  normalized_unit?: string;
  unit_quantity?: number;
  normalized_price_per_unit?: number;
}

class DataCleaningService {
  private readonly UNIT_CONVERSION_RULES: UnitConversionRule[] = [
    // Weight conversions
    { pattern: /(\d+(?:\.\d+)?)\s*kg\b/gi, standardUnit: 'kg', conversionFactor: 1, category: 'weight' },
    { pattern: /(\d+(?:\.\d+)?)\s*g\b/gi, standardUnit: 'kg', conversionFactor: 0.001, category: 'weight' },
    { pattern: /(\d+(?:\.\d+)?)\s*gram[s]?\b/gi, standardUnit: 'kg', conversionFactor: 0.001, category: 'weight' },
    { pattern: /(\d+(?:\.\d+)?)\s*kilogram[s]?\b/gi, standardUnit: 'kg', conversionFactor: 1, category: 'weight' },
    
    // Volume conversions  
    { pattern: /(\d+(?:\.\d+)?)\s*l\b/gi, standardUnit: 'L', conversionFactor: 1, category: 'volume' },
    { pattern: /(\d+(?:\.\d+)?)\s*ml\b/gi, standardUnit: 'L', conversionFactor: 0.001, category: 'volume' },
    { pattern: /(\d+(?:\.\d+)?)\s*litre[s]?\b/gi, standardUnit: 'L', conversionFactor: 1, category: 'volume' },
    { pattern: /(\d+(?:\.\d+)?)\s*millilitre[s]?\b/gi, standardUnit: 'L', conversionFactor: 0.001, category: 'volume' },
    
    // Count conversions
    { pattern: /(\d+(?:\.\d+)?)\s*pack[s]?\b/gi, standardUnit: 'each', conversionFactor: 1, category: 'count' },
    { pattern: /(\d+(?:\.\d+)?)\s*each\b/gi, standardUnit: 'each', conversionFactor: 1, category: 'count' },
    { pattern: /(\d+(?:\.\d+)?)\s*dozen\b/gi, standardUnit: 'each', conversionFactor: 12, category: 'count' },
  ];

  private readonly CLEANING_PATTERNS = {
    // Remove marketing terms
    marketing_terms: /\b(new|sale|special|offer|limited|fresh|premium|select|choice|value|best|top|super|ultra|extra|max|plus)\b/gi,
    
    // Remove HTML tags
    html_tags: /<[^>]*>/g,
    
    // Normalize whitespace
    excess_whitespace: /\s+/g,
    
    // Remove special characters (but preserve essential ones)
    special_chars: /[^\w\s\-\.\,\(\)\%\/&]/g,
    
    // Price cleaning patterns
    currency_symbols: /[$£€¥₹₽]/g,
    price_format: /\$?(\d+(?:\.\d{2})?)/g
  };

  /**
   * Clean and normalize product data from scraped listings
   */
  async cleanScrapedProductData(options?: {
    batchSize?: number;
    skipMatched?: boolean;
  }): Promise<CleaningResult> {
    const startTime = performance.now();
    const batchSize = options?.batchSize || 1000;
    const skipMatched = options?.skipMatched ?? true;
    
    let processedCount = 0;
    let cleanedCount = 0;
    let errorCount = 0;
    const errors: string[] = [];

    try {
      console.log('🧹 Starting data cleaning process...');
      
      // Get unprocessed scraped listings
      let query = supabase
        .from('store_product_listings')
        .select('id, scraped_product_name, scraped_description, scraped_price, scraped_size_info');

      if (skipMatched) {
        query = query.eq('is_matched', false);
      }

      const { data: listings, error: fetchError } = await query.limit(batchSize);

      if (fetchError) {
        throw new Error(`Failed to fetch listings: ${fetchError.message}`);
      }

      if (!listings || listings.length === 0) {
        console.log('No listings to clean');
        return {
          success: true,
          processed_count: 0,
          cleaned_count: 0,
          error_count: 0,
          errors: [],
          execution_time_ms: performance.now() - startTime
        };
      }

      console.log(`📋 Processing ${listings.length} product listings...`);

      // Process each listing
      for (const listing of listings) {
        try {
          processedCount++;
          
          const cleaned = await this.cleanProductListing(listing);
          
          if (cleaned.was_cleaned) {
            // Update the listing with cleaned data
            const { error: updateError } = await supabase
              .from('store_product_listings')
              .update({
                scraped_product_name: cleaned.cleaned_name,
                scraped_description: cleaned.cleaned_description,
                scraped_price: cleaned.cleaned_price,
                scraped_size_info: cleaned.cleaned_size_info,
                // Add metadata about cleaning
                meta_data: {
                  ...((listing as any).meta_data || {}),
                  cleaned_at: new Date().toISOString(),
                  original_name: listing.scraped_product_name,
                  cleaning_applied: true
                }
              })
              .eq('id', listing.id);

            if (updateError) {
              throw new Error(`Failed to update listing ${listing.id}: ${updateError.message}`);
            }

            cleanedCount++;
          }

          // Log progress every 100 items
          if (processedCount % 100 === 0) {
            console.log(`📊 Progress: ${processedCount}/${listings.length} processed, ${cleanedCount} cleaned`);
          }

        } catch (error) {
          errorCount++;
          const errorMessage = `Error processing listing ${listing.id}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          errors.push(errorMessage);
          console.error(errorMessage);
        }
      }

      const endTime = performance.now();
      const executionTime = endTime - startTime;

      console.log(`✅ Data cleaning completed in ${executionTime.toFixed(2)}ms`);
      console.log(`📈 Results: ${processedCount} processed, ${cleanedCount} cleaned, ${errorCount} errors`);

      return {
        success: errorCount === 0 || (errorCount / processedCount) < 0.1, // Success if < 10% error rate
        processed_count: processedCount,
        cleaned_count: cleanedCount,
        error_count: errorCount,
        errors: errors.slice(0, 10), // Limit errors to first 10
        execution_time_ms: executionTime
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error in cleaning process';
      console.error('❌ Critical error in data cleaning:', errorMessage);
      
      return {
        success: false,
        processed_count: processedCount,
        cleaned_count: cleanedCount,
        error_count: errorCount + 1,
        errors: [...errors, errorMessage],
        execution_time_ms: performance.now() - startTime
      };
    }
  }

  /**
   * Clean a single product listing
   */
  private async cleanProductListing(listing: any): Promise<{
    was_cleaned: boolean;
    cleaned_name: string;
    cleaned_description?: string;
    cleaned_price?: number;
    cleaned_size_info?: string;
    normalized_data?: NormalizedProduct;
  }> {
    let wasChanged = false;
    
    // Clean product name
    const originalName = listing.scraped_product_name || '';
    let cleanedName = this.cleanText(originalName);
    if (cleanedName !== originalName) wasChanged = true;

    // Clean description
    let cleanedDescription = listing.scraped_description;
    if (cleanedDescription) {
      const originalDescription = cleanedDescription;
      cleanedDescription = this.cleanText(cleanedDescription);
      if (cleanedDescription !== originalDescription) wasChanged = true;
    }

    // Clean and validate price
    let cleanedPrice = listing.scraped_price;
    if (typeof listing.scraped_price === 'string') {
      const originalPriceStr = listing.scraped_price;
      cleanedPrice = this.extractAndCleanPrice(originalPriceStr);
      if (cleanedPrice !== parseFloat(originalPriceStr)) wasChanged = true;
    }

    // Clean size information
    let cleanedSizeInfo = listing.scraped_size_info;
    if (cleanedSizeInfo) {
      const originalSizeInfo = cleanedSizeInfo;
      cleanedSizeInfo = this.cleanText(cleanedSizeInfo);
      if (cleanedSizeInfo !== originalSizeInfo) wasChanged = true;
    }

    // Extract and normalize units
    const normalizedData = this.extractAndNormalizeUnits(cleanedName, cleanedSizeInfo, cleanedPrice);

    return {
      was_cleaned: wasChanged,
      cleaned_name: cleanedName,
      cleaned_description: cleanedDescription,
      cleaned_price: cleanedPrice,
      cleaned_size_info: cleanedSizeInfo,
      normalized_data: normalizedData
    };
  }

  /**
   * Clean text by removing unwanted patterns and normalizing
   */
  private cleanText(text: string): string {
    if (!text) return '';

    let cleaned = text;

    // Remove HTML tags
    cleaned = cleaned.replace(this.CLEANING_PATTERNS.html_tags, '');

    // Remove marketing terms
    cleaned = cleaned.replace(this.CLEANING_PATTERNS.marketing_terms, ' ');

    // Remove special characters but keep essential ones
    cleaned = cleaned.replace(this.CLEANING_PATTERNS.special_chars, ' ');

    // Normalize whitespace
    cleaned = cleaned.replace(this.CLEANING_PATTERNS.excess_whitespace, ' ');

    // Trim and convert to proper case
    cleaned = cleaned.trim();
    
    // Convert to title case for consistency
    cleaned = cleaned.toLowerCase().replace(/\b\w/g, l => l.toUpperCase());

    return cleaned;
  }

  /**
   * Extract and clean price from string
   */
  private extractAndCleanPrice(priceStr: string): number | null {
    if (!priceStr) return null;

    // Remove currency symbols
    let cleaned = priceStr.replace(this.CLEANING_PATTERNS.currency_symbols, '');
    
    // Extract numeric price
    const match = cleaned.match(/(\d+(?:\.\d{1,2})?)/);
    if (match) {
      const price = parseFloat(match[1]);
      return isNaN(price) ? null : price;
    }

    return null;
  }

  /**
   * Extract and normalize units from product name and size info
   */
  private extractAndNormalizeUnits(
    productName: string, 
    sizeInfo?: string, 
    price?: number
  ): NormalizedProduct {
    const textToAnalyze = `${productName} ${sizeInfo || ''}`.toLowerCase();
    
    let extractedSize: string | undefined;
    let extractedUnit: string | undefined;
    let normalizedUnit: string | undefined;
    let unitQuantity: number | undefined;
    let normalizedPricePerUnit: number | undefined;

    // Try to match unit patterns
    for (const rule of this.UNIT_CONVERSION_RULES) {
      const match = textToAnalyze.match(rule.pattern);
      if (match && match[1]) {
        const quantity = parseFloat(match[1]);
        if (!isNaN(quantity)) {
          extractedSize = match[0];
          extractedUnit = rule.standardUnit;
          normalizedUnit = rule.standardUnit;
          unitQuantity = quantity * rule.conversionFactor;
          
          // Calculate normalized price per unit
          if (price && price > 0 && unitQuantity > 0) {
            normalizedPricePerUnit = price / unitQuantity;
          }
          
          break; // Use first match
        }
      }
    }

    return {
      original_name: productName,
      cleaned_name: productName,
      extracted_size: extractedSize,
      extracted_unit: extractedUnit,
      normalized_unit: normalizedUnit,
      unit_quantity: unitQuantity,
      normalized_price_per_unit: normalizedPricePerUnit
    };
  }

  /**
   * Normalize prices for comparison across different package sizes
   */
  async normalizeProductPricesForComparison(): Promise<CleaningResult> {
    const startTime = performance.now();
    
    try {
      console.log('💰 Starting price normalization...');
      
      // Get products with pricing data
      const { data: products, error } = await supabase
        .from('product_store_prices')
        .select(`
          id,
          product_id,
          price,
          scraped_listing_id,
          store_product_listings (
            scraped_product_name,
            scraped_size_info
          )
        `);

      if (error) {
        throw new Error(`Failed to fetch product prices: ${error.message}`);
      }

      if (!products || products.length === 0) {
        return {
          success: true,
          processed_count: 0,
          cleaned_count: 0,
          error_count: 0,
          errors: [],
          execution_time_ms: performance.now() - startTime
        };
      }

      let processedCount = 0;
      let cleanedCount = 0;
      const errors: string[] = [];

      // Process each product price
      for (const product of products) {
        try {
          processedCount++;
          
          const listing = (product as any).store_product_listings;
          if (!listing) continue;

          const normalized = this.extractAndNormalizeUnits(
            listing.scraped_product_name,
            listing.scraped_size_info,
            product.price
          );

          if (normalized.normalized_price_per_unit && normalized.normalized_unit) {
            // Update the product price with normalized data
            const { error: updateError } = await supabase
              .from('product_store_prices')
              .update({
                unit_price: normalized.normalized_price_per_unit,
                unit_of_measure: normalized.normalized_unit
              })
              .eq('id', product.id);

            if (updateError) {
              throw new Error(`Failed to update price normalization for ${product.id}: ${updateError.message}`);
            }

            cleanedCount++;
          }

        } catch (error) {
          const errorMessage = `Error normalizing price for product ${product.id}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          errors.push(errorMessage);
          console.error(errorMessage);
        }
      }

      const executionTime = performance.now() - startTime;
      
      console.log(`✅ Price normalization completed in ${executionTime.toFixed(2)}ms`);
      console.log(`📊 Results: ${processedCount} processed, ${cleanedCount} normalized`);

      return {
        success: true,
        processed_count: processedCount,
        cleaned_count: cleanedCount,
        error_count: errors.length,
        errors: errors.slice(0, 10),
        execution_time_ms: executionTime
      };

    } catch (error) {
      console.error('❌ Error in price normalization:', error);
      return {
        success: false,
        processed_count: 0,
        cleaned_count: 0,
        error_count: 1,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
        execution_time_ms: performance.now() - startTime
      };
    }
  }

  /**
   * Get data quality metrics
   */
  async getDataQualityMetrics(): Promise<{
    total_listings: number;
    cleaned_listings: number;
    price_normalized: number;
    missing_prices: number;
    missing_names: number;
    duplicate_names: number;
    quality_score: number;
  }> {
    try {
      const [
        totalResult,
        cleanedResult,
        normalizedResult,
        missingPricesResult,
        missingNamesResult
      ] = await Promise.all([
        supabase.from('store_product_listings').select('id', { count: 'exact', head: true }),
        supabase.from('store_product_listings').select('id', { count: 'exact', head: true }).not('meta_data->cleaning_applied', 'is', null),
        supabase.from('product_store_prices').select('id', { count: 'exact', head: true }).not('unit_price', 'is', null),
        supabase.from('store_product_listings').select('id', { count: 'exact', head: true }).is('scraped_price', null),
        supabase.from('store_product_listings').select('id', { count: 'exact', head: true }).is('scraped_product_name', null)
      ]);

      const totalListings = totalResult.count || 0;
      const cleanedListings = cleanedResult.count || 0;
      const normalizedPrices = normalizedResult.count || 0;
      const missingPrices = missingPricesResult.count || 0;
      const missingNames = missingNamesResult.count || 0;

      // Calculate quality score (0-100)
      const qualityFactors = [
        totalListings > 0 ? (cleanedListings / totalListings) * 30 : 0, // 30% weight for cleaning
        totalListings > 0 ? (normalizedPrices / totalListings) * 25 : 0, // 25% weight for price normalization
        totalListings > 0 ? ((totalListings - missingPrices) / totalListings) * 25 : 0, // 25% weight for price completeness
        totalListings > 0 ? ((totalListings - missingNames) / totalListings) * 20 : 0, // 20% weight for name completeness
      ];

      const qualityScore = Math.round(qualityFactors.reduce((sum, factor) => sum + factor, 0));

      return {
        total_listings: totalListings,
        cleaned_listings: cleanedListings,
        price_normalized: normalizedPrices,
        missing_prices: missingPrices,
        missing_names: missingNames,
        duplicate_names: 0, // TODO: Implement duplicate detection
        quality_score: qualityScore
      };

    } catch (error) {
      console.error('Error getting data quality metrics:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const dataCleaningService = new DataCleaningService();