import React, { useState, useCallback, memo } from 'react';
import {
  Image,
  View,
  StyleSheet,
  ImageStyle,
  ViewStyle,
  ImageSourcePropType,
} from 'react-native';

interface OptimizedImageProps {
  source: ImageSourcePropType;
  style?: ImageStyle;
  placeholder?: React.ReactNode;
  onError?: () => void;
  onLoad?: () => void;
  resizeMode?: 'cover' | 'contain' | 'stretch' | 'repeat' | 'center';
  fadeDuration?: number;
}

const OptimizedImage: React.FC<OptimizedImageProps> = memo(({
  source,
  style,
  placeholder,
  onError,
  onLoad,
  resizeMode = 'contain',
  fadeDuration = 300,
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const handleLoad = useCallback(() => {
    setIsLoading(false);
    onLoad?.();
  }, [onLoad]);

  const handleError = useCallback(() => {
    setIsLoading(false);
    setHasError(true);
    onError?.();
  }, [onError]);

  if (hasError) {
    return placeholder ? <>{placeholder}</> : null;
  }

  return (
    <View style={style}>
      {isLoading && placeholder && (
        <View style={[StyleSheet.absoluteFill, styles.placeholder]}>
          {placeholder}
        </View>
      )}
      <Image
        source={source}
        style={[style, isLoading && styles.hidden]}
        resizeMode={resizeMode}
        onLoad={handleLoad}
        onError={handleError}
        fadeDuration={fadeDuration}
        // Performance optimizations
        progressiveRenderingEnabled={true}
      />
    </View>
  );
});

OptimizedImage.displayName = 'OptimizedImage';

const styles = StyleSheet.create({
  placeholder: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  hidden: {
    opacity: 0,
  },
});

export { OptimizedImage };
