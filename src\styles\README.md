# 🎨 Centralized Theme System

The AI Recipe Planner app uses a comprehensive centralized theme system that provides consistent styling, easy maintenance, and built-in dark mode support.

## 📁 File Structure

```
src/styles/
├── index.ts          # Main export file - import everything from here
├── theme.ts          # Core theme tokens (colors, typography, spacing, etc.)
├── commonStyles.ts   # Reusable common styles (buttons, cards, text, etc.)
├── recipeStyles.ts   # Recipe-specific styles (cards, badges, nutrition info)
├── examples.ts       # Usage examples and patterns
└── README.md         # This documentation
```

## 🚀 Quick Start

### Basic Import
```typescript
import { theme, commonStyles, recipeStyles } from '../styles';
```

### Using Theme Tokens
```typescript
import { theme, colors, spacing, typography } from '../styles';

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.light.background,
    padding: theme.spacing.base,
  },
  title: {
    fontSize: theme.typography.fontSize['2xl'],
    fontWeight: theme.typography.fontWeight.bold,
    color: theme.colors.light.text,
  },
});
```

### Theme-Aware Components
```typescript
import { getThemeColors, ThemeMode } from '../styles';

const MyComponent = ({ themeMode = 'light' }: { themeMode?: ThemeMode }) => {
  const colors = getThemeColors(themeMode);
  
  return (
    <View style={{ backgroundColor: colors.background }}>
      <Text style={{ color: colors.text }}>Hello World</Text>
    </View>
  );
};
```

## 🎯 Core Theme Tokens

### Colors
- **Primary**: `#007AFF` (iOS blue)
- **Secondary**: `#FF6B35` (Food-inspired orange)
- **Accent**: `#34C759` (Success green)
- **Semantic**: Success, warning, error, info colors
- **Light/Dark**: Complete color schemes for both modes

### Typography
- **Font Sizes**: xs (12px) → 5xl (48px)
- **Font Weights**: normal, medium, semibold, bold
- **Line Heights**: Matching each font size
- **Platform Fonts**: System fonts for iOS/Android

### Spacing
- **Scale**: xs (4px) → 8xl (128px)
- **Based on 4px grid** for consistent spacing

### Border Radius
- **Scale**: xs (2px) → full (9999px)
- **Consistent rounded corners** throughout the app

### Shadows
- **Cross-platform**: iOS shadows + Android elevation
- **Multiple levels**: sm, base, lg, xl
- **Theme-aware**: Shadow colors adapt to light/dark mode

## 🍳 Recipe-Specific Features

### Difficulty Colors
```typescript
theme.recipeColors.difficulty.easy    // Green
theme.recipeColors.difficulty.medium  // Orange
theme.recipeColors.difficulty.hard    // Red
```

### Meal Type Colors
```typescript
theme.recipeColors.mealType.breakfast // Orange
theme.recipeColors.mealType.lunch     // Green
theme.recipeColors.mealType.dinner    // Blue
theme.recipeColors.mealType.dessert   // Purple
```

### Nutrition Colors
```typescript
theme.recipeColors.nutrition.calories // Orange
theme.recipeColors.nutrition.protein  // Blue
theme.recipeColors.nutrition.carbs    // Green
theme.recipeColors.nutrition.fat      // Yellow-orange
```

## 🎛️ Usage Patterns

### 1. Static Styles (Light Mode Only)
```typescript
const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.light.background,
    padding: theme.spacing.base,
  },
});
```

### 2. Theme-Aware Styles
```typescript
import { getThemeColors } from '../styles';

const getStyles = (themeMode: ThemeMode) => {
  const colors = getThemeColors(themeMode);
  
  return StyleSheet.create({
    container: {
      backgroundColor: colors.background,
      padding: theme.spacing.base,
    },
  });
};
```

### 3. Using createThemedStyles Helper
```typescript
import { createThemedStyles } from '../styles';

const useStyles = createThemedStyles((theme, colors) => ({
  container: {
    backgroundColor: colors.background,
    padding: theme.spacing.base,
  },
  card: {
    backgroundColor: colors.surface,
    ...theme.shadows.base,
    shadowColor: colors.shadow,
  },
}));

// Usage in component
const styles = useStyles('light'); // or 'dark'
```

### 4. Common Styles Extension
```typescript
import { commonStyles, getThemeColors } from '../styles';

const MyComponent = ({ themeMode = 'light' }) => {
  const colors = getThemeColors(themeMode);
  
  return (
    <View style={[
      commonStyles.card,
      { backgroundColor: colors.surface }
    ]}>
      <Text style={[commonStyles.title, { color: colors.text }]}>
        Recipe Title
      </Text>
    </View>
  );
};
```

## 🌙 Dark Mode Support

The theme system is built with dark mode in mind:

```typescript
// Get colors for specific theme
const lightColors = getThemeColors('light');
const darkColors = getThemeColors('dark');

// Set global theme mode
setThemeMode('dark');

// Check current theme
const currentMode = getThemeMode();
```

### Dark Mode Colors
- **Background**: Deep blacks and grays
- **Text**: High contrast whites and grays
- **Surfaces**: Elevated dark cards
- **Borders**: Subtle dark borders

## 🔧 Component Integration

### RecipeCard Example
```typescript
import { recipeStyles, getThemeColors } from '../styles';

const RecipeCard = ({ themeMode = 'light' }) => {
  const colors = getThemeColors(themeMode);
  
  return (
    <View style={[
      recipeStyles.recipeCard,
      { backgroundColor: colors.surface, shadowColor: colors.shadow }
    ]}>
      {/* Recipe content */}
    </View>
  );
};
```

### Navigation Example
```typescript
// App.tsx - Tab bar styling
tabBarStyle: {
  backgroundColor: colors.surface,
  borderTopColor: colors.border,
  height: theme.components.tabBar.height,
  paddingTop: theme.spacing.xs,
}
```

## 📱 Common Styles Library

Pre-built styles for common UI patterns:

```typescript
import { commonStyles } from '../styles';

// Layout
commonStyles.container
commonStyles.containerPadded
commonStyles.row
commonStyles.center

// Cards
commonStyles.card
commonStyles.cardCompact

// Buttons
commonStyles.buttonPrimary
commonStyles.buttonSecondary

// Text
commonStyles.title
commonStyles.heading
commonStyles.body
commonStyles.caption

// Loading states
commonStyles.loadingContainer
commonStyles.emptyState
```

## 🍎 Recipe Styles Library

Recipe-specific pre-built styles:

```typescript
import { recipeStyles } from '../styles';

// Cards
recipeStyles.recipeCard
recipeStyles.recipeCardCompact

// Metadata
recipeStyles.recipeMetadata
recipeStyles.nutritionContainer

// Tags and badges
recipeStyles.calorieTag
recipeStyles.difficultyBadge
recipeStyles.tagContainer
```

## ✅ Best Practices

### DO ✅
- Import from `../styles` (the index file)
- Use theme tokens instead of hardcoded values
- Make components theme-aware for dark mode support
- Use semantic color names (success, warning, error)
- Leverage common styles to reduce duplication
- Use recipe-specific colors for food-related UI

### DON'T ❌
- Import directly from individual theme files
- Hardcode colors, spacing, or font sizes
- Create styles that only work in light mode
- Ignore the 4px spacing grid
- Duplicate common style patterns

## 🔄 Migration Guide

### From Hardcoded Values
```typescript
// Before ❌
const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    padding: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1a1a1a',
  },
});

// After ✅
const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.light.background,
    padding: theme.spacing.base,
  },
  title: {
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.bold,
    color: theme.colors.light.text,
  },
});
```

### To Theme-Aware Component
```typescript
// Before ❌
const MyComponent = () => (
  <View style={{ backgroundColor: '#fff' }}>
    <Text style={{ color: '#000' }}>Hello</Text>
  </View>
);

// After ✅
const MyComponent = ({ themeMode = 'light' }) => {
  const colors = getThemeColors(themeMode);
  
  return (
    <View style={{ backgroundColor: colors.background }}>
      <Text style={{ color: colors.text }}>Hello</Text>
    </View>
  );
};
```

## 🚀 Future Enhancements

The theme system is designed to easily support:

- **Dynamic theme switching** (light/dark toggle)
- **User theme preferences** (saved in AsyncStorage)
- **Custom color schemes** (user personalization)
- **High contrast mode** (accessibility)
- **Brand variations** (seasonal themes)

## 📚 Examples

See `examples.ts` for comprehensive usage patterns and component integration examples.

---

This centralized theme system provides the foundation for consistent, maintainable, and accessible styling throughout the AI Recipe Planner app. By following these patterns, we ensure a cohesive user experience and make future design changes much easier to implement. 