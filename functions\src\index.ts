/**
 * AI Recipe Planner - Secure Firebase Cloud Functions
 *
 * These functions provide secure API proxies for external services.
 * API keys are stored securely in Firebase environment configuration.
 *
 * Setup: firebase functions:config:set gemini.api_key="YOUR_KEY"
 */

import * as functions from "firebase-functions";
import * as logger from "firebase-functions/logger";
import {HttpsError} from "firebase-functions/v1/auth";
import {GoogleAuth} from "google-auth-library";

/**
 * Firebase Cloud Functions for AI Recipe Planner
 * Secure backend for recipe generation and processing
 */

// Initialize Firebase Admin SDK
import * as admin from "firebase-admin";

if (!admin.apps.length) {
    admin.initializeApp();
}

// CORS headers for web clients
const corsHeaders = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, Authorization",
};

// Type definitions for function parameters
interface GenerateRecipesData {
  prompt: string;
  count?: number;
  userPreferences?: Record<string, unknown>;
}

interface SearchIngredientsData {
  query: string;
  limit?: number;
}

interface CookingTipData {
  topic?: string;
}

/**
 * Generate recipes using secure Gemini API
 */
export const generateRecipes = functions.https.onCall(
    async (request) => {
        const data: GenerateRecipesData = request.data;
        // Uncomment if you need CORS for direct HTTP calls
        // if (request.method === "OPTIONS") {
        //   response.set(corsHeaders);
        //   return response.status(204).send("");
        // }

        // response.set(corsHeaders);

        // if (request.method !== "POST") {
        //   return response.status(405).send("Method Not Allowed");
        // }

        const {prompt, count = 1, userPreferences} = data;

        if (!prompt || typeof prompt !== "string") {
            throw new HttpsError(
                "invalid-argument",
                "The function must be called with a \"prompt\" argument.",
            );
        }

        // Validate count parameter
        if (count < 1 || count > 50) {
            throw new HttpsError(
                "invalid-argument",
                "Count must be between 1 and 50.",
            );
        }

        // Securely get the API key from Firebase environment configuration
        const geminiApiKey = functions.config().gemini?.api_key;
        if (!geminiApiKey) {
            logger.error("Gemini API key is not configured in Firebase.");
            throw new HttpsError(
                "internal",
                "The server is missing its API configuration.",
            );
        }

        const API_ENDPOINT = "us-central1-aiplatform.googleapis.com";
        const PROJECT_ID = process.env.GCLOUD_PROJECT || "your-project-id";
        const MODEL_ID = "gemini-1.5-flash";
        const baseUrl = `https://${API_ENDPOINT}/v1/projects/${PROJECT_ID}`;
        const url = `${baseUrl}/locations/us-central1/publishers/google/models/${MODEL_ID}:generateContent`;

        const auth = new GoogleAuth({
            scopes: "https://www.googleapis.com/auth/cloud-platform",
        });
        const client = await auth.getClient();

        logger.info("Generating recipes with Gemini API", {
            promptLength: prompt.length,
            count,
            userPreferences: userPreferences ? Object.keys(userPreferences) : [],
        });

        try {
            const response = await client.request({
                url,
                method: "POST",
                data: {
                    contents: [{parts: [{text: prompt}]}],
                    generationConfig: {
                        temperature: 0.8,
                        topK: 40,
                        topP: 0.95,
                        maxOutputTokens: count > 1 ? 8192 : 4096,
                    },
                },
            });

            logger.info("Successfully generated recipes", {
                responseSize: JSON.stringify(response.data).length,
            });

            return {
                success: true,
                data: response.data,
                metadata: {
                    count,
                    generatedAt: new Date().toISOString(),
                    model: MODEL_ID,
                },
            };
        } catch (error) {
            logger.error("Error calling Gemini API", {error});
            throw new HttpsError(
                "internal",
                "Failed to generate recipes. Please try again later.",
            );
        }
    });

/**
 * Secure ingredient search using Algolia
 */
export const searchIngredients = functions.https.onCall(
    async (data: SearchIngredientsData) => {
        const {query, limit = 10} = data;

        if (!query || typeof query !== "string" || query.trim().length < 2) {
            throw new HttpsError(
                "invalid-argument",
                "Query must be at least 2 characters long.",
            );
        }

        // Get Algolia configuration
        const algoliaAppId = functions.config().algolia?.app_id;
        const algoliaApiKey = functions.config().algolia?.api_key;
        const algoliaIndex = functions.config().algolia?.index_name || "recipes";

        if (!algoliaAppId || !algoliaApiKey) {
            logger.warn("Algolia not configured, using fallback search");
            // Return fallback results
            return {
                success: true,
                data: [],
                fallback: true,
            };
        }

        logger.info("Searching ingredients with Algolia", {query, limit});

        try {
            const algoliaUrl = `https://${algoliaAppId}-dsn.algolia.net/1/indexes/${algoliaIndex}/query`;
            const response = await fetch(algoliaUrl, {
                method: "POST",
                headers: {
                    "X-Algolia-API-Key": algoliaApiKey,
                    "X-Algolia-Application-Id": algoliaAppId,
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({
                    query: query.trim(),
                    hitsPerPage: Math.min(limit, 50),
                    attributesToRetrieve: ["name", "category", "nutrition", "common"],
                }),
            });

            if (!response.ok) {
                throw new Error(`Algolia API error: ${response.status}`);
            }

            const algoliaData = await response.json();

            return {
                success: true,
                data: algoliaData.hits,
                metadata: {
                    query,
                    count: algoliaData.hits.length,
                    searchedAt: new Date().toISOString(),
                },
            };
        } catch (error) {
            logger.error("Error searching ingredients", {error});
            throw new HttpsError(
                "internal",
                "Failed to search ingredients. Please try again later.",
            );
        }
    });

/**
 * Secure Cloud Function to categorize recipes using Gemini AI
 */
export const categorizeRecipe = functions.https.onCall(
    async (request) => {
        const {ingredients} = request.data;

        if (!ingredients || !Array.isArray(ingredients)) {
            throw new HttpsError(
                "invalid-argument",
                "Must provide ingredients array",
            );
        }

        // Get secure API key
        const geminiApiKey = functions.config().gemini?.api_key;
        if (!geminiApiKey) {
            logger.error("Gemini API key not configured");
            throw new HttpsError("internal", "API not configured");
        }

        const prompt = `
Analyze these recipe ingredients and provide categorization: 
${ingredients.join(", ")}

Return only valid JSON:
{
  "primaryCategory": "Italian|Asian|Mexican|American|Mediterranean|Indian",
  "cuisine": "specific cuisine type",
  "dishType": "Main Course|Appetizer|Soup|Salad|Dessert",
  "cookingMethod": "Baked|Grilled|Fried|Sautéed|Steamed|Roasted",
  "difficulty": "Easy|Medium|Hard",
  "dietaryTags": ["Vegetarian", "Vegan", "Gluten-Free"],
  "suggestedTags": ["descriptive tags"]
}`;

        try {
            const geminiUrl = "https://generativelanguage.googleapis.com/v1beta/models/" +
        `gemini-1.5-flash:generateContent?key=${geminiApiKey}`;
            const response = await fetch(geminiUrl, {
                method: "POST",
                headers: {"Content-Type": "application/json"},
                body: JSON.stringify({
                    contents: [{parts: [{text: prompt}]}],
                    generationConfig: {
                        temperature: 0.3,
                        maxOutputTokens: 300,
                    },
                }),
            });

            if (!response.ok) {
                throw new Error(`Gemini API error: ${response.status}`);
            }

            const result = await response.json();
            const responseText = result.candidates?.[0]?.content?.parts?.[0]?.text;

            if (!responseText) {
                throw new Error("No response from Gemini API");
            }

            const categorization = JSON.parse(responseText.trim());

            return {
                success: true,
                categorization,
            };
        } catch (error) {
            logger.error("Categorization error:", error);

            // Return fallback categorization
            return {
                success: true,
                categorization: {
                    primaryCategory: "American",
                    cuisine: "American",
                    dishType: "Main Course",
                    cookingMethod: "Cooked",
                    difficulty: "Medium",
                    dietaryTags: [],
                    suggestedTags: ["Homemade"],
                },
                fallback: true,
            };
        }
    },
);

/**
 * Generate recipe title from ingredients
 */
export const suggestRecipeTitle = functions.https.onCall(
    async (request) => {
        const {ingredients} = request.data;

        if (!ingredients || !Array.isArray(ingredients)) {
            throw new HttpsError(
                "invalid-argument",
                "Must provide ingredients array",
            );
        }

        const geminiApiKey = functions.config().gemini?.api_key;
        if (!geminiApiKey) {
            throw new HttpsError("internal", "API not configured");
        }

        const prompt = `
Based on ingredients: ${ingredients.join(", ")}

Generate an appealing recipe name (2-5 words):
- Use primary protein/main ingredient
- Add cooking method or style
- Include flavor profile
- Make it sound delicious

Return only the recipe name, no quotes.`;

        try {
            const geminiUrl = "https://generativelanguage.googleapis.com/v1beta/models/" +
        `gemini-1.5-flash:generateContent?key=${geminiApiKey}`;
            const response = await fetch(geminiUrl, {
                method: "POST",
                headers: {"Content-Type": "application/json"},
                body: JSON.stringify({
                    contents: [{parts: [{text: prompt}]}],
                    generationConfig: {
                        temperature: 0.8,
                        maxOutputTokens: 50,
                    },
                }),
            });

            const result = await response.json();
            const title = result.candidates?.[0]?.content?.parts?.[0]?.text?.trim();

            return {
                success: true,
                title: title || `${ingredients[0]} Recipe`,
            };
        } catch (error) {
            logger.error("Title suggestion error:", error);
            return {
                success: true,
                title: `${ingredients[0]} Recipe`,
                fallback: true,
            };
        }
    },
);

/**
 * Get cooking tip
 */
export const getCookingTip = functions.https.onCall(async () => {
    const geminiApiKey = functions.config().gemini?.api_key;
    if (!geminiApiKey) {
        return {
            success: true,
            tip: "Always taste as you cook and adjust seasonings accordingly!",
            fallback: true,
        };
    }

    const prompt = "Give a helpful cooking tip in under 80 words.";

    try {
        const geminiUrl = "https://generativelanguage.googleapis.com/v1beta/models/" +
      `gemini-1.5-flash:generateContent?key=${geminiApiKey}`;
        const response = await fetch(geminiUrl, {
            method: "POST",
            headers: {"Content-Type": "application/json"},
            body: JSON.stringify({
                contents: [{parts: [{text: prompt}]}],
                generationConfig: {
                    temperature: 0.7,
                    maxOutputTokens: 150,
                },
            }),
        });

        const result = await response.json();
        const tip = result.candidates?.[0]?.content?.parts?.[0]?.text?.trim();

        return {
            success: true,
            tip: tip || "Cook with love and patience!",
        };
    } catch (error) {
        logger.error("Cooking tip error:", error);
        return {
            success: true,
            tip: "Season your food gradually and taste as you go!",
            fallback: true,
        };
    }
});

/**
 * Index recipe to Algolia with proper JSON formatting
 */
export const indexRecipeToAlgolia = functions.https.onCall(
    async (request) => {
        const {recipe} = request.data;

        if (!recipe || typeof recipe !== "object") {
            throw new HttpsError(
                "invalid-argument",
                "Must provide recipe object",
            );
        }

        // Get Algolia configuration
        const algoliaAppId = functions.config().algolia?.app_id;
        const algoliaApiKey = functions.config().algolia?.api_key;

        if (!algoliaAppId || !algoliaApiKey) {
            logger.warn("Algolia not configured, skipping indexing");
            return {
                success: true,
                message: "Recipe saved (Algolia indexing skipped)",
                indexed: false,
            };
        }

        try {
            // Format recipe data as proper JSON for Algolia
            const algoliaRecipe = {
                objectID: recipe.id,
                title: recipe.title,
                description: recipe.description || "",
                ingredients: recipe.ingredients || [],
                instructions: recipe.instructions || [],
                cookingTime: recipe.cookingTime || "",
                difficulty: recipe.difficulty || "Medium",
                servings: recipe.servings || 4,
                mealType: recipe.mealType || "",
                tags: recipe.tags || [],
                category: recipe.category || "",
                cuisine: recipe.cuisine || "",
                dishType: recipe.dishType || "",
                cookingMethod: recipe.cookingMethod || "",
                dietaryTags: recipe.dietaryTags || [],
                createdAt: recipe.createdAt || new Date().toISOString(),
                nutritionInfo: recipe.nutritionInfo || {},
                // Additional searchable fields
                searchableText: [
                    recipe.title,
                    recipe.description,
                    ...(recipe.ingredients || []),
                    recipe.category,
                    recipe.cuisine,
                    ...(recipe.tags || []),
                ].filter(Boolean).join(" ").toLowerCase(),
            };

            // Index to Algolia
            const algoliaUrl = `https://${algoliaAppId}-dsn.algolia.net/1/indexes/recipes`;
            const response = await fetch(algoliaUrl, {
                method: "POST",
                headers: {
                    "X-Algolia-API-Key": algoliaApiKey,
                    "X-Algolia-Application-Id": algoliaAppId,
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(algoliaRecipe),
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`Algolia indexing failed: ${errorText}`);
            }

            const result = await response.json();

            logger.info("Recipe indexed to Algolia successfully", {
                recipeId: recipe.id,
                algoliaObjectID: result.objectID,
            });

            return {
                success: true,
                message: "Recipe indexed to Algolia successfully",
                indexed: true,
                algoliaResult: result,
            };
        } catch (error) {
            logger.error("Algolia indexing error:", error);

            // Don't fail the entire operation if Algolia indexing fails
            return {
                success: true,
                message: "Recipe saved (Algolia indexing failed)",
                indexed: false,
                error: (error as Error).message,
            };
        }
    },
);

/**
 * Search recipes in Algolia with proper JSON handling
 */
export const searchRecipesInAlgolia = functions.https.onCall(
    async (request) => {
        const {query, options = {}} = request.data;

        if (!query || typeof query !== "string") {
            throw new HttpsError(
                "invalid-argument",
                "Must provide search query string",
            );
        }

        // Get Algolia configuration
        const algoliaAppId = functions.config().algolia?.app_id;
        const algoliaApiKey = functions.config().algolia?.api_key;

        if (!algoliaAppId || !algoliaApiKey) {
            logger.warn("Algolia not configured, returning empty results");
            return {
                success: true,
                hits: [],
                nbHits: 0,
                fallback: true,
            };
        }

        try {
            const searchParams = {
                query: query.trim(),
                hitsPerPage: options.hitsPerPage || 20,
                page: options.page || 0,
                attributesToRetrieve: [
                    "objectID",
                    "title",
                    "description",
                    "ingredients",
                    "cookingTime",
                    "difficulty",
                    "servings",
                    "category",
                    "cuisine",
                    "tags",
                    "nutritionInfo",
                    "createdAt",
                    "dishType",
                    "cookingMethod",
                    "dietaryTags",
                ],
                attributesToHighlight: ["title", "description", "ingredients", "tags"],
                filters: options.filters || "",
                facetFilters: options.facetFilters || [],
            };

            const algoliaUrl = `https://${algoliaAppId}-dsn.algolia.net/1/indexes/recipes/query`;
            const response = await fetch(algoliaUrl, {
                method: "POST",
                headers: {
                    "X-Algolia-API-Key": algoliaApiKey,
                    "X-Algolia-Application-Id": algoliaAppId,
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(searchParams),
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`Algolia search failed: ${errorText}`);
            }

            const searchResults = await response.json();

            logger.info("Algolia search completed", {
                query,
                nbHits: searchResults.nbHits,
                processingTimeMS: searchResults.processingTimeMS,
            });

            return {
                success: true,
                hits: searchResults.hits,
                nbHits: searchResults.nbHits,
                page: searchResults.page,
                nbPages: searchResults.nbPages,
                processingTimeMS: searchResults.processingTimeMS,
            };
        } catch (error) {
            logger.error("Algolia search error:", error);
            throw new HttpsError("internal", "Search failed");
        }
    },
);

/**
 * Health check endpoint
 */
export const healthCheck = functions.https.onCall(async () => {
    return {
        success: true,
        message: "AI Recipe Planner backend is healthy",
        timestamp: new Date().toISOString(),
        services: {
            gemini: !!functions.config().gemini?.api_key,
            algolia: !!(
                functions.config().algolia?.app_id &&
        functions.config().algolia?.api_key
            ),
        },
    };
});
 