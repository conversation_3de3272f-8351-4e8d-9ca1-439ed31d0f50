import React, { useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, CommonActions } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';
import { useAuth } from '../../context/AuthContext';
import { RootStackParamList } from '../../types/navigation';
import { UserPreferencesService } from '../../services/userPreferencesService';

type OnboardingNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Onboarding'>;

const { width } = Dimensions.get('window');

interface OnboardingStep {
  id: string;
  title: string;
  subtitle: string;
  icon: keyof typeof Ionicons.glyphMap;
  color: string;
  options: {
    id: string;
    title: string;
    description: string;
    icon: keyof typeof Ionicons.glyphMap;
    value: string;
  }[];
}

const onboardingSteps: OnboardingStep[] = [
  {
    id: 'shopping_mode',
    title: 'How do you prefer to shop?',
    subtitle: 'Choose your shopping style to get personalized recommendations',
    icon: 'basket',
    color: '#10B981',
    options: [
      {
        id: 'budgeting',
        title: 'Budget-Focused',
        description: 'Find the cheapest prices across all stores',
        icon: 'calculator',
        value: 'budgeting'
      },
      {
        id: 'bulk_buying',
        title: 'Bulk & Deals',
        description: 'Buy in bulk when prices drop, save long-term',
        icon: 'cube',
        value: 'bulk_buying'
      },
      {
        id: 'convenience',
        title: 'Convenience First',
        description: 'Shop at one store, minimize travel time',
        icon: 'time',
        value: 'convenience'
      }
    ]
  },
  {
    id: 'family_size',
    title: 'Family Size',
    subtitle: 'Help us recommend the right quantities',
    icon: 'people',
    color: '#3B82F6',
    options: [
      {
        id: 'single',
        title: 'Just Me',
        description: 'Individual portions and small packages',
        icon: 'person',
        value: '1'
      },
      {
        id: 'couple',
        title: 'Couple',
        description: 'Perfect for two people',
        icon: 'people-outline',
        value: '2'
      },
      {
        id: 'family_small',
        title: 'Small Family',
        description: '3-4 people, growing portions',
        icon: 'people',
        value: '3-4'
      },
      {
        id: 'family_large',
        title: 'Large Family',
        description: '5+ people, bulk buying recommended',
        icon: 'people-circle',
        value: '5+'
      }
    ]
  },
  {
    id: 'dietary_preferences',
    title: 'Dietary Preferences',
    subtitle: 'Filter recipes and products for your needs',
    icon: 'nutrition',
    color: '#F59E0B',
    options: [
      {
        id: 'none',
        title: 'No Restrictions',
        description: 'I eat everything',
        icon: 'checkmark-circle',
        value: 'none'
      },
      {
        id: 'vegetarian',
        title: 'Vegetarian',
        description: 'No meat, but dairy and eggs okay',
        icon: 'leaf',
        value: 'vegetarian'
      },
      {
        id: 'vegan',
        title: 'Vegan',
        description: 'Plant-based only',
        icon: 'flower',
        value: 'vegan'
      },
      {
        id: 'gluten_free',
        title: 'Gluten-Free',
        description: 'No gluten-containing products',
        icon: 'medical',
        value: 'gluten_free'
      }
    ]
  },
  {
    id: 'notifications',
    title: 'Stay Updated',
    subtitle: 'Get notified about deals and price drops',
    icon: 'notifications',
    color: '#EF4444',
    options: [
      {
        id: 'all_notifications',
        title: 'All Notifications',
        description: 'Price drops, deals, and shopping reminders',
        icon: 'notifications',
        value: 'all'
      },
      {
        id: 'price_alerts_only',
        title: 'Price Alerts Only',
        description: 'Just notify me when prices drop',
        icon: 'pricetag',
        value: 'price_alerts'
      },
      {
        id: 'no_notifications',
        title: 'No Notifications',
        description: 'I\'ll check the app manually',
        icon: 'notifications-off',
        value: 'none'
      }
    ]
  }
];

export const OnboardingScreen: React.FC = () => {
  const navigation = useNavigation<OnboardingNavigationProp>();
  const { colors: themeColors, isDark: isDarkMode } = useTheme();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(0);
  const [selections, setSelections] = useState<Record<string, string>>({});
  const [isCompleting, setIsCompleting] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);

  const currentStepData = onboardingSteps[currentStep];

  // Professional color scheme
  const colors = {
    light: {
      background: '#F8FAFC',
      surface: '#FFFFFF',
      primary: '#475569',
      text: '#0F172A',
      textSecondary: '#64748B',
      border: '#E2E8F0',
      shadow: 'rgba(15, 23, 42, 0.1)',
      card: '#FFFFFF',
      cardSelected: '#EFF6FF',
    },
    dark: {
      background: '#0F172A',
      surface: '#1E293B',
      primary: '#06B6D4',
      text: '#F8FAFC',
      textSecondary: '#94A3B8',
      border: '#334155',
      shadow: 'rgba(0, 0, 0, 0.3)',
      card: '#334155',
      cardSelected: '#0F4C75',
    }
  };

  const currentColors = isDarkMode ? colors.dark : colors.light;

  const handleSelection = (stepId: string, value: string) => {
    setSelections(prev => ({
      ...prev,
      [stepId]: value
    }));
  };

  const handleNext = () => {
    if (currentStep < onboardingSteps.length - 1) {
      setCurrentStep(currentStep + 1);
      scrollViewRef.current?.scrollTo({ x: 0, y: 0, animated: true });
    } else {
      handleComplete();
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
      scrollViewRef.current?.scrollTo({ x: 0, y: 0, animated: true });
    }
  };

  const handleComplete = async () => {
    setIsCompleting(true);
    
    try {
      if (!user?.id) {
        Alert.alert('Error', 'User not found. Please login again.');
        return;
      }

      // Save preferences using the service
      const preferencesService = UserPreferencesService.getInstance();
      const success = await preferencesService.completeOnboarding(user.id, {
        shoppingMode: selections.shopping_mode || 'budgeting',
        familySize: selections.family_size || '2',
        dietaryPreferences: selections.dietary_preferences || 'none',
        notificationSettings: selections.notifications || 'all',
      });

      if (success) {
        console.log('✅ Onboarding completed successfully!');
        
        // Show success message and navigate directly to main app
        Alert.alert(
          'Welcome!', 
          'Your preferences have been saved. You can change them anytime in settings.',
          [
            {
              text: 'Get Started',
              onPress: () => {
                console.log('Onboarding completed, navigating to main app...');
                
                // Force navigation to main app by resetting the navigation stack
                // This bypasses the bootstrap hook and directly shows the main app
                navigation.dispatch(
                  CommonActions.reset({
                    index: 0,
                    routes: [
                      { name: 'Main' },
                    ],
                  })
                );
              }
            }
          ]
        );
      } else {
        Alert.alert('Error', 'Failed to save preferences. Please try again.');
      }
    } catch (error) {
      console.error('❌ Onboarding error:', error);
      Alert.alert('Error', 'An error occurred while saving your preferences.');
    } finally {
      setIsCompleting(false);
    }
  };

  const isCurrentStepSelected = selections[currentStepData.id];
  const canProceed = !!isCurrentStepSelected;

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: currentColors.background }]}>
      {/* Progress Bar */}
      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <View 
            style={[
              styles.progressFill, 
              { 
                width: `${((currentStep + 1) / onboardingSteps.length) * 100}%`,
                backgroundColor: currentStepData.color
              }
            ]} 
          />
        </View>
        <Text style={[styles.progressText, { color: currentColors.textSecondary }]}>
          {currentStep + 1} of {onboardingSteps.length}
        </Text>
      </View>

      <ScrollView 
        ref={scrollViewRef}
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={[styles.iconContainer, { backgroundColor: currentStepData.color + '20' }]}>
            <Ionicons name={currentStepData.icon} size={40} color={currentStepData.color} />
          </View>
          <Text style={[styles.title, { color: currentColors.text }]}>{currentStepData.title}</Text>
          <Text style={[styles.subtitle, { color: currentColors.textSecondary }]}>{currentStepData.subtitle}</Text>
        </View>

        {/* Options */}
        <View style={styles.optionsContainer}>
          {currentStepData.options.map((option) => (
            <TouchableOpacity
              key={option.id}
              style={[
                styles.optionCard,
                {
                  backgroundColor: currentColors.surface,
                  borderColor: selections[currentStepData.id] === option.value 
                    ? currentStepData.color 
                    : currentColors.border,
                  borderWidth: selections[currentStepData.id] === option.value ? 2 : 1,
                }
              ]}
              onPress={() => handleSelection(currentStepData.id, option.value)}
              activeOpacity={0.7}
            >
              <View style={styles.optionHeader}>
                <View style={[styles.optionIcon, { backgroundColor: currentStepData.color + '15' }]}>
                  <Ionicons name={option.icon} size={24} color={currentStepData.color} />
                </View>
                {selections[currentStepData.id] === option.value && (
                  <View style={[styles.selectedBadge, { backgroundColor: currentStepData.color }]}>
                    <Ionicons name="checkmark" size={16} color="white" />
                  </View>
                )}
              </View>
              <Text style={[styles.optionTitle, { color: currentColors.text }]}>{option.title}</Text>
              <Text style={[styles.optionDescription, { color: currentColors.textSecondary }]}>
                {option.description}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>

      {/* Navigation Buttons */}
      <View style={styles.navigationContainer}>
        {currentStep > 0 && (
          <TouchableOpacity
            style={[styles.backButton, { backgroundColor: currentColors.surface, borderColor: currentColors.border }]}
            onPress={handleBack}
          >
            <Ionicons name="chevron-back" size={20} color={currentColors.text} />
            <Text style={[styles.backButtonText, { color: currentColors.text }]}>Back</Text>
          </TouchableOpacity>
        )}
        
        <TouchableOpacity
          style={[
            styles.nextButton,
            { backgroundColor: canProceed ? currentStepData.color : currentColors.border },
            currentStep === 0 && styles.fullWidthButton
          ]}
          onPress={handleNext}
          disabled={!canProceed || isCompleting}
        >
          {isCompleting ? (
            <>
              <ActivityIndicator size="small" color="white" />
              <Text style={styles.nextButtonText}>Completing...</Text>
            </>
          ) : (
            <>
              <Text style={styles.nextButtonText}>
                {currentStep === onboardingSteps.length - 1 ? 'Get Started' : 'Next'}
              </Text>
              <Ionicons name="chevron-forward" size={20} color="white" />
            </>
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};


const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  progressContainer: {
    paddingHorizontal: 24,
    paddingTop: 16,
    paddingBottom: 24,
  },
  progressBar: {
    height: 4,
    backgroundColor: '#E5E7EB',
    borderRadius: 2,
    overflow: 'hidden',
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 24,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
  optionsContainer: {
    gap: 16,
  },
  optionCard: {
    padding: 20,
    borderRadius: 16,
    position: 'relative',
  },
  optionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  optionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectedBadge: {
    width: 28,
    height: 28,
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
  },
  optionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  optionDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  navigationContainer: {
    flexDirection: 'row',
    paddingHorizontal: 24,
    paddingVertical: 24,
    gap: 16,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    borderWidth: 1,
    gap: 8,
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  nextButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    gap: 8,
  },
  fullWidthButton: {
    flex: 1,
  },
  nextButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
}); 