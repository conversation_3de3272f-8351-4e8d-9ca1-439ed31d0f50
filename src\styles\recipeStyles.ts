/**
 * Recipe Styles - Specific styles for recipe components
 * 
 * These styles provide consistent recipe-related UI patterns
 * and color coding throughout the app.
 */

import { StyleSheet } from 'react-native';
import { theme, getThemeColors, ThemeMode } from './theme';

// Get recipe-specific styles based on theme mode
export const getRecipeStyles = (mode: ThemeMode = 'light') => {
  const colors = getThemeColors(mode);
  
  return StyleSheet.create({
    // Recipe card styles
    recipeCard: {
      backgroundColor: colors.surface,
      borderRadius: theme.radius.xl,
      padding: theme.spacing.base,
      marginBottom: theme.spacing.base,
      ...theme.shadows.base,
      shadowColor: colors.shadow,
      overflow: 'hidden',
    },
    
    recipeCardCompact: {
      backgroundColor: colors.surface,
      borderRadius: theme.radius.lg,
      padding: theme.spacing.md,
      marginBottom: theme.spacing.md,
      ...theme.shadows.sm,
      shadowColor: colors.shadow,
      overflow: 'hidden',
    },
    
    recipeCardImage: {
      width: '100%',
      height: 200,
      borderRadius: theme.radius.lg,
      marginBottom: theme.spacing.md,
      backgroundColor: colors.backgroundSecondary,
    },
    
    recipeCardImageCompact: {
      width: 80,
      height: 80,
      borderRadius: theme.radius.base,
      marginRight: theme.spacing.md,
      backgroundColor: colors.backgroundSecondary,
    },
    
    recipeCardHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: theme.spacing.xs,
    },
    
    recipeTitle: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: theme.typography.fontWeight.semibold,
      color: colors.text,
      lineHeight: theme.typography.lineHeight.lg,
      flex: 1,
      marginRight: theme.spacing.md,
    },
    
    recipeTitleCompact: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: theme.typography.fontWeight.semibold,
      color: colors.text,
      lineHeight: theme.typography.lineHeight.base,
      marginBottom: theme.spacing.xs,
    },
    
    recipeDescription: {
      fontSize: theme.typography.fontSize.sm,
      color: colors.textSecondary,
      lineHeight: theme.typography.lineHeight.sm,
      marginBottom: theme.spacing.md,
    },
    
    recipeDescriptionCompact: {
      fontSize: theme.typography.fontSize.xs,
      color: colors.textSecondary,
      lineHeight: theme.typography.lineHeight.xs,
      marginBottom: theme.spacing.sm,
    },
    
    // Recipe metadata styles
    recipeMetadata: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: theme.spacing.md,
    },
    
    recipeMetadataCompact: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: theme.spacing.sm,
    },
    
    recipeMetaItem: {
      flexDirection: 'row',
      alignItems: 'center',
      marginRight: theme.spacing.lg,
    },
    
    recipeMetaItemCompact: {
      flexDirection: 'row',
      alignItems: 'center',
      marginRight: theme.spacing.md,
    },
    
    recipeMetaIcon: {
      marginRight: theme.spacing.xs,
      color: colors.textTertiary,
    },
    
    recipeMetaText: {
      fontSize: theme.typography.fontSize.sm,
      color: colors.textSecondary,
      fontWeight: theme.typography.fontWeight.medium,
    },
    
    recipeMetaTextCompact: {
      fontSize: theme.typography.fontSize.xs,
      color: colors.textSecondary,
      fontWeight: theme.typography.fontWeight.medium,
    },
    
    // Difficulty indicator styles
    difficultyBadge: {
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xs,
      borderRadius: theme.radius.base,
      alignSelf: 'flex-start',
    },
    
    difficultyBadgeEasy: {
      backgroundColor: theme.recipeColors.difficulty.easy,
    },
    
    difficultyBadgeMedium: {
      backgroundColor: theme.recipeColors.difficulty.medium,
    },
    
    difficultyBadgeHard: {
      backgroundColor: theme.recipeColors.difficulty.hard,
    },
    
    difficultyText: {
      fontSize: theme.typography.fontSize.xs,
      fontWeight: theme.typography.fontWeight.semibold,
      color: '#FFFFFF',
    },
    
    // Meal type badge styles
    mealTypeBadge: {
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xs,
      borderRadius: theme.radius.base,
      marginRight: theme.spacing.xs,
      marginBottom: theme.spacing.xs,
    },
    
    mealTypeBadgeBreakfast: {
      backgroundColor: theme.recipeColors.mealType.breakfast,
    },
    
    mealTypeBadgeLunch: {
      backgroundColor: theme.recipeColors.mealType.lunch,
    },
    
    mealTypeBadgeDinner: {
      backgroundColor: theme.recipeColors.mealType.dinner,
    },
    
    mealTypeBadgeSnack: {
      backgroundColor: theme.recipeColors.mealType.snack,
    },
    
    mealTypeBadgeDessert: {
      backgroundColor: theme.recipeColors.mealType.dessert,
    },
    
    mealTypeText: {
      fontSize: theme.typography.fontSize.xs,
      fontWeight: theme.typography.fontWeight.semibold,
      color: '#FFFFFF',
    },
    
    // Dietary restriction badge styles
    dietaryBadge: {
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xs,
      borderRadius: theme.radius.base,
      marginRight: theme.spacing.xs,
      marginBottom: theme.spacing.xs,
    },
    
    dietaryBadgeVegetarian: {
      backgroundColor: theme.recipeColors.dietary.vegetarian,
    },
    
    dietaryBadgeVegan: {
      backgroundColor: theme.recipeColors.dietary.vegan,
    },
    
    dietaryBadgeGlutenFree: {
      backgroundColor: theme.recipeColors.dietary.glutenFree,
    },
    
    dietaryBadgeDairyFree: {
      backgroundColor: theme.recipeColors.dietary.dairyFree,
    },
    
    dietaryBadgeKeto: {
      backgroundColor: theme.recipeColors.dietary.keto,
    },
    
    dietaryBadgePaleo: {
      backgroundColor: theme.recipeColors.dietary.paleo,
    },
    
    dietaryText: {
      fontSize: theme.typography.fontSize.xs,
      fontWeight: theme.typography.fontWeight.semibold,
      color: '#FFFFFF',
    },
    
    // Nutrition info styles
    nutritionContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      backgroundColor: colors.backgroundSecondary,
      borderRadius: theme.radius.lg,
      padding: theme.spacing.md,
      marginTop: theme.spacing.md,
    },
    
    nutritionItem: {
      alignItems: 'center',
      flex: 1,
    },
    
    nutritionValue: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: theme.typography.fontWeight.bold,
      marginBottom: theme.spacing.xs,
    },
    
    nutritionValueCalories: {
      color: theme.recipeColors.nutrition.calories,
    },
    
    nutritionValueProtein: {
      color: theme.recipeColors.nutrition.protein,
    },
    
    nutritionValueCarbs: {
      color: theme.recipeColors.nutrition.carbs,
    },
    
    nutritionValueFat: {
      color: theme.recipeColors.nutrition.fat,
    },
    
    nutritionLabel: {
      fontSize: theme.typography.fontSize.xs,
      color: colors.textTertiary,
      fontWeight: theme.typography.fontWeight.medium,
      textTransform: 'uppercase',
      letterSpacing: 0.5,
    },
    
    // Tag container styles
    tagContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginTop: theme.spacing.sm,
    },
    
    tagContainerCompact: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginTop: theme.spacing.xs,
    },
    
    tag: {
      backgroundColor: colors.backgroundTertiary,
      borderRadius: theme.radius.base,
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xs,
      marginRight: theme.spacing.xs,
      marginBottom: theme.spacing.xs,
    },
    
    tagText: {
      fontSize: theme.typography.fontSize.xs,
      color: colors.textSecondary,
      fontWeight: theme.typography.fontWeight.medium,
    },
    
    // Calorie tag (special styling for calorie information)
    calorieTag: {
      backgroundColor: theme.recipeColors.nutrition.calories,
      borderRadius: theme.radius.base,
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xs,
      marginRight: theme.spacing.xs,
      marginBottom: theme.spacing.xs,
    },
    
    calorieTagText: {
      fontSize: theme.typography.fontSize.xs,
      color: '#FFFFFF',
      fontWeight: theme.typography.fontWeight.semibold,
    },
    
    // Recipe actions styles
    recipeActions: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginTop: theme.spacing.md,
      paddingTop: theme.spacing.md,
      borderTopWidth: 1,
      borderTopColor: colors.separator,
    },
    
    recipeActionsCompact: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      alignItems: 'center',
      marginTop: theme.spacing.sm,
    },
    
    recipeActionButton: {
      padding: theme.spacing.sm,
      borderRadius: theme.radius.base,
      marginLeft: theme.spacing.sm,
    },
    
    recipeActionButtonPrimary: {
      backgroundColor: theme.colors.primary,
    },
    
    recipeActionButtonSecondary: {
      backgroundColor: colors.backgroundTertiary,
    },
    
    // Recipe list styles
    recipeList: {
      paddingHorizontal: theme.spacing.base,
      paddingBottom: theme.spacing['2xl'],
    },
    
    recipeListHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: theme.spacing.base,
      paddingVertical: theme.spacing.md,
      backgroundColor: colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    
    recipeListTitle: {
      fontSize: theme.typography.fontSize.xl,
      fontWeight: theme.typography.fontWeight.bold,
      color: colors.text,
    },
    
    recipeListCount: {
      fontSize: theme.typography.fontSize.sm,
      color: colors.textSecondary,
      fontWeight: theme.typography.fontWeight.medium,
    },
    
    // Load more button styles
    loadMoreButton: {
      backgroundColor: colors.surface,
      borderWidth: 2,
      borderColor: theme.colors.primary,
      borderRadius: theme.radius.lg,
      paddingVertical: theme.spacing.md,
      paddingHorizontal: theme.spacing.xl,
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: theme.spacing.base,
      marginHorizontal: theme.spacing.base,
      ...theme.shadows.sm,
      shadowColor: colors.shadow,
    },
    
    loadMoreButtonText: {
      color: theme.colors.primary,
      fontSize: theme.typography.fontSize.base,
      fontWeight: theme.typography.fontWeight.semibold,
    },
    
    loadMoreButtonDisabled: {
      backgroundColor: colors.backgroundSecondary,
      borderColor: colors.border,
    },
    
    loadMoreButtonTextDisabled: {
      color: colors.textTertiary,
    },
  });
};

// Default recipe styles (light mode)
export const recipeStyles = getRecipeStyles('light'); 