/**
 * Common Styles - Reusable style components
 * 
 * These styles provide consistent patterns across the app
 * and reduce code duplication.
 */

import { StyleSheet } from 'react-native';
import { theme, getThemeColors, ThemeMode } from './theme';

// Get common styles based on theme mode
export const getCommonStyles = (mode: ThemeMode = 'light') => {
  const colors = getThemeColors(mode);
  
  return StyleSheet.create({
    // Layout styles
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    
    containerPadded: {
      flex: 1,
      backgroundColor: colors.background,
      padding: theme.spacing.base,
    },
    
    contentContainer: {
      flexGrow: 1,
      padding: theme.spacing.base,
    },
    
    row: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    
    rowSpaceBetween: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    
    center: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    
    // Card styles
    card: {
      backgroundColor: colors.surface,
      borderRadius: theme.radius.xl,
      padding: theme.spacing.base,
      marginBottom: theme.spacing.base,
      ...theme.shadows.base,
      shadowColor: colors.shadow,
    },
    
    cardCompact: {
      backgroundColor: colors.surface,
      borderRadius: theme.radius.lg,
      padding: theme.spacing.md,
      marginBottom: theme.spacing.md,
      ...theme.shadows.sm,
      shadowColor: colors.shadow,
    },
    
    // Button styles
    buttonPrimary: {
      backgroundColor: theme.colors.primary,
      borderRadius: theme.radius.lg,
      ...theme.components.button.padding.base,
      ...theme.shadows.sm,
      shadowColor: theme.colors.primary,
    },
    
    buttonSecondary: {
      backgroundColor: colors.surface,
      borderRadius: theme.radius.lg,
      borderWidth: 2,
      borderColor: theme.colors.primary,
      ...theme.components.button.padding.base,
    },
    
    buttonText: {
      color: '#FFFFFF',
      fontSize: theme.typography.fontSize.base,
      fontWeight: theme.typography.fontWeight.semibold,
      textAlign: 'center',
    },
    
    buttonTextSecondary: {
      color: theme.colors.primary,
      fontSize: theme.typography.fontSize.base,
      fontWeight: theme.typography.fontWeight.semibold,
      textAlign: 'center',
    },
    
    // Text styles
    title: {
      fontSize: theme.typography.fontSize['2xl'],
      fontWeight: theme.typography.fontWeight.bold,
      color: colors.text,
      lineHeight: theme.typography.lineHeight['2xl'],
    },
    
    subtitle: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: theme.typography.fontWeight.semibold,
      color: colors.text,
      lineHeight: theme.typography.lineHeight.lg,
    },
    
    heading: {
      fontSize: theme.typography.fontSize.xl,
      fontWeight: theme.typography.fontWeight.semibold,
      color: colors.text,
      lineHeight: theme.typography.lineHeight.xl,
    },
    
    body: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: theme.typography.fontWeight.normal,
      color: colors.text,
      lineHeight: theme.typography.lineHeight.base,
    },
    
    bodySecondary: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: theme.typography.fontWeight.normal,
      color: colors.textSecondary,
      lineHeight: theme.typography.lineHeight.base,
    },
    
    caption: {
      fontSize: theme.typography.fontSize.sm,
      fontWeight: theme.typography.fontWeight.normal,
      color: colors.textTertiary,
      lineHeight: theme.typography.lineHeight.sm,
    },
    
    small: {
      fontSize: theme.typography.fontSize.xs,
      fontWeight: theme.typography.fontWeight.normal,
      color: colors.textTertiary,
      lineHeight: theme.typography.lineHeight.xs,
    },
    
    // Input styles
    input: {
      backgroundColor: colors.surface,
      borderColor: colors.border,
      borderWidth: 1,
      borderRadius: theme.radius.lg,
      padding: theme.spacing.md,
      fontSize: theme.typography.fontSize.base,
      color: colors.text,
      ...theme.shadows.sm,
      shadowColor: colors.shadow,
    },
    
    inputFocused: {
      borderColor: theme.colors.primary,
      borderWidth: 2,
    },
    
    // Separator styles
    separator: {
      height: 1,
      backgroundColor: colors.separator,
      marginVertical: theme.spacing.md,
    },
    
    separatorThick: {
      height: 1,
      backgroundColor: colors.border,
      marginVertical: theme.spacing.base,
    },
    
    // Loading styles
    loadingContainer: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: colors.background,
      padding: theme.spacing['2xl'],
    },
    
    loadingText: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: theme.typography.fontWeight.semibold,
      color: colors.text,
      marginTop: theme.spacing.base,
      textAlign: 'center',
    },
    
    loadingSubtext: {
      fontSize: theme.typography.fontSize.sm,
      color: colors.textSecondary,
      marginTop: theme.spacing.xs,
      textAlign: 'center',
    },
    
    // Empty state styles
    emptyState: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      padding: theme.spacing['3xl'],
    },
    
    emptyStateIcon: {
      fontSize: 64,
      marginBottom: theme.spacing.base,
    },
    
    emptyStateTitle: {
      fontSize: theme.typography.fontSize.xl,
      fontWeight: theme.typography.fontWeight.semibold,
      color: colors.text,
      marginBottom: theme.spacing.xs,
      textAlign: 'center',
    },
    
    emptyStateSubtitle: {
      fontSize: theme.typography.fontSize.base,
      color: colors.textSecondary,
      textAlign: 'center',
      lineHeight: theme.typography.lineHeight.lg,
    },
    
    // Header styles
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.surface,
      paddingHorizontal: theme.spacing.base,
      paddingVertical: theme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    
    headerTitle: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: theme.typography.fontWeight.semibold,
      color: colors.text,
      marginLeft: theme.spacing.md,
    },
    
    // Tab styles
    tabContainer: {
      backgroundColor: colors.surface,
      borderTopWidth: 1,
      borderTopColor: colors.border,
      paddingTop: theme.spacing.xs,
      paddingBottom: theme.spacing.xs,
      height: theme.components.tabBar.height,
    },
    
    // Badge styles
    badge: {
      backgroundColor: theme.colors.primary,
      borderRadius: theme.radius.full,
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xs,
      minWidth: 20,
      alignItems: 'center',
      justifyContent: 'center',
    },
    
    badgeText: {
      color: '#FFFFFF',
      fontSize: theme.typography.fontSize.xs,
      fontWeight: theme.typography.fontWeight.semibold,
    },
    
    badgeSuccess: {
      backgroundColor: theme.colors.success,
    },
    
    badgeWarning: {
      backgroundColor: theme.colors.warning,
    },
    
    badgeError: {
      backgroundColor: theme.colors.error,
    },
    
    // Shadow styles
    shadowSm: {
      ...theme.shadows.sm,
      shadowColor: colors.shadow,
    },
    
    shadowBase: {
      ...theme.shadows.base,
      shadowColor: colors.shadow,
    },
    
    shadowLg: {
      ...theme.shadows.lg,
      shadowColor: colors.shadow,
    },
  });
};

// Default common styles (light mode)
export const commonStyles = getCommonStyles('light'); 