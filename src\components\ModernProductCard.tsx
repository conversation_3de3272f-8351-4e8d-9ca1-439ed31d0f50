/**
 * Modern Product Card - Premium Design
 * 
 * Sophisticated product card component with micro-interactions,
 * optimized for grocery price comparison and multi-store display.
 */

import React, { useRef, useState, useMemo } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Animated,
  StyleSheet,
  Image,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
// import { BlurView } from 'expo-blur'; // Optional dependency
import { getGroceryTheme, ThemeMode } from '../styles/modernGroceryTheme';

interface Product {
  id: string;
  name: string;
  price: number;
  current_price?: number;
  original_price?: number;
  store: string;
  category?: string;
  brand?: string;
  unit?: string;
  size?: string;
  image_url?: string;
  is_on_sale?: boolean;
  promotion_text?: string;
}

// Import enhanced ProductGroup from types
import { IUnifiedProduct } from '../types/shoppingList';

interface ModernProductCardProps {
  item: IUnifiedProduct;
  viewMode: 'grid' | 'list';
  onPress: (item: IUnifiedProduct) => void;
  onAddToList: (item: IUnifiedProduct) => void;
  themeMode?: ThemeMode;
  isLoading?: boolean;
}

export const ModernProductCard: React.FC<ModernProductCardProps> = ({
  item,
  viewMode,
  onPress,
  onAddToList,
  themeMode = 'light',
  isLoading = false,
}) => {
  const theme = getGroceryTheme(themeMode);
  const [pressed, setPressed] = useState(false);
  const scaleValue = useRef(new Animated.Value(1)).current;
  const opacityValue = useRef(new Animated.Value(1)).current;

  // Memoized styles to prevent re-creation
  const styles = useMemo(() => createStyles(theme, viewMode), [theme, viewMode]);

  // Animation handlers
  const handlePressIn = () => {
    setPressed(true);
    Animated.parallel([
      Animated.spring(scaleValue, {
        toValue: theme.animations.pressScale,
        useNativeDriver: true,
      }),
      Animated.timing(opacityValue, {
        toValue: theme.animations.pressedOpacity,
        duration: theme.animations.timing.fast,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handlePressOut = () => {
    setPressed(false);
    Animated.parallel([
      Animated.spring(scaleValue, {
        toValue: 1,
        useNativeDriver: true,
      }),
      Animated.timing(opacityValue, {
        toValue: 1,
        duration: theme.animations.timing.fast,
        useNativeDriver: true,
      }),
    ]).start();
  };

  // Calculate savings
  const prices = Object.values(item.storePrices);
  const lowestPrice = Math.min(...prices);
  const highestPrice = Math.max(...prices);
  const savings = highestPrice - lowestPrice;
  const hasSignificantSavings = savings > 0.50;

  // Get product image (use enhanced bestImage from deduplication service)
  const getImageUrl = () => {
    // Use the best image selected by deduplication service
    return item.imageUrl;
  };

  // Render store prices (enhanced unified display)
  const renderStorePrices = () => {
    const storeConfig = {
      woolworths: { name: 'Woolworths', color: '#00A651', abbrev: 'W' },
      newworld: { name: 'New World', color: '#E31E24', abbrev: 'NW' },
      paknsave: { name: "Pak'nSave", color: '#FFD100', abbrev: 'PS' },
    };

    return (
      <View style={styles.storePricesContainer}>
        {Object.entries(storeConfig).map(([storeKey, config]) => {
          const price = item.storePrices[storeKey];
          const isAvailable = !!price;
          const isLowestPrice = price === lowestPrice;

          return (
            <View
              key={storeKey}
              style={[
                styles.storePriceItem,
                { borderColor: config.color },
                !isAvailable && styles.storePriceItemUnavailable,
                isLowestPrice && styles.storePriceItemBest
              ]}
            >
              <View style={[styles.storeIndicator, { backgroundColor: config.color }]}>
                <Text style={[styles.storeIndicatorText, { color: storeKey === 'paknsave' ? '#000' : '#FFF' }]}>
                  {config.abbrev}
                </Text>
              </View>
              
              <View style={styles.storePriceInfo}>
                {isAvailable ? (
                  <>
                    <Text style={[
                      styles.storePriceText,
                      isLowestPrice && styles.storePriceTextBest
                    ]}>
                      ${price?.toFixed(2)}
                    </Text>
                    {isLowestPrice && (
                      <Text style={styles.bestPriceIndicator}>BEST</Text>
                    )}
                  </>
                ) : (
                  <Text style={styles.unavailableText}>N/A</Text>
                )}
              </View>
            </View>
          );
        })}
      </View>
    );
  };

  // Render store indicators (fallback for simple view)
  const renderStoreIndicators = () => {
    const uniqueStores = Array.from(new Set(item.allStores));
    const maxVisible = viewMode === 'grid' ? 3 : 4;
    const visibleStores = uniqueStores.slice(0, maxVisible);
    const remainingCount = uniqueStores.length - maxVisible;

    return (
      <View style={styles.storeIndicators}>
        {visibleStores.map((store) => {
          const storeConfig = theme.colors.stores[store as keyof typeof theme.colors.stores];
          if (!storeConfig) return null;

          return (
            <View
              key={store}
              style={[
                styles.storeIndicator,
                { backgroundColor: storeConfig.primary }
              ]}
            >
              <Text style={[styles.storeIndicatorText, { color: storeConfig.contrast }]}>
                {getStoreAbbreviation(store)}
              </Text>
            </View>
          );
        })}
        
        {remainingCount > 0 && (
          <View style={styles.moreStoresIndicator}>
            <Text style={styles.moreStoresText}>+{remainingCount}</Text>
          </View>
        )}
      </View>
    );
  };

  // Get store abbreviation
  const getStoreAbbreviation = (store: string): string => {
    const abbreviations: { [key: string]: string } = {
      woolworths: 'W',
      newworld: 'NW',
      paknsave: 'PS',
    };
    return abbreviations[store] || store.charAt(0).toUpperCase();
  };

  // Render product image
  const renderProductImage = () => {
    const imageUrl = getImageUrl();
    
    if (!imageUrl) {
      return (
        <View style={styles.productImagePlaceholder}>
          <Ionicons 
            name="image-outline" 
            size={32} 
            color={theme.colors.neutral[400]} 
          />
        </View>
      );
    }

    return (
      <Image
        source={{ uri: imageUrl }}
        style={styles.productImage}
        resizeMode="contain"
      />
    );
  };

  // Render savings badge
  const renderSavingsBadge = () => {
    if (!hasSignificantSavings) return null;

    return (
      <View style={styles.savingsBadge}>
        <Text style={styles.savingsText}>
          Save ${savings.toFixed(2)}
        </Text>
      </View>
    );
  };

  // Handle add to list with haptic feedback
  const handleAddToList = () => {
    // Add haptic feedback for premium feel (optional)
    try {
      if (Platform.OS === 'ios') {
        const { HapticFeedback } = require('expo-haptics');
        HapticFeedback.impactAsync(HapticFeedback.ImpactFeedbackStyle.Light);
      }
    } catch (error) {
      // Graceful fallback if expo-haptics is not installed
      console.log('Haptic feedback not available');
    }
    onAddToList(item);
  };

  if (isLoading) {
    return <ProductCardSkeleton viewMode={viewMode} theme={theme} />;
  }

  return (
    <Animated.View
      style={[
        styles.container,
        {
          transform: [{ scale: scaleValue }],
          opacity: opacityValue,
        },
      ]}
    >
      <TouchableOpacity
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        onPress={() => onPress(item)}
        activeOpacity={1}
        style={styles.cardContent}
      >
        {/* Savings Badge */}
        {renderSavingsBadge()}

        {/* Product Image */}
        <View style={styles.productImageContainer}>
          {renderProductImage()}
        </View>

        {/* Product Info */}
        <View style={styles.productInfo}>
          <Text style={styles.productName} numberOfLines={viewMode === 'grid' ? 2 : 1}>
            {item.name}
          </Text>
          
          {/* Price Range */}
          <View style={styles.priceContainer}>
            <Text style={styles.priceRange}>
              ${item.lowestPrice.toFixed(2)}
              {item.lowestPrice !== item.highestPrice && 
                ` - $${item.highestPrice.toFixed(2)}`
              }
            </Text>
          </View>

          {/* Enhanced Store Prices Display */}
          {viewMode === 'list' ? renderStorePrices() : renderStoreIndicators()}

          {/* Size/Unit Info */}
          {item.size && (
            <Text style={styles.sizeInfo} numberOfLines={1}>
              {item.size} {item.unit || ''}
            </Text>
          )}
        </View>

        {/* Add Button */}
        <TouchableOpacity
          style={styles.addButtonContainer}
          onPress={handleAddToList}
          hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
        >
          <View style={[styles.addButton, styles.addButtonDefault]}>
            <Ionicons 
              name="add" 
              size={20} 
              color={theme.colors.neutral[0]} 
            />
          </View>
        </TouchableOpacity>
      </TouchableOpacity>
    </Animated.View>
  );
};

// Skeleton loading component
const ProductCardSkeleton: React.FC<{ viewMode: 'grid' | 'list'; theme: any }> = ({ 
  viewMode, 
  theme 
}) => {
  const shimmerValue = useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    const shimmer = Animated.loop(
      Animated.sequence([
        Animated.timing(shimmerValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(shimmerValue, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );
    shimmer.start();
    return () => shimmer.stop();
  }, [shimmerValue]);

  const shimmerOpacity = shimmerValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 0.7],
  });

  const styles = createStyles(theme, viewMode);

  return (
    <View style={styles.container}>
      <Animated.View style={[styles.skeletonImage, { opacity: shimmerOpacity }]} />
      <View style={styles.productInfo}>
        <Animated.View style={[styles.skeletonText, { opacity: shimmerOpacity }]} />
        <Animated.View style={[styles.skeletonTextSmall, { opacity: shimmerOpacity }]} />
        <View style={styles.storeIndicators}>
          <Animated.View style={[styles.skeletonStore, { opacity: shimmerOpacity }]} />
          <Animated.View style={[styles.skeletonStore, { opacity: shimmerOpacity }]} />
        </View>
      </View>
    </View>
  );
};

// Create dynamic styles based on theme and view mode
const createStyles = (theme: any, viewMode: 'grid' | 'list') => {
  const isGrid = viewMode === 'grid';
  
  return StyleSheet.create({
    container: {
      backgroundColor: theme.colors.semantic.surface,
      borderRadius: theme.borderRadius.xl,
      marginBottom: theme.spacing.base,
      ...theme.shadows.card,
      borderWidth: 1,
      borderColor: theme.colors.semantic.borderLight,
      overflow: 'hidden',
      ...(isGrid ? {
        width: theme.layout.cardWidth,
        marginHorizontal: theme.spacing[1],
      } : {
        flexDirection: 'row',
        alignItems: 'center',
        marginHorizontal: theme.spacing.base,
      }),
    },

    cardContent: {
      padding: theme.spacing.base,
      flex: 1,
      ...(isGrid ? {} : {
        flexDirection: 'row',
        alignItems: 'center',
      }),
    },

    savingsBadge: {
      position: 'absolute',
      top: theme.spacing.sm,
      left: theme.spacing.sm,
      backgroundColor: theme.colors.success[500],
      paddingHorizontal: theme.spacing[2],
      paddingVertical: theme.spacing[1],
      borderRadius: theme.borderRadius.lg,
      zIndex: 1,
      ...theme.shadows.card,
    },

    savingsText: {
      fontSize: theme.typography.fontSize.xs,
      fontWeight: theme.typography.fontWeight.semibold,
      color: theme.colors.neutral[0],
      letterSpacing: theme.typography.letterSpacing.wide,
    },

    productImageContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.neutral[50],
      borderRadius: theme.borderRadius.lg,
      ...theme.shadows.card,
      ...(isGrid ? {
        width: 80,
        height: 80,
        alignSelf: 'center',
        marginBottom: theme.spacing.sm,
      } : {
        width: 64,
        height: 64,
        marginRight: theme.spacing.base,
      }),
    },

    productImage: {
      ...(isGrid ? {
        width: 64,
        height: 64,
      } : {
        width: 48,
        height: 48,
      }),
      borderRadius: theme.borderRadius.base,
    },

    productImagePlaceholder: {
      ...(isGrid ? {
        width: 64,
        height: 64,
      } : {
        width: 48,
        height: 48,
      }),
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: theme.colors.neutral[100],
      borderRadius: theme.borderRadius.base,
    },

    productInfo: {
      flex: 1,
      gap: theme.spacing[1],
    },

    productName: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: theme.typography.fontWeight.medium,
      color: theme.colors.neutral[900],
      lineHeight: theme.typography.lineHeight.base,
    },

    priceContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },

    priceRange: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: theme.typography.fontWeight.semibold,
      color: theme.colors.primary[600],
      fontFamily: theme.typography.fontFamily.numeric,
    },

    storeIndicators: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: theme.spacing[1],
      marginTop: theme.spacing[1],
    },

    storePricesContainer: {
      gap: theme.spacing[1],
      marginTop: theme.spacing[2],
    },

    storePriceItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: theme.spacing[1],
      paddingHorizontal: theme.spacing[2],
      borderRadius: theme.borderRadius.base,
      borderWidth: 1,
      backgroundColor: theme.colors.neutral[50],
    },

    storePriceItemUnavailable: {
      backgroundColor: theme.colors.neutral[100],
      borderColor: theme.colors.neutral[300],
      opacity: 0.6,
    },

    storePriceItemBest: {
      backgroundColor: theme.colors.success[50],
      borderWidth: 2,
    },

    storePriceInfo: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginLeft: theme.spacing[2],
    },

    storePriceText: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: theme.typography.fontWeight.medium,
      color: theme.colors.neutral[900],
      fontFamily: theme.typography.fontFamily.numeric,
    },

    storePriceTextBest: {
      color: theme.colors.success[700],
      fontWeight: theme.typography.fontWeight.semibold,
    },

    storeSaleIndicator: {
      fontSize: 9,
      fontWeight: theme.typography.fontWeight.bold,
      color: theme.colors.warning[600],
      backgroundColor: theme.colors.warning[100],
      paddingHorizontal: 4,
      paddingVertical: 1,
      borderRadius: 4,
    },

    bestPriceIndicator: {
      fontSize: 9,
      fontWeight: theme.typography.fontWeight.bold,
      color: theme.colors.success[700],
      backgroundColor: theme.colors.success[100],
      paddingHorizontal: 4,
      paddingVertical: 1,
      borderRadius: 4,
    },

    unavailableText: {
      fontSize: theme.typography.fontSize.sm,
      fontWeight: theme.typography.fontWeight.medium,
      color: theme.colors.neutral[400],
      fontStyle: 'italic',
    },

    storeIndicator: {
      width: 24,
      height: 24,
      borderRadius: 12,
      justifyContent: 'center',
      alignItems: 'center',
      ...theme.shadows.card,
    },

    storeIndicatorText: {
      fontSize: 9,
      fontWeight: theme.typography.fontWeight.bold,
      letterSpacing: theme.typography.letterSpacing.wide,
    },

    moreStoresIndicator: {
      backgroundColor: theme.colors.neutral[200],
      paddingHorizontal: theme.spacing[2],
      paddingVertical: theme.spacing[1],
      borderRadius: theme.borderRadius.lg,
    },

    moreStoresText: {
      fontSize: theme.typography.fontSize.xs,
      fontWeight: theme.typography.fontWeight.medium,
      color: theme.colors.neutral[600],
    },

    sizeInfo: {
      fontSize: theme.typography.fontSize.sm,
      fontWeight: theme.typography.fontWeight.normal,
      color: theme.colors.neutral[500],
      marginTop: theme.spacing[1],
    },

    addButtonContainer: {
      position: 'absolute',
      bottom: theme.spacing.base,
      right: theme.spacing.base,
    },

    addButton: {
      width: 36,
      height: 36,
      borderRadius: 18,
      justifyContent: 'center',
      alignItems: 'center',
      ...theme.shadows.floating,
    },

    addButtonDefault: {
      backgroundColor: theme.colors.primary[500],
    },

    // Skeleton styles
    skeletonImage: {
      width: isGrid ? 80 : 64,
      height: isGrid ? 80 : 64,
      backgroundColor: theme.colors.neutral[200],
      borderRadius: theme.borderRadius.lg,
      ...(isGrid ? {
        alignSelf: 'center',
        marginBottom: theme.spacing.sm,
      } : {
        marginRight: theme.spacing.base,
      }),
    },

    skeletonText: {
      height: 16,
      backgroundColor: theme.colors.neutral[200],
      borderRadius: theme.borderRadius.sm,
      marginBottom: theme.spacing[1],
    },

    skeletonTextSmall: {
      height: 12,
      width: '60%',
      backgroundColor: theme.colors.neutral[200],
      borderRadius: theme.borderRadius.sm,
      marginBottom: theme.spacing[2],
    },

    skeletonStore: {
      width: 24,
      height: 24,
      backgroundColor: theme.colors.neutral[200],
      borderRadius: 12,
      marginRight: theme.spacing[1],
    },
  });
};