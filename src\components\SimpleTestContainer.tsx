import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

interface SimpleTestContainerProps {
  shoppingListComponent: React.ReactNode;
  allProductsComponent: React.ReactNode;
  cookbookComponent: React.ReactNode;
  onOpenDrawer: () => void;
}

export const SimpleTestContainer: React.FC<SimpleTestContainerProps> = ({
  shoppingListComponent,
  allProductsComponent,
  cookbookComponent,
  onOpenDrawer,
}) => {
  const [currentScreen, setCurrentScreen] = React.useState<'shopping' | 'products' | 'cookbook'>('shopping');

  const renderCurrentScreen = () => {
    switch (currentScreen) {
      case 'shopping':
        return (
          <View style={styles.screenContent}>
            <Text style={styles.screenTitle}>🛒 Shopping List Screen</Text>
            {shoppingListComponent}
          </View>
        );
      case 'products':
        return (
          <View style={styles.screenContent}>
            <Text style={styles.screenTitle}>🏪 All Products Screen</Text>
            {allProductsComponent}
          </View>
        );
      case 'cookbook':
        return (
          <View style={styles.screenContent}>
            <Text style={styles.screenTitle}>👨‍🍳 Cookbook Screen</Text>
            {cookbookComponent}
          </View>
        );
      default:
        return (
          <View style={styles.screenContent}>
            <Text style={styles.screenTitle}>🛒 Shopping List Screen</Text>
            {shoppingListComponent}
          </View>
        );
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.drawerButton} onPress={onOpenDrawer}>
          <Text style={styles.drawerText}>☰</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Test Navigation</Text>
      </View>
      
      <View style={styles.content}>
        {renderCurrentScreen()}
      </View>
      
      <View style={styles.bottomNav}>
        <TouchableOpacity 
          style={[styles.navButton, currentScreen === 'shopping' && styles.activeButton]}
          onPress={() => {
            console.log('🛒 Shopping button pressed');
            setCurrentScreen('shopping');
          }}
        >
          <Text style={[styles.navText, currentScreen === 'shopping' && styles.activeText]}>🛒 Shopping</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.navButton, currentScreen === 'products' && styles.activeButton]}
          onPress={() => {
            console.log('🏪 Products button pressed');
            setCurrentScreen('products');
          }}
        >
          <Text style={[styles.navText, currentScreen === 'products' && styles.activeText]}>🏪 Products</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.navButton, currentScreen === 'cookbook' && styles.activeButton]}
          onPress={() => {
            console.log('👨‍🍳 Cookbook button pressed');
            setCurrentScreen('cookbook');
          }}
        >
          <Text style={[styles.navText, currentScreen === 'cookbook' && styles.activeText]}>👨‍🍳 Cookbook</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  drawerButton: {
    padding: 8,
    marginRight: 16,
  },
  drawerText: {
    fontSize: 18,
    color: '#333',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  content: {
    flex: 1,
  },
  bottomNav: {
    flexDirection: 'row',
    backgroundColor: '#f8f8f8',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  navButton: {
    flex: 1,
    padding: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  activeButton: {
    backgroundColor: '#007AFF',
  },
  navText: {
    fontSize: 12,
    color: '#333',
  },
  activeText: {
    color: '#ffffff',
    fontWeight: 'bold',
  },
  screenContent: {
    flex: 1,
    padding: 16,
  },
  screenTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
    textAlign: 'center',
  },
});