import React, { useState, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Dimensions,
  Modal,
  ScrollView,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';
// import { ProductDetailModal } from '../../components/ProductDetailModal'; // Temporarily disabled
import { ListSelectionModal } from '../../components/ListSelectionModal';
import { useProductDeduplication } from '../../hooks/useProductDeduplication';
import { ModernProductCard } from '../../components/ModernProductCard';
import { IUnifiedProduct } from '../../services/productDeduplicationService';
import { ListSkeleton } from '../../components/SkeletonLoader';


import SimpleSearchBar from '../../components/SimpleSearchBar'; // Using simple search bar to avoid animation issues
import { ModernFilterPills, StoreFilterPills } from '../../components/ModernFilterPills';

const { width } = Dimensions.get('window');
const ITEM_WIDTH = (width - 48) / 2; // 2 columns with padding

interface Product {
  id: string;
  name: string;
  price: number;
  current_price?: number;
  original_price?: number;
  store: string;
  category?: string;
  brand?: string;
  brand_name?: string;
  unit?: string;
  size?: string;
  availability?: string;
  image_url?: string;
  is_available?: boolean;
  is_on_sale?: boolean;
  promotion_text?: string;
  last_updated?: string;
}

interface ProductGroup {
  name: string;
  products: Product[];
  lowestPrice: number;
  highestPrice: number;
  stores: string[];
}

const STORE_CONFIG = {
  woolworths: {
    name: 'Woolworths',
    color: '#00A651',
    logo: 'W',
    backgroundColor: '#00A651',
    textColor: '#FFFFFF',
  },
  newworld: {
    name: 'New World',
    color: '#E31E24',
    logo: 'NW',
    backgroundColor: '#E31E24',
    textColor: '#FFFFFF',
  },
  paknsave: {
    name: "Pak'nSave",
    color: '#FFD100',
    logo: 'PS',
    backgroundColor: '#FFD100',
    textColor: '#000000',
  },
};

const CATEGORIES = [
  { id: 'All', label: 'All Categories', icon: 'apps' },
  { id: 'fruit-veg', label: 'Fruit & Veg', icon: 'leaf' },
  { id: 'meat-poultry', label: 'Meat & Poultry', icon: 'restaurant' },
  { id: 'fish-seafood', label: 'Fish & Seafood', icon: 'fish' },
  { id: 'fridge-deli', label: 'Fridge & Deli', icon: 'snow' },
  { id: 'bakery', label: 'Bakery', icon: 'cafe' },
  { id: 'pantry', label: 'Pantry', icon: 'archive' },
  { id: 'beer-wine', label: 'Beer & Wine', icon: 'wine' },
  { id: 'drinks', label: 'Drinks', icon: 'water' },
  { id: 'health-body', label: 'Health & Body', icon: 'medical' },
  { id: 'household', label: 'Household', icon: 'home' },
  { id: 'baby-child', label: 'Baby & Child', icon: 'happy' },
  { id: 'pet', label: 'Pet', icon: 'paw' },
];

// Helper function to get display names for categories
const getCategoryDisplayName = (category: string): string => {
  const displayNames: { [key: string]: string } = {
    'All': 'All',
    'fruit-veg': 'Fruit & Veg',
    'meat-poultry': 'Meat & Poultry',
    'fish-seafood': 'Fish & Seafood',
    'fridge-deli': 'Fridge & Deli',
    'bakery': 'Bakery',
    'pantry': 'Pantry',
    'beer-wine': 'Beer & Wine',
    'drinks': 'Drinks',
    'health-body': 'Health & Body',
    'household': 'Household',
    'baby-child': 'Baby & Child',
    'pet': 'Pet',
  };
  return displayNames[category] || category;
};

const SORT_OPTIONS = [
  { label: 'Relevance', value: 'relevance' },
  { label: 'Price: Low to High', value: 'price_asc' },
  { label: 'Price: High to Low', value: 'price_desc' },
  { label: 'Name: A to Z', value: 'name_asc' },
  { label: 'Name: Z to A', value: 'name_desc' },
];

export const AllProductsScreen: React.FC = () => {
  const { colors, mode } = useTheme();

  // Memoize theme object to prevent infinite re-renders
  const theme = useMemo(() => ({
    colors: {
      semantic: {
        surface: colors.background,
        background: colors.background
      },
      primary: { 500: colors.primary }
    },
    mode
  }), [colors.background, colors.primary, mode]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [sortBy, setSortBy] = useState('relevance');
  const [showFilters, setShowFilters] = useState(false);
  const [showSort, setShowSort] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedStores, setSelectedStores] = useState<string[]>(['woolworths', 'newworld', 'paknsave']);
  const [selectedProduct, setSelectedProduct] = useState<IUnifiedProduct | null>(null);
  const [productToAdd, setProductToAdd] = useState<Product | null>(null);
  const [showListModal, setShowListModal] = useState(false);

  const { unifiedProducts, loading, error } = useProductDeduplication();

  // Optimized handlers
  const handleAddToShoppingList = useCallback((product: IUnifiedProduct) => {
    setProductToAdd(product);
    setShowListModal(true);
  }, []);

  const handleProductPress = useCallback((product: IUnifiedProduct) => {
    setSelectedProduct(product);
  }, []);

  const toggleStore = useCallback((store: string) => {
    setSelectedStores(prev =>
      prev.includes(store)
        ? prev.filter(s => s !== store)
        : [...prev, store]
    );
  }, []);

  // Enhanced category data with counts
  const categoriesWithCounts = useMemo(() => {
    // Add safety check for unifiedProducts
    if (!unifiedProducts || !Array.isArray(unifiedProducts)) {
      return CATEGORIES.map(category => ({ ...category, count: 0 }));
    }

    return CATEGORIES.map(category => ({
      ...category,
      count: category.id === 'All'
        ? unifiedProducts.length
        : unifiedProducts.filter(product =>
            product && product.category === category.id
          ).length
    }));
  }, [unifiedProducts]);

  // Memoize render functions to prevent infinite re-renders
  const renderHeader = useCallback(() => (
    <View style={[styles.header, { backgroundColor: theme.colors.semantic.surface }]}>
      <View style={styles.headerTop}>
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          All Products
        </Text>
        <View style={styles.headerButtons}>
          <TouchableOpacity
            style={[styles.headerButton, { backgroundColor: colors.background }]}
            onPress={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
          >
            <Ionicons
              name={viewMode === 'grid' ? 'list' : 'grid'}
              size={20}
              color={colors.text}
            />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.headerButton, { backgroundColor: colors.background }]}
            onPress={() => setShowSort(true)}
          >
            <Ionicons name="funnel" size={20} color={colors.text} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Simple Search Bar (no animations to avoid conflicts) */}
      <SimpleSearchBar
        value={searchQuery}
        onChangeText={setSearchQuery}
        placeholder="Search products..."
        themeMode={theme.mode}
        autoFocus={false}
        onSubmitEditing={() => {
          // Optional: trigger search on submit
        }}
      />

      {/* Modern Category Filter Pills */}
      <ModernFilterPills
        options={categoriesWithCounts}
        selectedId={selectedCategory}
        onSelectionChange={setSelectedCategory}
        themeMode={theme.mode}
        showCounts={true}
        scrollable={true}
      />

      {/* Modern Store Filter Pills */}
      <StoreFilterPills
        selectedStores={selectedStores}
        onStoreToggle={toggleStore}
        themeMode={theme.mode}
      />

      <View style={styles.resultsCount}>
        <Text style={[styles.resultsCountText, { color: colors.textSecondary }]}>
          {unifiedProducts?.length || 0} products found
        </Text>
      </View>
    </View>
  ), [
    theme.colors.semantic.surface,
    theme.mode,
    colors.text,
    colors.background,
    colors.textSecondary,
    viewMode,
    searchQuery,
    setSearchQuery,
    categoriesWithCounts,
    selectedCategory,
    setSelectedCategory,
    selectedStores,
    toggleStore,
    unifiedProducts?.length || 0
  ]);

  const getImageUrl = (products: Product[]) => {
    // Try multiple image fields in order of preference
    for (const product of products) {
      if (product.image_url && product.image_url.trim()) return product.image_url;
    }
    // If no image found, return null
    return null;
  };

  const getStoreDisplayInfo = (store: string) => {
    return STORE_CONFIG[store as keyof typeof STORE_CONFIG] || {
      name: store,
      color: '#666',
      logo: store.charAt(0).toUpperCase(),
      backgroundColor: '#666',
      textColor: '#FFFFFF'
    };
  };



  const renderSortModal = useCallback(() => (
    <Modal
      visible={showSort}
      transparent
      animationType="fade"
      onRequestClose={() => setShowSort(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor: colors.surface }]}>
          <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              Sort Products
            </Text>
            <TouchableOpacity onPress={() => setShowSort(false)}>
              <Ionicons name="close" size={24} color={colors.text} />
            </TouchableOpacity>
          </View>

          {SORT_OPTIONS.map(option => (
            <TouchableOpacity
              key={option.value}
              style={[
                styles.sortOption,
                { backgroundColor: sortBy === option.value ? colors.primary : 'transparent' }
              ]}
              onPress={() => {
                setSortBy(option.value);
                setShowSort(false);
              }}
            >
              <Text style={[
                styles.sortOptionText,
                { color: sortBy === option.value ? 'white' : colors.text }
              ]}>
                {option.label}
              </Text>
              {sortBy === option.value && (
                <Ionicons name="checkmark" size={20} color="white" />
              )}
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </Modal>
  ), [showSort, colors.surface, colors.text, colors.primary, sortBy]);

  const renderFooter = useCallback(() => {
    if (!loading) return null;

    return (
      <View style={styles.footer}>
        <ListSkeleton count={2} />
        <Text style={[styles.footerText, { color: colors.text }]}>
          Loading more products...
        </Text>
      </View>
    );
  }, [loading, colors.text]);

  const renderEmptyState = useCallback(() => {
    if (loading) return null;

    return (
      <View style={styles.emptyState}>
        <Ionicons name="search" size={64} color={colors.border} />
        <Text style={[styles.emptyTitle, { color: colors.text }]}>
          No products found
        </Text>
        <Text style={[styles.emptySubtext, { color: colors.textSecondary }]}>
          Try adjusting your search or filters
        </Text>
        {error && (
          <Text style={[styles.errorText, { color: colors.error || '#DC2626' }]}>
            {error.message || 'An error occurred'}
          </Text>
        )}
      </View>
    );
  }, [loading, colors.border, colors.text, colors.textSecondary, colors.error, error]);




  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        {renderHeader()}
        <ListSkeleton count={8} style={styles.skeletonContainer} />
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.semantic.background }]}>
      <FlatList
        data={unifiedProducts || []}
        renderItem={({ item, index }) => (
          <ModernProductCard
            item={item}
            viewMode={viewMode}
            onPress={() => handleProductPress(item)}
            onAddToList={() => handleAddToShoppingList(item)}
            themeMode={mode}
            isLoading={loading}
          />
        )}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={renderEmptyState}
        numColumns={viewMode === 'grid' ? 2 : 1}
        key={viewMode}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
      />

      {renderSortModal()}

      {/* ProductDetailModal temporarily disabled during type system fixes
      <ProductDetailModal
        visible={!!selectedProduct}
        productGroup={selectedProduct}
        onClose={() => setSelectedProduct(null)}
        onAddToList={handleAddToShoppingList}
      />
      */}

      <ListSelectionModal
        visible={showListModal}
        onClose={() => {
          setShowListModal(false);
          setProductToAdd(null);
        }}
        product={productToAdd}
        onProductAdded={(listName) => {
          console.log(`Product added to ${listName}`);
        }}
      />

    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  listContainer: {
    padding: 16,
  },
  header: {
    padding: 16,
    marginBottom: 16,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  headerButton: {
    padding: 8,
    borderRadius: 8,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    marginBottom: 16,
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
  },
  categoriesContainer: {
    marginBottom: 16,
    maxHeight: 50,
  },
  categoriesContent: {
    paddingRight: 16,
    paddingLeft: 4,
    alignItems: 'center',
  },
  categoryButton: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 24,
    marginRight: 10,
    borderWidth: 1,
    minWidth: 80,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  categoryText: {
    fontSize: 13,
    fontWeight: '600',
    textAlign: 'center',
  },
  categoryCount: {
    fontSize: 11,
    fontWeight: '500',
    textAlign: 'center',
    marginTop: 2,
  },
  storeSelector: {
    marginBottom: 16,
  },
  storeSelectorTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  storeButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  storeButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  storeButtonContent: {
    alignItems: 'center',
    gap: 6,
  },
  storeLogo: {
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  storeLogoText: {
    fontSize: 10,
    fontWeight: 'bold',
  },
  storeButtonText: {
    fontSize: 12,
    fontWeight: '600',
  },
  resultsCount: {
    marginTop: 8,
  },
  resultsCountText: {
    fontSize: 14,
  },
  productCard: {
    borderRadius: 12,
    padding: 12,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  gridCard: {
    width: ITEM_WIDTH,
    marginHorizontal: 4,
  },
  listCard: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
  },
  productImageContainer: {
    alignItems: 'center',
    marginBottom: 8,
  },
  productImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
  },
  productImagePlaceholder: {
    width: 60,
    height: 60,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  priceInfo: {
    marginBottom: 8,
  },
  priceRange: {
    fontSize: 16,
    fontWeight: '700',
  },
  savings: {
    fontSize: 12,
    fontWeight: '600',
    marginTop: 2,
  },
  sizeVariantsText: {
    fontSize: 11,
    fontStyle: 'italic',
    marginTop: 2,
  },
  storeIndicators: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  storeIndicator: {
    width: 20,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 4,
  },
  storeIndicatorText: {
    fontSize: 8,
    fontWeight: 'bold',
  },
  moreStoresText: {
    fontSize: 11,
    fontWeight: '500',
    marginLeft: 4,
  },
  addButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '80%',
    borderRadius: 12,
    padding: 20,
    maxHeight: '70%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  sortOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  sortOptionText: {
    fontSize: 16,
  },
  footer: {
    padding: 20,
    alignItems: 'center',
  },
  footerText: {
    marginTop: 8,
    fontSize: 14,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
  },
  errorText: {
    fontSize: 14,
    marginTop: 12,
    textAlign: 'center',
    fontWeight: '500',
  },
  skeletonContainer: {
    padding: 16,
  },

});
