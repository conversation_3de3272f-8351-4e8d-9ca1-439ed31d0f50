SUPABASE DATABASE DOCUMENTATION - NEW ZEALAND SUPERMARKET PRICE SCRAPER
========================================================================

This document describes the consolidated Supabase database structure for the NZ Supermarket
Price Scraper mobile app. The system has migrated from individual store-specific tables
to a unified, normalized schema that consolidates products across Woolworths, New World,
and PakNSave stores.

========================================================================
1. DATABASE SCHEMA OVERVIEW
========================================================================

The new consolidated database consists of the following main tables:

PRIMARY TABLES:
- consolidated_products (Primary Key: uuid)
- product_variants (Primary Key: bigint, Links store products to consolidated products)  
- consolidated_prices (Primary Key: bigint, Price history for consolidated products)
- category_hierarchy (Primary Key: bigint, Hierarchical category structure)
- stores (Primary Key: bigint, Store information)
- brands (Primary Key: bigint, Brand information)

SUPPORTING TABLES:
- product_size_variants (Primary Key: bigint, Multiple sizes for same product)

LEGACY TABLES (Still present for migration):
- products (Original store-specific products)
- prices (Original store-specific prices)
- categories (Original flat category structure)

========================================================================
2. TABLE STRUCTURES
========================================================================

2.1 CONSOLIDATED_PRODUCTS
--------------------------
Purpose: Master table containing unified product information across all stores

Columns:
- id (uuid, PRIMARY KEY) - Unique identifier for consolidated product
- normalized_name (text, NOT NULL) - Cleaned, standardized name for matching
- display_name (text, NOT NULL) - Best display name chosen from variants
- primary_size (text) - Most common size format
- category_id (bigint, FOREIGN KEY -> category_hierarchy.id)
- brand_id (bigint, FOREIGN KEY -> brands.id)
- match_confidence (integer, default 100) - 0-100 confidence in product matching
- manual_match (boolean, default false) - If manually verified/adjusted
- created_at (timestamptz, default now())
- updated_at (timestamptz, default now())

Indexes:
- consolidated_products(category_id)
- consolidated_products(brand_id) 
- consolidated_products(normalized_name)

2.2 PRODUCT_VARIANTS
--------------------
Purpose: Links store-specific products to their consolidated counterparts

Columns:
- id (bigint, PRIMARY KEY, auto-increment)
- consolidated_product_id (uuid, FOREIGN KEY -> consolidated_products.id)
- store_product_id (text, NOT NULL) - Original store ID (W123, N456, P789)
- store_id (bigint, FOREIGN KEY -> stores.id)
- store_name (text, NOT NULL) - Store-specific product name
- store_size (text) - Store-specific size description
- store_unit_price (numeric(10,2)) - Store-specific unit pricing
- store_unit_name (text) - Store-specific unit name
- last_seen (timestamptz, default now())
- is_active (boolean, default true)

Unique Constraints:
- (store_product_id, store_id)

Indexes:
- product_variants(consolidated_product_id)
- product_variants(store_id)

2.3 CONSOLIDATED_PRICES
-----------------------
Purpose: Price history for consolidated products across all stores

Columns:
- id (bigint, PRIMARY KEY, auto-increment)
- consolidated_product_id (uuid, FOREIGN KEY -> consolidated_products.id)
- store_id (bigint, FOREIGN KEY -> stores.id)
- price (numeric(10,2), NOT NULL)
- recorded_at (timestamptz, default now())
- is_special (boolean, default false) - Special/sale price indicator
- was_available (boolean, default true) - Product availability

Indexes:
- consolidated_prices(consolidated_product_id, store_id)
- consolidated_prices(recorded_at)
- consolidated_prices(consolidated_product_id, recorded_at DESC)

2.4 CATEGORY_HIERARCHY
----------------------
Purpose: Hierarchical category structure with main categories and subcategories

Columns:
- id (bigint, PRIMARY KEY, auto-increment)
- name (text, NOT NULL) - Category name
- parent_id (bigint, FOREIGN KEY -> category_hierarchy.id) - Parent category
- level (integer, NOT NULL, default 0) - 0=main category, 1=subcategory
- sort_order (integer, default 0) - Display ordering

Category Structure:
MAIN CATEGORIES (level 0):
1. Fresh Foods
2. Chilled & Frozen  
3. Pantry & Dry Goods
4. Beverages
5. Health & Household

SUBCATEGORIES (level 1):
- Fresh Foods: Fruit & Vegetables, Meat & Poultry, Fish & Seafood, Bakery
- Chilled & Frozen: Dairy & Deli, Frozen Foods, Desserts
- Pantry & Dry Goods: Canned & Jarred, Pasta & Rice, Snacks & Confectionery, Baking & Cooking
- Beverages: Hot Drinks, Cold Drinks, Beer Wine & Cider
- Health & Household: Health & Body, Baby & Toddler, Household & Cleaning, Pet Supplies

2.5 STORES
----------
Purpose: Store information and metadata

Columns:
- id (bigint, PRIMARY KEY, auto-increment)
- name (text, NOT NULL, UNIQUE) - Store name (Woolworths, New World, PakNSave)
- logo_url (text) - Store logo URL

2.6 BRANDS
----------
Purpose: Product brand information

Columns:
- id (bigint, PRIMARY KEY, auto-increment)
- name (text, NOT NULL, UNIQUE) - Brand name

2.7 PRODUCT_SIZE_VARIANTS
-------------------------
Purpose: Handle products available in multiple sizes

Columns:
- id (bigint, PRIMARY KEY, auto-increment)
- consolidated_product_id (uuid, FOREIGN KEY -> consolidated_products.id)
- size_name (text, NOT NULL) - Size description
- size_weight_grams (integer) - Normalized weight for comparison
- size_volume_ml (integer) - Normalized volume for comparison
- is_primary_size (boolean, default false)

Unique Constraints:
- (consolidated_product_id, size_name)

========================================================================
3. CATEGORY ORGANIZATION
========================================================================

3.1 QUERYING PRODUCTS BY CATEGORY
----------------------------------

Get all products in a main category:
```sql
SELECT cp.* 
FROM consolidated_products cp
JOIN category_hierarchy ch ON cp.category_id = ch.id
WHERE ch.name = 'Fresh Foods' OR ch.parent_id = (
  SELECT id FROM category_hierarchy WHERE name = 'Fresh Foods' AND level = 0
);
```

Get products in specific subcategory:
```sql
SELECT cp.*
FROM consolidated_products cp
JOIN category_hierarchy ch ON cp.category_id = ch.id
WHERE ch.name = 'Fruit & Vegetables';
```

Get category hierarchy:
```sql
SELECT 
  main.name as main_category,
  sub.name as subcategory,
  COUNT(cp.id) as product_count
FROM category_hierarchy main
LEFT JOIN category_hierarchy sub ON sub.parent_id = main.id
LEFT JOIN consolidated_products cp ON cp.category_id = COALESCE(sub.id, main.id)
WHERE main.level = 0
GROUP BY main.name, sub.name
ORDER BY main.sort_order, sub.sort_order;
```

3.2 CATEGORY MAPPING FROM LEGACY SYSTEM
----------------------------------------
Products are mapped from store-specific categories to the unified hierarchy during
the consolidation process. The mapping logic considers product names, original
categories, and manual overrides to assign appropriate unified categories.

========================================================================
4. API INTEGRATION GUIDE
========================================================================

4.1 QUERY CONSOLIDATED PRODUCTS ACROSS STORES
----------------------------------------------

Get all products with current prices from all stores:
```sql
SELECT * FROM consolidated_product_details
ORDER BY display_name;
```

Get products with price comparison:
```sql
SELECT 
  cp.display_name,
  cp.primary_size,
  b.name as brand,
  ch.name as category,
  json_agg(
    json_build_object(
      'store', s.name,
      'price', latest.price,
      'updated', latest.recorded_at
    ) ORDER BY latest.price
  ) as prices
FROM consolidated_products cp
LEFT JOIN brands b ON cp.brand_id = b.id
LEFT JOIN category_hierarchy ch ON cp.category_id = ch.id
LEFT JOIN LATERAL (
  SELECT DISTINCT ON (store_id) store_id, price, recorded_at
  FROM consolidated_prices 
  WHERE consolidated_product_id = cp.id AND was_available = true
  ORDER BY store_id, recorded_at DESC
) latest ON true
LEFT JOIN stores s ON latest.store_id = s.id
GROUP BY cp.id, cp.display_name, cp.primary_size, b.name, ch.name;
```

4.2 FILTER PRODUCTS BY CATEGORY
--------------------------------

Filter by main category:
```sql
SELECT cp.*, ch_main.name as main_category, ch_sub.name as subcategory
FROM consolidated_products cp
LEFT JOIN category_hierarchy ch_sub ON cp.category_id = ch_sub.id
LEFT JOIN category_hierarchy ch_main ON ch_sub.parent_id = ch_main.id
WHERE ch_main.name = 'Beverages' OR (ch_sub.parent_id IS NULL AND ch_sub.name = 'Beverages');
```

Filter by subcategory:
```sql
SELECT cp.*
FROM consolidated_products cp
JOIN category_hierarchy ch ON cp.category_id = ch.id
WHERE ch.name = 'Cold Drinks';
```

4.3 SEARCH PRODUCTS
-------------------

Search by name (case-insensitive):
```sql
SELECT cp.*, b.name as brand_name
FROM consolidated_products cp
LEFT JOIN brands b ON cp.brand_id = b.id
WHERE cp.display_name ILIKE '%coca cola%'
   OR cp.normalized_name ILIKE '%coca cola%'
   OR b.name ILIKE '%coca cola%';
```

Full-text search across products:
```sql
SELECT cp.*, b.name as brand_name,
       ts_rank(to_tsvector('english', cp.display_name || ' ' || COALESCE(b.name, '')), 
                plainto_tsquery('english', 'search term')) as rank
FROM consolidated_products cp
LEFT JOIN brands b ON cp.brand_id = b.id
WHERE to_tsvector('english', cp.display_name || ' ' || COALESCE(b.name, '')) 
      @@ plainto_tsquery('english', 'search term')
ORDER BY rank DESC;
```

4.4 PRICE COMPARISONS AND ANALYTICS
------------------------------------

Get cheapest stores for a product:
```sql
SELECT 
  cp.display_name,
  s.name as store,
  cp_prices.price,
  cp_prices.is_special
FROM consolidated_products cp
JOIN consolidation_prices cp_prices ON cp.id = cp_prices.consolidated_product_id
JOIN stores s ON cp_prices.store_id = s.id
WHERE cp.id = 'your-product-uuid'
  AND cp_prices.recorded_at = (
    SELECT MAX(recorded_at) 
    FROM consolidated_prices 
    WHERE consolidated_product_id = cp.id 
      AND store_id = cp_prices.store_id
  )
ORDER BY cp_prices.price;
```

Get price trends over time:
```sql
SELECT 
  DATE_TRUNC('day', recorded_at) as date,
  s.name as store,
  AVG(price) as avg_price,
  MIN(price) as min_price,
  MAX(price) as max_price
FROM consolidated_prices cp_prices
JOIN stores s ON cp_prices.store_id = s.id
WHERE consolidated_product_id = 'your-product-uuid'
  AND recorded_at >= NOW() - INTERVAL '30 days'
GROUP BY DATE_TRUNC('day', recorded_at), s.name
ORDER BY date, s.name;
```

========================================================================
5. MIGRATION NOTES
========================================================================

5.1 CHANGES FROM LEGACY SYSTEM
-------------------------------

OLD STRUCTURE:
- products table: Store-specific products with individual records per store
- prices table: Direct relationship to store-specific products
- categories table: Flat category structure
- Direct store identification via product.id prefixes (W123, N456, P789)

NEW STRUCTURE:
- consolidated_products: Unified products across all stores
- product_variants: Links store-specific products to consolidated products
- consolidated_prices: Prices linked to consolidated products + store
- category_hierarchy: Two-level hierarchical categories
- Explicit store relationships via foreign keys

5.2 MIGRATION BENEFITS
----------------------

1. REDUCED DUPLICATION: Same products across stores are now consolidated
2. BETTER PRICE COMPARISON: Direct price comparison via consolidated_product_id
3. IMPROVED SEARCH: Search once across all stores instead of per-store queries
4. HIERARCHICAL CATEGORIES: Better organization and filtering capabilities
5. NORMALIZED DATA: Cleaner data structure with proper relationships

5.3 MOBILE APP TRANSITION GUIDE
--------------------------------

UPDATE QUERIES FROM:
```sql
-- OLD: Query products from specific store
SELECT * FROM products WHERE id LIKE 'W%';
```

TO:
```sql
-- NEW: Query consolidated products with store prices
SELECT * FROM consolidated_product_details;
```

UPDATE PRICE QUERIES FROM:
```sql
-- OLD: Get prices for product across stores
SELECT p.*, pr.price, pr.recorded_at 
FROM products p 
JOIN prices pr ON p.id = pr.product_id 
WHERE p.name ILIKE '%product name%';
```

TO:
```sql
-- NEW: Get consolidated product with all store prices
SELECT cp.*, 
       (SELECT json_agg(json_build_object('store', s.name, 'price', cpr.price, 'date', cpr.recorded_at))
        FROM consolidated_prices cpr 
        JOIN stores s ON cpr.store_id = s.id 
        WHERE cpr.consolidated_product_id = cp.id) as prices
FROM consolidated_products cp 
WHERE cp.display_name ILIKE '%product name%';
```

5.4 API ENDPOINT UPDATES NEEDED
--------------------------------

1. Update product listing endpoints to use consolidated_product_details view
2. Modify search endpoints to query consolidated_products instead of products
3. Update category endpoints to use category_hierarchy instead of categories
4. Modify price comparison endpoints to use consolidated_prices
5. Add new endpoints for:
   - Category hierarchy browsing
   - Store-specific product variant details
   - Advanced price analytics and trends

5.5 PERFORMANCE CONSIDERATIONS
------------------------------

1. Use the consolidated_product_details view for most read operations
2. Index usage is optimized for common query patterns
3. Consider implementing caching for frequently accessed category hierarchies
4. Price queries benefit from the compound indexes on (consolidated_product_id, recorded_at)
5. Full-text search capabilities are available but may require additional indexes for large datasets

========================================================================
END OF DOCUMENTATION
========================================================================

Last Updated: January 2025
Database Version: Supabase PostgreSQL with consolidated products schema
Compatible with: Expo mobile app, React Native applications