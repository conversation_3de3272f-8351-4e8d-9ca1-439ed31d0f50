import React from 'react';
import {
  View,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface SimpleSearchBarProps {
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  autoFocus?: boolean;
  editable?: boolean;
  themeMode?: 'light' | 'dark';
  style?: ViewStyle;
  inputStyle?: TextStyle;
  onFocus?: () => void;
  onBlur?: () => void;
  onSubmitEditing?: () => void;
  onClear?: () => void;
}

const SimpleSearchBar: React.FC<SimpleSearchBarProps> = ({
  value,
  onChangeText,
  placeholder = 'Search products...',
  autoFocus = false,
  editable = true,
  themeMode = 'light',
  style,
  inputStyle,
  onFocus,
  onBlur,
  onSubmitEditing,
  onClear,
}) => {
  const handleClear = () => {
    onChangeText('');
    onClear?.();
  };

  // Simple hardcoded colors to avoid theme import issues
  const colors = {
    background: '#f8f9fa',
    containerBg: '#ffffff',
    border: '#e9ecef',
    text: '#212529',
    placeholder: '#6c757d',
    primary: '#007bff',
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }, style]}>
      <View style={[
        styles.searchContainer,
        {
          backgroundColor: colors.containerBg,
          borderColor: colors.border,
        }
      ]}>
        <Ionicons
          name="search"
          size={20}
          color={colors.placeholder}
          style={styles.searchIcon}
        />

        <TextInput
          style={[
            styles.input,
            {
              color: colors.text,
            },
            inputStyle,
          ]}
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          placeholderTextColor={colors.placeholder}
          onFocus={onFocus}
          onBlur={onBlur}
          onSubmitEditing={onSubmitEditing}
          autoFocus={autoFocus}
          editable={editable}
          autoCorrect={false}
          autoCapitalize="none"
          returnKeyType="search"
          blurOnSubmit={false}
          selectionColor={colors.primary}
          keyboardAppearance={themeMode === 'dark' ? 'dark' : 'light'}
        />

        {value.length > 0 && (
          <TouchableOpacity
            onPress={handleClear}
            style={styles.clearButton}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Ionicons
              name="close-circle"
              size={20}
              color={colors.placeholder}
            />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
    minHeight: 44,
  },
  searchIcon: {
    marginRight: 8,
  },
  input: {
    flex: 1,
    fontSize: 16,
    lineHeight: 20,
    paddingVertical: 4,
  },
  clearButton: {
    marginLeft: 8,
    padding: 2,
  },
});

export default SimpleSearchBar;
