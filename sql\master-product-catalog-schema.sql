-- MASTER PRODUCT CATALOG SCHEMA
-- Implements the comprehensive product matching and consolidation system
-- Based on your research blueprint for robust price comparison

-- Enable required extensions for advanced matching
CREATE EXTENSION IF NOT EXISTS pg_trgm;
CREATE EXTENSION IF NOT EXISTS fuzzystrmatch;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create schemas for logical separation
CREATE SCHEMA IF NOT EXISTS master_data;
CREATE SCHEMA IF NOT EXISTS scraped_data;

-- 1. MASTER PRODUCTS TABLE (Single source of truth)
CREATE TABLE IF NOT EXISTS master_data.products (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  canonical_name TEXT NOT NULL,
  description TEXT,
  brand TEXT,
  category TEXT,
  image_url TEXT,
  ean_upc TEXT, -- For identifier matching
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. PRODUCT VARIANTS TABLE (<PERSON>les sizing, colors, etc.)
CREATE TABLE IF NOT EXISTS master_data.product_variants (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_id UUID REFERENCES master_data.products(id) ON DELETE CASCADE,
  variant_type TEXT NOT NULL, -- 'size', 'color', 'pack_size'
  variant_value TEXT NOT NULL, -- 'Small', 'Medium', '500g', '1L'
  sku TEXT,
  meta_data JSONB, -- Flexible attributes
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. STORES TABLE (Unchanged from existing)
-- Keep using your existing public.stores table

-- 4. SCRAPED DATA STAGING TABLE (Raw data preservation)
CREATE TABLE IF NOT EXISTS scraped_data.store_product_listings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  store_id TEXT NOT NULL, -- Reference to public.stores
  scraped_product_name TEXT NOT NULL,
  scraped_description TEXT,
  scraped_url TEXT,
  scraped_price NUMERIC,
  scraped_unit_of_measure TEXT,
  scraped_size_info TEXT,
  last_scraped_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_matched BOOLEAN DEFAULT FALSE,
  matched_product_id UUID REFERENCES master_data.products(id),
  matched_product_variant_id UUID REFERENCES master_data.product_variants(id),
  match_confidence REAL, -- For quality tracking
  match_method TEXT -- 'identifier', 'fuzzy', 'semantic', 'manual'
);

-- 5. FINAL PRODUCT STORE PRICES (Clean, matched data)
CREATE TABLE IF NOT EXISTS master_data.product_store_prices (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_id UUID NOT NULL REFERENCES master_data.products(id),
  product_variant_id UUID REFERENCES master_data.product_variants(id),
  store_id TEXT NOT NULL, -- Reference to public.stores
  price NUMERIC NOT NULL,
  unit_price NUMERIC, -- Normalized price per standard unit
  unit_of_measure TEXT, -- Standardized unit
  product_url TEXT,
  last_updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  scraped_listing_id UUID REFERENCES scraped_data.store_product_listings(id)
);

-- INDEXES FOR PERFORMANCE

-- Master products indexes
CREATE INDEX IF NOT EXISTS idx_master_products_canonical_name_gin 
ON master_data.products USING gin(canonical_name gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_master_products_brand 
ON master_data.products(brand);
CREATE INDEX IF NOT EXISTS idx_master_products_category 
ON master_data.products(category);
CREATE INDEX IF NOT EXISTS idx_master_products_ean_upc 
ON master_data.products(ean_upc) WHERE ean_upc IS NOT NULL;

-- Product variants indexes
CREATE INDEX IF NOT EXISTS idx_product_variants_product_id 
ON master_data.product_variants(product_id);
CREATE INDEX IF NOT EXISTS idx_product_variants_type_value 
ON master_data.product_variants(variant_type, variant_value);

-- Scraped data indexes
CREATE INDEX IF NOT EXISTS idx_scraped_listings_store_id 
ON scraped_data.store_product_listings(store_id);
CREATE INDEX IF NOT EXISTS idx_scraped_listings_product_name_gin 
ON scraped_data.store_product_listings USING gin(scraped_product_name gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_scraped_listings_is_matched 
ON scraped_data.store_product_listings(is_matched);
CREATE INDEX IF NOT EXISTS idx_scraped_listings_matched_product 
ON scraped_data.store_product_listings(matched_product_id) WHERE matched_product_id IS NOT NULL;

-- Product store prices indexes
CREATE INDEX IF NOT EXISTS idx_product_store_prices_product_id 
ON master_data.product_store_prices(product_id);
CREATE INDEX IF NOT EXISTS idx_product_store_prices_store_id 
ON master_data.product_store_prices(store_id);
CREATE INDEX IF NOT EXISTS idx_product_store_prices_price 
ON master_data.product_store_prices(price);
CREATE INDEX IF NOT EXISTS idx_product_store_prices_unit_price 
ON master_data.product_store_prices(unit_price) WHERE unit_price IS NOT NULL;

-- DATA MIGRATION FUNCTIONS

-- Function to migrate existing products to master catalog
CREATE OR REPLACE FUNCTION migrate_existing_products_to_master_catalog()
RETURNS void AS $$
DECLARE
  product_record RECORD;
  master_product_id UUID;
  existing_master_id UUID;
BEGIN
  -- Iterate through existing products table
  FOR product_record IN 
    SELECT DISTINCT 
      name,
      brand,
      category,
      image_url,
      barcode as ean_upc
    FROM public.products 
    WHERE name IS NOT NULL
  LOOP
    -- Check if master product already exists
    SELECT id INTO existing_master_id 
    FROM master_data.products 
    WHERE canonical_name = product_record.name 
      AND COALESCE(brand, '') = COALESCE(product_record.brand, '');

    IF existing_master_id IS NULL THEN
      -- Create new master product
      INSERT INTO master_data.products (
        canonical_name,
        brand,
        category,
        image_url,
        ean_upc
      ) VALUES (
        product_record.name,
        product_record.brand,
        product_record.category,
        product_record.image_url,
        product_record.ean_upc
      ) RETURNING id INTO master_product_id;

      RAISE NOTICE 'Created master product: % (ID: %)', product_record.name, master_product_id;
    ELSE
      master_product_id := existing_master_id;
    END IF;

    -- Migrate store-specific data to scraped_data
    INSERT INTO scraped_data.store_product_listings (
      store_id,
      scraped_product_name,
      scraped_description,
      scraped_price,
      scraped_size_info,
      is_matched,
      matched_product_id,
      match_method,
      match_confidence
    )
    SELECT 
      p.store,
      p.name,
      p.description,
      COALESCE(p.current_price, p.price),
      COALESCE(p.size, p.unit),
      true, -- Mark as matched since we're doing direct migration
      master_product_id,
      'migration',
      1.0 -- High confidence for direct migration
    FROM public.products p
    WHERE p.name = product_record.name 
      AND COALESCE(p.brand, '') = COALESCE(product_record.brand, '')
    ON CONFLICT DO NOTHING; -- Avoid duplicates

    -- Create product store prices
    INSERT INTO master_data.product_store_prices (
      product_id,
      store_id,
      price,
      product_url,
      scraped_listing_id
    )
    SELECT 
      master_product_id,
      p.store,
      COALESCE(p.current_price, p.price),
      p.image_url, -- Using as product URL for now
      spl.id
    FROM public.products p
    JOIN scraped_data.store_product_listings spl ON (
      spl.store_id = p.store 
      AND spl.scraped_product_name = p.name
      AND spl.matched_product_id = master_product_id
    )
    WHERE p.name = product_record.name 
      AND COALESCE(p.brand, '') = COALESCE(product_record.brand, '')
      AND COALESCE(p.current_price, p.price) IS NOT NULL
    ON CONFLICT DO NOTHING;

  END LOOP;

  RAISE NOTICE 'Migration to master catalog completed';
END;
$$ LANGUAGE plpgsql;

-- PRODUCT MATCHING FUNCTIONS (Multi-stage pipeline)

-- 1. Identifier-based matching function
CREATE OR REPLACE FUNCTION match_products_by_identifier()
RETURNS TABLE(matched_count INTEGER) AS $$
DECLARE
  match_count INTEGER := 0;
BEGIN
  -- Match unmatched products by EAN/UPC
  UPDATE scraped_data.store_product_listings spl
  SET 
    matched_product_id = mp.id,
    is_matched = true,
    match_method = 'identifier',
    match_confidence = 1.0
  FROM master_data.products mp
  WHERE spl.is_matched = false
    AND mp.ean_upc IS NOT NULL
    AND spl.scraped_product_name ~ mp.ean_upc -- Simple regex match for now
    AND spl.matched_product_id IS NULL;

  GET DIAGNOSTICS match_count = ROW_COUNT;
  RETURN QUERY SELECT match_count;
END;
$$ LANGUAGE plpgsql;

-- 2. Enhanced fuzzy matching with size awareness
CREATE OR REPLACE FUNCTION match_products_by_fuzzy_similarity(
  similarity_threshold REAL DEFAULT 0.7
)
RETURNS TABLE(matched_count INTEGER) AS $$
DECLARE
  match_count INTEGER := 0;
  unmatched_record RECORD;
  best_match_id UUID;
  best_similarity REAL;
BEGIN
  -- Set similarity threshold
  PERFORM set_limit(similarity_threshold);

  -- Process unmatched products
  FOR unmatched_record IN 
    SELECT id, scraped_product_name, scraped_size_info
    FROM scraped_data.store_product_listings 
    WHERE is_matched = false
  LOOP
    -- Find best fuzzy match from master products
    SELECT mp.id, similarity(lower(mp.canonical_name), lower(unmatched_record.scraped_product_name))
    INTO best_match_id, best_similarity
    FROM master_data.products mp
    WHERE lower(mp.canonical_name) % lower(unmatched_record.scraped_product_name)
      AND similarity(lower(mp.canonical_name), lower(unmatched_record.scraped_product_name)) >= similarity_threshold
    ORDER BY similarity(lower(mp.canonical_name), lower(unmatched_record.scraped_product_name)) DESC
    LIMIT 1;

    -- Update if we found a good match
    IF best_match_id IS NOT NULL THEN
      UPDATE scraped_data.store_product_listings
      SET 
        matched_product_id = best_match_id,
        is_matched = true,
        match_method = 'fuzzy',
        match_confidence = best_similarity
      WHERE id = unmatched_record.id;
      
      match_count := match_count + 1;
    END IF;
  END LOOP;

  RETURN QUERY SELECT match_count;
END;
$$ LANGUAGE plpgsql;

-- 3. Update product store prices from matched listings
CREATE OR REPLACE FUNCTION update_product_store_prices()
RETURNS void AS $$
BEGIN
  -- Insert/update product store prices from matched scraped data
  INSERT INTO master_data.product_store_prices (
    product_id,
    store_id,
    price,
    product_url,
    last_updated_at,
    scraped_listing_id
  )
  SELECT 
    spl.matched_product_id,
    spl.store_id,
    spl.scraped_price,
    spl.scraped_url,
    spl.last_scraped_at,
    spl.id
  FROM scraped_data.store_product_listings spl
  WHERE spl.is_matched = true 
    AND spl.matched_product_id IS NOT NULL
    AND spl.scraped_price IS NOT NULL
  ON CONFLICT (product_id, store_id) DO UPDATE SET
    price = EXCLUDED.price,
    product_url = EXCLUDED.product_url,
    last_updated_at = EXCLUDED.last_updated_at,
    scraped_listing_id = EXCLUDED.scraped_listing_id;
END;
$$ LANGUAGE plpgsql;

-- VIEW FOR CONSOLIDATED PRODUCT DISPLAY (Your ConsolidatedProductCard needs this)
CREATE OR REPLACE VIEW master_data.consolidated_products AS
SELECT 
  mp.id,
  mp.canonical_name as name,
  mp.brand,
  mp.category,
  mp.image_url,
  -- Aggregate store prices
  json_object_agg(
    COALESCE(psp.store_id, 'unknown'), 
    COALESCE(psp.price, 0)
  ) FILTER (WHERE psp.price IS NOT NULL) as store_prices,
  -- Price statistics
  MIN(psp.price) as lowest_price,
  MAX(psp.price) as highest_price,
  MAX(psp.price) - MIN(psp.price) as max_savings,
  -- Best store (lowest price)
  (
    SELECT psp2.store_id 
    FROM master_data.product_store_prices psp2 
    WHERE psp2.product_id = mp.id 
    ORDER BY psp2.price ASC 
    LIMIT 1
  ) as best_store,
  -- Available stores
  array_agg(DISTINCT psp.store_id) FILTER (WHERE psp.store_id IS NOT NULL) as available_stores,
  -- Update timestamp
  MAX(psp.last_updated_at) as last_updated
FROM master_data.products mp
LEFT JOIN master_data.product_store_prices psp ON mp.id = psp.product_id
GROUP BY mp.id, mp.canonical_name, mp.brand, mp.category, mp.image_url
HAVING COUNT(psp.id) > 0; -- Only products with prices

-- PERMISSIONS
GRANT USAGE ON SCHEMA master_data TO authenticated;
GRANT USAGE ON SCHEMA scraped_data TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA master_data TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA scraped_data TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA master_data TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA scraped_data TO authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA master_data TO authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA scraped_data TO authenticated;

-- SAMPLE EXECUTION
-- Run initial migration (uncomment to execute)
-- SELECT migrate_existing_products_to_master_catalog();
-- SELECT match_products_by_identifier();
-- SELECT match_products_by_fuzzy_similarity(0.7);
-- SELECT update_product_store_prices();