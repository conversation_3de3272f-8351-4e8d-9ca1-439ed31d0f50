import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Modal,
  ScrollView,
  Alert,
  ActivityIndicator,
  FlatList,
  Image,
} from 'react-native';
import { useTheme } from '../context/ThemeContext';
import { theme } from '../styles';
import { productFetchService, Product } from '../services/productFetchService';
import { smartProductMatcher } from '../services/smartProductMatcher';
import { smartSearchService, SmartSearchResult } from '../services/smartSearchService';
import { productImageService, ProductImage } from '../services/productImageService';
import { SwipeableBrandSelector } from './SwipeableBrandSelector';
import { StorePrice, ShoppingListItem } from '../types/shoppingList';

// Simple debounce implementation
const debounce = (func: Function, delay: number) => {
  let timeoutId: NodeJS.Timeout;
  return (...args: any[]) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(null, args), delay);
  };
};

interface ProductSuggestion {
  id: string;
  name: string;
  brand: string;
  category: string;
  unit: string;
  storePrices: StorePrice[];
  sizeVariants?: SizeVariant[];
  image_url?: string;
  relevance_score?: number;
  match_type?: string;
  isCommonItem?: boolean;
}

interface SizeVariant {
  brand: string;
  size: string;
  storePrices: StorePrice[];
}

interface AddEditItemModalProps {
  visible: boolean;
  onClose: () => void;
  onSave: (item: ShoppingListItem) => void;
  editItem?: ShoppingListItem; // If provided, we're editing; otherwise adding
}

export const AddEditItemModal: React.FC<AddEditItemModalProps> = ({
  visible,
  onClose,
  onSave,
  editItem,
}) => {
  const { colors } = useTheme();
  const isEditing = !!editItem;

  // Form state
  const [name, setName] = useState('');
  const [quantity, setQuantity] = useState(1);
  const [unit, setUnit] = useState('');
  const [category, setCategory] = useState('');
  const [selectedBrand, setSelectedBrand] = useState('');
  const [selectedSize, setSelectedSize] = useState('');
  
  // Search state
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<ProductSuggestion[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<ProductSuggestion | null>(null);
  const [availableBrands, setAvailableBrands] = useState<string[]>([]);
  const [availableSizes, setAvailableSizes] = useState<string[]>([]);
  const [spellingSuggestions, setSpellingSuggestions] = useState<string[]>([]);
  const [smartSuggestions, setSmartSuggestions] = useState<string[]>([]);

  // Predefined categories
  const categories = [
    'Dairy', 'Produce', 'Meat', 'Bakery', 'Pantry', 'Beverages', 
    'Frozen', 'Snacks', 'Health & Beauty', 'Household', 'Other'
  ];

  // Common units
  const units = ['', 'kg', 'g', 'L', 'ml', 'pack', 'bottle', 'can', 'box'];

  // Quick suggestions for elderly users
  const quickSuggestions = smartProductMatcher.getProductSuggestions();

  // Load smart suggestions on mount
  useEffect(() => {
    const loadSmartSuggestions = async () => {
      try {
        const suggestions = await smartSearchService.getSuggestions('', 12);
        setSmartSuggestions(suggestions.map(s => s.query));
      } catch (error) {
        console.warn('Failed to load smart suggestions:', error);
      }
    };
    
    if (visible && !isEditing) {
      loadSmartSuggestions();
    }
  }, [visible, isEditing]);

  // Debounced search function with smart search
  const debouncedSearch = useCallback(
    debounce(async (query: string) => {
      if (query.trim().length < 2) {
        setSearchResults([]);
        setShowSuggestions(false);
        setSpellingSuggestions([]);
        return;
      }

      try {
        setIsSearching(true);
        
        // Use smart search service for better results
        const smartResults = await smartSearchService.searchProducts(query, {
          maxResults: 8,
          includeImages: true,
          fuzzyTolerance: 0.7
        });

        // Convert smart search results to ProductSuggestion format
        const suggestions: ProductSuggestion[] = [];
        const productGroups = new Map<string, SmartSearchResult[]>();

        // Group by product name to combine different stores
        smartResults.forEach(result => {
          const key = `${result.name}_${result.brand}`;
          if (!productGroups.has(key)) {
            productGroups.set(key, []);
          }
          productGroups.get(key)!.push(result);
        });

        productGroups.forEach((results, key) => {
          const firstResult = results[0];
          const storePrices: StorePrice[] = results.map(result => ({
            store: result.store as 'woolworths' | 'newworld' | 'paknsave',
            price: result.price,
            available: true,
            brand: result.brand,
            size: result.unit
          }));

          suggestions.push({
            id: firstResult.id,
            name: firstResult.name,
            brand: firstResult.brand,
            category: firstResult.category,
            unit: firstResult.unit || 'each',
            storePrices,
            sizeVariants: [],
            image_url: firstResult.image_url,
            relevance_score: firstResult.relevance_score,
            match_type: firstResult.match_type,
            isCommonItem: firstResult.isCommonItem
          });
        });

        // Sort by relevance score
        suggestions.sort((a, b) => (b.relevance_score || 0) - (a.relevance_score || 0));

        setSearchResults(suggestions);
        setShowSuggestions(true);

        // Get spelling suggestions if no good results
        if (suggestions.length === 0 || suggestions[0].relevance_score! < 60) {
          const spellingSugs = await smartSearchService.getSpellingCorrections(query);
          setSpellingSuggestions(spellingSugs);
        } else {
          setSpellingSuggestions([]);
        }

      } catch (error) {
        console.error('Smart search failed:', error);
        // Fallback to original search
        try {
          const result = await productFetchService.searchProducts(query, {
            maxResults: 8,
            sortBy: 'relevance'
          });

          const grouped = result.products.reduce((acc, product) => {
            const key = `${product.name}_${product.brand}`;
            if (!acc[key]) {
              acc[key] = {
                id: product.id,
                name: product.name,
                brand: product.brand,
                category: product.category,
                unit: product.unit,
                storePrices: [],
                sizeVariants: []
              };
            }
            
            acc[key].storePrices.push({
              store: product.store,
              price: product.price,
              available: product.availability === 'in_stock',
              brand: product.brand,
              size: product.unit
            });
            
            return acc;
          }, {} as Record<string, ProductSuggestion>);

          setSearchResults(Object.values(grouped));
          setShowSuggestions(true);
        } catch (fallbackError) {
          console.error('Fallback search also failed:', fallbackError);
        }
      } finally {
        setIsSearching(false);
      }
    }, 300),
    []
  );

  // Handle search input
  const handleSearchChange = (text: string) => {
    setSearchQuery(text);
    setName(text);
    debouncedSearch(text);
  };

  // Handle quick suggestion selection
  const handleQuickSuggestion = (suggestion: string) => {
    setSearchQuery(suggestion);
    setName(suggestion);
    debouncedSearch(suggestion);
  };

  // Select a product suggestion
  const selectProduct = useCallback((product: ProductSuggestion) => {
    setSelectedProduct(product);
    setName(product.name);
    setSelectedBrand(product.brand);
    setCategory(product.category);
    setUnit(product.unit);
    setSearchQuery(product.name);
    setShowSuggestions(false);
    
    // Get available brands for this product
    const brands = [...new Set(product.storePrices.map(sp => sp.brand).filter((brand): brand is string => Boolean(brand)))];
    setAvailableBrands(brands);
    
    // Get available sizes
    const sizes = [...new Set(product.storePrices.map(sp => sp.size).filter((size): size is string => Boolean(size)))];
    setAvailableSizes(sizes);
    
    if (sizes.length > 0) {
      setSelectedSize(sizes[0]);
    }
  }, []);

  // Reset form when modal opens/closes or editItem changes
  useEffect(() => {
    if (visible) {
      if (editItem) {
        setName(editItem.name);
        setSearchQuery(editItem.name);
        setQuantity(editItem.quantity);
        setUnit(editItem.unit || '');
        setCategory(editItem.category || '');
        setSelectedBrand(editItem.selectedBrand || '');
        setSelectedSize(editItem.selectedSize || '');
        setSelectedProduct(null);
        setShowSuggestions(false);
      } else {
        // Reset for new item
        setName('');
        setSearchQuery('');
        setQuantity(1);
        setUnit('');
        setCategory('');
        setSelectedBrand('');
        setSelectedSize('');
        setSelectedProduct(null);
        setSearchResults([]);
        setShowSuggestions(false);
        setAvailableBrands([]);
        setAvailableSizes([]);
      }
    }
  }, [visible, editItem]);

  const generateDefaultStorePrices = (): StorePrice[] => {
    if (selectedProduct) {
      return selectedProduct.storePrices;
    }
    return [
      { store: 'woolworths', price: undefined, available: false },
      { store: 'paknsave', price: undefined, available: false },
      { store: 'newworld', price: undefined, available: false },
    ];
  };

  const generateSizeVariants = (): SizeVariant[] => {
    if (!selectedProduct) return [];
    
    // Group by size to create variants
    const sizeGroups = selectedProduct.storePrices.reduce((acc, sp) => {
      if (sp.size) {
        if (!acc[sp.size]) {
          acc[sp.size] = [];
        }
        acc[sp.size].push(sp);
      }
      return acc;
    }, {} as Record<string, StorePrice[]>);
    
    return Object.entries(sizeGroups).map(([size, storePrices]) => ({
      brand: selectedBrand || selectedProduct?.brand || 'Generic',
      size,
      storePrices
    }));
  };

  const handleSave = () => {
    if (!name.trim()) {
      Alert.alert('Error', 'Please enter a product name');
      return;
    }

    const item: ShoppingListItem = {
      id: editItem?.id || `item_${Date.now()}`,
      name: name.trim(),
      checked: editItem?.checked || false,
      quantity,
      unit: unit.trim() || undefined,
      category: category || 'Other',
      selectedBrand: selectedBrand.trim() || undefined,
      selectedSize: selectedSize.trim() || undefined,
      storePrices: generateDefaultStorePrices(),
      sizeVariants: generateSizeVariants(),
      addedAt: editItem?.addedAt || new Date(),
    };

    onSave(item);
    onClose();
  };

  const renderProductSuggestions = () => {
    if (!showSuggestions) return null;

    // Show spelling suggestions if no results or poor matches
    if (spellingSuggestions.length > 0 && (searchResults.length === 0 || (searchResults[0]?.relevance_score || 0) < 60)) {
      return (
        <View style={[styles.suggestionsContainer, { backgroundColor: colors.surface, borderColor: colors.border }]}>
          <View style={styles.spellingSuggestionsHeader}>
            <Text style={[styles.spellingSuggestionsTitle, { color: colors.textSecondary }]}>
              Did you mean:
            </Text>
          </View>
          {spellingSuggestions.map((suggestion, index) => (
            <TouchableOpacity
              key={index}
              style={[styles.suggestionItem, { borderBottomColor: colors.border }]}
              onPress={() => {
                setSearchQuery(suggestion);
                setName(suggestion);
                debouncedSearch(suggestion);
              }}
            >
              <Text style={[styles.suggestionName, { color: theme.colors.primary }]}>
                "{suggestion}"
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      );
    }

    if (searchResults.length === 0) return null;

    return (
      <View style={[styles.suggestionsContainer, { backgroundColor: colors.surface, borderColor: colors.border }]}>
        {searchResults.slice(0, 6).map((item) => (
          <TouchableOpacity
            key={item.id}
            style={[styles.suggestionItem, { borderBottomColor: colors.border }]}
            onPress={() => selectProduct(item)}
          >
            <View style={styles.suggestionContent}>
              <View style={styles.suggestionHeader}>
                <View style={styles.suggestionTextContent}>
                  <View style={styles.suggestionNameRow}>
                    <Text style={[styles.suggestionName, { color: colors.text }]}>
                      {item.name}
                    </Text>
                    {item.isCommonItem && (
                      <View style={[styles.popularBadge, { backgroundColor: theme.colors.primary }]}>
                        <Text style={styles.popularBadgeText}>Popular</Text>
                      </View>
                    )}
                  </View>
                  <Text style={[styles.suggestionBrand, { color: colors.textSecondary }]}>
                    {item.brand} • {item.category}
                    {item.match_type && item.match_type !== 'exact' && (
                      <Text style={{ color: colors.textTertiary }}> • {item.match_type} match</Text>
                    )}
                  </Text>
                  <View style={styles.suggestionPrices}>
                    {item.storePrices.slice(0, 3).map((sp, index) => (
                      <Text key={index} style={[styles.suggestionPrice, { color: colors.textTertiary }]}>
                        {sp.store === 'woolworths' ? '🍎' : sp.store === 'paknsave' ? '💰' : '🛒'} ${sp.price?.toFixed(2) || 'N/A'}
                      </Text>
                    ))}
                  </View>
                </View>
                {item.image_url && (
                  <View style={styles.suggestionImageContainer}>
                    <Image
                      source={{ uri: item.image_url }}
                      style={styles.suggestionImage}
                      resizeMode="contain"
                    />
                  </View>
                )}
              </View>
            </View>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const renderQuickSuggestions = () => {
    if (searchQuery.trim().length > 0 || showSuggestions) return null;

    return (
      <View style={styles.quickSuggestionsContainer}>
        <Text style={[styles.quickSuggestionsTitle, { color: colors.text }]}>
          Common Items
        </Text>
        <Text style={[styles.quickSuggestionsSubtitle, { color: colors.textSecondary }]}>
          Tap any item below to add it to your list
        </Text>
        <View style={styles.quickSuggestionsGrid}>
          {quickSuggestions.slice(0, 12).map((suggestion, index) => (
            <TouchableOpacity
              key={suggestion}
              style={[styles.quickSuggestionChip, { backgroundColor: colors.surface, borderColor: colors.border }]}
              onPress={() => handleQuickSuggestion(suggestion)}
            >
              <Text style={[styles.quickSuggestionText, { color: colors.text }]}>
                {suggestion.charAt(0).toUpperCase() + suggestion.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  const renderBrandSelector = () => {
    if (availableBrands.length === 0) return null;

    return (
      <View style={styles.section}>
        <Text style={[styles.label, { color: colors.text }]}>Available Brands</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.brandScroll}>
          {availableBrands.map((brand) => (
            <TouchableOpacity
              key={brand}
              style={[
                styles.brandChip,
                { 
                  backgroundColor: selectedBrand === brand ? theme.colors.primary : colors.backgroundSecondary,
                  borderColor: selectedBrand === brand ? theme.colors.primary : colors.border,
                }
              ]}
              onPress={() => setSelectedBrand(brand)}
            >
              <Text style={[
                styles.brandText,
                { 
                  color: selectedBrand === brand ? 'white' : colors.text,
                }
              ]}>
                {brand}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  const renderSizeSelector = () => {
    if (availableSizes.length === 0) return null;

    return (
      <View style={styles.section}>
        <Text style={[styles.label, { color: colors.text }]}>Available Sizes</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.sizeScroll}>
          {availableSizes.map((size) => (
            <TouchableOpacity
              key={size}
              style={[
                styles.sizeChip,
                { 
                  backgroundColor: selectedSize === size ? theme.colors.primary : colors.backgroundSecondary,
                  borderColor: selectedSize === size ? theme.colors.primary : colors.border,
                }
              ]}
              onPress={() => setSelectedSize(size)}
            >
              <Text style={[
                styles.sizeText,
                { 
                  color: selectedSize === size ? 'white' : colors.text,
                }
              ]}>
                {size}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  const renderCategorySelector = () => (
    <View style={styles.section}>
      <Text style={[styles.label, { color: colors.text }]}>Category</Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoryScroll}>
        {categories.map((cat) => (
          <TouchableOpacity
            key={cat}
            style={[
              styles.categoryChip,
              { 
                backgroundColor: category === cat ? theme.colors.primary : colors.backgroundSecondary,
                borderColor: category === cat ? theme.colors.primary : colors.border,
              }
            ]}
            onPress={() => setCategory(cat)}
          >
            <Text style={[
              styles.categoryText,
              { 
                color: category === cat ? 'white' : colors.text,
              }
            ]}>
              {cat}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderUnitSelector = () => (
    <View style={styles.section}>
      <Text style={[styles.label, { color: colors.text }]}>Unit (Optional)</Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.unitScroll}>
        {units.map((u) => (
          <TouchableOpacity
            key={u}
            style={[
              styles.unitChip,
              { 
                backgroundColor: unit === u ? theme.colors.primary : colors.backgroundSecondary,
                borderColor: unit === u ? theme.colors.primary : colors.border,
              }
            ]}
            onPress={() => setUnit(u)}
          >
            <Text style={[
              styles.unitText,
              { 
                color: unit === u ? 'white' : colors.text,
              }
            ]}>
              {u || 'None'}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderQuantitySelector = () => (
    <View style={styles.section}>
      <Text style={[styles.label, { color: colors.text }]}>Quantity</Text>
      <View style={styles.quantityContainer}>
        <TouchableOpacity
          style={[styles.quantityButton, { borderColor: colors.border }]}
          onPress={() => setQuantity(Math.max(1, quantity - 1))}
        >
          <Text style={[styles.quantityButtonText, { color: colors.text }]}>−</Text>
        </TouchableOpacity>
        <Text style={[styles.quantityText, { color: colors.text }]}>{quantity}</Text>
        <TouchableOpacity
          style={[styles.quantityButton, { borderColor: colors.border }]}
          onPress={() => setQuantity(Math.min(99, quantity + 1))}
        >
          <Text style={[styles.quantityButtonText, { color: colors.text }]}>+</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        {/* Header */}
        <View style={[styles.header, { borderBottomColor: colors.border }]}>
          <TouchableOpacity onPress={onClose}>
            <Text style={[styles.cancelButton, { color: theme.colors.primary }]}>Cancel</Text>
          </TouchableOpacity>
          <Text style={[styles.title, { color: colors.text }]}>
            {isEditing ? 'Edit Item' : 'Add Item'}
          </Text>
          <TouchableOpacity onPress={handleSave}>
            <Text style={[styles.saveButton, { color: theme.colors.primary }]}>
              {isEditing ? 'Save' : 'Add'}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Form Content */}
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Product Search */}
          <View style={styles.section}>
            <Text style={[styles.label, { color: colors.text }]}>Search Products *</Text>
            <View style={styles.searchContainer}>
              <TextInput
                style={[
                  styles.input,
                  { 
                    backgroundColor: colors.surface,
                    borderColor: showSuggestions ? theme.colors.primary : colors.border,
                    color: colors.text,
                  }
                ]}
                value={searchQuery}
                onChangeText={handleSearchChange}
                placeholder="Start typing a product name..."
                placeholderTextColor={colors.textTertiary}
                autoFocus={!isEditing}
                onFocus={() => {
                  if (searchResults.length > 0) {
                    setShowSuggestions(true);
                  }
                }}
              />
              {isSearching && (
                <ActivityIndicator 
                  size="small" 
                  color={theme.colors.primary} 
                  style={styles.searchLoader}
                />
              )}
            </View>
            {renderProductSuggestions()}
            {renderQuickSuggestions()}
          </View>

          {/* Quantity */}
          {renderQuantitySelector()}

          {/* Unit */}
          {renderUnitSelector()}

          {/* Selected Product Info */}
          {selectedProduct && (
            <View style={[styles.selectedProductCard, { backgroundColor: colors.backgroundSecondary, borderColor: colors.border }]}>
              <Text style={[styles.selectedProductName, { color: colors.text }]}>
                ✓ {selectedProduct.name}
              </Text>
              <Text style={[styles.selectedProductInfo, { color: colors.textSecondary }]}>
                {selectedProduct.brand} • {selectedProduct.category}
              </Text>
            </View>
          )}

          {/* Brand and size selection removed - use "Change" button on shopping list items instead */}

          {/* Category (only if no product selected) */}
          {!selectedProduct && renderCategorySelector()}

          {/* Price Preview */}
          {selectedProduct && selectedProduct.storePrices.length > 0 && (
            <View style={[styles.pricePreviewSection, { backgroundColor: colors.backgroundSecondary, borderColor: colors.border }]}>
              <Text style={[styles.pricePreviewTitle, { color: colors.text }]}>💰 Current Prices</Text>
              <View style={styles.pricePreviewContainer}>
                {selectedProduct.storePrices.map((sp, index) => (
                  <View key={index} style={styles.pricePreviewItem}>
                    <Text style={styles.storeEmoji}>
                      {sp.store === 'woolworths' ? '🍎' : sp.store === 'paknsave' ? '💰' : '🛒'}
                    </Text>
                    <Text style={[styles.pricePreviewPrice, { color: colors.text }]}>
                      ${sp.price?.toFixed(2) || 'N/A'}
                    </Text>
                  </View>
                ))}
              </View>
            </View>
          )}

          {/* Note */}
          <View style={[styles.noteSection, { backgroundColor: colors.backgroundSecondary }]}>
            <Text style={[styles.noteText, { color: colors.textSecondary }]}>
              {selectedProduct 
                ? '✨ Real-time prices from our store partners. Prices may vary.' 
                : '🔍 Start typing to search thousands of products with real prices.'}
            </Text>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.base,
    paddingVertical: theme.spacing.sm,
    borderBottomWidth: 1,
  },
  cancelButton: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.medium,
  },
  title: {
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.semibold,
  },
  saveButton: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.semibold,
  },
  content: {
    flex: 1,
    paddingHorizontal: theme.spacing.base,
  },
  section: {
    marginVertical: theme.spacing.sm,
  },
  label: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.medium,
    marginBottom: theme.spacing.xs,
  },
  input: {
    borderWidth: 1,
    borderRadius: theme.radius.base,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.sm,
    fontSize: theme.typography.fontSize.base,
  },
  categoryScroll: {
    flexDirection: 'row',
  },
  categoryChip: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.radius.lg,
    borderWidth: 1,
    marginRight: theme.spacing.xs,
  },
  categoryText: {
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.medium,
  },
  unitScroll: {
    flexDirection: 'row',
  },
  unitChip: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.radius.lg,
    borderWidth: 1,
    marginRight: theme.spacing.xs,
  },
  unitText: {
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.medium,
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
  quantityButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  quantityButtonText: {
    fontSize: 20,
    fontWeight: theme.typography.fontWeight.medium,
  },
  quantityText: {
    fontSize: theme.typography.fontSize.xl,
    fontWeight: theme.typography.fontWeight.semibold,
    minWidth: 40,
    textAlign: 'center',
  },
  noteSection: {
    padding: theme.spacing.sm,
    borderRadius: theme.radius.base,
    marginVertical: theme.spacing.base,
  },
  noteText: {
    fontSize: theme.typography.fontSize.sm,
    lineHeight: 20,
    textAlign: 'center',
  },
  searchContainer: {
    position: 'relative',
  },
  searchLoader: {
    position: 'absolute',
    right: theme.spacing.sm,
    top: '50%',
    marginTop: -10,
  },
  suggestionsContainer: {
    borderWidth: 1,
    borderRadius: theme.radius.base,
    marginTop: theme.spacing.xs,
    maxHeight: 200,
  },
  suggestionsList: {
    maxHeight: 200,
  },
  suggestionItem: {
    padding: theme.spacing.sm,
    borderBottomWidth: 1,
  },
  suggestionContent: {
    flex: 1,
  },
  suggestionName: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.medium,
    marginBottom: 2,
  },
  suggestionBrand: {
    fontSize: theme.typography.fontSize.sm,
    marginBottom: 4,
  },
  suggestionPrices: {
    flexDirection: 'row',
    gap: theme.spacing.sm,
  },
  suggestionPrice: {
    fontSize: theme.typography.fontSize.xs,
  },
  selectedProductCard: {
    padding: theme.spacing.sm,
    borderRadius: theme.radius.base,
    borderWidth: 1,
    marginBottom: theme.spacing.sm,
  },
  selectedProductName: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.semibold,
    marginBottom: 2,
  },
  selectedProductInfo: {
    fontSize: theme.typography.fontSize.sm,
  },
  brandScroll: {
    flexDirection: 'row',
  },
  brandChip: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.radius.lg,
    borderWidth: 1,
    marginRight: theme.spacing.xs,
  },
  brandText: {
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.medium,
  },
  sizeScroll: {
    flexDirection: 'row',
  },
  sizeChip: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.radius.lg,
    borderWidth: 1,
    marginRight: theme.spacing.xs,
  },
  sizeText: {
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.medium,
  },
  pricePreviewSection: {
    padding: theme.spacing.sm,
    borderRadius: theme.radius.base,
    borderWidth: 1,
    marginVertical: theme.spacing.sm,
  },
  pricePreviewTitle: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.semibold,
    marginBottom: theme.spacing.xs,
  },
  pricePreviewContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  pricePreviewItem: {
    alignItems: 'center',
    gap: 4,
  },
  storeEmoji: {
    fontSize: 24,
  },
  pricePreviewPrice: {
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.medium,
  },
  quickSuggestionsContainer: {
    padding: theme.spacing.base,
    marginBottom: theme.spacing.sm,
  },
  quickSuggestionsTitle: {
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.semibold,
    marginBottom: theme.spacing.xs,
  },
  quickSuggestionsSubtitle: {
    fontSize: theme.typography.fontSize.sm,
    marginBottom: theme.spacing.base,
  },
  quickSuggestionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing.xs,
  },
  quickSuggestionChip: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.radius.lg,
    borderWidth: 1,
    marginRight: theme.spacing.xs,
    marginBottom: theme.spacing.xs,
  },
  quickSuggestionText: {
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.medium,
  },
  // Spelling suggestions styles
  spellingSuggestionsHeader: {
    padding: theme.spacing.sm,
    borderBottomWidth: 1,
  },
  spellingSuggestionsTitle: {
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.medium,
  },
  // Enhanced suggestion styles
  suggestionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  suggestionTextContent: {
    flex: 1,
  },
  suggestionNameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  popularBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    marginLeft: theme.spacing.xs,
  },
  popularBadgeText: {
    fontSize: theme.typography.fontSize.xs,
    color: 'white',
    fontWeight: theme.typography.fontWeight.medium,
  },
  suggestionImageContainer: {
    width: 40,
    height: 40,
    marginLeft: theme.spacing.sm,
    borderRadius: theme.radius.sm,
    overflow: 'hidden',
    backgroundColor: '#f5f5f5',
  },
  suggestionImage: {
    width: '100%',
    height: '100%',
  },
});