import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  FlatList,
  TouchableOpacity,
  Image,
  StyleSheet,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { useProductBrands } from '../hooks/useProductBrands';
import { productImageService } from '../services/productImageService';

const { width: screenWidth } = Dimensions.get('window');
const CARD_WIDTH = 120;
const CARD_MARGIN = 8;

interface BrandCard {
  brand: string;
  imageUrl?: string;
  isLoading?: boolean;
}

interface SwipeableBrandSelectorProps {
  userId: string;
  onBrandSelected: (product: string, brand: string) => void;
  placeholder?: string;
  style?: any;
}

export function SwipeableBrandSelector({
  userId,
  onBrandSelected,
  placeholder = "Search products...",
  style,
}: SwipeableBrandSelectorProps) {
  const [searchText, setSearchText] = useState('');
  const [brandCards, setBrandCards] = useState<BrandCard[]>([]);
  const [imagesLoading, setImagesLoading] = useState(false);
  const flatListRef = useRef<FlatList>(null);
  
  const { brands, isLoading, pick } = useProductBrands(searchText, userId);

  // Load images when brands change
  React.useEffect(() => {
    if (brands.length > 0 && searchText.length > 1) {
      loadBrandImages();
    } else {
      setBrandCards([]);
    }
  }, [brands, searchText]);

  const loadBrandImages = async () => {
    setImagesLoading(true);
    const cards: BrandCard[] = [];

    for (const brand of brands) {
      cards.push({ brand, isLoading: true });
    }
    setBrandCards(cards);

    // Load images in parallel
    const imagePromises = brands.map(async (brand, index) => {
      try {
        const productImage = await productImageService.getBrandImage({
          productName: searchText,
          brand,
          size: 'thumbnail',
          fallbackToGeneric: true,
        });

        return {
          index,
          brand,
          imageUrl: productImage?.imageUrl || productImage?.thumbnailUrl,
          isLoading: false,
        };
      } catch (error) {
        console.warn(`Failed to load image for ${brand}:`, error);
        return {
          index,
          brand,
          imageUrl: undefined,
          isLoading: false,
        };
      }
    });

    const results = await Promise.all(imagePromises);
    
    // Update cards with loaded images
    setBrandCards(prevCards => {
      const newCards = [...prevCards];
      results.forEach(result => {
        if (newCards[result.index]) {
          newCards[result.index] = {
            brand: result.brand,
            imageUrl: result.imageUrl,
            isLoading: false,
          };
        }
      });
      return newCards;
    });
    
    setImagesLoading(false);
  };

  const handleBrandSelect = async (brand: string) => {
    await pick(brand);
    onBrandSelected(searchText, brand);
    setSearchText('');
    setBrandCards([]);
  };

  const renderBrandCard = ({ item }: { item: BrandCard }) => (
    <TouchableOpacity
      style={styles.brandCard}
      onPress={() => handleBrandSelect(item.brand)}
      activeOpacity={0.8}
    >
      <View style={styles.imageContainer}>
        {item.isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color="#007AFF" />
          </View>
        ) : item.imageUrl ? (
          <Image 
            source={{ uri: item.imageUrl }} 
            style={styles.brandImage}
            resizeMode="contain"
          />
        ) : (
          <View style={styles.placeholderImage}>
            <Text style={styles.placeholderText}>📦</Text>
          </View>
        )}
      </View>
      
      <Text style={styles.brandName} numberOfLines={2}>
        {item.brand}
      </Text>
      
      <Text style={styles.forText}>for {searchText}</Text>
    </TouchableOpacity>
  );

  const showCards = searchText.length > 1 && (brandCards.length > 0 || isLoading);

  return (
    <View style={[styles.container, style]}>
      <TextInput
        style={styles.searchInput}
        value={searchText}
        onChangeText={setSearchText}
        placeholder={placeholder}
        autoCapitalize="none"
        autoCorrect={false}
        returnKeyType="search"
      />
      
      {isLoading && (
        <View style={styles.searchLoadingContainer}>
          <ActivityIndicator size="small" color="#007AFF" />
          <Text style={styles.searchLoadingText}>Searching brands...</Text>
        </View>
      )}
      
      {showCards && !isLoading && (
        <View style={styles.cardsContainer}>
          <Text style={styles.cardsTitle}>
            👆 Swipe to browse {brandCards.length} brands for "{searchText}"
          </Text>
          
          <FlatList
            ref={flatListRef}
            data={brandCards}
            renderItem={renderBrandCard}
            keyExtractor={(item) => item.brand}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.cardsList}
            snapToInterval={CARD_WIDTH + (CARD_MARGIN * 2)}
            decelerationRate="fast"
            snapToAlignment="start"
            ItemSeparatorComponent={() => <View style={{ width: CARD_MARGIN }} />}
          />
          
          {brandCards.length === 0 && !imagesLoading && (
            <View style={styles.noBrandsContainer}>
              <Text style={styles.noBrandsText}>
                No brands found for "{searchText}"
              </Text>
              <Text style={styles.tryAgainText}>
                Try a different search term
              </Text>
            </View>
          )}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  searchInput: {
    height: 56,
    borderWidth: 2,
    borderColor: '#E5E5E5',
    borderRadius: 12,
    paddingHorizontal: 20,
    fontSize: 16,
    backgroundColor: '#FAFAFA',
    fontWeight: '500',
  },
  searchLoadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
  },
  searchLoadingText: {
    marginLeft: 8,
    color: '#666',
    fontSize: 14,
  },
  cardsContainer: {
    paddingTop: 16,
    paddingBottom: 8,
  },
  cardsTitle: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  cardsList: {
    paddingHorizontal: 16,
  },
  brandCard: {
    width: CARD_WIDTH,
    alignItems: 'center',
    backgroundColor: '#FAFAFA',
    borderRadius: 12,
    padding: 12,
    marginHorizontal: CARD_MARGIN,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  imageContainer: {
    width: 80,
    height: 80,
    marginBottom: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  brandImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },
  loadingContainer: {
    width: 80,
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F0F0F0',
    borderRadius: 8,
  },
  placeholderImage: {
    width: 80,
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F0F0F0',
    borderRadius: 8,
  },
  placeholderText: {
    fontSize: 32,
  },
  brandName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
    marginBottom: 4,
    lineHeight: 18,
  },
  forText: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  noBrandsContainer: {
    alignItems: 'center',
    paddingVertical: 32,
    paddingHorizontal: 16,
  },
  noBrandsText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 8,
  },
  tryAgainText: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
  },
});